FROM ubuntu:22.04

WORKDIR /build

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo 'Asia/Shanghai' > /etc/timezone

RUN chmod 777 /tmp \
    && apt-get update  \
    && apt-get install -y --no-install-recommends \
    tzdata wget cron git gcc g++ make cmake gdb libtool build-essential \
    libgl1 libglib2.0-dev pkg-config autoconf openssh-server \
    && mkdir /var/run/sshd \
    && sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config \
    && sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config \
    && rm -rf /var/lib/apt/lists/*

# 设置系统时区
ENV TZ=Asia/Shanghai
# 支持显示中文
ENV LANG=C.UTF-8
# 添加conda到环境变量中
ENV CONDAHOME=/opt/miniforge3
ENV PATH=$PATH:$CONDAHOME/bin
ENV CONDA_AUTO_UPDATE_CONDA=false

# 安装Miniforge3
RUN wget --no-check-certificate https://github.com/conda-forge/miniforge/releases/download/23.11.0-0/Miniforge3-Linux-x86_64.sh -O ~/miniforge3.sh \
    && chmod +x ~/miniforge3.sh \
    && bash ~/miniforge3.sh -b -p $CONDAHOME \
    && $CONDAHOME/bin/conda init bash \
    && echo 'export PATH=/usr/bin:$PATH' >> ~/.bashrc \
    && conda config --set show_channel_urls true \
    && conda config --add channels conda-forge \
    && conda config --add channels pytorch \
    && rm ~/miniforge3.sh

# 安装Python相关依赖
RUN conda install -y \
    gcc=12.2.0 \
    && conda clean -ya

RUN pip install tensorflow==2.15.0 \
    && rm -rf /root/.cache/pip \
    && rm -rf /tmp/*

RUN pip install -i https://download.pytorch.org/whl/cu121 \
    torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 \
    && rm -rf /root/.cache/pip \
    && rm -rf /tmp/*

RUN pip install -i https://www.paddlepaddle.org.cn/packages/stable/cu123/ \
    paddlepaddle-gpu==3.0.0b1 \
    && rm -rf /root/.cache/pip \
    && rm -rf /tmp/*

RUN pip install mmcv_full=='1.7.0+torch2.1.1cu121' \
    -f https://modelscope.oss-cn-beijing.aliyuncs.com/releases/repo.html \
    && rm -rf /root/.cache/pip \
    && rm -rf /tmp/*

COPY . .

RUN pip install -r requirements_base.txt \
    && rm -rf /root/.cache/pip \
    && rm -rf /tmp/*

RUN pip install -r requirements.txt \
    && rm -rf /root/.cache/pip \
    && rm -rf /tmp/*

RUN rm -rf /build

WORKDIR /root