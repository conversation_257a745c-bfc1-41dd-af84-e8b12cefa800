#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/3/29 22:55
# @Modified: <EMAIL>
# @FileName: act.py

from functools import partial
from typing import Optional

import torch.nn as nn

from ...models.utils import build_kwargs_from_config

__all__ = ["build_act"]

# register activation function here
REGISTERED_ACT_DICT: dict[str, type] = {
    "relu": nn.ReLU,
    "relu6": nn.ReLU6,
    "hswish": nn.Hardswish,
    "silu": nn.SiLU,
    "gelu": partial(nn.GELU, approximate="tanh"),
}


def build_act(name: str, **kwargs) -> Optional[nn.Module]:
    if name in REGISTERED_ACT_DICT:
        act_cls = REGISTERED_ACT_DICT[name]
        args = build_kwargs_from_config(kwargs, act_cls)
        return act_cls(**args)
    else:
        return None
