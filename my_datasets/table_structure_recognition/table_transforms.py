#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-03 10:30
# <AUTHOR> <EMAIL>
# @FileName: table_transforms.py

import yaml
import random
from pathlib import Path
from abc import ABC, abstractmethod
from typing import Dict, Any, Tu<PERSON>, List, Optional, Union

import cv2
import torch
import numpy as np
from omegaconf import ListConfig

from modules.utils.log import LOGGER


class BaseTransform(ABC):
    """变换基类"""

    @abstractmethod
    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """应用变换"""
        pass

    def __repr__(self):
        return f"{self.__class__.__name__}()"


class LoadImageFromFile(BaseTransform):
    """从文件加载图像"""

    def __init__(self, to_float32: bool = False, color_type: str = 'color'):
        self.to_float32 = to_float32
        self.color_type = color_type

    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        if 'image_path' in sample:
            image_path = sample['image_path']
            if self.color_type == 'color':
                image = cv2.imread(str(image_path), cv2.IMREAD_COLOR)
            else:
                image = cv2.imread(str(image_path), cv2.IMREAD_GRAYSCALE)

            if image is None:
                raise ValueError(f"无法加载图像: {image_path}")

            if self.to_float32:
                image = image.astype(np.float32)

            sample['image'] = image
            sample['img_shape'] = image.shape
            sample['ori_shape'] = image.shape

        return sample


class LoadAnnotations(BaseTransform):
    """加载标注信息"""

    def __init__(
        self,
        with_bbox: bool = True,
        with_mask: bool = False,
        with_seg: bool = False,
        with_border_mask: bool = False
    ):
        self.with_bbox = with_bbox
        self.with_mask = with_mask
        self.with_seg = with_seg
        self.with_border_mask = with_border_mask

    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        # 这里的标注加载逻辑由数据集类处理
        # 这个变换类主要是标记需要加载哪些标注
        sample['load_annotations'] = {
            'with_bbox': self.with_bbox,
            'with_mask': self.with_mask,
            'with_seg': self.with_seg,
            'with_border_mask': self.with_border_mask
        }
        return sample


class PhotoMetricDistortion(BaseTransform):
    """光度变换，严格对齐Cycle-CenterNet参数"""

    def __init__(
        self,
        brightness_delta: int = 32,
        contrast_range: Tuple[float, float] = (0.5, 1.5),
        saturation_range: Tuple[float, float] = (0.5, 1.5),
        hue_delta: int = 18
    ):
        self.brightness_delta = brightness_delta
        self.contrast_lower, self.contrast_upper = contrast_range
        self.saturation_lower, self.saturation_upper = saturation_range
        self.hue_delta = hue_delta

    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        image = sample['image'].astype(np.float32)

        # 记录实际使用的参数
        actual_params = {
            'brightness_applied': False,
            'brightness_delta': 0.0,
            'contrast_applied': False,
            'contrast_alpha': 1.0,
            'contrast_mode': None,
            'saturation_applied': False,
            'saturation_alpha': 1.0,
            'hue_applied': False,
            'hue_delta': 0.0,
            'channel_swap_applied': False,
            'channel_order': [0, 1, 2]
        }

        # 1. 随机亮度
        brightness_flag = random.randint(0, 1)
        if brightness_flag:
            delta = random.uniform(-self.brightness_delta, self.brightness_delta)
            image += delta
            actual_params['brightness_applied'] = True
            actual_params['brightness_delta'] = delta

        # mode == 0 --> 先做随机对比度
        # mode == 1 --> 后做随机对比度
        mode = random.randint(0, 1)
        actual_params['contrast_mode'] = mode

        if mode == 1:
            contrast_flag = random.randint(0, 1)
            if contrast_flag:
                alpha = random.uniform(self.contrast_lower, self.contrast_upper)
                image *= alpha
                actual_params['contrast_applied'] = True
                actual_params['contrast_alpha'] = alpha

        # # 3. HSV变换
        # if image.shape[2] == 3:
        #     image_bgr = cv2.cvtColor(image.astype(np.uint8), cv2.COLOR_RGB2BGR)
        #     image_hsv = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2HSV).astype(np.float32)
        #
        #     # 4. 随机饱和度
        #     saturation_flag = random.randint(0, 1)
        #     if saturation_flag:
        #         saturation_alpha = random.uniform(self.saturation_lower, self.saturation_upper)
        #         image_hsv[:, :, 1] *= saturation_alpha
        #         actual_params['saturation_applied'] = True
        #         actual_params['saturation_alpha'] = saturation_alpha
        #
        #     # 5. 随机色调
        #     hue_flag = random.randint(0, 1)
        #     if hue_flag:
        #         hue_delta = random.uniform(-self.hue_delta, self.hue_delta)
        #         image_hsv[:, :, 0] += hue_delta
        #         image_hsv[:, :, 0] = np.clip(image_hsv[:, :, 0], 0, 179)
        #         actual_params['hue_applied'] = True
        #         actual_params['hue_delta'] = hue_delta
        #
        #     # 6. 转回RGB
        #     image_hsv = np.clip(image_hsv, 0, 255).astype(np.uint8)
        #     image_bgr = cv2.cvtColor(image_hsv, cv2.COLOR_HSV2BGR)
        #     image = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2RGB).astype(np.float32)

        # 7. 随机对比度（模式0）
        if mode == 0:
            contrast_flag = random.randint(0, 1)
            if contrast_flag:
                alpha = random.uniform(self.contrast_lower, self.contrast_upper)
                image *= alpha
                actual_params['contrast_applied'] = True
                actual_params['contrast_alpha'] = alpha

        # 8. 随机交换通道
        channel_swap_flag = random.random() < 0.5
        if channel_swap_flag:
            permutation = np.random.permutation(3)
            image = image[:, :, permutation]
            actual_params['channel_swap_applied'] = True
            actual_params['channel_order'] = permutation.tolist()

        sample['image'] = image
        sample['photometric_params'] = actual_params

        return sample


class RandomCrop(BaseTransform):
    """随机裁剪变换，确保所有bbox完整保留"""

    def __init__(
        self,
        min_crop_ratio: float = 0.6,
        max_crop_ratio: float = 1.3,
        margin_ratio: float = 0.1,
        apply_prob: float = 1.0
    ):
        """
        初始化RandomCrop

        Args:
            min_crop_ratio: 最小裁剪比例（相对于bbox包围矩形）
            max_crop_ratio: 最大裁剪比例（相对于bbox包围矩形）
            margin_ratio: bbox边界的安全边距比例
            apply_prob: 应用该变换的概率
        """
        self.min_crop_ratio = min_crop_ratio
        self.max_crop_ratio = max_crop_ratio
        self.margin_ratio = margin_ratio
        self.apply_prob = apply_prob

    def _compute_bbox_bounds(self, bboxes: np.ndarray) -> Tuple[int, int, int, int]:
        """
        计算所有bbox的包围矩形

        Args:
            bboxes: 四边形边界框数组，形状为 (N, 8)，格式为 [x1, y1, x2, y2, x3, y3, x4, y4]

        Returns:
            包围矩形 (min_x, min_y, max_x, max_y)
        """
        if len(bboxes) == 0:
            return 0, 0, 0, 0

        # 将四边形bbox重塑为顶点格式 (N, 4, 2)
        vertices = bboxes.reshape(-1, 4, 2)

        # 计算所有顶点的最小和最大坐标
        all_x = vertices[:, :, 0].flatten()  # 所有x坐标
        all_y = vertices[:, :, 1].flatten()  # 所有y坐标

        min_x = int(np.floor(all_x.min()))
        min_y = int(np.floor(all_y.min()))
        max_x = int(np.ceil(all_x.max()))
        max_y = int(np.ceil(all_y.max()))

        return min_x, min_y, max_x, max_y

    def _compute_crop_region(
        self,
        img_h: int,
        img_w: int,
        bbox_bounds: Tuple[int, int, int, int]
    ) -> Tuple[int, int, int, int]:
        """
        根据bbox边界计算裁剪区域，确保完整包含表格

        新策略：
        1. 先按参数计算初始裁剪区域
        2. 检查是否完全包含表格
        3. 如果不包含，从区域中心向外扩展直到完全包含
        4. 应用margin_ratio做微调
        5. 根据图像边界截断

        Args:
            img_h, img_w: 原始图像尺寸
            bbox_bounds: bbox包围矩形 (min_x, min_y, max_x, max_y)

        Returns:
            裁剪区域 (crop_x1, crop_y1, crop_x2, crop_y2)
        """
        min_x, min_y, max_x, max_y = bbox_bounds
        bbox_w = max_x - min_x
        bbox_h = max_y - min_y
        bbox_center_x = (min_x + max_x) // 2
        bbox_center_y = (min_y + max_y) // 2

        # 1. 按参数计算初始裁剪尺寸
        initial_crop_w = random.uniform(
            bbox_w * self.min_crop_ratio,
            bbox_w * self.max_crop_ratio
        )
        initial_crop_h = random.uniform(
            bbox_h * self.min_crop_ratio,
            bbox_h * self.max_crop_ratio
        )

        # 2. 随机选择裁剪区域中心（在合理范围内）
        # 允许中心点在bbox中心附近浮动
        center_offset_x = random.randint(-bbox_w//4, bbox_w//4)
        center_offset_y = random.randint(-bbox_h//4, bbox_h//4)
        crop_center_x = bbox_center_x + center_offset_x
        crop_center_y = bbox_center_y + center_offset_y

        # 3. 计算初始裁剪区域
        half_w = int(initial_crop_w // 2)
        half_h = int(initial_crop_h // 2)
        crop_x1 = crop_center_x - half_w
        crop_y1 = crop_center_y - half_h
        crop_x2 = crop_center_x + half_w
        crop_y2 = crop_center_y + half_h

        # 4. 检查并扩展以确保完全包含表格
        # 如果裁剪区域不能完全包含bbox，则向外扩展
        if crop_x1 > min_x:  # 左边界需要扩展
            crop_x1 = min_x
        if crop_y1 > min_y:  # 上边界需要扩展
            crop_y1 = min_y
        if crop_x2 < max_x:  # 右边界需要扩展
            crop_x2 = max_x
        if crop_y2 < max_y:  # 下边界需要扩展
            crop_y2 = max_y

        # 5. 应用margin_ratio进行微调扩展
        margin_w = int(bbox_w * self.margin_ratio)
        margin_h = int(bbox_h * self.margin_ratio)
        crop_x1 -= margin_w
        crop_y1 -= margin_h
        crop_x2 += margin_w
        crop_y2 += margin_h

        # 6. 根据图像边界进行截断
        crop_x1 = max(0, crop_x1)
        crop_y1 = max(0, crop_y1)
        crop_x2 = min(img_w, crop_x2)
        crop_y2 = min(img_h, crop_y2)

        # 7. 确保裁剪区域有效（宽度和高度都大于0）
        if crop_x2 <= crop_x1:
            crop_x1 = 0
            crop_x2 = img_w
        if crop_y2 <= crop_y1:
            crop_y1 = 0
            crop_y2 = img_h

        return crop_x1, crop_y1, crop_x2, crop_y2

    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用RandomCrop变换

        Args:
            sample: 输入样本数据

        Returns:
            变换后的样本数据
        """
        # 概率控制：如果随机数大于apply_prob，则跳过该变换
        if random.random() > self.apply_prob:
            return sample

        image = sample['image']
        bboxes = sample.get('bboxes', np.array([]))
        cell_centers = sample.get('cell_centers', np.array([]))

        # 确保图像是float32类型
        if image.dtype != np.float32:
            image = image.astype(np.float32)

        h, w, c = image.shape

        # 如果没有bbox，直接返回原图
        if len(bboxes) == 0:
            return sample

        # 计算bbox包围矩形
        bbox_bounds = self._compute_bbox_bounds(bboxes)

        # 计算裁剪区域
        crop_x1, crop_y1, crop_x2, crop_y2 = self._compute_crop_region(h, w, bbox_bounds)

        # 裁剪图像
        cropped_img = image[crop_y1:crop_y2, crop_x1:crop_x2, :]

        # 更新样本数据
        sample['image'] = cropped_img
        sample['img_shape'] = cropped_img.shape

        # 更新bbox坐标
        if len(bboxes) > 0:
            updated_bboxes = bboxes.copy()
            updated_bboxes[:, 0::2] -= crop_x1  # x坐标
            updated_bboxes[:, 1::2] -= crop_y1  # y坐标
            sample['bboxes'] = updated_bboxes

        # 更新中心点坐标
        if len(cell_centers) > 0:
            updated_centers = cell_centers.copy()
            updated_centers[:, 0] -= crop_x1  # x坐标
            updated_centers[:, 1] -= crop_y1  # y坐标
            sample['cell_centers'] = updated_centers

        # 裁剪border_masks（如果存在）
        if 'border_masks' in sample and sample['border_masks'] is not None:
            border_masks = sample['border_masks']
            if isinstance(border_masks, np.ndarray) and border_masks.size > 0:
                # 对每个通道的mask进行裁剪
                cropped_masks = []
                for i in range(border_masks.shape[0]):  # 遍历通道
                    mask = border_masks[i]
                    cropped_mask = mask[crop_y1:crop_y2, crop_x1:crop_x2]
                    cropped_masks.append(cropped_mask)

                sample['border_masks'] = np.stack(cropped_masks, axis=0)

        return sample

    def __repr__(self):
        repr_str = self.__class__.__name__
        repr_str += f'(min_crop_ratio={self.min_crop_ratio}, '
        repr_str += f'max_crop_ratio={self.max_crop_ratio}, '
        repr_str += f'margin_ratio={self.margin_ratio}, '
        repr_str += f'apply_prob={self.apply_prob})'
        return repr_str


class Resize(BaseTransform):
    """图像缩放变换"""

    def __init__(
        self,
        img_scale: Union[Tuple[int, int], List[Tuple[int, int]]],
        keep_ratio: bool = True,
        random_keep_ratio: bool = False,
        interpolation: str = 'bilinear',
        strict_validation: bool = True  # 严格验证模式
    ):
        """
        初始化Resize变换

        Args:
            img_scale: 目标尺寸，可以是单个尺寸或尺寸列表
            keep_ratio: 是否保持宽高比（仅当random_keep_ratio=False时生效）
            random_keep_ratio: 是否随机选择保持宽高比（优先级高于keep_ratio）
                - True: 忽略keep_ratio参数，随机选择True/False
                - False: 使用keep_ratio参数的固定值
            interpolation: 插值方法，'bilinear' 或 'nearest'
        """
        if isinstance(img_scale, list) and len(img_scale) > 0:
            # 如果是列表，检查第一个元素
            if isinstance(img_scale[0], (list, tuple, ListConfig)):
                self.img_scale = img_scale
            else:
                # 如果是单个尺寸，包装成列表
                self.img_scale = [img_scale]
        else:
            # 如果是单个尺寸，包装成列表
            self.img_scale = [img_scale]
        self.keep_ratio = keep_ratio
        self.random_keep_ratio = random_keep_ratio
        self.interpolation = interpolation
        self.strict_validation = strict_validation

    def _extract_dominant_color(self, image: np.ndarray) -> Tuple[float, float, float]:
        """
        提取图像的主色调

        Args:
            image: 输入图像，形状为 (H, W, 3)

        Returns:
            主色调的RGB值 (r, g, b)
        """
        # 确保图像是uint8格式
        if image.dtype == np.float32:
            img_uint8 = np.clip(image, 0, 255).astype(np.uint8)
        else:
            img_uint8 = image.astype(np.uint8)

        # 使用边缘像素的均值（通常是背景色）
        h, w = img_uint8.shape[:2]
        edge_pixels = []

        # 提取边缘像素
        edge_pixels.extend(img_uint8[0, :].reshape(-1, 3))      # 上边缘
        edge_pixels.extend(img_uint8[-1, :].reshape(-1, 3))     # 下边缘
        edge_pixels.extend(img_uint8[:, 0].reshape(-1, 3))      # 左边缘
        edge_pixels.extend(img_uint8[:, -1].reshape(-1, 3))     # 右边缘

        edge_pixels = np.array(edge_pixels)

        # 计算边缘像素的均值作为主色调
        dominant_color = np.mean(edge_pixels, axis=0)

        return tuple(dominant_color.astype(float))

    def _determine_fill_color(self, image: np.ndarray, current_keep_ratio: bool) -> Tuple[float, float, float]:
        """
        根据填充策略规则确定填充颜色

        Args:
            image: 输入图像
            current_keep_ratio: 当前是否保持宽高比

        Returns:
            填充颜色的RGB值 (r, g, b)
        """
        # 规则1: 当 keep_ratio=True 时（无论random_keep_ratio值如何）：严格使用黑色填充
        if self.keep_ratio:
            return (0.0, 0.0, 0.0)

        # 规则2: 当 keep_ratio=False 且 random_keep_ratio=True 时：随机选择黑色填充或主色调填充
        if not self.keep_ratio and self.random_keep_ratio:
            if current_keep_ratio:  # 如果这次随机选择了保持宽高比
                # 50%概率选择黑色或主色调
                if random.random() < 0.5:
                    return (0.0, 0.0, 0.0)  # 黑色填充
                else:
                    return self._extract_dominant_color(image)  # 主色调填充
            else:
                # 如果这次随机选择了不保持宽高比，不需要填充
                return (0.0, 0.0, 0.0)  # 默认黑色（实际不会用到）

        # 规则3: 当 keep_ratio=False 且 random_keep_ratio=False 时：保持现有行为（不涉及填充）
        return (0.0, 0.0, 0.0)  # 默认黑色（实际不会用到）

    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        # 随机选择一个尺寸
        target_size = random.choice(self.img_scale)

        # 决定是否保持宽高比
        # random_keep_ratio 的优先级比 keep_ratio 高
        if self.random_keep_ratio:
            # 当 random_keep_ratio=True 时，忽略 keep_ratio，随机选择
            current_keep_ratio = random.choice([True, False])
        else:
            # 当 random_keep_ratio=False 时，使用 keep_ratio 参数
            current_keep_ratio = self.keep_ratio

        # 缩放图像
        image, scale_factor = self._resize_image(sample['image'], target_size, current_keep_ratio)
        sample['image'] = image
        sample['img_shape'] = image.shape

        # 缩放边界框和中心点
        if 'bboxes' in sample and len(sample['bboxes']) > 0:
            scaled_bboxes = self._scale_bboxes(sample['bboxes'], scale_factor)

            # 严格验证：检查缩放后的坐标是否超出边界，如有问题则修正坐标
            if self.strict_validation:
                h_new, w_new = image.shape[:2]
                x_coords = scaled_bboxes[:, 0::2]
                y_coords = scaled_bboxes[:, 1::2]

                # 检查是否有坐标超出图像边界（坐标应该在[0, size)范围内）
                has_truncation = (
                    (x_coords < 0).any() or (x_coords > w_new).any() or
                    (y_coords < 0).any() or (y_coords > h_new).any()
                )

                if has_truncation:
                    # Resize不能跳过！必须修正坐标确保训练正常进行
                    LOGGER.debug(f"[Resize] 检测到坐标超出边界，修正坐标 (目标尺寸={target_size})")

                    # 修正坐标：将超出边界的坐标夹紧到有效范围内
                    scaled_bboxes[:, 0::2] = np.clip(scaled_bboxes[:, 0::2], 0, w_new - 1)  # x坐标
                    scaled_bboxes[:, 1::2] = np.clip(scaled_bboxes[:, 1::2], 0, h_new - 1)  # y坐标

                    LOGGER.debug(f"[Resize] 坐标修正完成，确保训练数据有效")

            sample['bboxes'] = scaled_bboxes

        if 'cell_centers' in sample and len(sample['cell_centers']) > 0:
            scaled_centers = self._scale_points(sample['cell_centers'], scale_factor)

            # 严格验证：检查缩放后的中心点是否超出边界，如有问题则修正坐标
            if self.strict_validation:
                h_new, w_new = image.shape[:2]

                has_center_truncation = (
                    (scaled_centers[:, 0] < 0).any() or (scaled_centers[:, 0] > w_new).any() or
                    (scaled_centers[:, 1] < 0).any() or (scaled_centers[:, 1] > h_new).any()
                )

                if has_center_truncation:
                    # Resize不能跳过！必须修正中心点坐标
                    LOGGER.debug(f"[Resize] 检测到中心点超出边界，修正坐标 (目标尺寸={target_size})")

                    # 修正中心点坐标
                    scaled_centers[:, 0] = np.clip(scaled_centers[:, 0], 0, w_new - 1)  # x坐标
                    scaled_centers[:, 1] = np.clip(scaled_centers[:, 1], 0, h_new - 1)  # y坐标

                    LOGGER.debug(f"[Resize] 中心点坐标修正完成")

            sample['cell_centers'] = scaled_centers

        # 缩放border_masks（如果存在）
        if 'border_masks' in sample and sample['border_masks'] is not None:
            border_masks = sample['border_masks']
            if isinstance(border_masks, np.ndarray) and border_masks.size > 0:
                # 使用与图像相同的缩放策略处理mask
                scaled_masks = self._resize_border_masks(border_masks, target_size, current_keep_ratio, scale_factor)
                sample['border_masks'] = scaled_masks

        sample['scale_factor'] = scale_factor
        sample['keep_ratio_used'] = current_keep_ratio  # 记录实际使用的keep_ratio
        return sample

    def _resize_image(self, image: np.ndarray, target_size: Tuple[int, int], keep_ratio: Optional[bool] = None) -> Tuple[np.ndarray, Tuple[float, float, float, float]]:
        """
        缩放图像

        Args:
            image: 输入图像
            target_size: 目标尺寸 (height, width)
            keep_ratio: 是否保持宽高比，如果为None则使用self.keep_ratio

        Returns:
            缩放后的图像和缩放因子 (scale_x, scale_y, offset_x, offset_y)
        """
        h, w = image.shape[:2]
        target_h, target_w = target_size

        # 如果没有指定keep_ratio，使用默认值
        if keep_ratio is None:
            keep_ratio = self.keep_ratio

        if keep_ratio:
            scale = min(target_h / h, target_w / w)
            new_h, new_w = int(h * scale), int(w * scale)

            if self.interpolation == 'bilinear':
                resized_image = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
            else:
                resized_image = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_NEAREST)

            # 🎨 智能填充策略：根据参数组合决定填充颜色
            fill_color = self._determine_fill_color(image, keep_ratio)

            # 创建填充背景
            final_image = np.full((target_h, target_w, image.shape[2]), fill_color, dtype=image.dtype)

            # 将缩放后的图像放置到中心位置
            y_offset = (target_h - new_h) // 2
            x_offset = (target_w - new_w) // 2
            final_image[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized_image

            # 返回 (scale_x, scale_y, offset_x, offset_y)
            scale_factor = (scale, scale, x_offset, y_offset)
        else:
            if self.interpolation == 'bilinear':
                final_image = cv2.resize(image, (target_w, target_h), interpolation=cv2.INTER_LINEAR)
            else:
                final_image = cv2.resize(image, (target_w, target_h), interpolation=cv2.INTER_NEAREST)

            # 无偏移的情况
            scale_factor = (target_w / w, target_h / h, 0, 0)

        return final_image, scale_factor

    def _scale_bboxes(self, bboxes: np.ndarray, scale_factor: Tuple[float, float, float, float]) -> np.ndarray:
        """
        缩放四边形bbox坐标

        Args:
            bboxes: 四边形bbox数组，形状为 (N, 8) - [x1, y1, x2, y2, x3, y3, x4, y4]
            scale_factor: 缩放因子 (scale_x, scale_y, offset_x, offset_y)

        Returns:
            缩放后的四边形bbox数组
        """
        if len(bboxes) == 0:
            return bboxes
        scale_x, scale_y, offset_x, offset_y = scale_factor
        scaled_bboxes = bboxes.copy()
        # 先缩放，再加偏移
        scaled_bboxes[:, 0::2] = scaled_bboxes[:, 0::2] * scale_x + offset_x  # x坐标
        scaled_bboxes[:, 1::2] = scaled_bboxes[:, 1::2] * scale_y + offset_y  # y坐标
        return scaled_bboxes

    def _scale_points(self, points: np.ndarray, scale_factor: Tuple[float, float, float, float]) -> np.ndarray:
        if len(points) == 0:
            return points
        scale_x, scale_y, offset_x, offset_y = scale_factor
        scaled_points = points.copy()
        # 先缩放，再加偏移
        scaled_points[:, 0] = scaled_points[:, 0] * scale_x + offset_x  # x坐标
        scaled_points[:, 1] = scaled_points[:, 1] * scale_y + offset_y  # y坐标
        return scaled_points

    def _resize_border_masks(
        self,
        border_masks: np.ndarray,
        target_size: Tuple[int, int],
        keep_ratio: bool,
        scale_factor: Tuple[float, float, float, float]
    ) -> np.ndarray:
        """
        缩放边框mask，与图像缩放策略保持一致

        Args:
            border_masks: 边框mask [C, H, W]
            target_size: 目标尺寸 (height, width)
            keep_ratio: 是否保持宽高比
            scale_factor: 缩放因子 (scale_x, scale_y, offset_x, offset_y)

        Returns:
            缩放后的边框mask [C, H, W]
        """
        target_h, target_w = target_size
        num_channels, orig_h, orig_w = border_masks.shape
        scale_x, scale_y, offset_x, offset_y = scale_factor

        scaled_masks = []

        for i in range(num_channels):
            mask = border_masks[i]

            if keep_ratio:
                # 保持宽高比模式：先缩放，再填充
                new_h, new_w = int(orig_h * scale_y), int(orig_w * scale_x)

                # 缩放mask（使用最近邻插值保持二值性质）
                resized_mask = cv2.resize(
                    mask.astype(np.float32),
                    (new_w, new_h),
                    interpolation=cv2.INTER_LINEAR
                )

                # 创建目标尺寸的mask，填充0（背景）
                final_mask = np.zeros((target_h, target_w), dtype=np.float32)

                # 将缩放后的mask放置到中心位置
                y_start = int(offset_y)
                x_start = int(offset_x)
                y_end = y_start + new_h
                x_end = x_start + new_w

                # 确保不超出边界
                y_end = min(y_end, target_h)
                x_end = min(x_end, target_w)
                actual_h = y_end - y_start
                actual_w = x_end - x_start

                final_mask[y_start:y_end, x_start:x_end] = resized_mask[:actual_h, :actual_w]

            else:
                # 不保持宽高比模式：直接缩放到目标尺寸
                final_mask = cv2.resize(
                    mask.astype(np.float32),
                    (target_w, target_h),
                    interpolation=cv2.INTER_LINEAR
                )

            scaled_masks.append(final_mask)

        return np.stack(scaled_masks, axis=0)

    def __repr__(self):
        repr_str = self.__class__.__name__
        repr_str += f'(img_scale={self.img_scale}, '
        repr_str += f'keep_ratio={self.keep_ratio}, '
        repr_str += f'random_keep_ratio={self.random_keep_ratio}, '
        repr_str += f'interpolation={self.interpolation}, '
        repr_str += f'smart_fill=True)'  # 标识支持智能填充
        return repr_str


class RandomFlip(BaseTransform):
    """随机翻转变换"""

    def __init__(self, flip_ratio: float = 0.5, direction: str = 'horizontal'):
        self.flip_ratio = flip_ratio
        self.direction = direction

    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        if random.random() < self.flip_ratio:
            image = sample['image']
            h, w = image.shape[:2]

            if self.direction == 'horizontal':
                image = cv2.flip(image, 1)
                if 'bboxes' in sample and len(sample['bboxes']) > 0:
                    bboxes = sample['bboxes'].copy()
                    # 对于四边形bbox，翻转所有x坐标
                    # bboxes形状: (N, 8) - [x1, y1, x2, y2, x3, y3, x4, y4]
                    bboxes[:, 0::2] = w - bboxes[:, 0::2]  # 翻转所有x坐标
                    sample['bboxes'] = bboxes
                if 'cell_centers' in sample and len(sample['cell_centers']) > 0:
                    centers = sample['cell_centers'].copy()
                    centers[:, 0] = w - centers[:, 0]
                    sample['cell_centers'] = centers

                # 翻转border_masks（如果存在）
                if 'border_masks' in sample and sample['border_masks'] is not None:
                    border_masks = sample['border_masks']
                    if isinstance(border_masks, np.ndarray) and border_masks.size > 0:
                        # 对每个通道的mask进行水平翻转
                        flipped_masks = []
                        for i in range(border_masks.shape[0]):  # 遍历通道
                            mask = border_masks[i]
                            flipped_mask = cv2.flip(mask, 1)  # 水平翻转
                            flipped_masks.append(flipped_mask)

                        sample['border_masks'] = np.stack(flipped_masks, axis=0)

            sample['image'] = image
        return sample


class Normalize(BaseTransform):
    """图像归一化变换"""

    def __init__(
        self,
        mean: List[float] = [0.485, 0.456, 0.406],
        std: List[float] = [0.229, 0.224, 0.225],
        to_rgb: bool = True
    ):
        self.mean = np.array(mean, dtype=np.float32)
        self.std = np.array(std, dtype=np.float32)
        self.to_rgb = to_rgb

    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        image = sample['image'].astype(np.float32)

        if self.to_rgb and image.shape[2] == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        image = image / 255.0
        image = (image - self.mean) / self.std

        sample['image'] = image
        return sample


class PerspectiveTransform(BaseTransform):
    """
    透视变换增强算子

    实现智能透视变换，确保变换后表格完整保留在图像区域内
    """

    def __init__(
        self,
        perspective_range: Tuple[float, float] = (0.0, 0.3),
        apply_prob: float = 0.5,
        preserve_aspect_ratio: bool = True,
        safety_margin: float = 0.05,
        fill_mode: str = 'dominant_color'  # 'constant', 'reflect', 'dominant_color'
    ):
        """
        初始化透视变换算子

        Args:
            perspective_range: 透视强度范围 (min_strength, max_strength)
                             0.0表示无变换，1.0表示最大变换
            apply_prob: 应用概率
            preserve_aspect_ratio: 是否尽量保持宽高比
            safety_margin: 安全边距比例，确保表格不会太接近图像边界
            fill_mode: 填充模式
                - 'constant': 使用常数值填充 (默认0)
                - 'reflect': 反射填充
                - 'dominant_color': 使用图像主色调填充
        """
        self.perspective_range = perspective_range
        self.apply_prob = apply_prob
        self.preserve_aspect_ratio = preserve_aspect_ratio
        self.safety_margin = safety_margin
        self.fill_mode = fill_mode

        # 验证参数
        if not (0.0 <= perspective_range[0] <= perspective_range[1] <= 1.0):
            raise ValueError("perspective_range must be in [0.0, 1.0] and min <= max")
        if not (0.0 <= apply_prob <= 1.0):
            raise ValueError("apply_prob must be in [0.0, 1.0]")
        if not (0.0 <= safety_margin <= 0.5):
            raise ValueError("safety_margin must be in [0.0, 0.5]")
        if fill_mode not in ['constant', 'reflect', 'dominant_color']:
            raise ValueError("fill_mode must be one of ['constant', 'reflect', 'dominant_color']")

    def _extract_dominant_color(self, image: np.ndarray) -> Tuple[float, float, float]:
        """
        提取图像的主色调（使用整体图像而非边缘）

        Args:
            image: 输入图像，形状为 (H, W, 3)

        Returns:
            主色调的RGB值 (r, g, b)
        """
        # 确保图像是uint8格式
        if image.dtype == np.float32:
            img_uint8 = np.clip(image, 0, 255).astype(np.uint8)
        else:
            img_uint8 = image.astype(np.uint8)

        # 降采样图像以提高计算效率
        h, w = img_uint8.shape[:2]
        # 将图像缩放到合适的尺寸进行颜色分析
        target_size = min(100, min(h, w))
        scale_factor = target_size / min(h, w)
        new_h, new_w = int(h * scale_factor), int(w * scale_factor)
        small_img = cv2.resize(img_uint8, (new_w, new_h))

        # 将图像重塑为像素数组
        pixels = small_img.reshape(-1, 3)

        # 方法1: 尝试使用K-means聚类找到主要颜色
        try:
            from sklearn.cluster import KMeans
            # 使用3个聚类中心
            kmeans = KMeans(n_clusters=3, random_state=42, n_init=10)
            kmeans.fit(pixels)

            # 找到最大的聚类作为主色调
            labels = kmeans.labels_
            unique, counts = np.unique(labels, return_counts=True)
            dominant_cluster = unique[np.argmax(counts)]
            dominant_color = kmeans.cluster_centers_[dominant_cluster]

        except ImportError:
            # 方法2: 如果没有sklearn，使用直方图方法
            # 将RGB空间量化为更少的颜色
            quantized = (pixels // 32) * 32  # 量化到32的倍数

            # 计算每种颜色的频率
            unique_colors, counts = np.unique(quantized.reshape(-1, 3), axis=0, return_counts=True)

            # 选择出现频率最高的颜色作为主色调
            dominant_idx = np.argmax(counts)
            dominant_color = unique_colors[dominant_idx]

        return tuple(dominant_color.astype(float))

    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """应用透视变换"""
        # 概率控制
        if random.random() > self.apply_prob:
            return sample

        image = sample['image']
        bboxes = sample.get('bboxes', np.array([]))
        cell_centers = sample.get('cell_centers', np.array([]))

        # 确保图像是float32类型
        if image.dtype != np.float32:
            image = image.astype(np.float32)

        h, w = image.shape[:2]

        # 如果没有bbox，直接返回原图
        if len(bboxes) == 0:
            return sample

        # 计算表格边界
        table_bounds = self._compute_table_bounds(bboxes)

        # 预估安全的透视强度
        safe_strength = self._estimate_safe_perspective_strength(
            table_bounds, (h, w), self.perspective_range
        )

        if safe_strength <= 0.0:
            # 如果无法安全应用透视变换，返回原图
            return sample

        # 生成透视变换矩阵
        perspective_matrix = self._generate_perspective_matrix(
            (h, w), safe_strength
        )

        # 确定填充策略
        if self.fill_mode == 'dominant_color':
            # 提取主色调
            dominant_color = self._extract_dominant_color(image)
            # 应用透视变换，使用主色调填充
            transformed_image = cv2.warpPerspective(
                image, perspective_matrix, (w, h),
                flags=cv2.INTER_LINEAR,
                borderMode=cv2.BORDER_CONSTANT,
                borderValue=dominant_color
            )
        elif self.fill_mode == 'reflect':
            # 使用反射填充
            transformed_image = cv2.warpPerspective(
                image, perspective_matrix, (w, h),
                flags=cv2.INTER_LINEAR,
                borderMode=cv2.BORDER_REFLECT_101
            )
        else:  # constant
            # 使用常数填充（默认黑色）
            transformed_image = cv2.warpPerspective(
                image, perspective_matrix, (w, h),
                flags=cv2.INTER_LINEAR,
                borderMode=cv2.BORDER_CONSTANT,
                borderValue=(0, 0, 0)
            )

        # 更新样本数据
        sample['image'] = transformed_image
        sample['img_shape'] = transformed_image.shape

        # 变换坐标
        if len(bboxes) > 0:
            transformed_bboxes = self._transform_bboxes(bboxes, perspective_matrix)
            sample['bboxes'] = transformed_bboxes

        if len(cell_centers) > 0:
            transformed_centers = self._transform_points(cell_centers, perspective_matrix)
            sample['cell_centers'] = transformed_centers

        # 变换border_masks（如果存在）
        if 'border_masks' in sample and sample['border_masks'] is not None:
            border_masks = sample['border_masks']
            if isinstance(border_masks, np.ndarray) and border_masks.size > 0:
                transformed_masks = []
                for i in range(border_masks.shape[0]):  # 遍历通道
                    mask = border_masks[i]
                    # 使用最近邻插值变换mask，保持二值性质
                    transformed_mask = cv2.warpPerspective(
                        mask.astype(np.float32),
                        perspective_matrix,
                        (w, h),
                        flags=cv2.INTER_LINEAR,
                        borderMode=cv2.BORDER_CONSTANT,
                        borderValue=0  # mask填充使用0（背景）
                    )
                    transformed_masks.append(transformed_mask)
                sample['border_masks'] = np.stack(transformed_masks, axis=0)

        # 记录变换信息
        sample['perspective_applied'] = True
        sample['perspective_strength'] = safe_strength
        sample['perspective_matrix'] = perspective_matrix

        return sample

    def _compute_table_bounds(self, bboxes: np.ndarray) -> Tuple[int, int, int, int]:
        """计算表格的边界矩形"""
        # 将四边形bbox重塑为顶点格式 (N, 4, 2)
        vertices = bboxes.reshape(-1, 4, 2)

        # 计算所有顶点的最小和最大坐标
        all_x = vertices[:, :, 0].flatten()
        all_y = vertices[:, :, 1].flatten()

        min_x = int(np.floor(all_x.min()))
        min_y = int(np.floor(all_y.min()))
        max_x = int(np.ceil(all_x.max()))
        max_y = int(np.ceil(all_y.max()))

        return min_x, min_y, max_x, max_y

    def _estimate_safe_perspective_strength(
        self,
        table_bounds: Tuple[int, int, int, int],
        image_size: Tuple[int, int],
        strength_range: Tuple[float, float]
    ) -> float:
        """
        预估安全的透视强度，确保表格不会超出图像边界

        Args:
            table_bounds: 表格边界 (min_x, min_y, max_x, max_y)
            image_size: 图像尺寸 (height, width)
            strength_range: 强度范围 (min_strength, max_strength)

        Returns:
            安全的透视强度
        """
        h, w = image_size
        min_x, min_y, max_x, max_y = table_bounds

        # 计算表格相对于图像的位置和大小
        table_w = max_x - min_x
        table_h = max_y - min_y
        table_center_x = (min_x + max_x) / 2
        table_center_y = (min_y + max_y) / 2

        # 计算到图像边界的距离
        margin_left = table_center_x - table_w / 2
        margin_right = w - (table_center_x + table_w / 2)
        margin_top = table_center_y - table_h / 2
        margin_bottom = h - (table_center_y + table_h / 2)

        # 计算最小边距
        min_margin = min(margin_left, margin_right, margin_top, margin_bottom)

        # 基于边距计算安全强度
        # 如果边距太小，降低透视强度
        margin_ratio = min_margin / min(w, h)
        safety_factor = max(0.0, min(1.0, margin_ratio / self.safety_margin))

        # 在给定范围内选择强度
        min_strength, max_strength = strength_range
        target_strength = random.uniform(min_strength, max_strength)

        # 应用安全因子
        safe_strength = target_strength * safety_factor

        return safe_strength

    def _generate_perspective_matrix(
        self,
        image_size: Tuple[int, int],
        strength: float
    ) -> np.ndarray:
        """
        生成透视变换矩阵

        Args:
            image_size: 图像尺寸 (height, width)
            strength: 透视强度 [0.0, 1.0]

        Returns:
            3x3透视变换矩阵
        """
        h, w = image_size

        # 定义原始四个角点
        src_points = np.float32([
            [0, 0],      # 左上
            [w, 0],      # 右上
            [w, h],      # 右下
            [0, h]       # 左下
        ])

        # 计算最大偏移量
        max_offset_x = w * strength * 0.2  # 最大20%的宽度偏移
        max_offset_y = h * strength * 0.2  # 最大20%的高度偏移

        # 随机生成目标点，创建透视效果
        dst_points = src_points.copy()

        # 随机选择透视方向
        perspective_type = random.choice(['horizontal', 'vertical', 'corner'])

        if perspective_type == 'horizontal':
            # 水平透视（梯形效果）
            offset_top = random.uniform(-max_offset_x, max_offset_x)
            offset_bottom = random.uniform(-max_offset_x, max_offset_x)
            dst_points[0, 0] += offset_top      # 左上x
            dst_points[1, 0] -= offset_top      # 右上x
            dst_points[2, 0] -= offset_bottom   # 右下x
            dst_points[3, 0] += offset_bottom   # 左下x

        elif perspective_type == 'vertical':
            # 垂直透视
            offset_left = random.uniform(-max_offset_y, max_offset_y)
            offset_right = random.uniform(-max_offset_y, max_offset_y)
            dst_points[0, 1] += offset_left     # 左上y
            dst_points[1, 1] += offset_right    # 右上y
            dst_points[2, 1] -= offset_right    # 右下y
            dst_points[3, 1] -= offset_left     # 左下y

        else:  # corner
            # 角点透视（更复杂的变形）
            for i in range(4):
                offset_x = random.uniform(-max_offset_x * 0.5, max_offset_x * 0.5)
                offset_y = random.uniform(-max_offset_y * 0.5, max_offset_y * 0.5)
                dst_points[i, 0] += offset_x
                dst_points[i, 1] += offset_y

        # 确保目标点在合理范围内
        dst_points[:, 0] = np.clip(dst_points[:, 0], -w * 0.1, w * 1.1)
        dst_points[:, 1] = np.clip(dst_points[:, 1], -h * 0.1, h * 1.1)

        # 计算透视变换矩阵
        perspective_matrix = cv2.getPerspectiveTransform(src_points, dst_points)

        return perspective_matrix

    def _transform_bboxes(self, bboxes: np.ndarray, matrix: np.ndarray) -> np.ndarray:
        """
        使用透视变换矩阵变换四边形bbox坐标

        Args:
            bboxes: 四边形bbox数组，形状为 (N, 8) - [x1, y1, x2, y2, x3, y3, x4, y4]
            matrix: 3x3透视变换矩阵

        Returns:
            变换后的四边形bbox数组
        """
        if len(bboxes) == 0:
            return bboxes

        # 将bbox重塑为顶点格式 (N, 4, 2)
        vertices = bboxes.reshape(-1, 4, 2)
        transformed_vertices = []

        for bbox_vertices in vertices:
            # 添加齐次坐标
            homogeneous_points = np.column_stack([bbox_vertices, np.ones(4)])

            # 应用透视变换
            transformed_points = matrix @ homogeneous_points.T

            # 转换回笛卡尔坐标
            transformed_points = transformed_points[:2] / transformed_points[2]
            transformed_vertices.append(transformed_points.T)

        # 重塑回原始格式 (N, 8)
        transformed_bboxes = np.array(transformed_vertices).reshape(-1, 8)

        return transformed_bboxes

    def _transform_points(self, points: np.ndarray, matrix: np.ndarray) -> np.ndarray:
        """
        使用透视变换矩阵变换点坐标

        Args:
            points: 点坐标数组，形状为 (N, 2)
            matrix: 3x3透视变换矩阵

        Returns:
            变换后的点坐标数组
        """
        if len(points) == 0:
            return points

        # 添加齐次坐标
        homogeneous_points = np.column_stack([points, np.ones(len(points))])

        # 应用透视变换
        transformed_points = matrix @ homogeneous_points.T

        # 转换回笛卡尔坐标
        transformed_points = transformed_points[:2] / transformed_points[2]

        return transformed_points.T

    def __repr__(self):
        repr_str = self.__class__.__name__
        repr_str += f'(perspective_range={self.perspective_range}, '
        repr_str += f'apply_prob={self.apply_prob}, '
        repr_str += f'preserve_aspect_ratio={self.preserve_aspect_ratio}, '
        repr_str += f'safety_margin={self.safety_margin})'
        return repr_str


class RandomRotation(BaseTransform):
    """
    随机旋转增强算子

    实现智能旋转变换，确保旋转后表格完整保留在图像区域内
    """

    def __init__(
        self,
        angle_range: Tuple[float, float] = (-15.0, 15.0),
        apply_prob: float = 0.5,
        center_mode: str = 'table_center',  # 'image_center', 'table_center', 'adaptive'
        safety_margin: float = 0.05,
        fill_mode: str = 'dominant_color',  # 'constant', 'dominant_color'
        fill_value: Union[int, Tuple[int, int, int]] = 0
    ):
        """
        初始化随机旋转算子

        Args:
            angle_range: 角度范围 (min_angle, max_angle) in degrees
            apply_prob: 应用概率
            center_mode: 旋转中心模式
                - 'image_center': 以图像中心为旋转中心
                - 'table_center': 以表格中心为旋转中心
                - 'adaptive': 自适应选择最佳旋转中心
            safety_margin: 安全边距比例
            fill_mode: 填充模式
                - 'constant': 使用fill_value填充
                - 'dominant_color': 使用图像主色调填充
            fill_value: 填充值，可以是单个值或RGB三元组（仅在fill_mode='constant'时使用）
        """
        self.angle_range = angle_range
        self.apply_prob = apply_prob
        self.center_mode = center_mode
        self.safety_margin = safety_margin
        self.fill_mode = fill_mode
        self.fill_value = fill_value

        # 验证参数
        if angle_range[0] > angle_range[1]:
            raise ValueError("angle_range[0] must be <= angle_range[1]")
        if not (0.0 <= apply_prob <= 1.0):
            raise ValueError("apply_prob must be in [0.0, 1.0]")
        if center_mode not in ['image_center', 'table_center', 'adaptive']:
            raise ValueError("center_mode must be one of ['image_center', 'table_center', 'adaptive']")
        if not (0.0 <= safety_margin <= 0.5):
            raise ValueError("safety_margin must be in [0.0, 0.5]")
        if fill_mode not in ['constant', 'dominant_color']:
            raise ValueError("fill_mode must be one of ['constant', 'dominant_color']")

    def _extract_dominant_color(self, image: np.ndarray) -> Tuple[float, float, float]:
        """
        提取图像的主色调（使用整体图像而非边缘）

        Args:
            image: 输入图像，形状为 (H, W, 3)

        Returns:
            主色调的RGB值 (r, g, b)
        """
        # 确保图像是uint8格式
        if image.dtype == np.float32:
            img_uint8 = np.clip(image, 0, 255).astype(np.uint8)
        else:
            img_uint8 = image.astype(np.uint8)

        # 降采样图像以提高计算效率
        h, w = img_uint8.shape[:2]
        # 将图像缩放到合适的尺寸进行颜色分析
        target_size = min(100, min(h, w))
        scale_factor = target_size / min(h, w)
        new_h, new_w = int(h * scale_factor), int(w * scale_factor)
        small_img = cv2.resize(img_uint8, (new_w, new_h))

        # 将图像重塑为像素数组
        pixels = small_img.reshape(-1, 3)

        # 方法1: 尝试使用K-means聚类找到主要颜色
        try:
            from sklearn.cluster import KMeans
            # 使用3个聚类中心
            kmeans = KMeans(n_clusters=3, random_state=42, n_init=10)
            kmeans.fit(pixels)

            # 找到最大的聚类作为主色调
            labels = kmeans.labels_
            unique, counts = np.unique(labels, return_counts=True)
            dominant_cluster = unique[np.argmax(counts)]
            dominant_color = kmeans.cluster_centers_[dominant_cluster]

        except ImportError:
            # 方法2: 如果没有sklearn，使用直方图方法
            # 将RGB空间量化为更少的颜色
            quantized = (pixels // 32) * 32  # 量化到32的倍数

            # 计算每种颜色的频率
            unique_colors, counts = np.unique(quantized.reshape(-1, 3), axis=0, return_counts=True)

            # 选择出现频率最高的颜色作为主色调
            dominant_idx = np.argmax(counts)
            dominant_color = unique_colors[dominant_idx]

        return tuple(dominant_color.astype(float))

    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """应用随机旋转"""
        # 概率控制
        if random.random() > self.apply_prob:
            return sample

        image = sample['image']
        bboxes = sample.get('bboxes', np.array([]))
        cell_centers = sample.get('cell_centers', np.array([]))

        # 确保图像是float32类型
        if image.dtype != np.float32:
            image = image.astype(np.float32)

        h, w = image.shape[:2]

        # 如果没有bbox，直接返回原图
        if len(bboxes) == 0:
            return sample

        # 计算表格边界
        table_bounds = self._compute_table_bounds(bboxes)

        # 确定旋转中心
        rotation_center = self._determine_rotation_center(
            table_bounds, (h, w), self.center_mode
        )

        # 预估安全的旋转角度
        safe_angle = self._estimate_safe_rotation_angle(
            table_bounds, (h, w), rotation_center, self.angle_range
        )

        if abs(safe_angle) < 0.1:  # 如果角度太小，跳过旋转
            return sample

        # 生成旋转矩阵
        rotation_matrix = cv2.getRotationMatrix2D(rotation_center, safe_angle, 1.0)

        # 确定填充策略
        if self.fill_mode == 'dominant_color':
            # 提取主色调
            dominant_color = self._extract_dominant_color(image)
            # 应用旋转变换，使用主色调填充
            rotated_image = cv2.warpAffine(
                image, rotation_matrix, (w, h),
                flags=cv2.INTER_LINEAR,
                borderMode=cv2.BORDER_CONSTANT,
                borderValue=dominant_color
            )
        else:  # constant
            # 使用指定的填充值
            rotated_image = cv2.warpAffine(
                image, rotation_matrix, (w, h),
                flags=cv2.INTER_LINEAR,
                borderMode=cv2.BORDER_CONSTANT,
                borderValue=self.fill_value
            )

        # 更新样本数据
        sample['image'] = rotated_image
        sample['img_shape'] = rotated_image.shape

        # 变换坐标
        if len(bboxes) > 0:
            rotated_bboxes = self._rotate_bboxes(bboxes, rotation_matrix)
            sample['bboxes'] = rotated_bboxes

        if len(cell_centers) > 0:
            rotated_centers = self._rotate_points(cell_centers, rotation_matrix)
            sample['cell_centers'] = rotated_centers

        # 变换border_masks（如果存在）
        if 'border_masks' in sample and sample['border_masks'] is not None:
            border_masks = sample['border_masks']
            if isinstance(border_masks, np.ndarray) and border_masks.size > 0:
                rotated_masks = []
                for i in range(border_masks.shape[0]):  # 遍历通道
                    mask = border_masks[i]
                    # 使用最近邻插值旋转mask，保持二值性质
                    rotated_mask = cv2.warpAffine(
                        mask.astype(np.float32),
                        rotation_matrix,
                        (w, h),
                        flags=cv2.INTER_NEAREST,
                        borderMode=cv2.BORDER_CONSTANT,
                        borderValue=0  # mask填充使用0（背景）
                    )
                    rotated_masks.append(rotated_mask)
                sample['border_masks'] = np.stack(rotated_masks, axis=0)

        # 记录变换信息
        sample['rotation_applied'] = True
        sample['rotation_angle'] = safe_angle
        sample['rotation_center'] = rotation_center
        sample['rotation_matrix'] = rotation_matrix

        return sample

    def _compute_table_bounds(self, bboxes: np.ndarray) -> Tuple[int, int, int, int]:
        """计算表格的边界矩形"""
        # 将四边形bbox重塑为顶点格式 (N, 4, 2)
        vertices = bboxes.reshape(-1, 4, 2)

        # 计算所有顶点的最小和最大坐标
        all_x = vertices[:, :, 0].flatten()
        all_y = vertices[:, :, 1].flatten()

        min_x = int(np.floor(all_x.min()))
        min_y = int(np.floor(all_y.min()))
        max_x = int(np.ceil(all_x.max()))
        max_y = int(np.ceil(all_y.max()))

        return min_x, min_y, max_x, max_y

    def _determine_rotation_center(
        self,
        table_bounds: Tuple[int, int, int, int],
        image_size: Tuple[int, int],
        center_mode: str
    ) -> Tuple[float, float]:
        """确定旋转中心"""
        h, w = image_size
        min_x, min_y, max_x, max_y = table_bounds

        if center_mode == 'image_center':
            return (w / 2, h / 2)
        elif center_mode == 'table_center':
            return ((min_x + max_x) / 2, (min_y + max_y) / 2)
        else:  # adaptive
            # 自适应选择：如果表格接近图像中心，使用表格中心；否则使用图像中心
            table_center_x = (min_x + max_x) / 2
            table_center_y = (min_y + max_y) / 2
            image_center_x = w / 2
            image_center_y = h / 2

            # 计算表格中心与图像中心的距离
            distance = np.sqrt((table_center_x - image_center_x)**2 +
                             (table_center_y - image_center_y)**2)

            # 如果距离小于图像对角线的1/4，使用表格中心
            if distance < np.sqrt(w**2 + h**2) / 4:
                return (table_center_x, table_center_y)
            else:
                return (image_center_x, image_center_y)

    def _estimate_safe_rotation_angle(
        self,
        table_bounds: Tuple[int, int, int, int],
        image_size: Tuple[int, int],
        rotation_center: Tuple[float, float],
        angle_range: Tuple[float, float]
    ) -> float:
        """
        预估安全的旋转角度，确保表格不会超出图像边界

        Args:
            table_bounds: 表格边界 (min_x, min_y, max_x, max_y)
            image_size: 图像尺寸 (height, width)
            rotation_center: 旋转中心 (cx, cy)
            angle_range: 角度范围 (min_angle, max_angle)

        Returns:
            安全的旋转角度
        """
        h, w = image_size
        min_x, min_y, max_x, max_y = table_bounds
        cx, cy = rotation_center

        # 计算表格四个角点
        table_corners = np.array([
            [min_x, min_y],  # 左上
            [max_x, min_y],  # 右上
            [max_x, max_y],  # 右下
            [min_x, max_y]   # 左下
        ])

        # 测试不同角度，找到最大安全角度
        min_angle, max_angle = angle_range
        test_angles = np.linspace(min_angle, max_angle, 20)

        safe_angles = []
        for angle in test_angles:
            # 计算旋转后的角点
            rad = np.radians(angle)
            cos_a, sin_a = np.cos(rad), np.sin(rad)

            rotated_corners = []
            for corner in table_corners:
                # 相对于旋转中心的坐标
                rel_x, rel_y = corner[0] - cx, corner[1] - cy
                # 旋转
                new_x = rel_x * cos_a - rel_y * sin_a + cx
                new_y = rel_x * sin_a + rel_y * cos_a + cy
                rotated_corners.append([new_x, new_y])

            rotated_corners = np.array(rotated_corners)

            # 检查是否所有角点都在图像边界内（考虑安全边距）
            margin_x = w * self.safety_margin
            margin_y = h * self.safety_margin

            if (np.all(rotated_corners[:, 0] >= margin_x) and
                np.all(rotated_corners[:, 0] <= w - margin_x) and
                np.all(rotated_corners[:, 1] >= margin_y) and
                np.all(rotated_corners[:, 1] <= h - margin_y)):
                safe_angles.append(angle)

        if not safe_angles:
            return 0.0  # 没有安全角度

        # 从安全角度中随机选择一个
        return random.choice(safe_angles)

    def _rotate_bboxes(self, bboxes: np.ndarray, rotation_matrix: np.ndarray) -> np.ndarray:
        """
        使用旋转矩阵旋转四边形bbox坐标

        Args:
            bboxes: 四边形bbox数组，形状为 (N, 8) - [x1, y1, x2, y2, x3, y3, x4, y4]
            rotation_matrix: 2x3旋转矩阵

        Returns:
            旋转后的四边形bbox数组
        """
        if len(bboxes) == 0:
            return bboxes

        # 将bbox重塑为顶点格式 (N, 4, 2)
        vertices = bboxes.reshape(-1, 4, 2)
        rotated_vertices = []

        for bbox_vertices in vertices:
            # 添加齐次坐标
            homogeneous_points = np.column_stack([bbox_vertices, np.ones(4)])

            # 应用旋转变换
            rotated_points = rotation_matrix @ homogeneous_points.T
            rotated_vertices.append(rotated_points.T)

        # 重塑回原始格式 (N, 8)
        rotated_bboxes = np.array(rotated_vertices).reshape(-1, 8)

        return rotated_bboxes

    def _rotate_points(self, points: np.ndarray, rotation_matrix: np.ndarray) -> np.ndarray:
        """
        使用旋转矩阵旋转点坐标

        Args:
            points: 点坐标数组，形状为 (N, 2)
            rotation_matrix: 2x3旋转矩阵

        Returns:
            旋转后的点坐标数组
        """
        if len(points) == 0:
            return points

        # 添加齐次坐标
        homogeneous_points = np.column_stack([points, np.ones(len(points))])

        # 应用旋转变换
        rotated_points = rotation_matrix @ homogeneous_points.T

        return rotated_points.T

    def __repr__(self):
        repr_str = self.__class__.__name__
        repr_str += f'(angle_range={self.angle_range}, '
        repr_str += f'apply_prob={self.apply_prob}, '
        repr_str += f'center_mode={self.center_mode}, '
        repr_str += f'safety_margin={self.safety_margin})'
        return repr_str


class PerspectiveTransformV2(BaseTransform):
    """
    透视变换增强算子 V2版本

    新思路：允许图像边界扩大，确保变换后的表格内容完整保留
    """

    def __init__(
        self,
        perspective_range: Tuple[float, float] = (0.0, 0.3),
        apply_prob: float = 0.5,
        fill_mode: str = 'dominant_color',  # 'constant', 'dominant_color'
        strict_validation: bool = True  # 严格验证模式，检测到截断时跳过变换
    ):
        """
        初始化透视变换算子V2

        Args:
            perspective_range: 透视强度范围 (min_strength, max_strength)
            apply_prob: 应用概率
            fill_mode: 填充模式
            strict_validation: 严格验证模式，检测到截断时跳过变换
        """
        self.perspective_range = perspective_range
        self.apply_prob = apply_prob
        self.fill_mode = fill_mode
        self.strict_validation = strict_validation

        # 验证参数
        if not (0.0 <= perspective_range[0] <= perspective_range[1] <= 1.0):
            raise ValueError("perspective_range must be in [0.0, 1.0] and min <= max")
        if not (0.0 <= apply_prob <= 1.0):
            raise ValueError("apply_prob must be in [0.0, 1.0]")

    def _extract_dominant_color(self, image: np.ndarray) -> Tuple[float, float, float]:
        """提取图像的主色调（使用整体图像而非边缘）"""
        if image.dtype == np.float32:
            img_uint8 = np.clip(image, 0, 255).astype(np.uint8)
        else:
            img_uint8 = image.astype(np.uint8)

        # 降采样图像以提高计算效率
        h, w = img_uint8.shape[:2]
        # 将图像缩放到合适的尺寸进行颜色分析
        target_size = min(100, min(h, w))
        scale_factor = target_size / min(h, w)
        new_h, new_w = int(h * scale_factor), int(w * scale_factor)
        small_img = cv2.resize(img_uint8, (new_w, new_h))

        # 将图像重塑为像素数组
        pixels = small_img.reshape(-1, 3)

        # 方法1: 尝试使用K-means聚类找到主要颜色
        try:
            from sklearn.cluster import KMeans
            # 使用3个聚类中心
            kmeans = KMeans(n_clusters=3, random_state=42, n_init=10)
            kmeans.fit(pixels)

            # 找到最大的聚类作为主色调
            labels = kmeans.labels_
            unique, counts = np.unique(labels, return_counts=True)
            dominant_cluster = unique[np.argmax(counts)]
            dominant_color = kmeans.cluster_centers_[dominant_cluster]

        except ImportError:
            # 方法2: 如果没有sklearn，使用直方图方法
            # 将RGB空间量化为更少的颜色
            quantized = (pixels // 32) * 32  # 量化到32的倍数

            # 计算每种颜色的频率
            unique_colors, counts = np.unique(quantized.reshape(-1, 3), axis=0, return_counts=True)

            # 选择出现频率最高的颜色作为主色调
            dominant_idx = np.argmax(counts)
            dominant_color = unique_colors[dominant_idx]

        return tuple(dominant_color.astype(float))

    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """应用透视变换V2"""
        # 概率控制
        if random.random() > self.apply_prob:
            return sample

        image = sample['image']
        bboxes = sample.get('bboxes', np.array([]))
        cell_centers = sample.get('cell_centers', np.array([]))

        # 保存原始数据用于回退
        original_border_masks = None
        if 'border_masks' in sample and sample['border_masks'] is not None:
            original_border_masks = sample['border_masks'].copy()
            sample['_original_border_masks'] = original_border_masks

        # 确保图像是float32类型
        if image.dtype != np.float32:
            image = image.astype(np.float32)

        h, w = image.shape[:2]

        # 如果没有bbox，直接返回原图
        if len(bboxes) == 0:
            return sample

        # 随机选择透视强度
        min_strength, max_strength = self.perspective_range
        strength = random.uniform(min_strength, max_strength)

        if strength <= 0.0:
            return sample

        # 生成透视变换矩阵
        perspective_matrix = self._generate_perspective_matrix((h, w), strength)

        # 计算变换后所需的图像尺寸
        new_size, offset = self._calculate_expanded_size(
            (h, w), perspective_matrix, bboxes
        )

        # 创建扩展后的图像
        expanded_image = self._create_expanded_image(image, new_size, offset)

        # 调整透视变换矩阵以适应新的图像尺寸
        adjusted_matrix = self._adjust_perspective_matrix(perspective_matrix, offset)

        # 应用透视变换
        if self.fill_mode == 'dominant_color':
            dominant_color = self._extract_dominant_color(image)
            transformed_image = cv2.warpPerspective(
                expanded_image, adjusted_matrix, (new_size[1], new_size[0]),
                flags=cv2.INTER_LINEAR,
                borderMode=cv2.BORDER_CONSTANT,
                borderValue=dominant_color
            )
        else:  # constant
            transformed_image = cv2.warpPerspective(
                expanded_image, adjusted_matrix, (new_size[1], new_size[0]),
                flags=cv2.INTER_LINEAR,
                borderMode=cv2.BORDER_CONSTANT,
                borderValue=(0, 0, 0)
            )

        # 更新样本数据
        sample['image'] = transformed_image
        sample['img_shape'] = transformed_image.shape

        # 变换border_masks（如果存在）
        if 'border_masks' in sample and sample['border_masks'] is not None:
            border_masks = sample['border_masks']
            if isinstance(border_masks, np.ndarray) and border_masks.size > 0:
                # 对每个通道的mask进行透视变换
                transformed_masks = []
                for i in range(border_masks.shape[0]):  # 遍历通道
                    mask = border_masks[i]

                    # 检查mask尺寸是否与当前图像尺寸匹配
                    mask_h, mask_w = mask.shape[:2]
                    if mask_h != h or mask_w != w:
                        # 如果尺寸不匹配，先将mask缩放到当前图像尺寸
                        mask = cv2.resize(
                            mask.astype(np.float32),
                            (w, h),
                            interpolation=cv2.INTER_LINEAR
                        )

                    # 创建扩展后的mask
                    expanded_mask = np.zeros(new_size, dtype=np.float32)
                    # 确保mask的实际尺寸与目标区域匹配
                    actual_h, actual_w = mask.shape[:2]
                    end_y = min(offset[0] + actual_h, new_size[0])
                    end_x = min(offset[1] + actual_w, new_size[1])
                    expanded_mask[offset[0]:end_y, offset[1]:end_x] = mask[:end_y-offset[0], :end_x-offset[1]]

                    # 应用透视变换（使用双线性插值）
                    transformed_mask = cv2.warpPerspective(
                        expanded_mask, adjusted_matrix, (new_size[1], new_size[0]),
                        flags=cv2.INTER_LINEAR,
                        borderMode=cv2.BORDER_CONSTANT,
                        borderValue=0  # mask填充0
                    )
                    transformed_masks.append(transformed_mask)

                sample['border_masks'] = np.stack(transformed_masks, axis=0)

        # 变换坐标（考虑偏移）
        if len(bboxes) > 0:
            # 先应用偏移，再应用透视变换
            offset_bboxes = bboxes.copy()
            offset_bboxes[:, 0::2] += offset[1]  # x坐标加偏移
            offset_bboxes[:, 1::2] += offset[0]  # y坐标加偏移

            transformed_bboxes = self._transform_bboxes(offset_bboxes, adjusted_matrix)

            # 严格验证：检查是否有坐标截断
            if self.strict_validation:
                h_new, w_new = transformed_image.shape[:2]
                x_coords = transformed_bboxes[:, 0::2]
                y_coords = transformed_bboxes[:, 1::2]

                # 检查是否有坐标超出图像边界（坐标应该在[0, size)范围内）
                has_truncation = (
                    (x_coords < 0).any() or (x_coords > w_new).any() or
                    (y_coords < 0).any() or (y_coords > h_new).any()
                )

                if has_truncation:
                    # 检测到截断，跳过变换，返回原始数据
                    LOGGER.debug(f"[PerspectiveTransformV2] 检测到坐标截断，跳过变换 (强度={strength:.3f})")
                    # 返回完整的原始样本，只更新必要的字段
                    sample['image'] = image  # 恢复原始图像
                    sample['img_shape'] = image.shape
                    sample['bboxes'] = bboxes  # 恢复原始bbox
                    if len(cell_centers) > 0:
                        sample['cell_centers'] = cell_centers  # 恢复原始中心点
                    # 恢复原始border_masks（如果存在）
                    if 'border_masks' in sample:
                        original_masks = sample.get('_original_border_masks')
                        if original_masks is not None:
                            sample['border_masks'] = original_masks
                    return sample

            sample['bboxes'] = transformed_bboxes

        if len(cell_centers) > 0:
            # 先应用偏移，再应用透视变换
            offset_centers = cell_centers.copy()
            offset_centers[:, 0] += offset[1]  # x坐标加偏移
            offset_centers[:, 1] += offset[0]  # y坐标加偏移

            transformed_centers = self._transform_points(offset_centers, adjusted_matrix)

            # 严格验证：检查中心点是否截断
            if self.strict_validation:
                h_new, w_new = transformed_image.shape[:2]

                has_center_truncation = (
                    (transformed_centers[:, 0] < 0).any() or (transformed_centers[:, 0] > w_new).any() or
                    (transformed_centers[:, 1] < 0).any() or (transformed_centers[:, 1] > h_new).any()
                )

                if has_center_truncation:
                    # 检测到中心点截断，跳过变换
                    LOGGER.debug(f"[PerspectiveTransformV2] 检测到中心点截断，跳过变换 (强度={strength:.3f})")
                    # 返回完整的原始样本，只更新必要的字段
                    sample['image'] = image  # 恢复原始图像
                    sample['img_shape'] = image.shape
                    sample['bboxes'] = bboxes  # 恢复原始bbox
                    sample['cell_centers'] = cell_centers  # 恢复原始中心点
                    # 恢复原始border_masks（如果存在）
                    if 'border_masks' in sample:
                        original_masks = sample.get('_original_border_masks')
                        if original_masks is not None:
                            sample['border_masks'] = original_masks
                    return sample

            sample['cell_centers'] = transformed_centers

        # 记录变换信息
        sample['perspective_v2_applied'] = True
        sample['perspective_v2_strength'] = strength
        sample['perspective_v2_matrix'] = adjusted_matrix
        sample['perspective_v2_offset'] = offset
        sample['perspective_v2_new_size'] = new_size

        return sample

    def _generate_perspective_matrix(
        self,
        image_size: Tuple[int, int],
        strength: float
    ) -> np.ndarray:
        """生成透视变换矩阵"""
        h, w = image_size

        # 定义原始四个角点
        src_points = np.float32([
            [0, 0],      # 左上
            [w, 0],      # 右上
            [w, h],      # 右下
            [0, h]       # 左下
        ])

        # 计算最大偏移量
        max_offset_x = w * strength * 0.2
        max_offset_y = h * strength * 0.2

        # 随机生成目标点
        dst_points = src_points.copy()

        # 随机选择透视方向
        perspective_type = random.choice(['horizontal', 'vertical', 'corner'])

        if perspective_type == 'horizontal':
            # 水平透视（梯形效果）
            offset_top = random.uniform(-max_offset_x, max_offset_x)
            offset_bottom = random.uniform(-max_offset_x, max_offset_x)
            dst_points[0, 0] += offset_top      # 左上x
            dst_points[1, 0] -= offset_top      # 右上x
            dst_points[2, 0] -= offset_bottom   # 右下x
            dst_points[3, 0] += offset_bottom   # 左下x

        elif perspective_type == 'vertical':
            # 垂直透视
            offset_left = random.uniform(-max_offset_y, max_offset_y)
            offset_right = random.uniform(-max_offset_y, max_offset_y)
            dst_points[0, 1] += offset_left     # 左上y
            dst_points[1, 1] += offset_right    # 右上y
            dst_points[2, 1] -= offset_right    # 右下y
            dst_points[3, 1] -= offset_left     # 左下y

        else:  # corner
            # 角点透视
            for i in range(4):
                offset_x = random.uniform(-max_offset_x * 0.5, max_offset_x * 0.5)
                offset_y = random.uniform(-max_offset_y * 0.5, max_offset_y * 0.5)
                dst_points[i, 0] += offset_x
                dst_points[i, 1] += offset_y

        # 计算透视变换矩阵
        perspective_matrix = cv2.getPerspectiveTransform(src_points, dst_points)
        return perspective_matrix

    def _calculate_expanded_size(
        self,
        original_size: Tuple[int, int],
        perspective_matrix: np.ndarray,
        bboxes: np.ndarray
    ) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        """计算扩展后的图像尺寸和偏移量"""
        h, w = original_size

        # 计算原图四个角点变换后的位置
        corners = np.float32([[0, 0], [w, 0], [w, h], [0, h]])
        corners_homogeneous = np.column_stack([corners, np.ones(4)])
        transformed_corners = perspective_matrix @ corners_homogeneous.T
        transformed_corners = transformed_corners[:2] / transformed_corners[2]
        transformed_corners = transformed_corners.T

        # 计算bbox变换后的位置
        vertices = bboxes.reshape(-1, 4, 2)
        all_transformed_points = []

        for bbox_vertices in vertices:
            homogeneous_points = np.column_stack([bbox_vertices, np.ones(4)])
            transformed_points = perspective_matrix @ homogeneous_points.T
            transformed_points = transformed_points[:2] / transformed_points[2]
            all_transformed_points.extend(transformed_points.T)

        # 合并所有变换后的点
        all_points = np.vstack([transformed_corners, np.array(all_transformed_points)])

        # 计算边界
        min_x = np.floor(all_points[:, 0].min()).astype(int)
        max_x = np.ceil(all_points[:, 0].max()).astype(int)
        min_y = np.floor(all_points[:, 1].min()).astype(int)
        max_y = np.ceil(all_points[:, 1].max()).astype(int)

        # 计算所需的图像尺寸
        new_w = max_x - min_x
        new_h = max_y - min_y

        # 计算偏移量（确保原图内容在新图中的位置）
        offset_x = max(0, -min_x)
        offset_y = max(0, -min_y)

        return (new_h, new_w), (offset_y, offset_x)

    def _create_expanded_image(
        self,
        image: np.ndarray,
        new_size: Tuple[int, int],
        offset: Tuple[int, int]
    ) -> np.ndarray:
        """创建扩展后的图像"""
        new_h, new_w = new_size
        offset_y, offset_x = offset

        # 确定填充颜色
        if self.fill_mode == 'dominant_color':
            fill_color = self._extract_dominant_color(image)
        else:
            fill_color = (0, 0, 0)

        # 创建新图像
        expanded_image = np.full((new_h, new_w, image.shape[2]), fill_color, dtype=image.dtype)

        # 将原图像放置到新图像中
        h, w = image.shape[:2]
        end_y = min(offset_y + h, new_h)
        end_x = min(offset_x + w, new_w)

        expanded_image[offset_y:end_y, offset_x:end_x] = image[:end_y-offset_y, :end_x-offset_x]

        return expanded_image

    def _adjust_perspective_matrix(
        self,
        matrix: np.ndarray,
        offset: Tuple[int, int]
    ) -> np.ndarray:
        """调整透视变换矩阵以适应偏移"""
        offset_y, offset_x = offset

        # 创建偏移矩阵
        offset_matrix = np.array([
            [1, 0, offset_x],
            [0, 1, offset_y],
            [0, 0, 1]
        ], dtype=np.float32)

        # 组合变换矩阵
        adjusted_matrix = matrix @ offset_matrix

        return adjusted_matrix

    def _transform_bboxes(self, bboxes: np.ndarray, matrix: np.ndarray) -> np.ndarray:
        """使用透视变换矩阵变换四边形bbox坐标"""
        if len(bboxes) == 0:
            return bboxes

        vertices = bboxes.reshape(-1, 4, 2)
        transformed_vertices = []

        for bbox_vertices in vertices:
            homogeneous_points = np.column_stack([bbox_vertices, np.ones(4)])
            transformed_points = matrix @ homogeneous_points.T
            transformed_points = transformed_points[:2] / transformed_points[2]
            transformed_vertices.append(transformed_points.T)

        transformed_bboxes = np.array(transformed_vertices).reshape(-1, 8)
        return transformed_bboxes

    def _transform_points(self, points: np.ndarray, matrix: np.ndarray) -> np.ndarray:
        """使用透视变换矩阵变换点坐标"""
        if len(points) == 0:
            return points

        homogeneous_points = np.column_stack([points, np.ones(len(points))])
        transformed_points = matrix @ homogeneous_points.T
        transformed_points = transformed_points[:2] / transformed_points[2]

        return transformed_points.T

    def __repr__(self):
        repr_str = self.__class__.__name__
        repr_str += f'(perspective_range={self.perspective_range}, '
        repr_str += f'apply_prob={self.apply_prob}, '
        repr_str += f'fill_mode={self.fill_mode}, '
        repr_str += f'strict_validation={self.strict_validation})'
        return repr_str


class RandomRotationV2(BaseTransform):
    """
    随机旋转增强算子 V2版本

    新思路：允许图像边界扩大，确保旋转后的表格内容完整保留
    """

    def __init__(
        self,
        angle_range: Tuple[float, float] = (-15.0, 15.0),
        apply_prob: float = 0.5,
        center_mode: str = 'table_center',  # 'image_center', 'table_center', 'adaptive'
        fill_mode: str = 'dominant_color',  # 'constant', 'dominant_color'
        fill_value: Union[int, Tuple[int, int, int]] = 0,
        strict_validation: bool = True  # 严格验证模式，检测到截断时跳过变换
    ):
        """
        初始化随机旋转算子V2

        Args:
            angle_range: 角度范围 (min_angle, max_angle) in degrees
            apply_prob: 应用概率
            center_mode: 旋转中心模式
            fill_mode: 填充模式
            fill_value: 填充值（仅在fill_mode='constant'时使用）
            strict_validation: 严格验证模式，检测到截断时跳过变换
        """
        self.angle_range = angle_range
        self.apply_prob = apply_prob
        self.center_mode = center_mode
        self.fill_mode = fill_mode
        self.fill_value = fill_value
        self.strict_validation = strict_validation

        # 验证参数
        if angle_range[0] > angle_range[1]:
            raise ValueError("angle_range[0] must be <= angle_range[1]")
        if not (0.0 <= apply_prob <= 1.0):
            raise ValueError("apply_prob must be in [0.0, 1.0]")
        if center_mode not in ['image_center', 'table_center', 'adaptive']:
            raise ValueError("center_mode must be one of ['image_center', 'table_center', 'adaptive']")
        if fill_mode not in ['constant', 'dominant_color']:
            raise ValueError("fill_mode must be one of ['constant', 'dominant_color']")


    def _extract_dominant_color(self, image: np.ndarray) -> Tuple[float, float, float]:
        """提取图像的主色调（使用整体图像而非边缘）"""
        if image.dtype == np.float32:
            img_uint8 = np.clip(image, 0, 255).astype(np.uint8)
        else:
            img_uint8 = image.astype(np.uint8)

        # 降采样图像以提高计算效率
        h, w = img_uint8.shape[:2]
        # 将图像缩放到合适的尺寸进行颜色分析
        target_size = min(100, min(h, w))
        scale_factor = target_size / min(h, w)
        new_h, new_w = int(h * scale_factor), int(w * scale_factor)
        small_img = cv2.resize(img_uint8, (new_w, new_h))

        # 将图像重塑为像素数组
        pixels = small_img.reshape(-1, 3)

        # 方法1: 尝试使用K-means聚类找到主要颜色
        try:
            from sklearn.cluster import KMeans
            # 使用3个聚类中心
            kmeans = KMeans(n_clusters=3, random_state=42, n_init=10)
            kmeans.fit(pixels)

            # 找到最大的聚类作为主色调
            labels = kmeans.labels_
            unique, counts = np.unique(labels, return_counts=True)
            dominant_cluster = unique[np.argmax(counts)]
            dominant_color = kmeans.cluster_centers_[dominant_cluster]

        except ImportError:
            # 方法2: 如果没有sklearn，使用直方图方法
            # 将RGB空间量化为更少的颜色
            quantized = (pixels // 32) * 32  # 量化到32的倍数

            # 计算每种颜色的频率
            unique_colors, counts = np.unique(quantized.reshape(-1, 3), axis=0, return_counts=True)

            # 选择出现频率最高的颜色作为主色调
            dominant_idx = np.argmax(counts)
            dominant_color = unique_colors[dominant_idx]

        return tuple(dominant_color.astype(float))

    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """应用随机旋转V2"""
        # 概率控制
        if random.random() > self.apply_prob:
            return sample

        image = sample['image']
        bboxes = sample.get('bboxes', np.array([]))
        cell_centers = sample.get('cell_centers', np.array([]))

        # 保存原始数据用于回退
        original_border_masks = None
        if 'border_masks' in sample and sample['border_masks'] is not None:
            original_border_masks = sample['border_masks'].copy()
            sample['_original_border_masks'] = original_border_masks

        # 确保图像是float32类型
        if image.dtype != np.float32:
            image = image.astype(np.float32)

        h, w = image.shape[:2]

        # 如果没有bbox，直接返回原图
        if len(bboxes) == 0:
            return sample

        # 计算表格边界
        table_bounds = self._compute_table_bounds(bboxes)

        # 确定旋转中心
        rotation_center = self._determine_rotation_center(
            table_bounds, (h, w), self.center_mode
        )

        # 随机选择旋转角度
        min_angle, max_angle = self.angle_range
        angle = random.uniform(min_angle, max_angle)

        if abs(angle) < 0.1:  # 如果角度太小，跳过旋转
            return sample

        # 计算旋转后所需的图像尺寸
        new_size, offset, actual_offset = self._calculate_expanded_size_for_rotation(
            (h, w), angle, rotation_center, bboxes
        )

        # 创建扩展后的图像
        expanded_image = self._create_expanded_image(image, new_size, offset)

        # 调整旋转中心以适应新的图像尺寸
        adjusted_center = (
            rotation_center[0] + offset[1],
            rotation_center[1] + offset[0]
        )

        # 生成旋转矩阵
        rotation_matrix = cv2.getRotationMatrix2D(adjusted_center, angle, 1.0)

        # 应用旋转变换
        if self.fill_mode == 'dominant_color':
            dominant_color = self._extract_dominant_color(image)
            rotated_image = cv2.warpAffine(
                expanded_image, rotation_matrix, (new_size[1], new_size[0]),
                flags=cv2.INTER_LINEAR,
                borderMode=cv2.BORDER_CONSTANT,
                borderValue=dominant_color
            )
        else:  # constant
            rotated_image = cv2.warpAffine(
                expanded_image, rotation_matrix, (new_size[1], new_size[0]),
                flags=cv2.INTER_LINEAR,
                borderMode=cv2.BORDER_CONSTANT,
                borderValue=self.fill_value
            )

        # 更新样本数据
        sample['image'] = rotated_image
        sample['img_shape'] = rotated_image.shape

        # 旋转border_masks（如果存在）
        if 'border_masks' in sample and sample['border_masks'] is not None:
            border_masks = sample['border_masks']
            if isinstance(border_masks, np.ndarray) and border_masks.size > 0:
                # 对每个通道的mask进行旋转
                rotated_masks = []
                for i in range(border_masks.shape[0]):  # 遍历通道
                    mask = border_masks[i]

                    # 检查mask尺寸是否与当前图像尺寸匹配
                    mask_h, mask_w = mask.shape[:2]
                    if mask_h != h or mask_w != w:
                        # 如果尺寸不匹配，先将mask缩放到当前图像尺寸
                        mask = cv2.resize(
                            mask.astype(np.float32),
                            (w, h),
                            interpolation=cv2.INTER_LINEAR
                        )

                    # 创建扩展后的mask
                    expanded_mask = np.zeros(new_size, dtype=np.float32)
                    # 确保mask的实际尺寸与目标区域匹配
                    actual_h, actual_w = mask.shape[:2]
                    end_y = min(actual_offset[0] + actual_h, new_size[0])
                    end_x = min(actual_offset[1] + actual_w, new_size[1])
                    expanded_mask[actual_offset[0]:end_y, actual_offset[1]:end_x] = mask[:end_y-actual_offset[0], :end_x-actual_offset[1]]

                    # 应用旋转变换（使用双线性插值）
                    rotated_mask = cv2.warpAffine(
                        expanded_mask, rotation_matrix, (new_size[1], new_size[0]),
                        flags=cv2.INTER_LINEAR,
                        borderMode=cv2.BORDER_CONSTANT,
                        borderValue=0  # mask填充0
                    )
                    rotated_masks.append(rotated_mask)

                sample['border_masks'] = np.stack(rotated_masks, axis=0)

        # 变换坐标（使用实际偏移量）
        if len(bboxes) > 0:
            # 先应用实际偏移，再应用旋转变换
            offset_bboxes = bboxes.copy()
            offset_bboxes[:, 0::2] += actual_offset[1]  # x坐标加实际偏移
            offset_bboxes[:, 1::2] += actual_offset[0]  # y坐标加实际偏移

            rotated_bboxes = self._rotate_bboxes(offset_bboxes, rotation_matrix)

            # 严格验证：检查是否有坐标截断
            if self.strict_validation:
                h_new, w_new = rotated_image.shape[:2]
                x_coords = rotated_bboxes[:, 0::2]
                y_coords = rotated_bboxes[:, 1::2]

                # 检查是否有坐标超出图像边界（坐标应该在[0, size)范围内）
                has_truncation = (
                    (x_coords < 0).any() or (x_coords > w_new).any() or
                    (y_coords < 0).any() or (y_coords > h_new).any()
                )

                if has_truncation:
                    # 检测到截断，跳过变换，返回原始数据
                    LOGGER.debug(f"[RandomRotationV2] 检测到坐标截断，跳过变换 (角度={angle:.1f}°)")
                    # 返回完整的原始样本，只更新必要的字段
                    sample['image'] = image  # 恢复原始图像
                    sample['img_shape'] = image.shape
                    sample['bboxes'] = bboxes  # 恢复原始bbox
                    if len(cell_centers) > 0:
                        sample['cell_centers'] = cell_centers  # 恢复原始中心点
                    # 恢复原始border_masks（如果存在）
                    if 'border_masks' in sample:
                        original_masks = sample.get('_original_border_masks')
                        if original_masks is not None:
                            sample['border_masks'] = original_masks
                    return sample

            sample['bboxes'] = rotated_bboxes

        if len(cell_centers) > 0:
            # 先应用实际偏移，再应用旋转变换
            offset_centers = cell_centers.copy()
            offset_centers[:, 0] += actual_offset[1]  # x坐标加实际偏移
            offset_centers[:, 1] += actual_offset[0]  # y坐标加实际偏移

            rotated_centers = self._rotate_points(offset_centers, rotation_matrix)

            # 严格验证：检查中心点是否截断
            if self.strict_validation:
                h_new, w_new = rotated_image.shape[:2]

                has_center_truncation = (
                    (rotated_centers[:, 0] < 0).any() or (rotated_centers[:, 0] > w_new).any() or
                    (rotated_centers[:, 1] < 0).any() or (rotated_centers[:, 1] > h_new).any()
                )

                if has_center_truncation:
                    # 检测到中心点截断，跳过变换
                    LOGGER.debug(f"[RandomRotationV2] 检测到中心点截断，跳过变换 (角度={angle:.1f}°)")
                    # 返回完整的原始样本，只更新必要的字段
                    sample['image'] = image  # 恢复原始图像
                    sample['img_shape'] = image.shape
                    sample['bboxes'] = bboxes  # 恢复原始bbox
                    sample['cell_centers'] = cell_centers  # 恢复原始中心点
                    # 恢复原始border_masks（如果存在）
                    if 'border_masks' in sample:
                        original_masks = sample.get('_original_border_masks')
                        if original_masks is not None:
                            sample['border_masks'] = original_masks
                    return sample

            sample['cell_centers'] = rotated_centers

        # 记录变换信息
        sample['rotation_v2_applied'] = True
        sample['rotation_v2_angle'] = angle
        sample['rotation_v2_center'] = adjusted_center
        sample['rotation_v2_matrix'] = rotation_matrix
        sample['rotation_v2_offset'] = offset
        sample['rotation_v2_actual_offset'] = actual_offset
        sample['rotation_v2_new_size'] = new_size

        return sample

    def _compute_table_bounds(self, bboxes: np.ndarray) -> Tuple[int, int, int, int]:
        """计算表格的边界矩形"""
        vertices = bboxes.reshape(-1, 4, 2)
        all_x = vertices[:, :, 0].flatten()
        all_y = vertices[:, :, 1].flatten()

        min_x = int(np.floor(all_x.min()))
        min_y = int(np.floor(all_y.min()))
        max_x = int(np.ceil(all_x.max()))
        max_y = int(np.ceil(all_y.max()))

        return min_x, min_y, max_x, max_y

    def _determine_rotation_center(
        self,
        table_bounds: Tuple[int, int, int, int],
        image_size: Tuple[int, int],
        center_mode: str
    ) -> Tuple[float, float]:
        """确定旋转中心"""
        h, w = image_size
        min_x, min_y, max_x, max_y = table_bounds

        if center_mode == 'image_center':
            return (w / 2, h / 2)
        elif center_mode == 'table_center':
            return ((min_x + max_x) / 2, (min_y + max_y) / 2)
        else:  # adaptive
            # 自适应选择：如果表格接近图像中心，使用表格中心；否则使用图像中心
            table_center_x = (min_x + max_x) / 2
            table_center_y = (min_y + max_y) / 2
            image_center_x = w / 2
            image_center_y = h / 2

            # 计算表格中心与图像中心的距离
            distance = np.sqrt((table_center_x - image_center_x)**2 +
                             (table_center_y - image_center_y)**2)

            # 如果距离小于图像对角线的1/4，使用表格中心
            if distance < np.sqrt(w**2 + h**2) / 4:
                return (table_center_x, table_center_y)
            else:
                return (image_center_x, image_center_y)

    def _calculate_expanded_size_for_rotation(
        self,
        original_size: Tuple[int, int],
        angle: float,
        rotation_center: Tuple[float, float],
        bboxes: np.ndarray
    ) -> Tuple[Tuple[int, int], Tuple[int, int], Tuple[int, int]]:
        """计算旋转后所需的图像尺寸和偏移量"""
        h, w = original_size
        cx, cy = rotation_center

        # 计算旋转角度的弧度值
        rad = np.radians(angle)
        cos_a, sin_a = np.cos(rad), np.sin(rad)

        # 计算原图四个角点旋转后的位置
        corners = np.array([[0, 0], [w, 0], [w, h], [0, h]])
        rotated_corners = []

        for corner in corners:
            # 相对于旋转中心的坐标
            rel_x, rel_y = corner[0] - cx, corner[1] - cy
            # 旋转
            new_x = rel_x * cos_a - rel_y * sin_a + cx
            new_y = rel_x * sin_a + rel_y * cos_a + cy
            rotated_corners.append([new_x, new_y])

        # 计算bbox旋转后的位置
        vertices = bboxes.reshape(-1, 4, 2)
        all_rotated_points = []

        for bbox_vertices in vertices:
            for vertex in bbox_vertices:
                # 相对于旋转中心的坐标
                rel_x, rel_y = vertex[0] - cx, vertex[1] - cy
                # 旋转
                new_x = rel_x * cos_a - rel_y * sin_a + cx
                new_y = rel_x * sin_a + rel_y * cos_a + cy
                all_rotated_points.append([new_x, new_y])

        # 合并所有旋转后的点
        all_points = np.vstack([np.array(rotated_corners), np.array(all_rotated_points)])

        # 计算边界
        min_x = np.floor(all_points[:, 0].min()).astype(int)
        max_x = np.ceil(all_points[:, 0].max()).astype(int)
        min_y = np.floor(all_points[:, 1].min()).astype(int)
        max_y = np.ceil(all_points[:, 1].max()).astype(int)

        # 计算所需的图像尺寸
        new_w = max_x - min_x
        new_h = max_y - min_y

        # 计算偏移量（确保原图内容在新图中的位置）
        offset_x = max(0, -min_x)
        offset_y = max(0, -min_y)

        # 返回：新尺寸，偏移量，偏移量（简化后理论偏移和实际偏移相同）
        return (new_h, new_w), (offset_y, offset_x), (offset_y, offset_x)

    def _create_expanded_image(
        self,
        image: np.ndarray,
        new_size: Tuple[int, int],
        offset: Tuple[int, int]
    ) -> np.ndarray:
        """创建扩展后的图像"""
        new_h, new_w = new_size
        offset_y, offset_x = offset

        # 确定填充颜色
        if self.fill_mode == 'dominant_color':
            fill_color = self._extract_dominant_color(image)
        else:
            fill_color = self.fill_value if isinstance(self.fill_value, tuple) else (self.fill_value, self.fill_value, self.fill_value)

        # 创建新图像
        expanded_image = np.full((new_h, new_w, image.shape[2]), fill_color, dtype=image.dtype)

        # 将原图像放置到新图像中
        h, w = image.shape[:2]
        end_y = min(offset_y + h, new_h)
        end_x = min(offset_x + w, new_w)

        expanded_image[offset_y:end_y, offset_x:end_x] = image[:end_y-offset_y, :end_x-offset_x]

        return expanded_image

    def _rotate_bboxes(self, bboxes: np.ndarray, rotation_matrix: np.ndarray) -> np.ndarray:
        """使用旋转矩阵旋转四边形bbox坐标"""
        if len(bboxes) == 0:
            return bboxes

        vertices = bboxes.reshape(-1, 4, 2)
        rotated_vertices = []

        for bbox_vertices in vertices:
            homogeneous_points = np.column_stack([bbox_vertices, np.ones(4)])
            rotated_points = rotation_matrix @ homogeneous_points.T
            rotated_vertices.append(rotated_points.T)

        rotated_bboxes = np.array(rotated_vertices).reshape(-1, 8)
        return rotated_bboxes

    def _rotate_points(self, points: np.ndarray, rotation_matrix: np.ndarray) -> np.ndarray:
        """使用旋转矩阵旋转点坐标"""
        if len(points) == 0:
            return points

        homogeneous_points = np.column_stack([points, np.ones(len(points))])
        rotated_points = rotation_matrix @ homogeneous_points.T

        return rotated_points.T

    def __repr__(self):
        repr_str = self.__class__.__name__
        repr_str += f'(angle_range={self.angle_range}, '
        repr_str += f'apply_prob={self.apply_prob}, '
        repr_str += f'center_mode={self.center_mode}, '
        repr_str += f'fill_mode={self.fill_mode}, '
        repr_str += f'strict_validation={self.strict_validation})'
        return repr_str


class ToTensor(BaseTransform):
    """转换为PyTorch张量"""

    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        image = sample['image']

        if isinstance(image, np.ndarray):
            if len(image.shape) == 3:
                image = image.transpose(2, 0, 1)
            image = torch.from_numpy(image.copy()).float()

        sample['image'] = image

        for key in ['bboxes', 'labels', 'cell_centers']:
            if key in sample and isinstance(sample[key], np.ndarray):
                sample[key] = torch.from_numpy(sample[key].copy())

        # 处理border_masks
        if 'border_masks' in sample and sample['border_masks'] is not None:
            border_masks = sample['border_masks']
            if isinstance(border_masks, np.ndarray):
                sample['border_masks'] = torch.from_numpy(border_masks.copy()).float()

        return sample


# 变换注册表
TRANSFORM_REGISTRY = {
    'LoadImageFromFile': LoadImageFromFile,
    'LoadAnnotations': LoadAnnotations,
    'PhotoMetricDistortion': PhotoMetricDistortion,
    'RandomCrop': RandomCrop,
    'Resize': Resize,
    'RandomFlip': RandomFlip,
    'PerspectiveTransform': PerspectiveTransform,
    'PerspectiveTransformV2': PerspectiveTransformV2,
    'RandomRotation': RandomRotation,
    'RandomRotationV2': RandomRotationV2,
    'Normalize': Normalize,
    'ToTensor': ToTensor,
}


class TransformPipeline:
    """变换流水线管理器"""

    def __init__(self, transforms: List[Dict[str, Any]]):
        """
        初始化变换流水线

        Args:
            transforms: 变换配置列表，每个元素包含type和参数
        """
        self.transforms = []

        for transform_cfg in transforms:
            transform_type = transform_cfg['type']
            transform_params = {k: v for k, v in transform_cfg.items() if k != 'type'}

            if transform_type not in TRANSFORM_REGISTRY:
                raise ValueError(f"未知的变换类型: {transform_type}")

            transform_class = TRANSFORM_REGISTRY[transform_type]
            transform_instance = transform_class(**transform_params)
            self.transforms.append(transform_instance)

    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """应用变换流水线"""
        for transform in self.transforms:
            sample = transform(sample)
        return sample

    def __repr__(self):
        repr_str = f"{self.__class__.__name__}(\n"
        for i, transform in enumerate(self.transforms):
            repr_str += f"  {i}: {transform}\n"
        repr_str += ")"
        return repr_str


class TableTransforms:
    """
    表格数据预处理和变换管道

    基于流水线的变换系统，支持从配置文件加载变换流水线
    """

    def __init__(
        self,
        pipeline_config: Optional[List[Dict[str, Any]]] = None,
        config_path: Optional[str] = None,
        mode: str = 'train'
    ):
        """
        初始化变换管道

        Args:
            pipeline_config: 流水线配置列表
            config_path: 配置文件路径
            mode: 模式 ('train', 'val', 'test')
        """
        if pipeline_config is not None:
            # 直接使用提供的流水线配置
            self.pipeline = TransformPipeline(pipeline_config)
        elif config_path is not None:
            # 从配置文件加载
            self.pipeline = self._load_pipeline_from_config(config_path, mode)
        else:
            # 使用默认配置
            self.pipeline = self._get_default_pipeline(mode)

    def _load_pipeline_from_config(self, config_path: str, mode: str) -> TransformPipeline:
        """从配置文件加载流水线"""
        config_path = Path(config_path)
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")

        with open(config_path, 'r', encoding='utf-8') as f:
            yaml_config = yaml.safe_load(f)

        # 提取流水线配置
        pipelines = yaml_config.get('data', {}).get('pipelines', {})
        pipeline_config = pipelines.get(mode, [])

        if not pipeline_config:
            raise ValueError(f"配置文件中未找到 '{mode}' 模式的流水线配置")

        return TransformPipeline(pipeline_config)

    def _get_default_pipeline(self, mode: str) -> TransformPipeline:
        """获取默认流水线配置"""
        if mode == 'train':
            default_config = [
                {'type': 'LoadImageFromFile', 'to_float32': True, 'color_type': 'color'},
                {'type': 'Resize', 'img_scale': [[512, 512]], 'keep_ratio': True},
                {'type': 'RandomFlip', 'flip_ratio': 0.5, 'direction': 'horizontal'},
                {'type': 'Normalize', 'mean': [0.485, 0.456, 0.406], 'std': [0.229, 0.224, 0.225], 'to_rgb': True},
                {'type': 'ToTensor'}
            ]
        else:
            default_config = [
                {'type': 'LoadImageFromFile', 'to_float32': True, 'color_type': 'color'},
                {'type': 'Resize', 'img_scale': [[512, 512]], 'keep_ratio': True},
                {'type': 'Normalize', 'mean': [0.485, 0.456, 0.406], 'std': [0.229, 0.224, 0.225], 'to_rgb': True},
                {'type': 'ToTensor'}
            ]

        return TransformPipeline(default_config)
    
    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用变换流水线

        Args:
            sample: 输入样本

        Returns:
            变换后的样本
        """
        return self.pipeline(sample)
    
    def __repr__(self):
        return f"{self.__class__.__name__}(pipeline={self.pipeline})"


# 工厂函数
def create_table_transforms(
    config_path: Optional[str] = None,
    pipeline_config: Optional[List[Dict[str, Any]]] = None,
    mode: str = 'train'
) -> TableTransforms:
    """
    创建TableTransforms实例的工厂函数

    Args:
        config_path: 配置文件路径
        pipeline_config: 流水线配置列表
        mode: 模式 ('train', 'val', 'test')

    Returns:
        TableTransforms实例
    """
    return TableTransforms(
        pipeline_config=pipeline_config,
        config_path=config_path,
        mode=mode
    )
