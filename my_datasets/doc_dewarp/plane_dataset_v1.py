#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/10/17 21:04
# <AUTHOR> <EMAIL>
# @FileName: plane_dataset_v1

import random
from pathlib import Path
from os.path import join as pjoin

import warnings

warnings.filterwarnings("ignore")

import torch
import numpy as np
from PIL import Image
from torch.utils.data import Dataset
from modules.utils.log import LOGGER
from modules.doc_dewrap.data_utils import bilinear_unwarping
from modules.utils.image_utils import get_all_image_path, normalize_image_rgb


class PlaneDataset(Dataset):
    def __init__(
        self,
        data_path,
        img_size,  # (Height, Width)
        grid_size,  # (Height, Width)
        split="train",
        resample_num=-1,  # 重采样数量
        seed=None,  # 随机种子
        align_aspect_ratio=True,
    ) -> None:
        super().__init__()

        # 设置随机数种子
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)
            torch.manual_seed(seed)

        self.data_path = data_path
        self.img_size = img_size
        self.grid_size = grid_size
        self.align_aspect_ratio = align_aspect_ratio

        # 根据 split 获取文件路径
        if split == "train":
            self.src_dir = pjoin(data_path, "train", "snapshot_doc")
        elif split == "val":
            self.src_dir = pjoin(data_path, "test", "snapshot_doc")
        else:
            raise ValueError(f"无效的 split：{split}, 只能是 'train' 或 'val'")

        self.all_samples = get_all_image_path(self.src_dir, recursive=True, path_op=Path)
        # 如果 resample_num > 0，进行重采样
        if resample_num > 0 and resample_num > len(self.all_samples):
            # 重采样，确保采样后的 self.all_samples 是采样前的父集
            old_samples_num = len(self.all_samples)
            additional_samples = random.choices(self.all_samples, k=resample_num - len(self.all_samples))
            self.all_samples.extend(additional_samples)
            new_samples_num = len(self.all_samples)
            LOGGER.info(f"{__class__.__name__}: 原始样本大小：{old_samples_num}, 重采样后样本大小：{new_samples_num}")
        elif resample_num > 0:
            old_samples_num = len(self.all_samples)
            self.all_samples = random.choices(self.all_samples, k=resample_num)
            new_samples_num = len(self.all_samples)
            LOGGER.info(f"{__class__.__name__}: 原始样本大小：{old_samples_num}, 重采样后样本大小：{new_samples_num}")

        # 打乱样本
        random.shuffle(self.all_samples)

    def __getitem__(self, index):
        image_path = self.all_samples[index]
        img_RGB = Image.open(image_path).convert("RGB")
        raw_width, raw_height = img_RGB.width, img_RGB.height

        # 将图像 resize 到指定大小
        img_RGB = img_RGB.resize((self.img_size[1], self.img_size[0]), Image.BILINEAR)

        # 归一化图像
        img_RGB = normalize_image_rgb(img_RGB)

        # 生成 grid2D
        H, W = self.grid_size
        grid2D = torch.zeros((2, H, W))  # Shape: [2, H, W]

        # 设置 x 维的值为从 -1 到 1 的线性插值
        grid2D[0, :, :] = torch.linspace(-1., 1., W).unsqueeze(0).repeat(H, 1)  # x 轴从 -1 到 1

        # 设置 y 维的值为从 -1 到 1 的线性插值
        grid2D[1, :, :] = torch.linspace(-1., 1., H).unsqueeze(1).repeat(1, W)  # y 轴从 -1 到 1

        # 生成 3D 网格
        grid3D = torch.zeros((3, H, W))  # Shape: [3, H, W]
        grid3D[0, :, :] = 0.5  # 固定 x 维度

        if self.align_aspect_ratio:
            if raw_height >= raw_width:
                l = raw_width / raw_height
                a = (1 - l) / 2
                b = l + (1 - l) / 2
                grid3D[1, :, :] = torch.linspace(a, b, H).unsqueeze(1).repeat(1, W)  # y 轴线性插值
                grid3D[2, :, :] = torch.linspace(0., 1., W).unsqueeze(0).repeat(H, 1)  # z 轴线性插值
            else:
                l = raw_height / raw_width
                a = (1 - l) / 2
                b = l + (1 - l) / 2
                grid3D[1, :, :] = torch.linspace(0., 1., H).unsqueeze(1).repeat(1, W)  # y 轴线性插值
                grid3D[2, :, :] = torch.linspace(a, b, W).unsqueeze(0).repeat(H, 1)  # z 轴线性插值
        else:
            grid3D[1, :, :] = torch.linspace(0., 1., H).unsqueeze(1).repeat(1, W)  # y 轴线性插值
            grid3D[2, :, :] = torch.linspace(0., 1., W).unsqueeze(0).repeat(H, 1)  # z 轴线性插值

        # 使用双线性反变形函数进行采样
        img_RGB_unwarped = bilinear_unwarping(img_RGB.unsqueeze(0), grid2D.unsqueeze(0), self.img_size).squeeze()

        return img_RGB, img_RGB_unwarped, grid2D, grid3D

    def __len__(self):
        return len(self.all_samples)
