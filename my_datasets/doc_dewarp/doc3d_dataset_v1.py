#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/9/24 16:43
# <AUTHOR> <EMAIL>
# @FileName: doc3d_dataset_v1

import random
import warnings
warnings.filterwarnings("ignore")
from os.path import join as pjoin

import cv2
import torch
# import h5py as h5
import hdf5storage as h5
import numpy as np
import torch.nn.functional as F
from PIL import Image

from .base_dataset import BaseDataset
from modules.utils.log import LOGGER
from modules.doc_dewrap.data_utils import bilinear_unwarping
from modules.doc_dewrap.data_utils import grid_locally_enhanced_v3,grid_locally_enhanced_v4
from scipy.ndimage import zoom

class Doc3DDataset(BaseDataset):
    def __init__(
        self,
        data_path,
        img_size,   # (H, W)
        grid_size,  # (H, W)
        split='train',
        use_grid=0,
        crop_tight_p=1.,
        normalize_grid3d=True,
        grid_locally_enhanced=False,
        grid_locally_enhanced_method=0,
        p_grid_locally_enhanced=0.5,
        appearance_augmentation=None,
        seed=None,  # 随机数种子
        open_midas=False,
        resample_num=-1
    ) -> None:
        super().__init__(
            data_path=data_path,
            img_size=img_size,
            grid_size=grid_size,
            normalize_grid3d=normalize_grid3d,
            appearance_augmentation=appearance_augmentation,
        )
        self.crop_tight_p=crop_tight_p
        self.use_grid = use_grid
        self.open_midas = open_midas
        if self.open_midas:
            from transformers import DPTImageProcessor
            self.midas_image_processor = DPTImageProcessor.from_pretrained("Intel/dpt-hybrid-midas")

        if appearance_augmentation is not None:
            LOGGER.info(f"{__class__.__name__}: appearance_augmentation: {appearance_augmentation}")

        # 设置随机数种子
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)
            torch.manual_seed(seed)
        self.grid_locally_enhanced = grid_locally_enhanced
        self.p_grid_locally_enhanced = p_grid_locally_enhanced
        self.grid_locally_enhanced_method=grid_locally_enhanced_method
        self.original_grid_size = (89, 61)
        self.grid3d_normalization = (
            1.2539363, -1.2442188,
            1.2396319, -1.2289206,
            0.6436657, -0.67492497
        )

        if split == "train":
            path = pjoin(self.dataroot, "traindoc.txt")
        elif split == "val":
            path = pjoin(self.dataroot, "valdoc3D.txt")

        with open(path, "r") as files:
            file_list = tuple(files)
        self.all_samples = np.array([id_.rstrip() for id_ in file_list], dtype=np.string_)


        # 如果 resample_num > 0，进行重采样
        if resample_num > 0 and resample_num > len(self.all_samples):
            # 重采样，确保采样后的 self.all_samples 是采样前的父集
            old_samples_num = len(self.all_samples)
            additional_samples = random.choices(self.all_samples, k=resample_num - len(self.all_samples))
            additional_samples=np.array(additional_samples)
            self.all_samples=np.append(self.all_samples,additional_samples)
            # self.all_samples.extend(additional_samples)
            new_samples_num = len(self.all_samples)
            LOGGER.info(f"{__class__.__name__}: 原始样本大小：{old_samples_num}, 重采样后样本大小：{new_samples_num}")
        elif resample_num > 0:
            old_samples_num = len(self.all_samples)
            self.all_samples = np.array(random.choices(self.all_samples, k=resample_num))
            new_samples_num = len(self.all_samples)
            LOGGER.info(f"{__class__.__name__}: 原始样本大小：{old_samples_num}, 重采样后样本大小：{new_samples_num}")

        # 打乱样本
        random.shuffle(self.all_samples)

    def __getitem__(self, index):
        # shape: (45,31)
        zoom_factors = (1, 89 / 45, 61 / 31)# 进行上采样才用到
        info = dict()
        im_name = self.all_samples[index].decode("UTF-8")
        img_path = pjoin(self.dataroot, "img", im_name + ".png")
        grid2D_path = pjoin(self.dataroot, "grid2D", im_name + ".mat")
        grid3D_path = pjoin(self.dataroot, "grid3D", im_name + ".mat")
        # bm_path = pjoin(self.dataroot, "bm", im_name + ".npy")  # 修改为 .npy 文件

        # Load 2D grid, 3D grid and image. Normalize 3D grid 官方文件(45,31,2)
        file=h5.loadmat(grid2D_path)
        # grid2D_ = np.array(file["grid2D"][:].T.transpose(2, 0, 1))
        grid2D_ = np.array(file["grid2D"][:].transpose(2, 0, 1))# 用hdf5storage 加载和用h5py 加载方式不一样 2,45,31
        # with h5.File(grid2D_path, "r") as file:
        #     res=file["grid2D"]
        #     grid2D_ = np.array(file["grid2D"][:].T.transpose(2, 0, 1))  # scale in range of img resolution
        
        if self.use_grid==1:
            # 使用双线性插值进行上采样 2024.11.1
            grid2D_ = zoom(grid2D_, zoom_factors, order=1)

        file = h5.loadmat(grid3D_path)
        # grid3D = np.array(file["grid3D"][:].T)
        grid3D = np.array(file["grid3D"][:])
        # with h5.File(grid3D_path, "r") as file:
        #     grid3D = np.array(file["grid3D"][:].T)

        # scale grid3D to [0,1], based on stats computed over the entire dataset
        if self.normalize_grid3d: # 做尺度归一化的目的是 让具体的x,y,z 坐标值 随着不同尺度的图像变化而变化
            xmx, xmn, ymx, ymn, zmx, zmn = self.grid3d_normalization
            # x轴单独归一化
            grid3D[:, :, 0] = (grid3D[:, :, 0] - xmn) / (xmx - xmn)
            # y和z轴使用相同的归一化尺度
            yz_max = max(ymx, zmx)
            yz_min = min(ymn, zmn)
            grid3D[:, :, 1] = (grid3D[:, :, 1] - yz_min) / (yz_max - yz_min)
            grid3D[:, :, 2] = (grid3D[:, :, 2] - yz_min) / (yz_max - yz_min)
            grid3D = np.array(grid3D, dtype=np.float32)

        grid3D[:, :, 1] = grid3D[:, :, 1][:, ::-1]
        grid3D[:, :, 1] = 1 - grid3D[:, :, 1]
        grid3D=grid3D.transpose(2, 0, 1)
        if self.use_grid==1:
            # 3D网格结构也进行上采样
            grid3D = zoom(grid3D, zoom_factors, order=1)
        grid3D = torch.from_numpy(grid3D)
        
        img_RGB_ = cv2.cvtColor(cv2.imread(img_path), cv2.COLOR_BGR2RGB)

        # Pixel-wise augmentation
        img_RGB_ = self.appearance_transform(image=img_RGB_)["image"]

        # Create unwarped image according to the backward mapping (load from .npy)
        # bm = np.load(bm_path)  # 使用 np.load 读取 .npy 文件
        # bm = ((bm / 448) - 0.5) * 2.0
        # bm = torch.from_numpy(bm).float()
        #
        # img_RGB_unwarped = bilinear_unwarping(
        #     torch.from_numpy(img_RGB_.transpose(2, 0, 1)).float().unsqueeze(0),
        #     bm.unsqueeze(0),
        #     self.img_size,
        # ).squeeze()
        
        # Tight crop 这个时候grid2D也归一化了
        grid2Dtmp = grid2D_
        # # 开启紧缩功能 但是是随机紧缩 可以紧缩 或者不紧缩 p是紧缩的概率
        img_RGB, grid2D = self.crop_tight(img_RGB_, grid2Dtmp,is_crop=True,p=self.crop_tight_p)
        # Convert 2D grid to torch tensor
        grid2D = torch.from_numpy(grid2D).float()
        # 对 img_RGB, grid2D, grid3D 做局部增强
        if self.grid_locally_enhanced and random.random() <= self.p_grid_locally_enhanced:
            if self.grid_locally_enhanced_method==0:
                img_RGB, grid2D, grid3D, pad_cnt = grid_locally_enhanced_v3(
                    img_RGB, grid2D, grid3D,
                    (0.5, 1.0), (0.5, 1.33),
                )
                info['grid_locally_enhanced'] = True
                info['pad_cnt'] = pad_cnt
            elif self.grid_locally_enhanced_method==1:

                img_RGB, grid2D,info = grid_locally_enhanced_v4(
                    img_RGB, grid2D,(0.9, 1.0))
                info['grid_locally_enhanced'] = True
                info['pad_cnt'] = 0
            

        else:
            info['grid_locally_enhanced'] = False
            info['pad_cnt'] = 0
        # 对图像进行修改的时候 因为网格是归一化 只需要和最终图像的w,h 相乘就能得到对应尺度上的值
        img_RGB = F.interpolate(img_RGB.unsqueeze(0), size=self.img_size, mode='bilinear', align_corners=False).squeeze(0)

        if self.open_midas:
            img_RGB_np = img_RGB.permute(1, 2, 0).cpu().numpy()
            img_RGB_np = (img_RGB_np).astype(np.uint8)
            img_pil = Image.fromarray(img_RGB_np)
            midas_inputs = self.midas_image_processor(images=img_pil, return_tensors="pt")
            midas_inputs = midas_inputs.data['pixel_values'].squeeze(0)

        # Unwarp the image according to grid
        img_RGB_unwarped = bilinear_unwarping(img_RGB.unsqueeze(0), grid2D.unsqueeze(0), self.img_size).squeeze()
        img_RGB = img_RGB.float() / 255.0
        img_RGB_unwarped = img_RGB_unwarped.float() / 255.0

        data = {
            'img_RGB': img_RGB,
            'img_RGB_unwarped': img_RGB_unwarped,
            'grid2D': grid2D,
            'grid3D': grid3D,
            'info': info,
        }

        if self.open_midas:
            data['midas_inputs'] = midas_inputs

        # 返回包含所有数据和 info 的字典
        return data