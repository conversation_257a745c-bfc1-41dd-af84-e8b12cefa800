#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/9/24 16:50
# <AUTHOR> <EMAIL>
# @FileName: base_dataset
import random

import cv2
import torch
from torch.utils.data import Dataset
from PIL import Image
from modules.doc_dewrap.data_utils import crop_image_tight, get_appearance_transform
import numpy as np

class BaseDataset(Dataset):
    def __init__(
        self,
        data_path,
        img_size,   # (H, W)
        grid_size,  # (H, W)
        normalize_grid3d=True,
        appearance_augmentation=None,
    ) -> None:
        super().__init__()
        if appearance_augmentation is None:
            appearance_augmentation = []

        self.dataroot = data_path
        self.img_size = img_size
        self.grid_size = grid_size
        self.normalize_grid3d = normalize_grid3d
        self.appearance_transform = get_appearance_transform(appearance_augmentation)

        self.all_samples = []

    def __len__(self):
        return len(self.all_samples)

    def crop_tight(self, img_RGB, grid2D,is_crop=True,p=1):
        # The incoming grid2D array is expressed in pixel coordinates (resolution of img_RGB before crop/resize)
        size = img_RGB.shape
        if is_crop and random.random()<p :
            img, top, bot, left, right = crop_image_tight(img_RGB, grid2D)
            # 存在短线问题 opencv 使用pillow
            # img = cv2.resize(img, (self.img_size[1], self.img_size[0]))
            # img=Image.fromarray(cv2.cvtColor(img,cv2.COLOR_BGR2RGB))
            # img=np.array(img.resize((self.img_size[1], self.img_size[0]),Image.BILINEAR))
            # img=cv2.cvtColor(img,cv2.COLOR_RGB2BGR)

            img = img.transpose(2, 0, 1)
            img = torch.from_numpy(img).float()
            # 网格点也进行 缩减
            grid2D[0, :, :] = (grid2D[0, :, :] - left) / (size[1] - left - right)
            grid2D[1, :, :] = (grid2D[1, :, :] - top) / (size[0] - top - bot)
            grid2D = (grid2D * 2.0) - 1.0
        else:
            img = cv2.resize(img_RGB, (self.img_size[1], self.img_size[0]))
            img = img.transpose(2, 0, 1)
            img = torch.from_numpy(img).float()
            # 网格点也进行 缩减
            grid2D[0, :, :] = grid2D[0, :, :] / size[1]
            grid2D[1, :, :] = grid2D[1, :, :] / size[0]
            grid2D = (grid2D * 2.0) - 1.0

        return img, grid2D