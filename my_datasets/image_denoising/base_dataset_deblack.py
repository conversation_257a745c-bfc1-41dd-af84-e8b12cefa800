#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/12/3 11:32
# <AUTHOR> <EMAIL>
# @FileName: base_dataset_deblack

import os
import random
import warnings

import torch
from PIL import Image
from torchvision import transforms
from torch.utils.data import Dataset
from modules.utils.log import LOGGER
from modules.utils.torch_utils import set_seed
from modules.doc_degradation.core.degradation_pipe import DegradationPipe, StrategyScheduler
from modules.utils.image_utils import get_all_image_path, default_degrade_image_quality_little

Image.MAX_IMAGE_PIXELS = None

# Suppress the specific warning
warnings.filterwarnings(
    "ignore",
    category=UserWarning,
    message="Palette images with Transparency expressed in bytes should be converted to RGBA images"
)


class MyCustomDataset(Dataset):
    def __init__(self, data_root, mode='train', seed=-1, debug=False, apply_degrade=True):
        """
        初始化数据集。
        :param data_root: 数据目录。
        :param mode: 模式 (train, val, test)。
        :param seed: 随机种子。
        """
        if seed == -1:
            seed = random.randint(1, 12580)
        set_seed(seed)

        self.apply_degrade = apply_degrade
        self.image_degrade_pipe = DegradationPipe(StrategyScheduler(config_path=None))
        self.data_root = os.path.join(data_root, mode)
        assert os.path.exists(self.data_root), f"数据目录不存在: {self.data_root}"

        LOGGER.info(f"{__file__}: 正在加载数据")

        all_samples = []
        all_input_crops = get_all_image_path(os.path.join(self.data_root, "input_crops"), recursive=False)
        for sample in all_input_crops:
            input_crop = sample
            target_crop = input_crop.replace("input_crops", "target_crops")
            if os.path.exists(input_crop) and os.path.exists(target_crop):
                all_samples.append((input_crop, target_crop))

        if debug:
            all_samples = all_samples[0:1000]
        self.dataset = all_samples

        LOGGER.info(f"{__file__}: 数据加载完毕, 共计{len(self.dataset)}对样本")

    def __len__(self):
        return len(self.dataset)

    def __getitem__(self, idx):
        """
        返回源图像和目标图像的路径及元信息。
        """
        noised_image_path, target_image_path = self.dataset[idx]

        # 加载源图像和目标图像
        noised_image = Image.open(noised_image_path).convert("RGB")
        if self.apply_degrade:
            noised_image = default_degrade_image_quality_little(noised_image, self.image_degrade_pipe)
        target_image = Image.open(target_image_path).convert("RGB")

        return {
            "noised_image": noised_image,
            "target_image": target_image
        }


def dynamic_collate_fn(batch, resolution_fun):
    """
    自定义的 collate_fn，用于动态调整分辨率。
    :param batch: 一个批次的数据，列表形式。
    :param resolution_fun: 一个函数，用于动态返回分辨率。
    :return: 返回动态调整分辨率后的批次数据。
    """
    # 动态获取分辨率
    resolution = resolution_fun()
    transform = transforms.Compose([transforms.Resize((resolution, resolution)), transforms.ToTensor()])

    # 图片变换操作
    noised_images = []
    target_images = []
    for item in batch:
        noised_image = item["noised_image"]
        target_image = item["target_image"]

        # 转为张量
        noised_images.append(transform(noised_image))
        target_images.append(transform(target_image))

    # 将图像数据拼接成 Tensor 格式
    noised_images = torch.stack(noised_images)  # 拼接成 (batch_size, C, H, W)
    target_images = torch.stack(target_images)  # 拼接成 (batch_size, C, H, W)

    # 返回批次
    return {
        "noised": noised_images,
        "target": target_images,
    }
