#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/11/25 17:46
# <AUTHOR> <EMAIL>
# @FileName: base_dataset_clean

import os
import json
import random
import warnings
from enum import Enum

import torch
from PIL import Image
from torch.utils.data import Dataset
from torchvision import transforms
from modules.utils.log import LOGGER
from modules.utils.torch_utils import set_seed
from modules.utils.image_utils import (
    resize_image_pair,
    random_crop_max_square_pair,
    random_crop_local_square_pair_with_resolution,
    general_degrade_image_quality, DegradationType,
)
from modules.doc_degradation.core.degradation_pipe import DegradationPipe, StrategyScheduler

DEGRADE_STRATEGIES = [
    (
        DegradationType.BLUR,
        {
            "gaussian_blur_kernel_range": (9, 13),
            "gaussian_blur_sigma_range": (5, 7),
            "motion_blur_kernel_range": (9, 13),
            "average_blur_kernel_range": (9, 13),
        },
    ),
    (DegradationType.JPEG, {"jpeg_quality_range": (10, 15)}),
    (DegradationType.SINC, {"sinc_kernel_size_range": (9, 13)}),
    (DegradationType.FADE_GLOBAL, None),
    (DegradationType.RANDOM_RESIZE, {"resize_scale_range": ((0.2, 0.3), (1.0, 1.5))}),
    (DegradationType.ESRGAN_BLUR, None),
]

Image.MAX_IMAGE_PIXELS = None

# Suppress the specific warning
warnings.filterwarnings(
    "ignore",
    category=UserWarning,
    message="Palette images with Transparency expressed in bytes should be converted to RGBA images"
)


class MyCustomDataset(Dataset):
    def __init__(self, data_root, mode='train', seed=-1, debug=False):
        """
        初始化数据集。
        :param data_root: 数据目录。
        :param mode: 模式 (train, val, test)。
        :param seed: 随机种子。
        """
        if seed == -1:
            seed = random.randint(1, 12580)
        set_seed(seed)

        self.image_degrade_pipe = DegradationPipe(StrategyScheduler(config_path=None))

        assert data_root, "必须指定训练数据目录"
        self.data_root = data_root
        if debug:
            jsonl_data_path = os.path.join(data_root, f"{mode}_debug.jsonl")
        else:
            jsonl_data_path = os.path.join(data_root, f"{mode}.jsonl")

        LOGGER.info(f"加载数据元信息：{jsonl_data_path}")
        with open(jsonl_data_path, "r") as f:
            all_samples = [json.loads(line.strip()) for line in f if line.strip()]
        random.shuffle(all_samples)
        self.dataset = all_samples
        stat = dict()
        for data in self.dataset:
            data_name = data["dataset_name"]
            if data_name in stat:
                stat[data_name] = stat[data_name] + 1
            else:
                stat[data_name] = 1
        for data_name, num in stat.items():
            LOGGER.info(f"数据统计, {data_name}: {num}")
        LOGGER.info(f"数据加载完毕, 共计{len(all_samples)}对样本")

    def __len__(self):
        return len(self.dataset)

    def __getitem__(self, idx):
        """
        返回源图像和目标图像的路径及元信息。
        """
        data = self.dataset[idx]
        data_name = data["dataset_name"]
        target_image_path = os.path.join(self.data_root, data_name, data['target'])

        # 加载源图像和目标图像
        target_image = Image.open(target_image_path).convert("RGB")
        noised_image = general_degrade_image_quality(target_image, self.image_degrade_pipe, DEGRADE_STRATEGIES)

        return {
            "noised_image": noised_image,
            "target_image": target_image
        }


class ImageProcessingMethod(Enum):
    RESIZE_IMAGE_PAIR = {
        "func": resize_image_pair,
        "weight": 0.125,
        "flag": 0,
    }
    RANDOM_CROP_MAX_SQUARE_PAIR = {
        "func": random_crop_max_square_pair,
        "weight": 0.125,
        "flag": 1,
    }
    RANDOM_CROP_LOCAL_SQUARE_PAIR = {
        "func": random_crop_local_square_pair_with_resolution,
        "weight": 0.75,
        "flag": 2,
    }

    @staticmethod
    def get_random_method():
        """
        随机选择一个图像预处理方法。
        :return: 返回 (函数, 标志值)
        """
        # 提取所有成员的权重
        methods = list(ImageProcessingMethod)
        weights = [method.value["weight"] for method in methods]

        # 按权重随机选择一个方法
        selected_method = random.choices(methods, weights, k=1)[0]

        # 获取对应的函数和标志值
        return selected_method.value["func"], selected_method.value["flag"]


def dynamic_collate_fn(batch, resolution_fun):
    """
    自定义的 collate_fn，用于动态调整分辨率。
    :param batch: 一个批次的数据，列表形式。
    :param resolution_fun: 一个函数，用于动态返回分辨率。
    :return: 返回动态调整分辨率后的批次数据。
    """
    # 动态获取分辨率
    resolution = resolution_fun()
    transform = transforms.Compose([transforms.ToTensor()])

    # 随机选择一个图像处理方法及其标志
    img_proc_func, img_proc_flag = ImageProcessingMethod.get_random_method()

    noised_images = []
    target_images = []
    resolutions = []
    img_proc_flags = []

    for item in batch:
        noised_image = item["noised_image"]
        target_image = item["target_image"]

        # 应用选定的图像处理方法
        noised_image, target_image = img_proc_func(
            image_list=[noised_image, target_image],
            size=resolution, sampling=Image.LANCZOS,
        )

        # 转为张量
        noised_images.append(transform(noised_image))
        target_images.append(transform(target_image))
        resolutions.append(resolution)
        img_proc_flags.append(img_proc_flag)

    # 将图像数据拼接成 Tensor 格式
    noised_images = torch.stack(noised_images)  # 拼接成 (batch_size, C, H, W)
    target_images = torch.stack(target_images)  # 拼接成 (batch_size, C, H, W)

    # 将分辨率和图像处理标志转为 Tensor 格式
    resolutions = torch.tensor(resolutions, dtype=torch.int32)        # 分辨率
    img_proc_flags = torch.tensor(img_proc_flags, dtype=torch.int32)  # 图像处理方法标志


    # 返回批次
    return {
        "noised": noised_images,
        "target": target_images,
        "resolution": resolutions,
        "img_proc_flag": img_proc_flags,
    }


def walk_dataloaders(loaders, weights=None):
    """
    遍历多个数据加载器，以随机顺序返回每个加载器的批次。
    每个加载器可以根据权重比例进行动态调整。
    :param loaders: 多个数据加载器的列表，每个元素是 (标志, DataLoader)。
    :param weights: 各个加载器的采样权重，列表或 None。如果为 None，则根据加载器数据长度自动计算。
    :yield: 返回 (标志, 批次)。
    """
    # 生成加载器的迭代器
    doing = [(flag, iter(loader), len(loader)) for flag, loader in loaders]

    # 如果未指定权重，根据数据加载器的长度计算权重
    if weights is None:
        total_length = sum(length for _, _, length in doing)
        weights = [length / total_length for _, _, length in doing]
    else:
        # 确保权重总和为 1
        weight_sum = sum(weights)
        weights = [w / weight_sum for w in weights]

    print()
    weight_with_flags = [(flag, weight) for (flag, _, _), weight in zip(doing, weights)]
    LOGGER.info(f"walk dataloader by weights: {weight_with_flags}")

    # 确保权重的数量与加载器数量一致
    assert len(weights) == len(doing), "权重数量必须与加载器数量一致"

    while doing:
        # 按权重随机选择一个加载器
        selected_idx = random.choices(range(len(doing)), weights=weights, k=1)[0]
        flag, it, length = doing[selected_idx]

        try:
            batch = next(it)
            yield flag, batch
        except StopIteration:
            # 当前加载器完成，移除
            del doing[selected_idx]
            del weights[selected_idx]

            # 重新计算权重
            if doing:
                total_length = sum(length for _, _, length in doing)
                weights = [length / total_length for _, _, length in doing]


if __name__ == '__main__':
    from torch.utils.data import DataLoader

    # 动态分辨率函数
    def dynamic_resolution(resol):
        return random.choice([256, 512, 1024])

    # 初始化数据集
    dataset = MyCustomDataset(data_root="path/to/data")

    # 初始化数据加载器，指定自定义 collate_fn
    loader1 = DataLoader(
        dataset,
        batch_size=8,
        shuffle=True,
        collate_fn=lambda batch: dynamic_collate_fn(batch, dynamic_resolution)  # 动态分辨率
    )

    loader2 = DataLoader(
        dataset,
        batch_size=16,
        shuffle=True,
        collate_fn=lambda batch: dynamic_collate_fn(batch, lambda: 512)  # 固定分辨率
    )

    # 遍历数据加载器
    for flag, batch in walk_dataloaders([("loader1", loader1), ("loader2", loader2)]):
        print(f"{flag}: {len(batch['noised'])} images at resolution {batch['resolution']}")