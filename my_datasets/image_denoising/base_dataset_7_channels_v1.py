#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/11/25 17:46
# <AUTHOR> <EMAIL>
# @FileName: base_dataset_v1

import os
import json
import random
import math
import numpy as np
import warnings
from enum import Enum
import torch
from torch.utils.data import Sampler
from PIL import Image
from torch.utils.data import Dataset
from torchvision import transforms
import  sys
import cv2
sys.path.insert(0, "/aicamera-mlp/fq_proj/codes/train-anything-new")  # only visual loader
from modules.utils.log import LOGGER
from modules.utils.torch_utils import set_seed
from modules.utils.image_utils import (
    resize_image_pair,
    random_crop_max_square_pair,
    default_degrade_image_quality,
    random_crop_local_square_pair_with_resolution,
)
from modules.doc_degradation.core.degradation_pipe import DegradationPipe, StrategyScheduler

Image.MAX_IMAGE_PIXELS = None

# Suppress the specific warning
warnings.filterwarnings(
    "ignore",
    category=UserWarning,
    message="Palette images with Transparency expressed in bytes should be converted to RGBA images"
)


def sampler_sub():
    pass

class MyCustomDataset(Dataset):
    def __init__(self, data_root, mode='train', seed=-1, patch_size=1024, debug=False, visual_loader=False, visual_num=100):
        """
        初始化数据集。
        :param data_root: 数据目录。
        :param mode: 模式 (train, val, test)。
        :param seed: 随机种子。
        """
        if seed == -1:
            seed = random.randint(1, 12580)
        set_seed(seed)
        self.patch_size = patch_size
        self.visual_loader = visual_loader

        self.image_degrade_pipe = DegradationPipe(StrategyScheduler(config_path=None))

        assert data_root, "必须指定训练数据目录"
        self.data_root = data_root

        self.all_imgs = []
        with open(os.path.join(data_root, f"data_merge_v7_{mode}.json"), "r") as f:
            data_info = json.load(f)
            for g_key in data_info:
                for key in data_info[g_key]:
                    self.all_imgs.append(os.path.join(data_root, key))
        LOGGER.info(f"数据加载完毕..., 共计{len(self.all_imgs)}对样本")
        if self.visual_loader:
            self.all_imgs = random.sample(self.all_imgs, visual_num)
        if debug:
            self.all_imgs = self.all_imgs[:20]

        self.transform = transforms.Compose([
            transforms.Resize((self.patch_size, self.patch_size), antialias=True),
            transforms.ToTensor(),
        ])

        # 在初始化时计算所有可能的patch位置
        self.patches_paths = []
        self.patches = []
        for i, img_path in enumerate(self.all_imgs):
            img = Image.open(img_path).convert("RGB")
            noised_image_path = img_path.replace("target", "source")
            w, h = img.size
            n_w = math.ceil(w / self.patch_size)
            n_h = math.ceil(h / self.patch_size)

            for x in range(n_w):
                for y in range(n_h):
                    start_x = min(x * self.patch_size, w - self.patch_size) if x < n_w - 1 else w - self.patch_size
                    start_y = min(y * self.patch_size, h - self.patch_size) if y < n_h - 1 else h - self.patch_size
                    self.patches.append((i, start_x, start_y))
                    self.patches_paths.append((i, img_path, noised_image_path))

        if self.visual_loader:
            self.patches = self.all_imgs


    def __len__(self):
        return len(self.patches)

    def __getitem__(self, idx):
        """
        返回源图像和目标图像的路径及元信息。

        """
        if self.visual_loader:
            target_image_path = self.patches[idx]
            noised_image_path = target_image_path.replace('target', 'source')
            noised_image = Image.open(noised_image_path).convert("RGB")
            if 'baidu_sr_1' in noised_image_path:  # TODO check
                noised_image = noised_image
            else:
                noised_image = default_degrade_image_quality(noised_image, self.image_degrade_pipe)
            target_image = Image.open(target_image_path).convert("RGB")
            base_name = os.path.basename(target_image_path)
            noised_image_array = cv2.cvtColor(np.array(noised_image), cv2.COLOR_RGB2BGR)
            target_image_array = cv2.cvtColor(np.array(target_image), cv2.COLOR_RGB2BGR)
            concate_images = np.concatenate((noised_image_array, target_image_array), axis=0)
            return concate_images, base_name

        i, x, y = self.patches[idx]
        i, target_image_path, noised_image_path = self.patches_paths[idx]

        # 加载源图像和目标图像
        noised_image = Image.open(noised_image_path).convert("RGB")
        if 'baidu_sr_1' in noised_image_path:  # TODO check
            noised_image = noised_image
        else:
            noised_image = default_degrade_image_quality(noised_image, self.image_degrade_pipe)
        target_image = Image.open(target_image_path).convert("RGB")

        w, h = noised_image.size[:2]
        mask = torch.zeros((1, h, w))
        mask[:, y:y + self.patch_size, x:x + self.patch_size] = 1
        mask_resized = transforms.Resize((self.patch_size, self.patch_size), antialias=True)(mask)
        mask_resized = torch.where(mask_resized > 0.5, torch.tensor(1.0), torch.tensor(0.0))

        target_image_resized = self.transform(target_image)
        noised_image_resized = self.transform(noised_image)

        # 裁剪相同位置
        noised_image = noised_image.crop((x, y, x + self.patch_size, y + self.patch_size))
        target_image = target_image.crop((x, y, x + self.patch_size, y + self.patch_size))

        noised_image = transforms.ToTensor()(noised_image)
        target_image = transforms.ToTensor()(target_image)

        combined_tensor_noised = torch.cat((noised_image, noised_image_resized, mask_resized), dim=0)
        combined_tensor_target = torch.cat((target_image, target_image_resized, mask_resized), dim=0)

        return {
            "noised_image": combined_tensor_noised,
            "target_image": combined_tensor_target
        }


class PatchSampler(Sampler):
    def __init__(self, dataset, patch_size=1024, shuffle=True):
        self.dataset = dataset
        self.patch_size = patch_size
        self.shuffle = shuffle
        self.indices = []

        # 计算每张图可以切出多少个完整patch
        for i in range(len(dataset)):
            data = dataset[i]
            data_name = data["dataset_name"]
            gt_path = os.path.join(dataset.data_root, data_name, data['target'])
            img = Image.open(gt_path).convert("RGB")
            w, h = img.size

            # 计算水平和垂直方向的patch数
            n_w = math.ceil(w / self.patch_size)
            n_h = math.ceil(h / self.patch_size)

            # 生成所有可能的patch位置
            for x in range(n_w):
                for y in range(n_h):
                    # 处理边缘情况：从右到左，从底到上
                    start_x = min(x * self.patch_size, w - self.patch_size) if x < n_w - 1 else w - self.patch_size
                    start_y = min(y * self.patch_size, h - self.patch_size) if y < n_h - 1 else h - self.patch_size
                    self.indices.append((i, start_x, start_y))
    def __iter__(self):
        if self.shuffle:
            random.shuffle(self.indices)
        return iter(self.indices)

    def __len__(self):
        return len(self.indices)


class MyCustomBatchSampler:
    def __init__(self, dataset, batch_size, patch_size):
        self.dataset = dataset
        self.batch_size = batch_size
        self.patch_size = patch_size

    def __iter__(self):
        batch = []
        for idx in random.sample(range(len(self.dataset)), len(self.dataset)):
            data = self.dataset[idx]
            noised_image = data["noised_image"]
            target_image = data["target_image"]

            print("正在裁剪中。。。")

            # 获取图像尺寸
            width, height = noised_image.size

            # 遍历图像进行裁剪
            for i in range(0, height, self.patch_size):
                for j in range(0, width, self.patch_size):
                    # 计算裁剪块的右下角坐标
                    right = min(j + self.patch_size, width)
                    bottom = min(i + self.patch_size, height)

                    # 判断是否需要调整步长（即当前块的宽度或高度不足 patch_size）
                    if right - j < self.patch_size:
                        # 调整步长，确保下一块完全填充
                        j = width - self.patch_size  # 直接跳到图像的右边缘

                    if bottom - i < self.patch_size:
                        # 调整步长，确保下一块完全填充
                        i = height - self.patch_size  # 直接跳到底部边缘

                    # 执行裁剪
                    crop_noised = noised_image.crop((j, i, j + self.patch_size, i + self.patch_size))
                    crop_target = target_image.crop((j, i, j + self.patch_size, i + self.patch_size))

                    # 将裁剪后的图像处理为7通道Tensor
                    tensor_noised = self._process_crops(crop_noised, noised_image, (j, i))
                    tensor_target = self._process_crops(crop_target, target_image, (j, i))

                    # 将两个7通道的Tensor合并为一个batch
                    batch.append((tensor_noised, tensor_target))
                    # 检查是否达到批次大小
                    if len(batch) == self.batch_size:
                        # 分离出 noised 和 target，拼接成 batch
                        noised_images = torch.stack([x[0] for x in batch])  # (batch_size, C, H, W)
                        target_images = torch.stack([x[1] for x in batch])  # (batch_size, C, H, W)

                        # 返回拼接后的批次
                        yield {
                            "noised": noised_images,
                            "target": target_images
                        }

                        # 清空 batch，准备下一个批次
                        batch = []

                    # 处理最后一个不满批次的情况
                if len(batch) > 0:
                    noised_images = torch.stack([x[0] for x in batch])
                    target_images = torch.stack([x[1] for x in batch])
                    yield {
                        "noised": noised_images,
                        "target": target_images
                    }

    def _process_crops(self, crop, original_image, crop_position):
        # 创建与原始图像相同大小的mask
        mask = torch.zeros((1, original_image.size[1], original_image.size[0]))  # [1, H, W]

        # 将原始图像resize为1024x1024
        transform = transforms.Compose([
            transforms.Resize((1024, 1024), antialias=True),
            transforms.ToTensor(),
        ])

        resized_original = transform(original_image)  # [3, 1024, 1024]
        resized_crop = transforms.ToTensor()(crop)  # [3, 1024, 1024]

        # 计算裁剪块在mask中的位置
        x, y = crop_position
        mask[:, y:y + self.patch_size, x:x + self.patch_size] = 1  # 设置对应区域为1

        # Resize mask为1024x1024
        mask_resized = transforms.Resize((1024, 1024), antialias=True)(mask)  # [1, 1024, 1024]

        # 拼接成7通道
        combined_tensor = torch.cat((resized_original, resized_crop, mask_resized), dim=0)  # [7, 1024, 1024]
        return combined_tensor

    def __len__(self):
        return len(self.dataset)


class ImageProcessingMethod(Enum):
    RESIZE_IMAGE_PAIR = {
        "func": resize_image_pair,
        "weight": 0.25,
        "flag": 0,
    }
    RANDOM_CROP_MAX_SQUARE_PAIR = {
        "func": random_crop_max_square_pair,
        "weight": 0.25,
        "flag": 1,
    }
    RANDOM_CROP_LOCAL_SQUARE_PAIR = {
        "func": random_crop_local_square_pair_with_resolution,
        "weight": 0.5,
        "flag": 2,
    }

    @staticmethod
    def get_random_method():
        """
        随机选择一个图像预处理方法。
        :return: 返回 (函数, 标志值)
        """
        # 提取所有成员的权重
        methods = list(ImageProcessingMethod)
        weights = [method.value["weight"] for method in methods]

        # 按权重随机选择一个方法
        selected_method = random.choices(methods, weights, k=1)[0]

        # 获取对应的函数和标志值
        return selected_method.value["func"], selected_method.value["flag"]


def dynamic_collate_fn_yf(batch):
    # transform = transforms.Compose([transforms.ToTensor()])

    noised_images = []
    target_images = []

    for item in batch:
        noised_image = item["noised_image"]
        target_image = item["target_image"]

        # 转为张量
        # noised_images.append(transform(noised_image))
        # target_images.append(transform(target_image))
        noised_images.append(noised_image)
        target_images.append(target_image)

    # 将图像数据拼接成 Tensor 格式
    noised_images = torch.stack(noised_images)  # 拼接成 (batch_size, C, H, W)
    target_images = torch.stack(target_images)  # 拼接成 (batch_size, C, H, W)

    # 返回批次
    return {
        "noised": noised_images,
        "target": target_images,
    }


def dynamic_collate_fn(batch, resolution_fun):
    """
    自定义的 collate_fn，用于动态调整分辨率。
    :param batch: 一个批次的数据，列表形式。
    :param resolution_fun: 一个函数，用于动态返回分辨率。
    :return: 返回动态调整分辨率后的批次数据。
    """
    # 动态获取分辨率
    resolution = resolution_fun()
    transform = transforms.Compose([transforms.ToTensor()])

    # 随机选择一个图像处理方法及其标志
    img_proc_func, img_proc_flag = ImageProcessingMethod.get_random_method()

    noised_images = []
    target_images = []
    resolutions = []
    img_proc_flags = []

    for item in batch:
        noised_image = item["noised_image"]
        target_image = item["target_image"]

        # 应用选定的图像处理方法
        noised_image, target_image = img_proc_func(
            image_list=[noised_image, target_image],
            size=resolution, sampling=Image.LANCZOS,
        )

        # 转为张量
        noised_images.append(transform(noised_image))
        target_images.append(transform(target_image))
        resolutions.append(resolution)
        img_proc_flags.append(img_proc_flag)

    # 将图像数据拼接成 Tensor 格式
    noised_images = torch.stack(noised_images)  # 拼接成 (batch_size, C, H, W)
    target_images = torch.stack(target_images)  # 拼接成 (batch_size, C, H, W)

    # 将分辨率和图像处理标志转为 Tensor 格式
    resolutions = torch.tensor(resolutions, dtype=torch.int32)        # 分辨率
    img_proc_flags = torch.tensor(img_proc_flags, dtype=torch.int32)  # 图像处理方法标志


    # 返回批次
    return {
        "noised": noised_images,
        "target": target_images,
        "resolution": resolutions,
        "img_proc_flag": img_proc_flags,
    }


def walk_dataloaders(loaders, weights=None):
    """
    遍历多个数据加载器，以随机顺序返回每个加载器的批次。
    每个加载器可以根据权重比例进行动态调整。
    :param loaders: 多个数据加载器的列表，每个元素是 (标志, DataLoader)。
    :param weights: 各个加载器的采样权重，列表或 None。如果为 None，则根据加载器数据长度自动计算。
    :yield: 返回 (标志, 批次)。
    """
    # 生成加载器的迭代器
    doing = [(flag, iter(loader), len(loader)) for flag, loader in loaders]

    # 如果未指定权重，根据数据加载器的长度计算权重
    if weights is None:
        total_length = sum(length for _, _, length in doing)
        weights = [length / total_length for _, _, length in doing]
    else:
        # 确保权重总和为 1
        weight_sum = sum(weights)
        weights = [w / weight_sum for w in weights]

    weight_with_flags = [(flag, weight) for (flag, _, _), weight in zip(doing, weights)]
    LOGGER.info(f"walk dataloader by weights: {weight_with_flags}")

    # 确保权重的数量与加载器数量一致
    assert len(weights) == len(doing), "权重数量必须与加载器数量一致"

    while doing:
        # 按权重随机选择一个加载器
        selected_idx = random.choices(range(len(doing)), weights=weights, k=1)[0]
        flag, it, length = doing[selected_idx]

        try:
            batch = next(it)
            yield flag, batch
        except StopIteration:
            # 当前加载器完成，移除
            del doing[selected_idx]
            del weights[selected_idx]

            # 重新计算权重
            if doing:
                total_length = sum(length for _, _, length in doing)
                weights = [length / total_length for _, _, length in doing]


# if __name__ == '__main__':
#     import  cv2
#     # 初始化数据集
#     data_dir = '/aicamera-mlp/yangyunfei/PrintProject/YFData/MySmartDocProducedV7'
#     dataset = MyCustomDataset(data_root=data_dir,seed=42, visual_loader=True, visual_num=100)
#     save_dir = '/mnt/aicamera-mlp/hz_datasets/tmp/lama_test/dataset_7_channel'
#     if not os.path.exists(save_dir):
#         os.makedirs(save_dir, exist_ok=True)
#     for i, (data, base_name) in enumerate(dataset):
#         cv2.imwrite(os.path.join(save_dir, f'random_sample_concate{i}_{base_name}'), data)