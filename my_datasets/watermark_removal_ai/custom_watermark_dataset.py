#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/7/18 16:08
# <AUTHOR> <EMAIL>
# @FileName: custom_watermark_dataset

import os
import random
import warnings
from io import BytesIO

import torch
from PIL import Image
from torch.utils.data import Dataset
from datasets import load_from_disk

from modules.utils.log import LOGGER
from modules.utils.image_utils import normalize_image_rgb, normalize_mask

Image.MAX_IMAGE_PIXELS = None

# Suppress the specific warning
warnings.filterwarnings(
    "ignore",
    category=UserWarning,
    message="Palette images with Transparency expressed in bytes should be converted to RGBA images"
)


class WatermarkDataset(Dataset):
    def __init__(self, hf_dataset_dir, resolution, mode='train', seed=42, select_num=-1):
        assert os.path.exists(os.path.join(hf_dataset_dir, "dataset_dict.json")), f"{hf_dataset_dir}: 无效数据或不存在"

        dataset = load_from_disk(hf_dataset_dir)[mode]
        dataset = dataset.shuffle(seed=seed)
        filter_conditions = {"image_size": f"{resolution}"}
        filtered_dataset = dataset.filter(
            lambda example: all(example[key] == value for key, value in filter_conditions.items())
        )
        if select_num > 0:
            filtered_dataset = filtered_dataset.select(range(select_num))
        self.dataset = filtered_dataset
        LOGGER.info(f"mode[{mode}], 指定分辨率：{resolution}, 共筛选样本总数：{len(self.dataset)}")

    def __len__(self):
        return len(self.dataset)

    def __getitem__(self, idx):
        example = self.dataset[idx]
        target_image = Image.open(BytesIO(example['target_image_bytes'])).convert('RGB')
        watermarked_image = Image.open(BytesIO(example['watermarked_image_bytes'])).convert('RGB')
        watermark_mask = Image.open(BytesIO(example['watermark_mask_bytes'])).convert("L")
        watermark_image = Image.open(BytesIO(example['watermark_image_bytes'])).convert('RGB')

        target_image = normalize_image_rgb(target_image)
        watermarked_image = normalize_image_rgb(watermarked_image)
        watermark_mask = normalize_mask(watermark_mask)
        watermark_image = normalize_image_rgb(watermark_image)

        batch = {
            "target_images": target_image,
            "watermarked_images": watermarked_image,
            "watermark_masks": watermark_mask,
            "watermark_images": watermark_image
        }

        return batch


def walk_dataloaders(loaders):
    doing = [iter(loader) for loader in loaders]
    random.shuffle(doing)
    i = 0
    while doing:
        i = i % len(doing)
        it = doing[i]
        try:
            batch = next(it)
            yield batch
            i += 1
        except StopIteration:
            del doing[i]


def check_inputs(target_images, watermarked_images, watermark_masks, watermark_images):
    # 检查所有 watermark_masks 是否全为0
    all_masks_zero = ~torch.any(watermark_masks.view(watermark_masks.size(0), -1), dim=1)

    # 如果所有的 watermark_masks 都为0，则返回 None
    if torch.all(all_masks_zero):
        return None, None, None, None

    # 否则，找到非全为0的索引，重新排列输入
    non_zero_indices = torch.where(~all_masks_zero)[0]

    target_images = target_images[non_zero_indices]
    watermarked_images = watermarked_images[non_zero_indices]
    watermark_masks = watermark_masks[non_zero_indices]
    watermark_images = watermark_images[non_zero_indices]

    return target_images, watermarked_images, watermark_masks, watermark_images