#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/7/2 10:19
# <AUTHOR> <EMAIL>
# @FileName: base_dataset_v4

import os
import random
import warnings
from io import BytesIO

import torch
import numpy as np
import torch.utils.data
import torchvision.transforms as transforms

from PIL import Image
from torch.utils.data import Dataset
from datasets import DatasetDict, concatenate_datasets

from modules.utils.log import LOGGER
from modules.watermark_removal_ai import WatermarkScheduler
from modules.utils.image_utils import random_crop_list, normalize_image_rgb, normalize_mask

Image.MAX_IMAGE_PIXELS = None
REFERENCE_RES = 1024  # 添加水印的参考尺寸，添加水印策略的图像参考尺寸，不要修改

# Suppress the specific warning
warnings.filterwarnings(
    "ignore",
    category=UserWarning,
    message="Palette images with Transparency expressed in bytes should be converted to RGBA images"
)

WATERMARK_TYPE_FLAG = {
    "tile_text_watermark": 0,
    "random_text_watermark": 1,
    "tile_logo_watermark": 2,
    "random_logo_watermark": 3,
    "logo_with_text_watermark": 4,
    "template_watermark": 5
}

LOGO_DIR_NAME_FLAG = {
    "pattern_based_common": 0,
    "pattern_based_others": 1,
    "text_based_common": 2,
    "text_based_others": 3,
    "pattern_text_based_common": 4,
    "pattern_text_based_others": 5,
    "custom_common_logo": 6,
    "custom_common_logo_binary": 7,
    "very_common_logo": 8,
    "very_common_logo_binary": 9,
    "custom_very_common_logo": 10,
    "custom_very_common_logo_binary": 11,
    "custom_watermark_templates": 12
}


class WatermarkDataset(Dataset):
    def __init__(
        self,
        resolutions,
        data_dirs,
        mode='train',
        retry_num=10,
        p_max_size_crop=0.5,
        p_blur=0.1,
        seed=42,
        dataset_config_file=""
    ):
        assert data_dirs is not None, "必须指定训练数据目录"
        for data_dir, _ in data_dirs:
            assert os.path.exists(data_dir), f"{data_dir} 不存在"

        dataset = []
        for data_dir, select_num in data_dirs:
            cur_dataset = DatasetDict.load_from_disk(data_dir)[mode]
            if select_num > 0:
                cur_dataset = cur_dataset.shuffle(seed=seed)
                cur_dataset = cur_dataset.select(range(select_num))
            dataset.append(cur_dataset)
        dataset = concatenate_datasets(dataset)
        dataset = dataset.shuffle(seed=seed)

        self.dataset = dataset
        self.resolutions = resolutions
        self.watermark_scheduler = WatermarkScheduler(config_file=dataset_config_file)
        self.retry_num = retry_num

        # Augmentation probabilities
        # 按比例裁剪出最大区域再resize到目标尺寸，然后加水印；否则，先加水印再按照目标尺寸裁剪出带有水印的区域
        self.p_max_size_crop = p_max_size_crop
        # 模糊增强的概率
        self.p_blur = p_blur
        msg = f"p_max_size_crop: {self.p_max_size_crop}; p_blur: {self.p_blur}"
        LOGGER.info(msg)
        LOGGER.info(f"total samples from dataset: {len(self.dataset)}")

    def __len__(self):
        return len(self.dataset)

    def get_patch_contains_watermark(self, image_pil, watermarked_image, watermark, watermark_mask, resolution, overlap=32):
        patches_contain_mask = []
        width, height = image_pil.size
        stride = resolution - overlap

        # 如果图片最短边小于目标裁剪分辨率，先按比例调整大小
        if min(width, height) < resolution:
            ratio = resolution / min(width, height)
            new_width = int(width * ratio)
            new_height = int(height * ratio)
            image_pil = image_pil.resize((new_width, new_height), Image.LANCZOS)
            watermarked_image = watermarked_image.resize((new_width, new_height), Image.LANCZOS)
            watermark = watermark.resize((new_width, new_height), Image.LANCZOS)
            watermark_mask = watermark_mask.resize((new_width, new_height), Image.LANCZOS)
            width, height = new_width, new_height

        for y in range(0, height, stride):
            for x in range(0, width, stride):
                right = min(x + resolution, width)
                bottom = min(y + resolution, height)

                if right - x < resolution or bottom - y < resolution:
                    # 反向裁剪
                    x = max(0, right - resolution)
                    y = max(0, bottom - resolution)
                    right = x + resolution
                    bottom = y + resolution

                patch_image = image_pil.crop((x, y, right, bottom))
                patch_watermarked = watermarked_image.crop((x, y, right, bottom))
                patch_watermark = watermark.crop((x, y, right, bottom))
                patch_mask = watermark_mask.crop((x, y, right, bottom))

                # Convert mask patch to numpy array to check for any non-zero values
                mask_array = np.asarray(patch_mask)
                if mask_array.sum() != 0:
                    cur_patches = (patch_image, patch_watermarked, patch_watermark, patch_mask)
                    patch_info = (x, y, right, bottom)
                    patches_contain_mask.append((cur_patches, patch_info))

        # 随机选择一个有效的patch
        if patches_contain_mask:
            return random.choice(patches_contain_mask)
        else:
            return None

    def apply_augmentations(self, image_list):
        # Apply blur
        if random.random() < self.p_blur:
            kernel_size = 5  # You can choose a different fixed size if needed
            sigma = random.uniform(0.1, 2.0)
            blur = transforms.GaussianBlur(kernel_size=kernel_size, sigma=sigma)
            image_list = [blur(image) for image in image_list]

        return image_list

    def __getitem__(self, idx):
        example = self.dataset[idx]
        image_bytes = example['image_bytes']
        image_pil = Image.open(BytesIO(image_bytes)).convert('RGB')

        return image_pil, idx

def custom_collate_fn(batch: list, watermark_dataset: WatermarkDataset, debug=False):
    # 随机选择一个分辨率
    resolution = random.choice(watermark_dataset.resolutions)
    target_size = (resolution, resolution)

    target_images = []
    watermarked_images = []
    watermark_masks = []
    watermark_images = []

    # TODO: 仅用于Debug, 后期可以移除
    crop_type_flag = []
    watermark_type_flag = []
    has_reverse_color_flag = []
    logo_dir_name_flag = []

    batch_images, batch_indices = zip(*batch)
    for image_pil, idx in zip(batch_images, batch_indices):
        # 按比例裁剪出最大区域再resize到目标尺寸，然后加水印
        if random.random() < watermark_dataset.p_max_size_crop:
            image_pil = random_crop_list([image_pil], crop_size=target_size)[0]
            image_pil = image_pil.resize((REFERENCE_RES, REFERENCE_RES), resample=Image.LANCZOS)

            for _ in range(watermark_dataset.retry_num):
                results = watermark_dataset.watermark_scheduler.add_watermark(image_pil, idx)
                watermarked_image, watermark, watermark_mask, has_reverse_color, flag, logo_dir_name = results
                watermarked_image = watermarked_image.resize(target_size, resample=Image.LANCZOS)
                watermark = watermark.resize(target_size, resample=Image.LANCZOS)
                watermark_mask = watermark_mask.resize(target_size, resample=Image.LANCZOS)
                target_image = image_pil.resize(target_size, resample=Image.LANCZOS)

                if np.asarray(watermark_mask).sum() != 0:
                    break

            # 数据标志位信息，可用于Debug
            crop_type_flag.append(0)
            watermark_type_flag.append(WATERMARK_TYPE_FLAG[flag])
            if has_reverse_color:
                has_reverse_color_flag.append(1)
            else:
                has_reverse_color_flag.append(0)
            if logo_dir_name is None:
                logo_dir_name_flag.append(-1)
            else:
                logo_dir_name_flag.append(LOGO_DIR_NAME_FLAG[logo_dir_name])

        # 先加水印再按照目标尺寸裁剪出带有水印的区域
        else:
            for _ in range(watermark_dataset.retry_num):
                results = watermark_dataset.watermark_scheduler.add_watermark(image_pil, idx)
                watermarked_image, watermark, watermark_mask, has_reverse_color, flag, logo_dir_name = results
                patches = watermark_dataset.get_patch_contains_watermark(
                    image_pil, watermarked_image, watermark, watermark_mask, resolution, overlap=32
                )
                if patches is None:
                    continue
                target_image, watermarked_image, watermark, watermark_mask = patches[0]

            # 数据标志位信息，可用于Debug
            crop_type_flag.append(1)
            watermark_type_flag.append(WATERMARK_TYPE_FLAG[flag])
            if has_reverse_color:
                has_reverse_color_flag.append(1)
            else:
                has_reverse_color_flag.append(0)
            if logo_dir_name is None:
                logo_dir_name_flag.append(-1)
            else:
                logo_dir_name_flag.append(LOGO_DIR_NAME_FLAG[logo_dir_name])

        # 数据增强
        target_image, watermarked_image = watermark_dataset.apply_augmentations([target_image, watermarked_image])
        target_image = normalize_image_rgb(target_image)
        watermarked_image = normalize_image_rgb(watermarked_image)
        watermark = normalize_image_rgb(watermark)
        watermark_mask = watermark_mask.convert('L')
        watermark_mask = normalize_mask(watermark_mask)
        watermark = (1 - watermark_mask) * target_image + watermark_mask * watermark

        target_images.append(target_image)
        watermarked_images.append(watermarked_image)
        watermark_masks.append(watermark_mask)
        watermark_images.append(watermark)

    target_images = torch.stack(target_images)
    target_images = target_images.to(dtype=torch.float32)
    watermark_masks = torch.stack(watermark_masks)
    watermark_masks = watermark_masks.to(dtype=torch.float32)
    watermark_images = torch.stack(watermark_images)
    watermark_images = watermark_images.to(dtype=torch.float32)
    watermarked_images = torch.stack(watermarked_images)
    watermarked_images = watermarked_images.to(dtype=torch.float32)

    batch = {
        "target_images": target_images,
        "watermarked_images": watermarked_images,
        "watermark_masks": watermark_masks,
        "watermark_images": watermark_images,
    }

    if debug:
        batch["crop_type_flags"] = torch.LongTensor(crop_type_flag)
        batch["watermark_type_flags"] = torch.LongTensor(watermark_type_flag)
        batch["has_reverse_color_flags"] = torch.LongTensor(has_reverse_color_flag)
        batch["logo_dir_name_flags"] = torch.LongTensor(logo_dir_name_flag)

    return batch
