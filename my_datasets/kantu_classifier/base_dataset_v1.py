#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/23 10:48
# <AUTHOR> <EMAIL>
# @FileName: base_dataset_v1

import os
import random
import warnings
from io import BytesIO

import torch
import torchvision.transforms as transforms

from PIL import Image
from torch.utils.data import Dataset
from datasets import DatasetDict, concatenate_datasets

from modules.utils.log import LOGGER

Image.MAX_IMAGE_PIXELS = None

# Suppress the specific warning
warnings.filterwarnings(
    "ignore",
    category=UserWarning,
    message="Palette images with Transparency expressed in bytes should be converted to RGBA images"
)

# 定义ImageNet的标准化参数
imagenet_mean = [0.485, 0.456, 0.406]
imagenet_std = [0.229, 0.224, 0.225]


class MyCustomDataset(Dataset):
    def __init__(self, data_dirs, data_type='cls_clear_blur', mode='train', seed=-1):
        if seed == -1:
            seed = random.randint(1, 12580)

        assert data_dirs is not None, "必须指定训练数据目录"
        for data_dir, _ in data_dirs:
            assert os.path.exists(data_dir), f"{data_dir} 不存在"

        dataset = []
        for data_dir, select_num in data_dirs:
            cur_dataset = DatasetDict.load_from_disk(data_dir)[mode]
            if select_num > 0:
                cur_dataset = cur_dataset.shuffle(seed=seed)
                cur_dataset = cur_dataset.select(range(select_num))
            dataset.append(cur_dataset)
        dataset = concatenate_datasets(dataset)
        dataset = dataset.shuffle(seed=seed)

        filter_conditions = {"data_type": data_type}
        dataset = dataset.filter(
            lambda example: all(example[key] == value for key, value in filter_conditions.items())
        )
        self.dataset = dataset
        LOGGER.info(f"total samples of `{data_type}`: {len(self.dataset)}")

        # 数据增强部分
        if mode == 'train':
            if data_type in ['cls_doc_nodoc', 'cls_snap_captured']:
                # 如果是指定的data_type，使用随机裁剪增强
                self.transform = transforms.Compose([
                    transforms.RandomResizedCrop(512, scale=(0.7, 1.0), ratio=(0.75, 1.33)),  # 随机裁剪到512x512
                    transforms.RandomHorizontalFlip(),
                    transforms.ToTensor(),
                    transforms.Normalize(mean=imagenet_mean, std=imagenet_std)
                ])
            else:
                # 默认的数据增强
                self.transform = transforms.Compose([
                    transforms.Resize((512, 512)),  # 缩放到512x512
                    transforms.RandomHorizontalFlip(),
                    transforms.ToTensor(),
                    transforms.Normalize(mean=imagenet_mean, std=imagenet_std)
                ])
        else:
            # 验证模式下的transform（无论 data_type）
            self.transform = transforms.Compose([
                transforms.Resize((512, 512)),
                transforms.ToTensor(),
                transforms.Normalize(mean=imagenet_mean, std=imagenet_std)
            ])

    def __len__(self):
        return len(self.dataset)

    def __getitem__(self, idx):
        # features: ['image_path', 'data_type', 'label', 'image_bytes']
        sample = self.dataset[idx]
        image_bytes = sample['image_bytes']
        label = sample['label']

        # 使用BytesIO将image_bytes转化为PIL.Image
        with Image.open(BytesIO(image_bytes)) as image:
            image = image.convert('RGB')
            image = self.transform(image)

        # 根据label映射到二分类标签
        if label in ['clear', 'no_human', 'normal', 'clean', 'noblack', 'noblur_doc', 'nodoc', 'noshadow', 'snapshot_doc', 'normal_doc']:
            label = torch.tensor([0.])
        elif label in ['blur', 'human', 'texted', 'watermarked', 'black', 'blur_doc', 'doc', 'shadow', 'captured_doc', 'cert']:
            label = torch.tensor([1.])
        else:
            raise ValueError(f"Found unexpected label: {label}")

        data = {
            'image': image,
            'label': label,
        }

        return data


def walk_dataloaders(loaders):
    # 生成带有标志和数据加载器的列表
    doing = [(flag, iter(loader)) for flag, loader in loaders]
    random.shuffle(doing)
    i = 0
    while doing:
        i = i % len(doing)
        flag, it = doing[i]
        try:
            batch = next(it)
            # 返回标志和批次
            yield flag, batch
            i += 1
        except StopIteration:
            del doing[i]