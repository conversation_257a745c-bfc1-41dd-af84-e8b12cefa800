#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/04/21 16:38
# <AUTHOR> <EMAIL>
# @FileName: rec_post_proc.py

import numpy as np
from typing import Dict, List, Union, Tuple


class BaseRecPostProc(object):
    """文本识别后处理基类"""
    
    def __init__(self, **kwargs):
        pass
    
    def __call__(self, pred: np.ndarray) -> Union[List[Dict], Dict]:
        """执行后处理操作"""
        raise NotImplementedError


class CTCLabelDecode(BaseRecPostProc):
    """基于CTC解码的识别模型后处理类"""
    
    def __init__(
        self,
        character_dict: str,
        use_space_char: bool = False,
        **kwargs
    ):
        """
        初始化CTC解码后处理器
        Args:
            character_dict: 字符字典字符串或字典路径
            use_space_char: 是否使用空格字符
            **kwargs: 其他参数
        """
        super().__init__(**kwargs)
        raw_character = self._parse_character_dict(character_dict)
        self.character = self.add_special_char(list(raw_character))
        
        # 如果需要空格字符且字典中没有
        if use_space_char and ' ' not in self.character:
            self.character.append(' ')
    
    def add_special_char(self, dict_character):
        """添加特殊字符到字符字典，与原版PaddleOCR保持一致"""
        dict_character = ["blank"] + dict_character
        return dict_character
    
    def _parse_character_dict(self, character_dict: str) -> str:
        """
        解析字符字典
        Args:
            character_dict: 字符字典字符串或字典路径
        Returns:
            字符串形式的字符字典
        """
        import os
        
        # 如果是路径，则读取文件
        if os.path.exists(character_dict):
            with open(character_dict, 'r', encoding='utf-8') as f:
                character = ''
                for line in f.readlines():
                    line = line.strip('\n').strip('\r\n')
                    character += line
            return character
        else:
            # 否则假设是直接的字符串
            return character_dict
    
    def get_ignored_tokens(self):
        """获取需要忽略的标记"""
        return [0]  # CTC中的blank标记
    
    def decode(self, preds: np.ndarray, preds_prob: np.ndarray = None, is_remove_duplicate: bool = True) -> Tuple[str, float, List[float]]:
        """
        对单个预测结果进行解码
        Args:
            preds: 预测的索引序列
            preds_prob: 预测的概率序列
            is_remove_duplicate: 是否合并重复字符，CTC算法通常需要设置为True
        Returns:
            text: 解码后的文本
            confidence: 整体置信度
            char_probs: 字符级别置信度列表
        """
        # 处理索引，过滤忽略的token和重复字符
        ignored_tokens = self.get_ignored_tokens()
        selection = np.ones(len(preds), dtype=bool)
        
        # 根据参数决定是否去除重复字符
        if is_remove_duplicate:
            # 去除相邻的重复字符
            selection[1:] = preds[1:] != preds[:-1]
        
        # 忽略特定标记
        for ignored_token in ignored_tokens:
            selection &= preds != ignored_token
        
        # 根据选择掩码提取字符索引和概率
        filtered_indices = preds[selection]
        filtered_probs = preds_prob[selection] if preds_prob is not None else None
        
        # 确保即使没有提取到任何字符也能返回有效结果
        if not filtered_indices.size:
            return "", 0.0, []
        
        # 转换为文本
        char_list = []
        char_probs = []
        for i, char_idx in enumerate(filtered_indices):
            if 0 <= char_idx < len(self.character):  # 直接使用索引，与原版保持一致
                char_list.append(self.character[char_idx])
                if filtered_probs is not None:
                    char_probs.append(filtered_probs[i])
        
        text = "".join(char_list)
        
        # 计算置信度
        if char_probs:
            confidence = sum(char_probs) / len(char_probs)
        else:
            confidence = 0.0
        
        return text, confidence, char_probs
    
    def __call__(self, preds: np.ndarray) -> List[Dict]:
        """
        处理模型输出，进行CTC解码
        Args:
            preds: 模型输出，形状为 [batch_size, seq_len, num_classes]
        Returns:
            解码结果列表，每项包含文本和置信度
        """
        if len(preds.shape) == 3:
            # 获取每个时间步的最大概率及其索引
            preds_prob = np.max(preds, axis=2)  # [batch_size, seq_len]
            preds_idx = np.argmax(preds, axis=2)  # [batch_size, seq_len]
            batch_size = preds.shape[0]
            
            # 对批次中的每个样本进行解码
            results = []
            for idx in range(batch_size):
                text, confidence, grouped_probs = self.decode(
                    preds_idx[idx], preds_prob[idx], is_remove_duplicate=True)  # 使用True与PaddleOCR保持一致
                
                # 即使没有识别出文本，也返回一个有效结果
                results.append({
                    "text": text,
                    "confidence": float(confidence),
                    "char_probs": grouped_probs
                })
            return results
        else:
            # 如果输入不是3D数组，则假设已经是索引和概率
            raise ValueError("预期输入形状为[batch_size, seq_len, num_classes]，但获得: {}".format(preds.shape))


def build_postprocess(config, global_config=None):
    """
    构建后处理器
    Args:
        config: 后处理配置
        global_config: 全局配置
    Returns:
        后处理器实例
    """
    config = config.copy() if config else {}
    module_name = config.pop('name', 'CTCLabelDecode')
    
    # 从全局配置中提取相关参数
    if global_config is not None:
        pass  # TODO: 有需要再添加支持双重配置

    # 处理字符字典配置
    character_dict = config.get('character_dict', None)
    use_space_char = config.get('use_space_char', False)
    
    # 确保有字符字典
    if not character_dict:
        raise ValueError("未提供字符字典路径，无法构建识别后处理器")
    
    # 构建对应的后处理器
    if module_name == 'CTCLabelDecode':
        module_class = CTCLabelDecode(**config)
    else:
        raise Exception(f'不支持的后处理类型: {module_name}')
    
    return module_class
