#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/3/9 01:00
# <AUTHOR> <EMAIL>
# @FileName: inner_augmentor

from typing import List, Tuple, Optional

import os
import random
import traceback
from pathlib import Path

import numpy as np
from tqdm import tqdm
from omegaconf import OmegaConf
from PIL import Image, ImageDraw, ImageEnhance

from modules.utils.log import LOGGER
from modules.utils.image_utils import get_all_image_path
from modules.utils.blend_utils import blend_gt_and_shadow


class SealAugmentor(object):
    def __init__(self, config_file: str, retry_num: int = 5):
        """
        根据配置信息，从印章图片根路径中递归搜索所有图片资源(png)，搜索时注意跳过隐藏文件
        需要检测是否带透明通道，如果透明通道数据全为1.（255）（即没有任何透明相关的信息，则不能放到有效印章数据里面）
        """
        self.config = OmegaConf.load(config_file)
        self.retry_num = retry_num
        self.logger = LOGGER
        self.seal_images = dict()
        # 创建高级印章融合器
        self.seal_blender = EnhancedSealBlender()

        # 获取印章资源目录
        seal_dir = self.config.get('seal_dir', None)
        if not seal_dir or not os.path.exists(seal_dir):
            self.logger.warning(f"印章目录不存在: {seal_dir}")
            return

        # 递归搜索所有png图片
        seal_path = Path(seal_dir)
        # 先收集所有图片路径
        all_png_paths = list(seal_path.rglob("*.png"))
        self.logger.info(f"共发现 {len(all_png_paths)} 个候选印章图片，正在检查和加载...")

        # 使用tqdm显示处理进度
        for img_path in tqdm(all_png_paths, desc="处理印章图片"):
            # 跳过隐藏文件
            if img_path.name.startswith("."):
                continue

            try:
                # 打开图片并检查是否有有效的透明通道
                img = Image.open(img_path)
                if img.mode == 'RGBA':
                    # 检查透明通道是否有效（不是全为255）
                    _, _, _, alpha = img.split()
                    alpha_array = np.array(alpha)
                    if not np.all(alpha_array == 255):
                        seal_src = img_path.parts[-2]
                        if seal_src in self.seal_images:
                            self.seal_images[seal_src].append(str(img_path))
                        else:
                            self.seal_images[seal_src] = [str(img_path)]
                    else:
                        self.logger.debug(f"印章透明通道无效（全为255），跳过: {img_path}")
                else:
                    # 非RGBA图片跳过
                    self.logger.debug(f"跳过非RGBA格式印章: {img_path}")

            except Exception as e:
                self.logger.warning(f"加载印章图片失败: {img_path}, 错误: {e}")

        total_num = 0
        for seal_src in self.seal_images:
            total_num += len(self.seal_images[seal_src])
        self.logger.info(f"成功加载印章图片: {total_num}个")

    def _add_seal(self, image: Image.Image, rect_pts: List[Tuple[int, int]], idx: int) -> Tuple[Image.Image, bool]:
        """
        内部方法：基于索引(idx)取模选择印章资源，对印章进行特定角度的旋转处理，然后将其合成到目标图像上。

        Args:
            image: 输入图像
            rect_pts: 允许放置印章的区域，格式为4个点组成的多边形: [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
            idx: 样本索引，用于选择印章

        Returns:
            添加印章后的图像和成功标志(Tuple[Image.Image, bool])
        """
        if not self.seal_images:
            return image, False

        # 克隆原始图像
        result = image.copy()

        # 根据idx选择印章
        # 使用随机选择替代固定的idx取模选择印章源
        seal_src_keys = list(self.seal_images.keys())
        seal_src_idx = random.randint(0, len(seal_src_keys) - 1)
        seal_images = self.seal_images[seal_src_keys[seal_src_idx]]
        seal_idx = idx % len(seal_images)
        seal_path = seal_images[seal_idx]

        try:
            # 加载印章图像
            seal_img = Image.open(seal_path)

            # 计算区域的边界框
            xs = [pt[0] for pt in rect_pts]
            ys = [pt[1] for pt in rect_pts]
            min_x, max_x = min(xs), max(xs)
            min_y, max_y = min(ys), max(ys)

            # 计算区域的宽度和高度
            rect_width = max_x - min_x
            rect_height = max_y - min_y

            # 先随机旋转印章（0-360度）
            rotation_angle = random.randint(0, 360)
            rotated_seal = seal_img.rotate(rotation_angle, expand=True, resample=Image.BICUBIC)

            # 计算旋转后的印章大小
            rotated_width, rotated_height = rotated_seal.size

            # 计算缩放比例，确保最长边不超出区域
            width_ratio = rect_width / rotated_width
            height_ratio = rect_height / rotated_height

            # 取最小比例，确保两个方向都不超过
            scale_ratio = min(width_ratio, height_ratio) * random.uniform(0.90, 1.40)

            # 计算新的尺寸
            new_width = int(rotated_width * scale_ratio)
            new_height = int(rotated_height * scale_ratio)

            # 调整印章大小
            seal_img = rotated_seal.resize((new_width, new_height), Image.LANCZOS)

            # 分析印章的有效区域（非透明区域）
            if seal_img.mode == 'RGBA':
                # 提取透明通道
                _, _, _, alpha = seal_img.split()
                alpha_array = np.array(alpha)

                # 找出非透明区域的像素点位置
                non_transparent = alpha_array > 10  # 透明度大于10的像素点视为非透明
                if np.any(non_transparent):  # 确保有非透明区域
                    # 找出所有非透明像素的行和列索引
                    rows = np.where(np.any(non_transparent, axis=1))[0]
                    cols = np.where(np.any(non_transparent, axis=0))[0]

                    if len(rows) > 0 and len(cols) > 0:
                        # 计算真实的边界框
                        effective_min_y, effective_max_y = rows.min(), rows.max() + 1
                        effective_min_x, effective_max_x = cols.min(), cols.max() + 1

                        # 计算有效印章区域的大小
                        effective_width = effective_max_x - effective_min_x
                        effective_height = effective_max_y - effective_min_y
                    else:
                        # 如果无法计算有效区域，使用整个印章大小
                        effective_min_x, effective_min_y = 0, 0
                        effective_width, effective_height = seal_img.width, seal_img.height
                else:
                    # 如果全透明，使用整个印章大小
                    effective_min_x, effective_min_y = 0, 0
                    effective_width, effective_height = seal_img.width, seal_img.height
            else:
                # 非RGBA格式印章，使用整个印章大小
                effective_min_x, effective_min_y = 0, 0
                effective_width, effective_height = seal_img.width, seal_img.height

            # 计算在多边形区域内可放置的位置范围
            # 考虑到印章尺寸，调整放置范围
            image_width, image_height = image.size

            # 计算默认的粘贴位置范围
            default_max_x = max_x - seal_img.width
            default_max_y = max_y - seal_img.height

            # 确保最大值大于最小值
            if default_max_x < min_x:
                default_max_x = min_x
            if default_max_y < min_y:
                default_max_y = min_y

            # 添加边缘印章效果的概率
            edge_seal_prob = 0.3

            if random.random() < edge_seal_prob:
                # 决定是水平方向还是垂直方向的边缘印章
                is_horizontal = random.random() < 0.5

                # 决定要漏出多少比例
                visible_ratio = random.uniform(0.2, 0.5)

                if is_horizontal:
                    # 水平方向的边缘印章 - 基于有效区域计算
                    # 决定是左边缘还是右边缘
                    is_left = random.random() < 0.5

                    if is_left:
                        # 左边缘印章 - 基于有效区域计算
                        visible_width = int(effective_width * visible_ratio)
                        hidden_part = effective_width - visible_width

                        # 粘贴位置调整，考虑有效区域的偏移
                        paste_x = 0 - (effective_min_x + hidden_part)
                        paste_y = random.randint(min_y, default_max_y)
                    else:
                        # 右边缘印章 - 基于有效区域计算
                        visible_width = int(effective_width * visible_ratio)

                        # 粘贴位置调整，考虑有效区域的偏移
                        paste_x = image_width - visible_width - effective_min_x
                        paste_y = random.randint(min_y, default_max_y)
                else:
                    # 垂直方向的边缘印章
                    # 决定是上边缘还是下边缘
                    is_top = random.random() < 0.5

                    if is_top:
                        # 上边缘印章 - 基于有效区域计算
                        visible_height = int(effective_height * visible_ratio)
                        hidden_part = effective_height - visible_height

                        # 粘贴位置调整，考虑有效区域的偏移
                        paste_x = random.randint(min_x, default_max_x)
                        paste_y = 0 - (effective_min_y + hidden_part)
                    else:
                        # 下边缘印章 - 基于有效区域计算
                        visible_height = int(effective_height * visible_ratio)

                        # 粘贴位置调整，考虑有效区域的偏移
                        paste_x = random.randint(min_x, default_max_x)
                        paste_y = image_height - visible_height - effective_min_y
            else:
                # 常规中间印章
                paste_x = random.randint(min_x, default_max_x)
                paste_y = random.randint(min_y, default_max_y)

            # 使用先进的印章融合技术来增强真实性，随机调整融合参数，增加变化性
            alpha_factor = random.uniform(0.85, 0.95)  # 总体透明度
            wear_level = random.uniform(0.0, 0.1)  # 磨损程度
            apply_pressure = random.random() > 0.5  # 50%的几率应用压力效果

            # 使用高级融合类来合成印章
            result = self.seal_blender.blend_seal(
                image=result,
                seal_img=seal_img,
                position=(paste_x, paste_y),
                alpha_factor=alpha_factor,
                wear_level=wear_level,
                apply_pressure=apply_pressure
            )
            return result, True

        except Exception as e:
            self.logger.warning(f"_add_seal内部处理失败: {e}")
            return image, False

    def add_seal(self, image: Image.Image, rect_pts: List[Tuple[int, int]], idx: int) -> Tuple[Image.Image, bool]:
        """
        基于索引(idx)取模选择印章资源，对印章进行特定角度的旋转处理，然后将其合成到目标图像上。

        关键技术要点：
        1. 空间约束：印章的合成区域严格限制在rect_pts定义的多边形范围内，需通过适当缩放确保印章完全落在目标区域
        2. 位置随机性：在约束区域内随机确定印章放置位置，增强数据多样性
        3. 融合真实性：文档图像（尤其含文字区域）与印章的叠加并非简单的前景-背景覆盖关系，而是需采用高级融合算法
           模拟物理印章与纸张的相互作用，如墨色渗透、压力分布不均、边缘磨损等特性，从而呈现更逼真的印章效果

        Args:
            image: 输入图像
            rect_pts: 允许放置印章的区域，格式为4个点组成的多边形: [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
            idx: 样本索引，用于选择印章

        Returns:
            Tuple[Image.Image, bool]: 添加印章后的图像和成功标志
        """
        try:
            # 首次尝试添加印章
            result, success = self._add_seal(image, rect_pts, idx)
            if success:
                return result, True
        except Exception as e:
            self.logger.warning(f"添加印章首次尝试失败: {e}")
            traceback.print_exc()

        # 如果首次尝试失败，进行重试
        for retry in range(self.retry_num):
            try:
                # 随机选择新的印章索引
                new_idx = random.randint(0, len(self.seal_images) - 1)
                result, success = self._add_seal(image, rect_pts, new_idx)
                if success:
                    self.logger.info(f"添加印章重试成功，使用了第{retry + 1}次重试")
                    return result, True
            except Exception as e:
                self.logger.warning(f"添加印章重试 {retry + 1}/{self.retry_num} 失败: {e}")
                continue

        # 所有尝试都失败，返回原始图像
        self.logger.warning(f"添加印章在 {self.retry_num} 次重试后彻底失败")
        return image, False


class EnhancedSealBlender:
    """
    高级印章融合类，提供更真实的印章效果
    通过模拟印章的物理特性（如压力不均、颜色渗透、边缘磨损等），使合成印章更加真实
    """

    def __init__(self, random_state: Optional[np.random.RandomState] = None):
        """
        初始化高级印章融合类

        Args:
            random_state: 随机状态，用于生成可重复的随机效果
        """
        self.random = random_state if random_state else np.random

    def enhance_seal_color(
        self,
        seal_img: Image.Image,
        contrast_factor: float = 2.5,  # 增加对比度因子，使红色更深更鲜明
        noise_level: float = 5.0       # 降低噪声水平，从10.0降低到5.0，保持一些变化但减少对红色的影响
    ) -> Image.Image:
        """
        增强印章颜色，使其更加逼真

        Args:
            seal_img: 原始印章图像 (RGBA)
            contrast_factor: 对比度增强因子
            noise_level: 噪声水平，用于模拟印泥浓淡不均

        Returns:
            增强后的印章图像
        """
        # 确保图像为RGBA模式
        if seal_img.mode != 'RGBA':
            seal_img = seal_img.convert('RGBA')

        # 分离通道
        r, g, b, alpha = seal_img.split()

        # 增强红色通道对比度
        enhanced_r = ImageEnhance.Contrast(r).enhance(contrast_factor)

        # 创建微妙的颜色变化，模拟印泥浓淡不均
        r_array = np.array(enhanced_r)
        noise = self.random.normal(0, noise_level, r_array.shape).astype(np.int16)
        r_array = np.clip(r_array.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        new_r = Image.fromarray(r_array)

        # 合并通道，创建红色印章
        # 通常印章是红色的，所以G和B通道设为0，只使用R通道
        return Image.merge('RGBA', (new_r, Image.new('L', r.size, 0), Image.new('L', r.size, 0), alpha))

    def apply_pressure_effect(self, seal_img: Image.Image, alpha: Image.Image) -> Image.Image:
        """
        应用压力效果，模拟印章按压不均匀

        Args:
            seal_img: 印章图像
            alpha: 原始透明通道

        Returns:
            应用压力效果后的透明通道
        """
        # 创建压力图
        pressure_map = Image.new('L', seal_img.size)
        draw = ImageDraw.Draw(pressure_map)
        center_x, center_y = seal_img.width / 2, seal_img.height / 2

        # 模拟径向压力分布
        for y in range(seal_img.height):
            for x in range(seal_img.width):
                # 计算到中心的距离
                distance = ((x - center_x) ** 2 + (y - center_y) ** 2) ** 0.5
                # 压力从中心向外递减
                pressure = max(0, 255 - int(distance * 255 / (seal_img.width / 2)))
                # 添加随机性，模拟不均匀按压
                pressure = min(255, pressure + random.randint(-30, 10))
                draw.point((x, y), fill=pressure)

        # 应用压力图调整透明度
        alpha_data = np.array(alpha)
        pressure_data = np.array(pressure_map)
        # 取最小值，确保压力较小的地方透明度也较低
        alpha_data = np.minimum(alpha_data, pressure_data)

        return Image.fromarray(alpha_data)

    def simulate_edge_wear(self, seal_img: Image.Image, alpha: Image.Image, wear_level: float = 0.2) -> Image.Image:
        """
        模拟印章边缘磨损效果

        Args:
            seal_img: 印章图像
            alpha: 透明通道
            wear_level: 磨损程度 (0.0-1.0)

        Returns:
            模拟磨损后的透明通道
        """
        alpha_array = np.array(alpha)

        # 创建边缘检测 - 模拟简单的边缘检测
        # 在没有OpenCV的情况下，使用numpy实现基本边缘检测
        edges = np.zeros_like(alpha_array)
        h, w = alpha_array.shape
        for y in range(1, h - 1):
            for x in range(1, w - 1):
                # 简单的Sobel算子检测边缘
                gx = alpha_array[y - 1, x + 1] + 2 * alpha_array[y, x + 1] + alpha_array[y + 1, x + 1] - \
                     (alpha_array[y - 1, x - 1] + 2 * alpha_array[y, x - 1] + alpha_array[y + 1, x - 1])
                gy = alpha_array[y + 1, x - 1] + 2 * alpha_array[y + 1, x] + alpha_array[y + 1, x + 1] - \
                     (alpha_array[y - 1, x - 1] + 2 * alpha_array[y - 1, x] + alpha_array[y - 1, x + 1])
                grad = np.sqrt(gx * gx + gy * gy)
                edges[y, x] = min(255, grad)

        # 根据边缘添加磨损效果
        mask = self.random.random(edges.shape) < wear_level
        wear_mask = edges > 50  # 边缘阈值
        wear_mask = wear_mask & mask

        # 边缘处透明度降低
        alpha_array[wear_mask] = alpha_array[wear_mask] * 0.5

        # 随机添加小缺口
        for _ in range(int(alpha_array.shape[0] * wear_level * 5)):
            x = self.random.randint(0, alpha_array.shape[1] - 1)
            y = self.random.randint(0, alpha_array.shape[0] - 1)
            rad = self.random.randint(1, 3)  # 缺口半径

            # 创建简单的圆形缺口
            for dy in range(-rad, rad + 1):
                for dx in range(-rad, rad + 1):
                    if dx * dx + dy * dy <= rad * rad:  # 圆形判定
                        ny, nx = y + dy, x + dx
                        if 0 <= ny < alpha_array.shape[0] and 0 <= nx < alpha_array.shape[1]:
                            alpha_array[ny, nx] = 0

        return Image.fromarray(alpha_array)

    def blend_seal(
        self,
        image: Image.Image,
        seal_img: Image.Image,
        position: Tuple[int, int],
        alpha_factor: float = 0.8,
        wear_level: float = 0.2,
        apply_pressure: bool = True
    ) -> Image.Image:
        """
        将增强后的印章融合到图像上

        Args:
            image: 背景图像
            seal_img: 印章图像 (RGBA)
            position: 印章放置位置 (x, y)
            alpha_factor: 总体透明度因子
            wear_level: 边缘磨损程度
            apply_pressure: 是否应用压力效果

        Returns:
            融合后的图像
        """
        # 克隆原始图像
        result = image.copy()

        # 确保印章为RGBA模式
        if seal_img.mode != 'RGBA':
            seal_img = seal_img.convert('RGBA')

        # 分离通道
        r, g, b, alpha = seal_img.split()

        # 增强印章颜色 - 红色印章效果
        enhanced_seal = self.enhance_seal_color(seal_img)
        _, _, _, alpha = enhanced_seal.split()

        # 应用压力效果
        if apply_pressure:
            new_alpha = self.apply_pressure_effect(enhanced_seal, alpha)
        else:
            # 调整整体透明度
            alpha_data = np.array(alpha)
            alpha_data = (alpha_data * alpha_factor).astype(np.uint8)
            new_alpha = Image.fromarray(alpha_data)

        # 模拟边缘磨损
        if wear_level > 0:
            new_alpha = self.simulate_edge_wear(enhanced_seal, new_alpha, wear_level)

        # 获取增强后的印章的R通道
        r, _, _, _ = enhanced_seal.split()

        # 创建最终印章图像
        final_seal = Image.merge('RGBA', (r, Image.new('L', r.size, 0), Image.new('L', r.size, 0), new_alpha))

        # 将印章粘贴到图像上
        result.paste(final_seal, position, final_seal)

        return result


class QRCodeAugmentor(object):
    def __init__(self, config_file: str, retry_num: int = 5):
        """
        根据配置信息，从二维码图片根路径中递归搜索所有图片资源，jpg, png 等都行，搜索时注意跳过隐藏文件
        """
        self.config = OmegaConf.load(config_file)
        self.retry_num = retry_num
        self.logger = LOGGER
        self.qrcode_images = dict()

        # 获取二维码资源目录
        qrcode_dir = self.config.get('qrcode_dir', None)
        if not qrcode_dir or not os.path.exists(qrcode_dir):
            self.logger.warning(f"二维码目录不存在: {qrcode_dir}")
            return

        # 递归搜索所有图片
        qrcode_path = Path(qrcode_dir)
        all_qr_paths = []
        for img_pattern in ['*.png', '*.jpg', '*.jpeg']:
            all_qr_paths.extend(list(qrcode_path.rglob(img_pattern)))

        self.logger.info(f"共发现 {len(all_qr_paths)} 个候选二维码图片，正在检查和加载...")

        # 使用tqdm显示处理进度
        for img_path in tqdm(all_qr_paths, desc="处理二维码图片"):
            if img_path.name.startswith("."):
                continue
            qrcode_src = img_path.parts[-2]
            if qrcode_src not in self.qrcode_images:
                self.qrcode_images[qrcode_src] = [str(img_path)]
            else:
                self.qrcode_images[qrcode_src].append(str(img_path))

        total_num = 0
        for qrcode_src in self.qrcode_images:
            total_num += len(self.qrcode_images[qrcode_src])
        self.logger.info(f"成功加载二维码图片: {total_num}个")

    def _add_qrcode(self, image: Image.Image, rect_pts: List[Tuple[int, int]], idx: int) -> Tuple[Image.Image, bool]:
        """
        内部方法：基于索引(idx)取模选择二维码资源，对二维码应用特定的旋转处理(0°, 90°或-90°)，随后将其合成到目标图像上。

        Args:
            image: 输入图像
            rect_pts: 允许放置二维码的区域，格式为4个点组成的多边形: [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
            idx: 样本索引，用于选择二维码

        Returns:
            添加二维码后的图像和成功标志(Tuple[Image.Image, bool])
        """
        if not self.qrcode_images:
            return image, False

        # 克隆原始图像
        result = image.copy()

        # 根据idx选择二维码
        qrcode_src_keys = list(self.qrcode_images.keys())
        qrcode_src_idx = random.randint(0, len(qrcode_src_keys) - 1)
        qrcode_images = self.qrcode_images[qrcode_src_keys[qrcode_src_idx]]
        qrcode_idx = idx % len(qrcode_images)
        qrcode_path = qrcode_images[qrcode_idx]

        try:
            # 加载二维码图像
            qrcode_img = Image.open(qrcode_path)

            # 计算区域的边界框
            xs = [pt[0] for pt in rect_pts]
            ys = [pt[1] for pt in rect_pts]
            min_x, max_x = min(xs), max(xs)
            min_y, max_y = min(ys), max(ys)

            # 计算区域的宽度和高度
            rect_width = max_x - min_x
            rect_height = max_y - min_y

            # 先决定旋转角度（0, 90, -90度三种可能性）
            rotation_angles = [0, 90, -90]
            rotation_angle = rotation_angles[random.randint(0, 2)]

            # 先旋转二维码
            if rotation_angle != 0:
                rotated_qrcode = qrcode_img.rotate(rotation_angle, expand=True, resample=Image.BICUBIC)
            else:
                rotated_qrcode = qrcode_img.copy()

            # 计算旋转后的二维码大小
            rotated_width, rotated_height = rotated_qrcode.size

            # 计算缩放比例，确保最长边不超出区域
            width_ratio = rect_width / rotated_width
            height_ratio = rect_height / rotated_height

            # 取最小比例，确保两个方向都不超过
            scale_ratio = min(width_ratio, height_ratio) * random.uniform(0.5, 0.9)

            # 计算新的尺寸
            new_width = int(rotated_width * scale_ratio)
            new_height = int(rotated_height * scale_ratio)

            # 调整二维码大小
            qrcode_img = rotated_qrcode.resize((new_width, new_height), Image.LANCZOS)

            # 计算在多边形区域内可放置的位置范围
            # 考虑到二维码尺寸，调整放置范围
            max_x = max_x - qrcode_img.width
            max_y = max_y - qrcode_img.height

            # 确保最大值大于最小值
            if max_x < min_x:
                max_x = min_x
            if max_y < min_y:
                max_y = min_y

            paste_x = random.randint(min_x, max_x)
            paste_y = random.randint(min_y, max_y)

            # 判断是否有透明通道
            if qrcode_img.mode == 'RGBA':
                # 如果有透明通道，使用它作为遮罩进行粘贴
                result.paste(qrcode_img, (paste_x, paste_y), qrcode_img)
            else:
                # 如果没有透明通道，直接粘贴
                result.paste(qrcode_img, (paste_x, paste_y))
            return result, True

        except Exception as e:
            self.logger.warning(f"_add_qrcode内部处理失败: {e}")
            return image, False

    def add_qrcode(self, image: Image.Image, rect_pts: List[Tuple[int, int]], idx: int) -> Tuple[Image.Image, bool]:
        """
        基于索引(idx)取模选择二维码资源，对二维码应用特定的旋转处理(0°, 90°或-90°)，随后将其合成到目标图像上。

        关键技术要点：
        1. 空间约束：二维码的合成区域严格限制在rect_pts定义的多边形范围内，需通过适当缩放确保二维码完全落在目标区域
        2. 位置随机性：在约束区域内随机确定二维码放置位置，增强数据多样性
        3. 通道处理：根据二维码图像格式进行不同处理：
           - RGB格式：直接进行图像合成
           - 含透明通道(RGBA)：确保透明区域保留底图内容，实现半透明效果和边缘平滑过渡

        Args:
            image: 输入图像
            rect_pts: 允许放置二维码的区域，格式为4个点组成的多边形: [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
            idx: 样本索引，用于选择二维码

        Returns:
            Tuple[Image.Image, bool]: 添加二维码后的图像和成功标志
        """
        try:
            # 首次尝试添加二维码
            result, success = self._add_qrcode(image, rect_pts, idx)
            if success:
                return result, True
        except Exception as e:
            self.logger.warning(f"添加二维码首次尝试失败: {e}")
            traceback.print_exc()

        # 如果首次尝试失败，进行重试
        for retry in range(self.retry_num):
            try:
                # 随机选择新的二维码索引
                new_idx = random.randint(0, len(self.qrcode_images) - 1)
                result, success = self._add_qrcode(image, rect_pts, new_idx)
                if success:
                    self.logger.info(f"添加二维码重试成功，使用了第{retry + 1}次重试")
                    return result, True
            except Exception as e:
                self.logger.warning(f"添加二维码重试 {retry + 1}/{self.retry_num} 失败: {e}")
                continue

        # 所有尝试都失败，返回原始图像
        self.logger.warning(f"添加二维码在 {self.retry_num} 次重试后彻底失败")
        return image, False


class ShadowDegradeAugmentor(object):
    def __init__(self, config_file: str, retry_num: int = 5):
        """
        根据配置信息，从阴影材质图片根路径中递归搜索所有图片资源，搜索时注意跳过隐藏文件
        """
        self.config = OmegaConf.load(config_file)
        self.retry_num = retry_num
        self.logger = LOGGER
        self.shadow_images = dict()

        # 获取阴影材质资源目录
        shadow_dir = self.config.get('shadow_dir', None)
        if not shadow_dir or not os.path.exists(shadow_dir):
            self.logger.warning(f"阴影材质目录不存在: {shadow_dir}")
            return

        # 使用image_utils.py中的get_all_image_path函数递归搜索所有图片
        all_shadow_paths = get_all_image_path(shadow_dir, path_op=Path, recursive=True)

        self.logger.info(f"共发现 {len(all_shadow_paths)} 个候选阴影材质图片，正在加载...")

        # 使用tqdm显示处理进度
        shadow_images = []
        for img_path in tqdm(all_shadow_paths, desc="处理阴影材质图片"):
            try:
                # 确认图片可以正常打开
                with Image.open(img_path) as img:
                    _ = img
                shadow_images.append(img_path)
            except Exception as e:
                self.logger.warning(f"加载阴影材质图片失败: {img_path}, 错误: {e}")

        for img_path in shadow_images:
            shadow_src = img_path.parts[-2]
            if shadow_src not in self.shadow_images:
                self.shadow_images[shadow_src] = [str(img_path)]
            else:
                self.shadow_images[shadow_src].append(str(img_path))
        self.logger.info(f"发现{len(self.shadow_images)}种类型的阴影材质，成功加载阴影材质图片: {len(shadow_images)}个")

    def _add_shadow_degrade(self, image: Image.Image, idx: int) -> Tuple[Image.Image, bool]:
        """
        内部方法：基于索引(idx)取模选择阴影材质资源，应用阴影和降质处理

        Args:
            image: 输入图像
            idx: 样本索引，用于选择阴影材质

        Returns:
            应用阴影和降质后的图像和成功标志(Tuple[Image.Image, bool])
        """
        if not self.shadow_images:
            return image, False

        # 克隆原始图像
        result = image.copy()

        # 根据idx选择阴影材质
        shadow_src_keys = list(self.shadow_images.keys())
        shadow_src_idx = random.randint(0, len(shadow_src_keys) - 1)
        shadow_images = self.shadow_images[shadow_src_keys[shadow_src_idx]]
        shadow_idx = idx % len(shadow_images)
        shadow_path = shadow_images[shadow_idx]

        try:
            # 获取配置参数
            enable_degrade = self.config.get('enable_degrade', True)
            enable_shadow = self.config.get('enable_shadow', True)

            # 直接将PIL图像传递给blend_gt_and_shadow函数
            processed_img = blend_gt_and_shadow(
                gt_path=image,
                shadow_path=shadow_path,
                enable_degrade=enable_degrade,
                enable_shadow=enable_shadow
            )

            return processed_img, True
        except Exception as e:
            self.logger.warning(f"应用阴影和降质处理时出错: {e}")
            traceback.print_exc()  # 打印完整的堆栈跟踪
            return image, False

    def add_shadow_degrade(self, image: Image.Image, idx: int) -> Tuple[Image.Image, bool]:
        """
        基于索引(idx)选择阴影材质资源，应用阴影和降质处理

        Args:
            image: 输入图像
            idx: 样本索引，用于选择阴影材质

        Returns:
            Tuple[Image.Image, bool]: 应用阴影和降质后的图像和成功标志
        """
        for _ in range(self.retry_num):
            try:
                return self._add_shadow_degrade(image, idx)
            except Exception as e:
                self.logger.warning(f"添加阴影和降质处理失败，正在重试: {e}")
                continue

        self.logger.warning(f"添加阴影和降质处理失败，已达到最大重试次数: {self.retry_num}")
        return image, False
