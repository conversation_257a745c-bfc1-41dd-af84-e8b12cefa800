#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/04/10 15:26
# <AUTHOR> <EMAIL>
# @FileName: utils.py

import cv2
import PIL
import math
import random
import numpy as np
from typing import List
from PIL import Image, ImageDraw, ImageFont


def sorted_boxes(dt_boxes):
    """将文本框按照从上到下、从左到右的顺序排序"""
    num_boxes = dt_boxes.shape[0]
    sorted_boxes = sorted(dt_boxes, key=lambda x: (x[0][1], x[0][0]))
    _boxes = list(sorted_boxes)
    
    for i in range(num_boxes - 1):
        for j in range(i, -1, -1):
            if abs(_boxes[j + 1][0][1] - _boxes[j][0][1]) < 10 and (
                _boxes[j + 1][0][0] < _boxes[j][0][0]
            ):
                tmp = _boxes[j]
                _boxes[j] = _boxes[j + 1]
                _boxes[j + 1] = tmp
            else:
                break
    return _boxes


def get_minarea_rect_crop(img, points):
    """获取最小外接矩形的裁剪图像"""
    bounding_box = cv2.minAreaRect(np.array(points).astype(np.int32))
    points = sorted(list(cv2.boxPoints(bounding_box)), key=lambda x: x[0])
    
    # 重新排列四个顶点的顺序
    index_a, index_b, index_c, index_d = 0, 1, 2, 3
    if points[1][1] > points[0][1]:
        index_a = 0
        index_d = 1
    else:
        index_a = 1
        index_d = 0
    if points[3][1] > points[2][1]:
        index_b = 2
        index_c = 3
    else:
        index_b = 3
        index_c = 2
    
    box = [points[index_a], points[index_b], points[index_c], points[index_d]]
    crop_img = get_rotate_crop_image(img, np.array(box))
    return crop_img


def get_rotate_crop_image(img, points):
    """获取旋转后的裁剪图像"""
    assert len(points) == 4, "文本框顶点数量必须为4"
    img_crop_width = int(
        max(
            np.linalg.norm(points[0] - points[1]),
            np.linalg.norm(points[2] - points[3])
        )
    )
    img_crop_height = int(
        max(
            np.linalg.norm(points[0] - points[3]),
            np.linalg.norm(points[1] - points[2])
        )
    )
    pts_std = np.float32([
        [0, 0],
        [img_crop_width, 0],
        [img_crop_width, img_crop_height],
        [0, img_crop_height],
    ])
    M = cv2.getPerspectiveTransform(points, pts_std)
    dst_img = cv2.warpPerspective(
        img,
        M,
        (img_crop_width, img_crop_height),
        borderMode=cv2.BORDER_REPLICATE,
        flags=cv2.INTER_CUBIC,
    )
    dst_img_height, dst_img_width = dst_img.shape[0:2]
    if dst_img_height * 1.0 / dst_img_width >= 1.5:
        dst_img = np.rot90(dst_img)
    return dst_img


def draw_text_on_image(img, text, position, text_color=(0, 0, 255), text_size=20):
    """使用PIL绘制中文文本"""
    try:
        img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(img_pil)
        
        # 尝试加载系统默认字体
        try:
            # macOS默认中文字体
            fontStyle = ImageFont.truetype(
                "/System/Library/Fonts/STHeiti Light.ttc",
                text_size,
                encoding="utf-8"
            )
        except IOError:
            # Linux下的微软雅黑字体
            try:
                fontStyle = ImageFont.truetype(
                    "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
                    text_size,
                    encoding="utf-8"
                )
            except IOError:
                # 如果没有找到字体，则使用默认字体
                fontStyle = ImageFont.load_default()
                
        draw.text(position, text, text_color, font=fontStyle)

        return cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)

    except Exception as e:
        print(f"绘制文本失败: {e}")
        # 如果使用PIL失败，返回原始图像
        return img


def get_image_paths(input_path: str) -> List[str]:
    """获取输入路径下的所有图像文件路径"""
    import os
    
    image_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    image_paths = []
    
    if os.path.isfile(input_path):
        ext = os.path.splitext(input_path)[1].lower()
        if ext in image_formats:
            image_paths.append(input_path)
    else:
        for root, _, files in os.walk(input_path):
            for file in files:
                ext = os.path.splitext(file)[1].lower()
                if ext in image_formats:
                    image_paths.append(os.path.join(root, file))
    
    return sorted(image_paths)


def draw_ocr_box_txt_v2(
    image,
    boxes,
    txts=None,
    scores=None,
    drop_score=0.5,
    font_path="./doc/fonts/simfang.ttf",
    show_original=True,
):
    """
    在图片上绘制文字检测和识别结果
    Args:
        image(Image|numpy.ndarray): RGB图片
        boxes(list): 文本框坐标列表，格式为list[list[float]], 包含n个框，每个框用4个点表示
        txts(list): 文本字符串列表
        scores(list): 识别置信度列表
        drop_score(float): 过滤分数阈值
        font_path(str): 字体文件路径
        show_original(bool): 是否显示原始图像部分，False只返回白底标注结果
    Returns:
        numpy.ndarray: 绘制结果图片
    """
    # 确保image是PIL.Image类型
    if isinstance(image, np.ndarray):
        image = Image.fromarray(image).convert('RGB')

    h, w = image.height, image.width
    is_width_long = w >= h

    img_left = image.copy()
    img_right = Image.new('RGB', (w, h), (255, 255, 255))

    draw_left = ImageDraw.Draw(img_left)

    if txts is None or len(txts) != len(boxes):
        txts = [None] * len(boxes)

    random.seed(0)

    # 转换img_right为numpy数组进行绘制
    img_right_array = np.array(img_right)

    # 绘制左侧图片(原图+文本框)
    for idx, (box, txt) in enumerate(zip(boxes, txts)):
        if scores is not None and scores[idx] < drop_score:
            continue
        color = (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))
        draw_left.polygon(box, fill=color)

        # 绘制右侧图片(白底+文本框+文字)
        img_right_text = draw_box_txt_fine((w, h), box, txt, font_path)
        pts = np.array(box, np.int32).reshape((-1, 1, 2))
        cv2.polylines(img_right_text, [pts], True, color, 1)
        img_right_array = cv2.bitwise_and(img_right_array, img_right_text)

    if show_original:
        img_left = Image.blend(image, img_left, 0.5)
        img_right = Image.fromarray(img_right_array)

        # 根据宽高比决定显示方式
        if is_width_long:
            # 宽>=高时，上下显示
            img_show = Image.new("RGB", (w, h * 2), (255, 255, 255))
            img_show.paste(img_left, (0, 0))
            img_show.paste(img_right, (0, h))
        else:
            # 宽<高时，左右显示
            img_show = Image.new("RGB", (w * 2, h), (255, 255, 255))
            img_show.paste(img_left, (0, 0))
            img_show.paste(img_right, (w, 0))

        return np.array(img_show)
    else:
        # 如果不显示原图，只返回右侧的文本标注结果
        return np.array(Image.fromarray(img_right_array))


def draw_box_txt_fine(img_size, box, txt, font_path="./doc/fonts/simfang.ttf"):
    box_height = int(
        math.sqrt((box[0][0] - box[3][0]) ** 2 + (box[0][1] - box[3][1]) ** 2)
    )
    box_width = int(
        math.sqrt((box[0][0] - box[1][0]) ** 2 + (box[0][1] - box[1][1]) ** 2)
    )

    # 判断是否使用SourceHanSerif-Regular.ttc字体
    is_source_han = "SourceHanSerif-Regular.ttc" in font_path
    is_huawen_han = "华文仿宋.ttf" in font_path
    
    if box_height > 2 * box_width and box_height > 30:
        img_text = Image.new("RGB", (box_height, box_width), (255, 255, 255))
        draw_text = ImageDraw.Draw(img_text)
        if txt:
            font = create_font(txt, (box_height, box_width), font_path)
            # 根据不同字体类型，上移指定百分比
            if is_source_han:
                y_offset = int(-0.21 * box_width)
            elif is_huawen_han:
                y_offset = int(-0.05 * box_width)
            else:
                y_offset = 0
            draw_text.text([0, y_offset], txt, fill=(0, 0, 0), font=font)
        img_text = img_text.transpose(Image.ROTATE_270)

    else:
        img_text = Image.new("RGB", (box_width, box_height), (255, 255, 255))
        draw_text = ImageDraw.Draw(img_text)
        if txt:
            font = create_font(txt, (box_width, box_height), font_path)
            # 根据不同字体类型，上移指定百分比
            if is_source_han:
                y_offset = int(-0.21 * box_height)
            elif is_huawen_han:
                y_offset = int(-0.05 * box_height)
            else:
                y_offset = 0
            draw_text.text([0, y_offset], txt, fill=(0, 0, 0), font=font)

    pts1 = np.float32(
        [[0, 0], [box_width, 0], [box_width, box_height], [0, box_height]]
    )
    pts2 = np.array(box, dtype=np.float32)
    M = cv2.getPerspectiveTransform(pts1, pts2)

    img_text = np.array(img_text, dtype=np.uint8)
    img_right_text = cv2.warpPerspective(
        img_text,
        M,
        img_size,
        flags=cv2.INTER_NEAREST,
        borderMode=cv2.BORDER_CONSTANT,
        borderValue=(255, 255, 255),
    )
    return img_right_text


def create_font(txt, sz, font_path="./doc/fonts/simfang.ttf"):
    font_size = int(sz[1] * 0.99)
    font = ImageFont.truetype(font_path, font_size, encoding="utf-8")
    if int(PIL.__version__.split(".")[0]) < 10:
        length = font.getsize(txt)[0]
    else:
        length = font.getlength(txt)

    if length > sz[0]:
        font_size = int(font_size * sz[0] / length)
        font = ImageFont.truetype(font_path, font_size, encoding="utf-8")

    return font