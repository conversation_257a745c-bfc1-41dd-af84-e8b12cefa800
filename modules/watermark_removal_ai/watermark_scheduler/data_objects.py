#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/7/15 16:20
# <AUTHOR> <EMAIL>
# @FileName: data_objects

from enum import Enum
from typing import Union
from dataclasses import dataclass

from PIL import Image


@dataclass
class WatermarkType:
    # 通用配置
    position: str = "tile"                         # tile (平铺), random (随机)
    is_position_fixed: bool = False                # 固定位水印，如右下角，左上角，居中等，只有当 position="random" 时才生效
    is_force_center_fixed: bool = False            # 当固定位水印生效时，强制居中
    content: Union[str, Image.Image] = None        # 水印内容，可以是文字字符串，也可以是PIL图像对象
    font_path: str = None                          # 字体路径
    font_size: int = None                          # 字体大小
    font_index: int = 0                            # ttc 字体索引
    opacity: float = None                          # 透明度
    color: tuple = None                            # 颜色
    is_watermark_template: bool = False            # 是否为匹配好的水印模板，可以直接调透明度合成
    is_logo_with_text: bool = False                # 是否水印加文字组合，模拟下角标水印
    is_grey: bool = False                          # 是否转换成灰度
    is_add_grid: bool = False                      # 是否添加网格线
    grid_spacing: int = 150                        # 网络间距范围, 建议150到350
    grid_line_width: int = 1                       # 网格线精细范围, 建议1到3
    angle: float = None                            # 角度
    logo_percent: float = 0.1                      # 当水印内容为LOGO图片时，占比原图的比例
    horizontal_margin: float = None                # 上下间隔
    vertical_margin: float = None                  # 左右间隔
    is_pure_doc: bool = False                      # 是否纯文档，只有文档图片才生效


class Colors(Enum):
    RED = (255, 0, 0)
    GREEN = (0, 255, 0)
    BLUE = (0, 0, 255)
    WHITE = (255, 255, 255)
    BLACK = (0, 0, 0)
    YELLOW = (255, 255, 0)
    CYAN = (0, 255, 255)
    MAGENTA = (255, 0, 255)
    ORANGE = (255, 165, 0)
    PURPLE = (128, 0, 128)
    PINK = (255, 192, 203)
    BROWN = (165, 42, 42)
    GRAY = (128, 128, 128)
    LIGHT_GRAY = (211, 211, 211)
    DARK_GRAY = (169, 169, 169)
    LIGHT_BLUE = (173, 216, 230)
    DARK_BLUE = (0, 0, 139)
    LIGHT_GREEN = (144, 238, 144)
    DARK_GREEN = (0, 100, 0)
    GOLD = (255, 215, 0)
    SILVER = (192, 192, 192)
    BRONZE = (205, 127, 50)
    BEIGE = (245, 245, 220)
    IVORY = (255, 255, 240)
    LAVENDER = (230, 230, 250)
    MAROON = (128, 0, 0)
    NAVY = (0, 0, 128)
    OLIVE = (128, 128, 0)
    TEAL = (0, 128, 128)
    VIOLET = (238, 130, 238)
    CORAL = (255, 127, 80)
    SALMON = (250, 128, 114)
    TURQUOISE = (64, 224, 208)
    MINT = (189, 252, 201)
    PEACH = (255, 218, 185)
    CHOCOLATE = (210, 105, 30)
    PLUM = (221, 160, 221)
    INDIGO = (75, 0, 130)
    LIME = (0, 255, 0)
    KHAKI = (240, 230, 140)

    @classmethod
    def get_all_colors(cls):
        color_choice = []
        for color in cls:
            color_choice.append(color)
        return color_choice
    
    @classmethod
    def get_sub_colors(cls, not_contained_colors=None):
        # 离白色比较近的背景，在选取水印颜色的时候，得避开这些色块
        if not_contained_colors is None:
            not_contained_colors = {
                cls.LIGHT_GRAY, cls.WHITE, cls.BLACK, cls.IVORY, cls.BEIGE, cls.LAVENDER
            }
        return [color for color in cls if color not in not_contained_colors]


class TextType(Enum):
    PURE_CHINESE_NO_PUNCTUATION = "中文无标点"
    PURE_ENGLISH_NO_PUNCTUATION = "英文无标点"
    MIXED_CHINESE_AND_ENGLISH = "中英文无标点"
    PURE_CHINESE_WITH_PUNCTUATION_AND_DIGITS = "中文带标点数字"
    PURE_ENGLISH_WITH_DIGITS = "英文带数字"
    PURE_ENGLISH_WITH_PUNCTUATION_AND_DIGITS = "英文带标点数字"
    MIXED_CHINESE_AND_ENGLISH_WITH_PUNCTUATION_AND_DIGITS = "中英文带标点数字"
    WEB_ADDRESS_MOCK = "网址类型"
    MUTILLINE_TEXT = "多行文本"

    @classmethod
    def get_all_text_types(cls):
        text_type_choice = []
        for text_type in cls:
            text_type_choice.append(text_type)
        return text_type_choice


def get_all_chinese_chars():
    with open("assets/watermark_removal_ai/docs/3500常用汉字.txt", "r", encoding="utf-8") as f:
        chars = [line.strip() for line in f if line.strip()]
    return ''.join(chars)


def get_all_english_chars():
    with open("assets/watermark_removal_ai/docs/eng_chars.txt", "r", encoding="utf-8") as f:
        return f.read().strip()


def get_all_punctuation():
    with open("assets/watermark_removal_ai/docs/punctuation.txt", "r", encoding="utf-8") as f:
        return f.read().strip()


def get_numerals():
    return "0123456789"
