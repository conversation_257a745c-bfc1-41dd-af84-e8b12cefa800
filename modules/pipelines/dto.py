#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/5/7 21:10
# <AUTHOR> <EMAIL>
# @FileName: dto

AVAILABLE_MODELS = {
    "V20250508-MNN（6.34MB）": {
        "det_config": "./configs/det/repsvtr_qint8_v20250421_e400.yaml",
        "cls_config": "./configs/cls/default_qint8.yaml",
        "rec_config": "./configs/rec/mb4_medium_030_qint8.yaml",
    },
    "V20250508-ONNX（23.84MB）": {
        "det_config": "./configs/det/repsvtr_v20250421_e400_onnx.yaml",
        "cls_config": "./configs/cls/default_onnx.yaml",
        "rec_config": "./configs/rec/mb4_medium_030_onnx.yaml",
    },
    # TODO: 后续新增模型在此处添加
}

def get_available_models():
    global AVAILABLE_MODELS
    return AVAILABLE_MODELS
