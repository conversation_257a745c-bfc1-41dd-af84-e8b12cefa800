#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Time: 2025-07-30
# Author: <EMAIL>
# FileName: metrics.py

"""
表格结构识别指标计算模块

包含多种表格识别指标的计算实现，主要包括：
- TEDS (Tree-Edit-Distance-based Similarity) 指标
- 相关的格式转换函数

"""

import os
import json
import glob
from typing import List, Tuple, Dict, Optional
from pathlib import Path

# 导入真实的TEDS库
from table_recognition_metric import TEDS


class TableStructureRecognitionMetrics:
    """
    表格结构识别指标计算器

    封装多种表格识别指标的计算，目前主要包含TEDS指标。
    支持单张图片指标计算和批量统计。
    """

    def __init__(self, output_dir: str):
        """
        初始化指标计算器

        Args:
            output_dir: 结果输出目录
        """
        self.output_dir = output_dir
        self.teds = TEDS()
        self.results = []  # 存储每张图片的结果

    def prediction_to_html(self, predictions: Dict) -> str:
        """
        占位符函数：将模型预测结果转换为HTML表格

        Args:
            predictions: 模型预测结果字典，包含边界框和关键点信息

        Returns:
            HTML表格字符串

        Note:
            这是一个占位符实现，实际的转换逻辑待后续实现
        """
        # TODO: 实现从预测结果到HTML的转换
        # predictions 包含了边界框坐标、关键点等信息
        # 需要根据这些信息重建表格结构并转换为HTML
        return "<html><body><table></table></body></html>"

    def find_json_annotation(self, image_path: str) -> Optional[str]:
        """
        查找对应的JSON标注文件

        Args:
            image_path: 图片文件路径

        Returns:
            JSON标注文件路径，如果不存在则返回None
        """
        base_path = os.path.splitext(image_path)[0]
        json_path = base_path + '.json'
        return json_path if os.path.exists(json_path) else None

    def calculate_single_metrics(self, image_path: str, predictions: Dict) -> Dict:
        """
        计算单张图片的指标

        Args:
            image_path: 图片文件路径
            predictions: 模型预测结果

        Returns:
            指标结果字典，包含TEDS分数、状态信息等
        """
        result = {
            'image_path': image_path,
            'image_name': os.path.basename(image_path),
            'teds_score': 0.0,
            'status': 'success',
            'error_msg': None
        }

        try:
            # 1. 查找对应的JSON标注文件
            json_path = self.find_json_annotation(image_path)
            if json_path is None:
                result['status'] = 'json_not_found'
                result['error_msg'] = 'JSON annotation file not found'
                return result

            # 2. 将JSON标注转换为HTML
            try:
                gt_html = self.logi_json_to_html(json_path)
                if not gt_html or gt_html == '<html><body><table></table></body></html>':
                    result['status'] = 'json_empty'
                    result['error_msg'] = 'Empty or invalid ground truth HTML'
                    return result
            except Exception as e:
                result['status'] = 'json_error'
                result['error_msg'] = f'JSON parsing error: {str(e)}'
                return result

            # 3. 将模型预测转换为HTML
            try:
                pred_html = self.prediction_to_html(predictions)
                if not pred_html or pred_html == '<html><body><table></table></body></html>':
                    result['status'] = 'prediction_empty'
                    result['error_msg'] = 'Empty or invalid prediction HTML'
                    return result
            except Exception as e:
                result['status'] = 'prediction_error'
                result['error_msg'] = f'Prediction conversion error: {str(e)}'
                return result

            # 4. 计算TEDS分数
            try:
                teds_score = self.teds(gt_html, pred_html)
                # 确保分数在合理范围内
                if teds_score < 0:
                    teds_score = max(0.0, teds_score)
                result['teds_score'] = teds_score
            except Exception as e:
                result['status'] = 'teds_error'
                result['error_msg'] = f'TEDS calculation error: {str(e)}'
                return result

        except Exception as e:
            result['status'] = 'unknown_error'
            result['error_msg'] = f'Unknown error: {str(e)}'

        return result

    def save_final_metrics(self, metric_results: List[Dict]):
        """
        保存最终的指标统计结果到txt文件

        Args:
            metric_results: 所有图片的指标结果列表
        """
        if not metric_results:
            return

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 统计结果
        total_images = len(metric_results)
        successful_evaluations = [r for r in metric_results if r['status'] == 'success']
        failed_evaluations = [r for r in metric_results if r['status'] != 'success']

        # 计算平均TEDS分数
        if successful_evaluations:
            avg_teds = sum(r['teds_score'] for r in successful_evaluations) / len(successful_evaluations)
            min_teds = min(r['teds_score'] for r in successful_evaluations)
            max_teds = max(r['teds_score'] for r in successful_evaluations)
        else:
            avg_teds = min_teds = max_teds = 0.0

        # 写入结果文件
        results_file = os.path.join(self.output_dir, 'metrics_results.txt')
        with open(results_file, 'w', encoding='utf-8') as f:
            f.write("=== Table Structure Recognition Metrics ===\n\n")

            # 写入每张图片的结果
            for result in metric_results:
                if result['status'] == 'success':
                    f.write(f"Image: {result['image_name']}, TEDS: {result['teds_score']:.4f}\n")
                else:
                    f.write(f"Image: {result['image_name']}, {result['error_msg']}, skipped\n")

            # 写入汇总统计
            f.write(f"\n=== Summary ===\n")
            f.write(f"Total images: {total_images}\n")
            f.write(f"Valid evaluations: {len(successful_evaluations)}\n")
            f.write(f"Failed evaluations: {len(failed_evaluations)}\n")

            if successful_evaluations:
                f.write(f"Average TEDS: {avg_teds:.4f}\n")
                f.write(f"Min TEDS: {min_teds:.4f}\n")
                f.write(f"Max TEDS: {max_teds:.4f}\n")
            else:
                f.write("Average TEDS: N/A (no successful evaluations)\n")

            # 写入失败的文件列表
            if failed_evaluations:
                f.write(f"\nFailed files:\n")
                for result in failed_evaluations:
                    f.write(f"  {result['image_name']}: {result['error_msg']}\n")

        print(f"Metrics results saved to: {results_file}")

    def logi_json_to_html(self, json_file_path: str, debug: bool = False) -> str:
        """将JSON标注文件转换为HTML格式"""
        if debug:
            print(f"[DEBUG] Converting JSON file: {json_file_path}")

        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            print(f"[ERROR] Failed to read JSON file {json_file_path}: {e}")
            return '<table></table>'

        cells = data.get('cells', [])
        if not cells:
            if debug:
                print(f"[DEBUG] No cells found in JSON file")
            return '<html><body><table></table></body></html>'

        if debug:
            print(f"[DEBUG] Found {len(cells)} cells in JSON")

        # 构建表格结构：找出最大行列数
        max_row = 0
        max_col = 0
        invalid_cells = 0

        for i, cell in enumerate(cells):
            lloc = cell.get('lloc', {})
            if not lloc:
                if debug:
                    print(f"[DEBUG] Cell {i} has no lloc information")
                invalid_cells += 1
                continue

            start_row = lloc.get('start_row', 0)
            end_row = lloc.get('end_row', 0)
            start_col = lloc.get('start_col', 0)
            end_col = lloc.get('end_col', 0)

            # 验证坐标有效性
            if (start_row < 0 or end_row < 0 or start_col < 0 or end_col < 0 or
                start_row > end_row or start_col > end_col):
                if debug:
                    print(f"[DEBUG] Cell {i} has invalid coordinates: start_row={start_row}, end_row={end_row}, start_col={start_col}, end_col={end_col}")
                invalid_cells += 1
                continue

            max_row = max(max_row, end_row)
            max_col = max(max_col, end_col)

        if debug:
            print(f"[DEBUG] Table dimensions: {max_row + 1} rows x {max_col + 1} cols")
            if invalid_cells > 0:
                print(f"[DEBUG] Found {invalid_cells} invalid cells")

        # 创建表格矩阵，初始化为None
        table_matrix = [[None for _ in range(max_col + 1)] for _ in range(max_row + 1)]

        # 填充单元格信息
        for cell in cells:
            lloc = cell.get('lloc', {})
            start_row = lloc.get('start_row', 0)
            end_row = lloc.get('end_row', 0)
            start_col = lloc.get('start_col', 0)
            end_col = lloc.get('end_col', 0)

            # 计算跨行跨列
            rowspan = end_row - start_row + 1
            colspan = end_col - start_col + 1

            # 创建单元格信息
            cell_info = {
                'text': '',
                'rowspan': rowspan if rowspan > 1 else None,
                'colspan': colspan if colspan > 1 else None
            }

            # 在起始位置放置单元格信息
            table_matrix[start_row][start_col] = cell_info

            # 在跨行跨列的其他位置标记为占位
            for r in range(start_row, end_row + 1):
                for c in range(start_col, end_col + 1):
                    if r != start_row or c != start_col:
                        table_matrix[r][c] = 'OCCUPIED'

        # 生成HTML
        html_parts = ['<html><body><table>']

        for row in table_matrix:
            html_parts.append('<tr>')
            for cell in row:
                if cell is None:
                    html_parts.append('<td></td>')
                elif cell == 'OCCUPIED':
                    continue
                else:
                    attrs = []
                    if cell['rowspan']:
                        attrs.append(f'rowspan="{cell["rowspan"]}"')
                    if cell['colspan']:
                        attrs.append(f'colspan="{cell["colspan"]}"')

                    attr_str = ' ' + ' '.join(attrs) if attrs else ''
                    html_parts.append(f'<td{attr_str}>{cell["text"]}</td>')

            html_parts.append('</tr>')

        html_parts.append('</table></body></html>')

        html_result = ''.join(html_parts)

        if debug:
            print(f"[DEBUG] Generated HTML length: {len(html_result)}")
            print(f"[DEBUG] HTML preview: {html_result[:200]}...")

        return html_result

    def logi_txt_to_html(self, txt_file_path: str, debug: bool = False) -> str:
        """将预测txt文件转换为HTML格式，使用表格矩阵方法确保单元格数量一致"""
        if debug:
            print(f"[DEBUG] Converting prediction file: {txt_file_path}")

        try:
            with open(txt_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        except Exception as e:
            print(f"[ERROR] Failed to read prediction file {txt_file_path}: {e}")
            return '<html><body><table></table></body></html>'

        # 解析TXT文件中的所有单元格
        txt_cells = []
        for line in lines:
            line = line.strip()
            if not line:
                continue

            try:
                parts = line.split(',')
                if len(parts) >= 4:
                    start_row = int(parts[0])
                    end_row = int(parts[1])
                    start_col = int(parts[2])
                    end_col = int(parts[3])

                    # 验证坐标有效性
                    if (start_row >= 0 and end_row >= 0 and start_col >= 0 and end_col >= 0 and
                        start_row <= end_row and start_col <= end_col):
                        txt_cells.append({
                            'start_row': start_row,
                            'end_row': end_row,
                            'start_col': start_col,
                            'end_col': end_col
                        })
            except ValueError:
                continue

        if debug:
            print(f"[DEBUG] Parsed {len(txt_cells)} valid cells from TXT file")

        if not txt_cells:
            return '<html><body><table></table></body></html>'

        # 去除重复的单元格定义
        unique_cells = []
        seen_cells = set()
        duplicates_removed = 0

        for cell in txt_cells:
            # 创建单元格的唯一标识
            cell_key = (cell['start_row'], cell['end_row'], cell['start_col'], cell['end_col'])

            if cell_key not in seen_cells:
                seen_cells.add(cell_key)
                unique_cells.append(cell)
            else:
                duplicates_removed += 1

        if debug:
            print(f"[DEBUG] Removed {duplicates_removed} duplicate cells")
            print(f"[DEBUG] Unique cells: {len(unique_cells)}")

        # 使用去重后的单元格列表
        txt_cells = unique_cells

        # 应用通用转置算法：将TXT坐标转置为正确的逻辑结构
        if debug:
            print(f"[DEBUG] Applying universal transpose algorithm to {len(txt_cells)} cells")

        # 分析原始坐标范围
        original_max_row = max(cell['end_row'] for cell in txt_cells)
        original_max_col = max(cell['end_col'] for cell in txt_cells)

        if debug:
            print(f"[DEBUG] Original dimensions: {original_max_row + 1} rows x {original_max_col + 1} cols")

        # 应用坐标转置：(start_row, end_row, start_col, end_col) -> (start_col, end_col, start_row, end_row)
        transposed_cells = []

        for cell in txt_cells:
            # 转置坐标
            new_start_row = cell['start_col']
            new_end_row = cell['end_col']
            new_start_col = cell['start_row']
            new_end_col = cell['end_row']

            # 验证转置后的坐标有效性
            if (new_start_row >= 0 and new_end_row >= 0 and
                new_start_col >= 0 and new_end_col >= 0 and
                new_start_row <= new_end_row and new_start_col <= new_end_col):

                transposed_cells.append({
                    'start_row': new_start_row,
                    'end_row': new_end_row,
                    'start_col': new_start_col,
                    'end_col': new_end_col,
                    'text': ''
                })

        if debug:
            transposed_max_row = max(cell['end_row'] for cell in transposed_cells) if transposed_cells else 0
            transposed_max_col = max(cell['end_col'] for cell in transposed_cells) if transposed_cells else 0
            print(f"[DEBUG] Transposed dimensions: {transposed_max_row + 1} rows x {transposed_max_col + 1} cols")
            print(f"[DEBUG] Successfully transposed {len(transposed_cells)}/{len(txt_cells)} cells")

        # 使用表格矩阵方法转换为HTML（与json_to_html相同的方法）
        if not transposed_cells:
            return '<html><body><table></table></body></html>'

        # 构建表格结构：找出最大行列数
        max_row = 0
        max_col = 0
        invalid_cells = 0

        for i, cell in enumerate(transposed_cells):
            start_row = cell['start_row']
            end_row = cell['end_row']
            start_col = cell['start_col']
            end_col = cell['end_col']

            # 验证坐标有效性（已经在转置时验证过，但再次确认）
            if (start_row < 0 or end_row < 0 or start_col < 0 or end_col < 0 or
                start_row > end_row or start_col > end_col):
                if debug:
                    print(f"[DEBUG] Cell {i} has invalid coordinates: start_row={start_row}, end_row={end_row}, start_col={start_col}, end_col={end_col}")
                invalid_cells += 1
                continue

            max_row = max(max_row, end_row)
            max_col = max(max_col, end_col)

        if debug:
            print(f"[DEBUG] Table dimensions: {max_row + 1} rows x {max_col + 1} cols")
            if invalid_cells > 0:
                print(f"[DEBUG] Found {invalid_cells} invalid cells")

        # 创建表格矩阵，初始化为None
        table_matrix = [[None for _ in range(max_col + 1)] for _ in range(max_row + 1)]

        # 填充单元格信息
        for cell in transposed_cells:
            start_row = cell['start_row']
            end_row = cell['end_row']
            start_col = cell['start_col']
            end_col = cell['end_col']

            # 计算跨行跨列
            rowspan = end_row - start_row + 1
            colspan = end_col - start_col + 1

            # 创建单元格信息
            cell_info = {
                'text': cell.get('text', ''),
                'rowspan': rowspan if rowspan > 1 else None,
                'colspan': colspan if colspan > 1 else None
            }

            # 在起始位置放置单元格信息
            table_matrix[start_row][start_col] = cell_info

            # 在跨行跨列的其他位置标记为占位
            for r in range(start_row, end_row + 1):
                for c in range(start_col, end_col + 1):
                    if r != start_row or c != start_col:
                        table_matrix[r][c] = 'OCCUPIED'

        # 生成HTML - 使用表格矩阵方法但不生成多余的空单元格
        html_parts = ['<html><body><table>']

        for row in table_matrix:
            html_parts.append('<tr>')
            for cell in row:
                if cell is None:
                    # 跳过空位置，不生成<td></td>
                    continue
                elif cell == 'OCCUPIED':
                    # 跳过被占用的位置
                    continue
                else:
                    # 生成实际的单元格
                    attrs = []
                    if cell['rowspan']:
                        attrs.append(f'rowspan="{cell["rowspan"]}"')
                    if cell['colspan']:
                        attrs.append(f'colspan="{cell["colspan"]}"')

                    attr_str = ' ' + ' '.join(attrs) if attrs else ''
                    html_parts.append(f'<td{attr_str}>{cell["text"]}</td>')

            html_parts.append('</tr>')

        html_parts.append('</table></body></html>')

        html_result = ''.join(html_parts)

        if debug:
            print(f"[DEBUG] Generated HTML length: {len(html_result)}")
            print(f"[DEBUG] HTML preview: {html_result[:200]}...")
            print(f"[DEBUG] Input cells: {len(txt_cells)}, Output cells in HTML: {html_result.count('<td')}")

        return html_result

    def evaluate_single_pair(self, gt_file: str, pred_file: str, debug: bool = False) -> float:
        """
        评估单个文件对的TEDS分数

        Args:
            gt_file: 原始标注文件路径
            pred_file: 预测标注文件路径
            debug: 是否打印调试信息

        Returns:
            TEDS分数
        """
        try:
            # 转换为HTML格式
            gt_html = self.logi_json_to_html(gt_file)
            pred_html = self.logi_txt_to_html(pred_file)
            if debug:
                print(gt_html)
                print(pred_html)

            # 验证HTML是否有效
            if not gt_html or gt_html == '<html><body><table></table></body></html>':
                print(f"Warning: Empty or invalid ground truth HTML for {gt_file}")
                return 0.0

            if not pred_html or pred_html == '<html><body><table></table></body></html>':
                print(f"Warning: Empty or invalid prediction HTML for {pred_file}")
                return 0.0

            if debug:
                print(f"GT HTML length: {len(gt_html)}")
                print(f"Pred HTML length: {len(pred_html)}")
                print(f"GT HTML preview: {gt_html[:200]}...")
                print(f"Pred HTML preview: {pred_html[:200]}...")

            # 计算TEDS分数
            score = self.teds(gt_html, pred_html)

            # 验证分数的有效性
            if score < 0:
                print(f"Warning: Negative TEDS score {score} for {os.path.basename(gt_file)}")
                print(f"  GT file: {gt_file}")
                print(f"  Pred file: {pred_file}")
                if debug:
                    print(f"  GT HTML: {gt_html}")
                    print(f"  Pred HTML: {pred_html}")
                # 将负数分数设为0，或者保留原值用于调试
                return max(0.0, score)  # 可以改为 return score 来保留负数用于调试

            return score

        except Exception as e:
            print(f"Error processing {gt_file} and {pred_file}: {e}")
            import traceback
            traceback.print_exc()
            return 0.0

    def find_matching_files(self, gt_dir: str, pred_dir: str) -> List[Tuple[str, str]]:
        """
        在两个目录中找到匹配的文件对

        Args:
            gt_dir: 原始标注目录（JSON文件）
            pred_dir: 预测标注目录（TXT文件）

        Returns:
            匹配的文件对列表 [(gt_file, pred_file), ...]
        """
        matching_pairs = []

        # 获取所有JSON文件
        json_files = glob.glob(os.path.join(gt_dir, "*.json"))

        for json_file in json_files:
            # 从JSON文件名提取基础名称
            base_name = os.path.basename(json_file)

            # 尝试不同的匹配模式
            possible_pred_names = []

            # 模式1: 直接替换扩展名
            possible_pred_names.append(base_name.replace('.json', '.txt'))

            # 模式2: 移除_table_annotation.json后缀，添加.txt
            if base_name.endswith('_table_annotation.json'):
                image_name = base_name.replace('_table_annotation.json', '')
                possible_pred_names.extend([
                    f"{image_name}.txt",
                    f"{image_name}.jpg.txt",
                    f"{image_name}.png.txt"
                ])

            # 查找匹配的预测文件
            for pred_name in possible_pred_names:
                pred_file = os.path.join(pred_dir, pred_name)
                if os.path.exists(pred_file):
                    matching_pairs.append((json_file, pred_file))
                    break

        return matching_pairs