#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Time: 2025-07-30
# Author: xela<PERSON><EMAIL>
# FileName: metrics.py

"""
表格结构识别指标计算模块

包含多种表格识别指标的计算实现，主要包括：
- TEDS (Tree-Edit-Distance-based Similarity) 指标
- 相关的格式转换函数

"""

import os
import json
import glob
import numpy as np
from typing import List, Tuple, Dict, Optional
from pathlib import Path

# 导入真实的TEDS库
from table_recognition_metric import TEDS


class TableStructureRecognitionMetrics:
    """
    表格结构识别指标计算器

    封装多种表格识别指标的计算，目前主要包含TEDS指标。
    支持单张图片指标计算和批量统计。
    """

    def __init__(self, output_dir: str):
        """
        初始化指标计算器

        Args:
            output_dir: 结果输出目录
        """
        self.output_dir = output_dir
        self.teds = TEDS()

    def find_json_annotation(self, image_path: str) -> Optional[str]:
        """
        查找对应的JSON标注文件

        Args:
            image_path: 图片文件路径

        Returns:
            JSON标注文件路径，如果不存在则返回None
        """
        base_path = os.path.splitext(image_path)[0]
        json_path = base_path + '.json'
        return json_path if os.path.exists(json_path) else None

    def prediction_to_html(self, predictions: Dict, tolerance_factor: float = 0.1,
                          min_tolerance: float = 2.0, max_tolerance: float = 10.0,
                          boundary_strategy: str = "median") -> str:
        """
        将模型预测结果转换为HTML表格

        Args:
            predictions: 模型预测结果字典，包含polygons等信息
            tolerance_factor: 容差因子，用于计算自适应容差
            min_tolerance: 最小容差值
            max_tolerance: 最大容差值
            boundary_strategy: 边界策略 ("median", "mean", "min", "max")

        Returns:
            HTML表格字符串
        """
        # 1. 从predictions中提取polygons
        polygons = predictions.get('polygons', [])

        # 检查polygons是否为空或格式不正确
        if polygons is None or len(polygons) == 0:
            return '<html><body><table></table></body></html>'

        # 确保polygons是numpy数组或列表
        try:
            if isinstance(polygons, np.ndarray):
                polygons_list = polygons.tolist()
            else:
                polygons_list = polygons
        except Exception:
            return '<html><body><table></table></body></html>'

        # 2. 解析多边形坐标并构建单元格信息
        cells = []
        for cell_id, polygon in enumerate(polygons_list):
            try:
                # 验证多边形格式：应该是8个坐标值 [x1, y1, x2, y2, x3, y3, x4, y4]
                if len(polygon) != 8:
                    continue

                # 提取四个角点
                points = []
                for i in range(0, 8, 2):
                    x, y = float(polygon[i]), float(polygon[i + 1])
                    points.append((x, y))

                # 计算边界框
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]

                left = min(x_coords)
                right = max(x_coords)
                top = min(y_coords)
                bottom = max(y_coords)

                cell = {
                    'id': cell_id,
                    'left': left,
                    'right': right,
                    'top': top,
                    'bottom': bottom,
                    'center_x': (left + right) / 2,
                    'center_y': (top + bottom) / 2,
                    'width': right - left,
                    'height': bottom - top
                }

                cells.append(cell)

            except (ValueError, IndexError, TypeError) as e:
                # 跳过格式不正确的多边形
                continue

        if not cells:
            return '<html><body><table></table></body></html>'

        # 3. 计算自适应容差（与原始实现相同）
        x_coords = []
        y_coords = []
        for cell in cells:
            x_coords.extend([cell['left'], cell['right']])
            y_coords.extend([cell['top'], cell['bottom']])

        x_coords = sorted(set(x_coords))
        y_coords = sorted(set(y_coords))

        min_x_gap = min([x_coords[i+1] - x_coords[i] for i in range(len(x_coords)-1)]) if len(x_coords) > 1 else 10
        min_y_gap = min([y_coords[i+1] - y_coords[i] for i in range(len(y_coords)-1)]) if len(y_coords) > 1 else 10

        tolerance = max(min_tolerance, min(max_tolerance, min(min_x_gap, min_y_gap) * tolerance_factor))

        # 4. 改进的边界检测（使用聚类算法）
        def merge_boundaries(coords, tol):
            if not coords:
                return []

            sorted_coords = sorted(set(coords))
            clusters = []
            current_cluster = [sorted_coords[0]]

            for coord in sorted_coords[1:]:
                if coord - current_cluster[-1] <= tol:
                    current_cluster.append(coord)
                else:
                    clusters.append(current_cluster)
                    current_cluster = [coord]

            clusters.append(current_cluster)

            # 使用指定策略作为代表值
            boundaries = []
            for cluster in clusters:
                if len(cluster) == 1:
                    boundaries.append(cluster[0])
                else:
                    if boundary_strategy == "median":
                        boundaries.append(cluster[len(cluster) // 2])
                    elif boundary_strategy == "mean":
                        boundaries.append(sum(cluster) / len(cluster))
                    elif boundary_strategy == "min":
                        boundaries.append(min(cluster))
                    elif boundary_strategy == "max":
                        boundaries.append(max(cluster))
                    else:  # 默认使用中位数
                        boundaries.append(cluster[len(cluster) // 2])

            return boundaries

        row_coords = [coord for cell in cells for coord in [cell['top'], cell['bottom']]]
        col_coords = [coord for cell in cells for coord in [cell['left'], cell['right']]]

        row_boundaries = merge_boundaries(row_coords, tolerance)
        col_boundaries = merge_boundaries(col_coords, tolerance)

        # 5. 构建逻辑网格（改进版本，避免冲突）
        rows = len(row_boundaries) - 1
        cols = len(col_boundaries) - 1

        if rows <= 0 or cols <= 0:
            return '<html><body><table></table></body></html>'

        matrix = [[None for _ in range(cols)] for _ in range(rows)]
        cell_positions = {}

        def find_boundary_index(coord, boundaries, tol):
            for i, boundary in enumerate(boundaries):
                if abs(coord - boundary) <= tol:
                    return i
            distances = [abs(coord - boundary) for boundary in boundaries]
            return distances.index(min(distances))

        # 填充网格（避免冲突）
        for cell in cells:
            start_row = find_boundary_index(cell['top'], row_boundaries, tolerance)
            end_row = find_boundary_index(cell['bottom'], row_boundaries, tolerance)
            start_col = find_boundary_index(cell['left'], col_boundaries, tolerance)
            end_col = find_boundary_index(cell['right'], col_boundaries, tolerance)

            # 边界检查和修正
            start_row = max(0, min(start_row, rows - 1))
            start_col = max(0, min(start_col, cols - 1))
            end_row = max(start_row, min(end_row - 1, rows - 1))
            end_col = max(start_col, min(end_col - 1, cols - 1))

            # 计算跨度
            row_span = end_row - start_row + 1
            col_span = end_col - start_col + 1

            # 检查冲突
            conflict = False
            for row in range(start_row, end_row + 1):
                for col in range(start_col, end_col + 1):
                    if 0 <= row < rows and 0 <= col < cols:
                        if matrix[row][col] is not None:
                            conflict = True
                            break
                if conflict:
                    break

            # 如果没有冲突，填充矩阵
            if not conflict:
                for row in range(start_row, end_row + 1):
                    for col in range(start_col, end_col + 1):
                        if 0 <= row < rows and 0 <= col < cols:
                            matrix[row][col] = cell['id']

                cell_positions[cell['id']] = {
                    'row': start_row,
                    'col': start_col,
                    'row_span': row_span,
                    'col_span': col_span,
                    'is_merged': row_span > 1 or col_span > 1
                }

        # 6. 智能HTML生成（只为必要位置生成td）
        html_parts = ['<html><body><table>']
        processed = [[False for _ in range(cols)] for _ in range(rows)]

        for row_idx in range(rows):
            html_parts.append('<tr>')

            for col_idx in range(cols):
                if processed[row_idx][col_idx]:
                    continue

                cell_id = matrix[row_idx][col_idx]

                if cell_id is None:
                    # 取消自动填充空单元格，保持预测和HTML单元格数量一致
                    # 不生成空的td标签
                    processed[row_idx][col_idx] = True
                else:
                    # 处理有内容的单元格
                    pos_info = cell_positions.get(cell_id)
                    if pos_info and pos_info['row'] == row_idx and pos_info['col'] == col_idx:
                        # 这是合并单元格的起始位置
                        row_span = pos_info['row_span']
                        col_span = pos_info['col_span']

                        # 生成td标签
                        if row_span > 1 and col_span > 1:
                            html_parts.append(f'<td rowspan="{row_span}" colspan="{col_span}"></td>')
                        elif row_span > 1:
                            html_parts.append(f'<td rowspan="{row_span}"></td>')
                        elif col_span > 1:
                            html_parts.append(f'<td colspan="{col_span}"></td>')
                        else:
                            html_parts.append('<td></td>')

                        # 标记被合并的位置
                        for r in range(row_idx, row_idx + row_span):
                            for c in range(col_idx, col_idx + col_span):
                                if r < rows and c < cols:
                                    processed[r][c] = True
                    else:
                        # 这是被合并单元格的一部分，跳过
                        processed[row_idx][col_idx] = True

            html_parts.append('</tr>')

        html_parts.append('</table></body></html>')
        return ''.join(html_parts)

    def logi_json_to_html(self, json_file_path: str, debug: bool = False) -> str:
        """将JSON标注文件转换为HTML格式"""
        if debug:
            print(f"[DEBUG] Converting JSON file: {json_file_path}")

        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            print(f"[ERROR] Failed to read JSON file {json_file_path}: {e}")
            return '<table></table>'

        cells = data.get('cells', [])
        if not cells:
            if debug:
                print(f"[DEBUG] No cells found in JSON file")
            return '<html><body><table></table></body></html>'

        if debug:
            print(f"[DEBUG] Found {len(cells)} cells in JSON")

        # 构建表格结构：找出最大行列数
        max_row = 0
        max_col = 0
        invalid_cells = 0

        for i, cell in enumerate(cells):
            lloc = cell.get('lloc', {})
            if not lloc:
                if debug:
                    print(f"[DEBUG] Cell {i} has no lloc information")
                invalid_cells += 1
                continue

            start_row = lloc.get('start_row', 0)
            end_row = lloc.get('end_row', 0)
            start_col = lloc.get('start_col', 0)
            end_col = lloc.get('end_col', 0)

            # 验证坐标有效性
            if (start_row < 0 or end_row < 0 or start_col < 0 or end_col < 0 or
                start_row > end_row or start_col > end_col):
                if debug:
                    print(f"[DEBUG] Cell {i} has invalid coordinates: start_row={start_row}, end_row={end_row}, start_col={start_col}, end_col={end_col}")
                invalid_cells += 1
                continue

            max_row = max(max_row, end_row)
            max_col = max(max_col, end_col)

        if debug:
            print(f"[DEBUG] Table dimensions: {max_row + 1} rows x {max_col + 1} cols")
            if invalid_cells > 0:
                print(f"[DEBUG] Found {invalid_cells} invalid cells")

        # 创建表格矩阵，初始化为None
        table_matrix = [[None for _ in range(max_col + 1)] for _ in range(max_row + 1)]

        # 填充单元格信息
        for cell in cells:
            lloc = cell.get('lloc', {})
            start_row = lloc.get('start_row', 0)
            end_row = lloc.get('end_row', 0)
            start_col = lloc.get('start_col', 0)
            end_col = lloc.get('end_col', 0)

            # 计算跨行跨列
            rowspan = end_row - start_row + 1
            colspan = end_col - start_col + 1

            # 创建单元格信息
            cell_info = {
                'text': '',
                'rowspan': rowspan if rowspan > 1 else None,
                'colspan': colspan if colspan > 1 else None
            }

            # 在起始位置放置单元格信息
            table_matrix[start_row][start_col] = cell_info

            # 在跨行跨列的其他位置标记为占位
            for r in range(start_row, end_row + 1):
                for c in range(start_col, end_col + 1):
                    if r != start_row or c != start_col:
                        table_matrix[r][c] = 'OCCUPIED'

        # 生成HTML
        html_parts = ['<html><body><table>']

        for row in table_matrix:
            html_parts.append('<tr>')
            for cell in row:
                if cell is None:
                    html_parts.append('<td></td>')
                elif cell == 'OCCUPIED':
                    continue
                else:
                    attrs = []
                    if cell['rowspan']:
                        attrs.append(f'rowspan="{cell["rowspan"]}"')
                    if cell['colspan']:
                        attrs.append(f'colspan="{cell["colspan"]}"')

                    attr_str = ' ' + ' '.join(attrs) if attrs else ''
                    html_parts.append(f'<td{attr_str}>{cell["text"]}</td>')

            html_parts.append('</tr>')

        html_parts.append('</table></body></html>')

        html_result = ''.join(html_parts)

        if debug:
            print(f"[DEBUG] Generated HTML length: {len(html_result)}")
            print(f"[DEBUG] HTML preview: {html_result[:200]}...")

        return html_result


    def calculate_single_metrics(self, image_path: str, predictions: Dict) -> Dict:
        """
        计算单张图片的指标

        Args:
            image_path: 图片文件路径
            predictions: 模型预测结果

        Returns:
            指标结果字典，包含TEDS分数、状态信息等
        """
        result = {
            'image_path': image_path,
            'image_name': os.path.basename(image_path),
            'teds_score': 0.0,
            'status': 'success',
            'error_msg': None
        }

        try:
            # 1. 查找对应的JSON标注文件
            json_path = self.find_json_annotation(image_path)
            if json_path is None:
                result['status'] = 'json_not_found'
                result['error_msg'] = 'JSON annotation file not found'
                return result

            # 2. 将JSON标注转换为HTML
            try:
                gt_html = self.logi_json_to_html(json_path)
                if not gt_html or gt_html == '<html><body><table></table></body></html>':
                    result['status'] = 'json_empty'
                    result['error_msg'] = 'Empty or invalid ground truth HTML'
                    return result
            except Exception as e:
                result['status'] = 'json_error'
                result['error_msg'] = f'JSON parsing error: {str(e)}'
                return result

            # 3. 将模型预测转换为HTML
            try:
                pred_html = self.prediction_to_html(predictions)
                if not pred_html or pred_html == '<html><body><table></table></body></html>':
                    result['status'] = 'prediction_empty'
                    result['error_msg'] = 'Empty or invalid prediction HTML'
                    return result
            except Exception as e:
                result['status'] = 'prediction_error'
                result['error_msg'] = f'Prediction conversion error: {str(e)}'
                return result

            # 4. 计算TEDS分数
            try:
                teds_score = self.teds(gt_html, pred_html)
                # 确保分数在合理范围内
                if teds_score < 0:
                    teds_score = max(0.0, teds_score)
                result['teds_score'] = teds_score
            except Exception as e:
                result['status'] = 'teds_error'
                result['error_msg'] = f'TEDS calculation error: {str(e)}'
                return result

        except Exception as e:
            result['status'] = 'unknown_error'
            result['error_msg'] = f'Unknown error: {str(e)}'

        return result


    def save_final_metrics(self, metric_results: List[Dict]):
        """
        保存最终的指标统计结果到txt文件

        Args:
            metric_results: 所有图片的指标结果列表
        """
        if not metric_results:
            return

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 统计结果
        total_images = len(metric_results)
        successful_evaluations = [r for r in metric_results if r['status'] == 'success']
        failed_evaluations = [r for r in metric_results if r['status'] != 'success']

        # 计算平均TEDS分数
        if successful_evaluations:
            avg_teds = sum(r['teds_score'] for r in successful_evaluations) / len(successful_evaluations)
            min_teds = min(r['teds_score'] for r in successful_evaluations)
            max_teds = max(r['teds_score'] for r in successful_evaluations)
        else:
            avg_teds = min_teds = max_teds = 0.0

        # 写入结果文件
        results_file = os.path.join(self.output_dir, 'metrics_results.txt')
        with open(results_file, 'w', encoding='utf-8') as f:
            f.write("=== Table Structure Recognition Metrics ===\n\n")

            # 写入每张图片的结果
            for result in metric_results:
                if result['status'] == 'success':
                    f.write(f"Image: {result['image_name']}, TEDS: {result['teds_score']:.4f}\n")
                else:
                    f.write(f"Image: {result['image_name']}, {result['error_msg']}, skipped\n")

            # 写入汇总统计
            f.write(f"\n=== Summary ===\n")
            f.write(f"Total images: {total_images}\n")
            f.write(f"Valid evaluations: {len(successful_evaluations)}\n")
            f.write(f"Failed evaluations: {len(failed_evaluations)}\n")

            if successful_evaluations:
                f.write(f"Average TEDS: {avg_teds:.4f}\n")
                f.write(f"Min TEDS: {min_teds:.4f}\n")
                f.write(f"Max TEDS: {max_teds:.4f}\n")
            else:
                f.write("Average TEDS: N/A (no successful evaluations)\n")

            # 写入失败的文件列表
            if failed_evaluations:
                f.write(f"\nFailed files:\n")
                for result in failed_evaluations:
                    f.write(f"  {result['image_name']}: {result['error_msg']}\n")

        print(f"Metrics results saved to: {results_file}")

