#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-08 16:00
# <AUTHOR> <EMAIL>
# @FileName: __init__.py

"""
可视化模块

该模块包含表格结构识别训练过程中的可视化功能，包括：
- table_structure_visualizer_ms.py: ModelScope版本表格结构可视化核心类
- image_utils.py: 图像处理工具函数
- table_border_visualizer.py: 表格边框可视化类
- dry_run_visualizer.py: 干运行可视化类
"""

# 导入图像工具函数
from .image_utils import (
    load_and_preprocess_image,
    draw_predictions_on_image,
    create_heatmap_visualization,
    combine_images_horizontally,
    save_grouped_images
)

# 导入可视化器类（按需导入，避免循环依赖）
try:
    from .table_structure_visualizer_ms import TableStructureVisualizerMS
    _HAS_TABLE_STRUCTURE_VISUALIZER_MS = True
except ImportError:
    _HAS_TABLE_STRUCTURE_VISUALIZER_MS = False

try:
    from .table_border_visualizer import TableBorderVisualizer
    _HAS_TABLE_BORDER_VISUALIZER = True
except ImportError:
    _HAS_TABLE_BORDER_VISUALIZER = False

try:
    from .dry_run_visualizer import DryRunVisualizer
    _HAS_DRY_RUN_VISUALIZER = True
except ImportError:
    _HAS_DRY_RUN_VISUALIZER = False

# 构建 __all__ 列表
__all__ = [
    'load_and_preprocess_image',
    'draw_predictions_on_image',
    'create_heatmap_visualization',
    'combine_images_horizontally',
    'save_grouped_images'
]

if _HAS_TABLE_STRUCTURE_VISUALIZER_MS:
    __all__.append('TableStructureVisualizerMS')

if _HAS_TABLE_BORDER_VISUALIZER:
    __all__.append('TableBorderVisualizer')

if _HAS_DRY_RUN_VISUALIZER:
    __all__.append('DryRunVisualizer')
