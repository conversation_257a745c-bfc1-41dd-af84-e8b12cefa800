#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/04/21 15:08
# <AUTHOR> <EMAIL>
# @FileName: recognizer.py

import os
import cv2
import time

import MNN
import numpy as np
from typing import Dict, List

from .base import BaseOCR, MNN_AVAILABLE
from ..utils.rec_pre_proc import build_preprocess
from ..utils.rec_post_proc import build_postprocess
from ..utils.log import get_logger

LOGGER = get_logger()


class TextRecognizer(BaseOCR):
    """文本识别器类 (支持ONNX和MNN模型)"""
    
    def __init__(self, config_path: str):
        """
        初始化识别器
        Args:
            config_path: 配置文件路径
        """
        super().__init__(config_path)
        self.init_model()
        self.log_model_loaded()
    
    def init_model(self):
        """
        初始化识别模型
        """
        if self.config is None:
            raise ValueError("配置为空，请提供有效的配置文件路径")
            
        # 获取全局配置
        global_config = self.config.get("Global", {})

        # 设置推理时batch的数目
        self.infer_batch_num = global_config.get("infer_batch", 4)

        # 模型路径
        model_path = global_config.get('model_path', '')
        if not model_path or not os.path.exists(model_path):
            raise ValueError(f"模型文件不存在: {model_path}")

        # 根据模型类型选择对应的引擎
        if self.model_type == "onnx":
            # 创建ONNX会话
            self.session = self.create_onnx_session(model_path)
            # 获取输入和输出名称
            self.input_name = self.session.get_inputs()[0].name
            self.output_names = [output.name for output in self.session.get_outputs()]
        elif self.model_type == "mnn":
            # 检查MNN是否可用
            if not MNN_AVAILABLE:
                raise ImportError("MNN库不可用，请先安装: pip install MNN")
            # 创建MNN模型
            self.mnn_net = self.create_mnn_net(model_path)
            # MNN模型的输入和输出名称
            self.input_name = "input"
            self.output_names = ["output"]
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}, 请使用'onnx'或'mnn'")
        
        # 构建前处理器
        preprocess_config = self.config.get("PreProcess", {})
        self.preprocess = build_preprocess(preprocess_config, global_config)
        
        # 构建后处理器
        postprocess_config = self.config.get("PostProcess", {})
        self.post_process = build_postprocess(postprocess_config, global_config)
    
    def _correct_confidence(self, rec_results: List[Dict]) -> List[Dict]:
        """
        对识别结果的置信度进行纠正
        Args:
            rec_results: 识别结果列表，每项包含文本, 置信度以及是否旋转的标志
        Returns:
            纠正后的识别结果列表
        """
        corrected_results = []
        for result in rec_results:
            if not result or 'text' not in result:
                # 如果格式不对或文本为空，直接添加原结果
                corrected_results.append(result)
                continue
            
            text = result.get('text', '')
            confidence = result.get('confidence', 0.0)
            char_probs = result.get('char_probs', [])
            
            # 如果字符置信度不为空且长度与文本不匹配，不进行纠正
            if char_probs and len(char_probs) != len(text):
                corrected_results.append(result)
                continue
            
            # 对于3个字符及以上的文本，进行置信度纠正
            if len(text) >= 3 and char_probs:
                # 计算平均值
                mean_conf = sum(char_probs) / len(char_probs)
                # 筛选出高于(平均值-0.3)的置信度
                filtered_confs = [conf for conf in char_probs if conf >= (mean_conf - 0.3)]
                if filtered_confs:  # 如果还有剩余置信度
                    new_avg_conf = sum(filtered_confs) / len(filtered_confs)
                    result['confidence'] = new_avg_conf
                    corrected_results.append(result)
                    continue
            elif char_probs:  # 对于短文本，直接计算平均置信度
                confidence = sum(char_probs) / len(char_probs)
                result['confidence'] = confidence
            corrected_results.append(result)
        
        return corrected_results
    
    def batch_inference(self, img_list: List[np.ndarray]) -> List[Dict]:
        """
        批量处理图像推理
        Args:
            img_list: 图像列表
        Returns:
            识别结果列表
        """
        time_preprocess = 0.
        time_inference = 0.
        time_postprocess = 0.
        batch_results = []
        
        if not img_list:
            return batch_results, (0, 0, 0)
            
        try:
            tic = time.time()
            # 计算每张图片的宽高比并创建索引映射
            aspect_ratios = []
            for idx, img in enumerate(img_list):
                h, w = img.shape[:2]
                aspect_ratio = w / h
                aspect_ratios.append((aspect_ratio, idx))
            # 按宽高比排序
            aspect_ratios.sort(key=lambda x: x[0])
            sorted_indices = [x[1] for x in aspect_ratios]
            sorted_img_list = [img_list[i] for i in sorted_indices]
            # 前处理, 预分配结果列表
            sorted_results = [None] * len(sorted_img_list)
            toc = time.time()
            time_preprocess += (toc - tic)

            # 按batch_size分批处理
            for i in range(0, len(sorted_img_list), self.infer_batch_num):
                batch_imgs = sorted_img_list[i:i + self.infer_batch_num]

                # 对当前batch进行预处理
                try:
                    tic = time.time()
                    batch_norm_imgs = self.preprocess(batch_imgs)
                    toc = time.time()
                    time_preprocess += (toc - tic)
                except Exception as e:
                    # 将当前batch中的所有图片结果设置为失败状态
                    for batch_idx in range(len(batch_imgs)):
                        sorted_results[i + batch_idx] = {
                            "text": "",
                            "confidence": 0.0,
                            "error": f"前处理失败: {str(e)}"
                        }
                    continue

                # 执行批量推理
                try:
                    tic = time.time()
                    if self.model_type == "onnx" and self.session is not None:
                        # ONNX模型推理
                        # 根据模型精度自适应准备输入数据
                        processed_input = self._prepare_input_tensor(batch_norm_imgs)
                        inputs = {self.input_name: processed_input}
                        outputs = self.session.run(self.output_names, inputs)
                        preds = outputs[0]
                        # 确保输出为FP32类型
                        preds = self._ensure_fp32_output(preds)
                    elif self.model_type == "mnn" and self.mnn_net is not None:
                        # 获取输入形状
                        input_shape = batch_norm_imgs.shape
                        # 1. 创建NCHW格式的输入tensor
                        input_var = MNN.expr.placeholder(
                            [input_shape[0], input_shape[1], input_shape[2], input_shape[3]], MNN.expr.NCHW
                        )
                        # 2. 写入数据
                        input_var.write(batch_norm_imgs.astype(np.float32))
                        # 3. 转换为NC4HW4格式，提高MNN内核计算效率
                        input_var = MNN.expr.convert(input_var, MNN.expr.NC4HW4)
                        # 4. 执行推理
                        output_var = self.mnn_net.forward(input_var)
                        # 5. 转换输出结果回NCHW格式
                        output_var = MNN.expr.convert(output_var, MNN.expr.NCHW)
                        # 6. 读取输出结果
                        preds = output_var.read()
                    else:
                        raise RuntimeError(f"模型引擎初始化失败，model_type={self.model_type}")
                    toc = time.time()
                    time_inference += (toc - tic)

                    # 后处理
                    tic = time.time()
                    rec_result = self.post_process(preds)
                    for batch_idx in range(len(rec_result)):
                        sorted_results[i + batch_idx] = rec_result[batch_idx]
                    toc = time.time()
                    time_postprocess += (toc - tic)

                except Exception as e:
                    for idx in range(len(batch_imgs)):
                        result_idx = i + idx
                        if sorted_results[result_idx] is None:
                            sorted_results[result_idx] = {
                                    "text": "",
                                    "confidence": 0.0,
                                    "error": f"推理失败: {str(e)}"
                                }
            tic = time.time()
            # 还原原始顺序
            restore_map = {idx: orig_idx for orig_idx, idx in enumerate(sorted_indices)}
            batch_results = [sorted_results[restore_map[i]] for i in range(len(img_list))]
            # 应用置信度纠正，与原版保持一致
            batch_results = self._correct_confidence(batch_results)
            toc = time.time()
            time_postprocess += (toc - tic)

            return batch_results, (time_preprocess, time_inference, time_postprocess)

        except Exception as e:
            LOGGER.error(f"批量推理发生错误: {str(e)}")
            return [], (time_preprocess, time_inference, time_postprocess)
    
    def recognize(
        self,
        img_list: List[np.ndarray],
        rotate_angles: List[str],
        rotate_scores: List[float],
        repeat_rotate: bool = True,
        global_rotate_threshold: float = 0.85,
        rotate_conf_threshold: float = 0.9,
        rec_conf_threshold: float = 0.75
    ) -> List[Dict]:
        """
        识别图像列表中的文本
        Args:
            img_list: 图像列表
            rotate_angles: 旋转角度列表
            rotate_scores: 旋转置信度列表
            repeat_rotate: 是否进行多次旋转尝试
            global_rotate_threshold: 全局旋转阈值，用于调整整体的旋转置信度
            rotate_conf_threshold: 旋转置信度阈值
            rec_conf_threshold: 识别置信度阈值
        Returns:
            识别结果列表，每项包含文本, 置信度以及是否旋转的标志
        """
        # 记录耗时
        time_track = dict()

        # 如果输入列表为空，直接返回空结果
        if not img_list:
            return [], time_track
            
        # 计算正向比例，用于全局方向判断
        positive_ori_rate = rotate_angles.count("0") / len(rotate_angles) if rotate_angles else 1.0
        if 5 <= len(img_list) < 10:
            positive_ori_rate = positive_ori_rate * 2
        if len(img_list) < 5:
            if positive_ori_rate > 0:
                positive_ori_rate = 1
            else:
                positive_ori_rate = positive_ori_rate * 3
        
        # 预分配结果列表，确保每个检测框都有结果
        rec_res = [None] * len(img_list)
        
        time_img_read = 0.
        time_preprocess = 0.
        time_inference = 0.
        time_postprocess = 0.
        
        # ===== 参考原版实现的批量处理方式 =====
        # 第一轮：统一预处理
        tic = time.time()
        infer_image_list = []
        rotate_try_list = [False] * len(img_list)
        
        for idx, (img, rotate_angle) in enumerate(zip(img_list, rotate_angles)):
            if rotate_angle == '180' and positive_ori_rate < global_rotate_threshold:
                # 根据旋转角度和全局方向比例决定是否旋转
                infer_image_list.append(cv2.rotate(img.copy(), cv2.ROTATE_180))
                rotate_try_list[idx] = True
            else:
                infer_image_list.append(img.copy())
        toc = time.time()
        time_img_read += (toc - tic)
        
        # 批量推理第一轮结果
        # time_track: time_preprocess, time_inference, time_postprocess
        first_rec_results, infer_time_track = self.batch_inference(infer_image_list)
        time_preprocess += infer_time_track[0]
        time_inference += infer_time_track[1]
        time_postprocess += infer_time_track[2]
        
        # 第二轮：对置信度不足的图像进行多角度旋转尝试
        if repeat_rotate:
            tic = time.time()
            repeat_infer_image_list = []
            repeat_infer_image_origin_idx = []
            
            # 筛选需要二次处理的图像
            for idx in range(len(first_rec_results)):
                # 保存原图的副本用于后续处理
                original_img = infer_image_list[idx]
                image_conf = first_rec_results[idx].get('confidence', 0.0)
                
                # 判断是否需要再次旋转尝试
                if rotate_scores[idx] < rotate_conf_threshold or image_conf < rec_conf_threshold:
                    # 180度旋转
                    repeat_infer_image_list.append(cv2.rotate(original_img, cv2.ROTATE_180))
                    repeat_infer_image_origin_idx.append(idx)
                    
                    # 根据长宽比决定是否添加90度旋转
                    h, w = original_img.shape[:2]
                    if w / h <= 1.5:  # 短窄图像才尝试90度旋转
                        repeat_infer_image_list.append(cv2.rotate(original_img, cv2.ROTATE_90_CLOCKWISE))
                        repeat_infer_image_origin_idx.append(idx)
                        repeat_infer_image_list.append(cv2.rotate(original_img, cv2.ROTATE_90_COUNTERCLOCKWISE))
                        repeat_infer_image_origin_idx.append(idx)

                    # 标记为已尝试旋转
                    rotate_try_list[idx] = True

            toc = time.time()
            time_img_read += (toc - tic)
            
            # 批量推理第二轮结果
            if repeat_infer_image_list:
                repeat_rec_results, infer_time_track = self.batch_inference(repeat_infer_image_list)
                time_preprocess += infer_time_track[0]
                time_inference += infer_time_track[1]
                time_postprocess += infer_time_track[2]
                
                # 处理第二轮结果并合并到最终结果
                if repeat_rec_results:
                    # 类似原版的结果合并逻辑
                    cur_idx = repeat_infer_image_origin_idx[0] if repeat_infer_image_origin_idx else -1
                    best_score = first_rec_results[cur_idx].get('confidence', 0.0) if cur_idx >= 0 else 0.0
                    best_result = first_rec_results[cur_idx] if cur_idx >= 0 else None
                    
                    for idx in range(len(repeat_infer_image_origin_idx)):
                        origin_idx = repeat_infer_image_origin_idx[idx]
                        if cur_idx == origin_idx:
                            # 同一原始图像的另一个旋转结果
                            repeat_conf = repeat_rec_results[idx].get('confidence', 0.0)
                            if repeat_conf > best_score:
                                best_score = repeat_conf
                                best_result = repeat_rec_results[idx]
                        else:
                            # 切换到新的原始图像
                            # 保存当前图像的最佳结果
                            if positive_ori_rate > global_rotate_threshold:
                                best_score -= 0.2  # 对旋转结果降低置信度

                            if first_rec_results[cur_idx].get('confidence', 0.0) >= best_score:
                                best_result = first_rec_results[cur_idx]

                            # 将最佳结果保存到对应位置
                            if best_result:
                                first_rec_results[cur_idx] = best_result

                            # 切换到新的原始图像
                            cur_idx = origin_idx
                            best_score = repeat_rec_results[idx].get('confidence', 0.0)
                            best_result = repeat_rec_results[idx]
                    
                    # 处理最后一个原始图像的结果
                    if cur_idx >= 0:
                        if positive_ori_rate > global_rotate_threshold:
                            best_score -= 0.2
                        if first_rec_results[cur_idx].get('confidence', 0.0) >= best_score:
                            best_result = first_rec_results[cur_idx]
                        if best_result:
                            first_rec_results[cur_idx] = best_result
        
        # 最终结果处理
        for idx, result in enumerate(first_rec_results):
            text = result.get('text', '')
            confidence = result.get('confidence', 0.0)
            
            # 调整短文本置信度
            if len(text) <= 2:
                confidence += 0.2
                
            # 构建最终结果
            rec_res[idx] = {
                "text": text,
                "confidence": min(1.0, confidence),
                "rotate_try": rotate_try_list[idx]
            }
        
        # 确保所有结果都是有效的字典对象，没有None值
        for idx in range(len(rec_res)):
            if rec_res[idx] is None:
                rec_res[idx] = {
                    "text": "", 
                    "confidence": 0.0, 
                    "rotate_try": False
                }
        
        # 记录耗时
        time_track["img_read"] = time_img_read
        time_track["preprocess"] = time_preprocess
        time_track[f"inference [{self.device_type}]"] = time_inference
        time_track["postprocess"] = time_postprocess
        
        return rec_res, time_track