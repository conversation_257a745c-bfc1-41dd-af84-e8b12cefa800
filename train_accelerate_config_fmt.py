#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/6/21 21:38
# <AUTHOR> <EMAIL>
# @FileName: train_accelerate_config_fmt

"""
通用环境变量列表
更新时间：2024.01.03 14:49:10
在使用【自定义训练】模块时，机器学习平台为用户注入了大量的通用环境变量，便于用户在代码中便捷地使用。

训练角色无关

变量名称	示例	变量说明
MLP_TASK_ID	t-20210924211641-nznsh	任务的 ID。
MLP_TASK_OWNER	username	创建任务的用户。
MLP_TASK_NAME	mnist	提交时的任务名称。
MLP_TASK_IMAGE	cr-cn-beijing.ivolces/ml_platform/python:2.7_3.7	训练镜像的地址。
MLP_TASK_CMD	python3 /root/code/main.py	任务的启动命令。
MLP_TASK_ARGS	--key1 value1 --key2 value2	直接拼接在启动命令后的参数列表。
MLP_ROLE	WORKER	分布式中的训练角色。取值范围是 WORKER / SERVER / SCHEDULER / CHIEF / PS / EVALUATOR。
MLP_ROLE_INDEX	0	该节点的角色编号（从0开始），如 worker0、worker1 ...。
MLP_${MLP_ROLE}_RACK_SW_ID	febd30c7302dda1fae1399fe0266930f	任务实例所处节点的交换机 hash id，仅对 HPC 的容器才会注入该值。
MLP_RACK_TOPO_FILE	/ml_platform/rack_topo	交换机信息文件在容器中的路径。
MLP_${MLP_ROLE}_RACK_RANK_INDEX	0	根据交换机 id 排序后，该实例所处位置的序号。排序保证 worker0 的 index=0。
MLP_IFNAME	eth0	默认网卡的名称。
GLOO_SOCKET_IFNAME	eth0	为 Gloo 指定的网卡名称。
MLP_HOST	************	当前节点的 IP，有 RDMA 时为 RDMA IP，否则为 VPC IP。
MLP_PRIMARY_HOST	************	当前节点的 VPC IP。
训练角色相关

变量名称	示例	变量说明
MLP_${ROLE}_NUM	2	该角色节点的数量。
MLP_${ROLE}_CPU	2	该角色节点的 CPU 数量。
MLP_${ROLE}_MEM	10240	该角色节点的 MEM 大小，单位 MB。
MLP_${ROLE}_GPU	3	该角色节点的 GPU 的数量。
MLP_${ROLE}_ALL_HOSTS	************,************,...	该角色的所有实例的主机名称。
MLP_${ROLE}_${INEDX}_HOST	************	该角色第 INEDX 个节点的主机名称。
MLP_${ROLE}_${INEDX}_PORT	9006	该角色第 INEDX 个节点的端口。
MLP_${ROLE}_ALL_PRIMARY_HOSTS	************	该角色的所有实例的 VPC IP。
MLP_${ROLE}_${INEDX}_PRIMARY_HOST	************	该角色第 INEDX 个节点的 VPC IP。
"""

import os
import yaml

worker_num_gpus = os.environ.get("MLP_WORKER_GPU")     # 节点GPU数量
world_size = os.environ.get("MLP_WORKER_NUM")          # 节点数量
master_addr = os.environ.get("MLP_WORKER_0_HOST")      # 主节点域名
master_port = os.environ.get("MLP_WORKER_0_PORT")      # 主节点端口
worker_node_rank = os.environ.get("MLP_ROLE_INDEX")    # 节点任务序列号


with open(f"hsyq_accelerate_rank{worker_node_rank}_config.yaml", 'w', encoding='utf-8') as f:
    config = dict()
    config['compute_environment'] = 'LOCAL_MACHINE'

    if int(worker_num_gpus) * int(world_size) == 1:
        config['distributed_type'] = 'NO'
    else:
        config['distributed_type'] = 'MULTI_GPU'
    config['downcast_bf16'] = 'no'

    gpu_ids = []
    for i in range(int(worker_num_gpus)):
        gpu_ids.append(str(i))
    config['gpu_ids'] = ','.join(gpu_ids)

    config['machine_rank'] = int(worker_node_rank)
    config['main_process_ip'] = master_addr
    config['main_process_port'] = int(master_port)
    config['main_training_function'] = 'main'
    config['mixed_precision'] = 'bf16'
    config['num_machines'] = int(world_size)
    config['num_processes'] = int(worker_num_gpus) * int(world_size)
    config['rdzv_backend'] = 'static'
    # config['nccl_timeout'] = 1800
    # config['gloo_timeout'] = 1800000
    config['same_network'] = False
    config['tpu_env'] = []
    config['tpu_use_cluster'] = False
    config['tpu_use_sudo'] = False
    config['use_cpu'] = False

    yaml.dump(config, f)
