#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/10/15 14:58
# <AUTHOR> <EMAIL>
# @FileName: train_birefnet

import os
import re
import math
import json
import random
import datetime
import warnings
from pathlib import Path

import torch
import torch.nn as nn
import torch.utils.data

from tqdm import tqdm
from PIL import Image
from accelerate.logging import get_logger
from diffusers.optimization import get_scheduler

from networks.birefnet import BiRefNet
from networks.birefnet.losses import PixLoss
from networks.utils.metrics.eval import AverageMeter, compute_IoU, FScore
from my_datasets.image_matting.dataset import ImageMattingDataset, collate_fn

import modules.utils.path_utils as path_utils
import modules.utils.torch_utils as torch_utils
from modules.utils.train_utils import prepare_training_enviornment
from modules.proj_cmd_args.image_matting.train_birefnet import parse_args

from modules.utils.image_utils import (
    get_all_image_path,
    normalize_mask,
    denormalize_mask,
    imagenet_normalize_image,
    imagenet_denormalize_image,
)

logger = get_logger(__name__)
torch.autograd.set_detect_anomaly(True)

# 忽略特定警告
warnings.filterwarnings("ignore", message="torch.meshgrid", category=UserWarning)
warnings.filterwarnings("ignore", message="Grad strides do not match bucket view strides", category=UserWarning)
warnings.filterwarnings("ignore", message="On January 1, 2023, MMCV will release v2.0.0", category=UserWarning, )


def walk_dataloaders(loaders):
    doing = [iter(loader) for loader in loaders]
    random.shuffle(doing)
    i = 0
    while doing:
        i = i % len(doing)
        it = doing[i]
        try:
            batch = next(it)
            yield batch
            i += 1
        except StopIteration:
            del doing[i]


def main():
    args = parse_args()
    accelerator, weight_dtype = prepare_training_enviornment(args, logger)

    # 初始化模型、checkpoint复用等
    state_dict = None
    start_steps = 0
    optimizer_ckpt = None
    lr_scheduler_ckpt = None
    best_val_record = os.path.join(args.output_dir, "best_val_record.json")

    exists_checkpoints = list(Path(args.output_dir).glob('checkpoint-*'))
    if len(exists_checkpoints) > 0:
        args.reuse_checkpoint = None

    if args.reuse_checkpoint is not None and os.path.exists(args.reuse_checkpoint):
        state_dict = torch.load(os.path.join(args.reuse_checkpoint, "model.bin"), map_location='cpu')
        logger.info(f"Reusing checkpoint from {args.reuse_checkpoint}")
    elif len(exists_checkpoints) > 0:
        checkpoint_numbers = [int(f.stem.split('-')[1]) for f in exists_checkpoints]
        resume_steps = max(checkpoint_numbers)
        resume_ckpt_dir = os.path.join(args.output_dir, f'checkpoint-{resume_steps}')
        state_dict = torch.load(os.path.join(resume_ckpt_dir, 'model.bin'), map_location='cpu')
        start_steps = resume_steps
        optimizer_ckpt = os.path.join(resume_ckpt_dir, "optimizer.bin")
        lr_scheduler_ckpt = os.path.join(resume_ckpt_dir, "scheduler.bin")
        logger.info(f"Reusing running checkpoint from {resume_ckpt_dir}")

    model = BiRefNet()
    is_out_ref = model.config.out_ref
    # 解决分布式训练BatchNorm2D引起的梯度计算异常
    model = torch_utils.convert_batchnorm_to_sync_batchnorm(model)

    if state_dict is not None:
        model.load_state_dict(state_dict, strict=False)

    model.to(accelerator.device, weight_dtype)
    model.train()

    # 初始化优化策略
    pixel_loss = PixLoss()
    gdt_loss = nn.BCELoss()
    params_to_opt = model.parameters()

    if args.use_8bit_adam:
        try:
            import bitsandbytes as bnb
        except ImportError:
            raise ImportError(
                "To use 8-bit Adam, please install the bitsandbytes library: `pip install bitsandbytes`."
            )
        optimizer_class = bnb.optim.AdamW8bit
    else:
        optimizer_class = torch.optim.AdamW

    optimizer = optimizer_class(
        params_to_opt,
        lr=args.learning_rate,
        betas=(args.adam_beta1, args.adam_beta2),
        weight_decay=args.adam_weight_decay,
        eps=args.adam_epsilon,
    )

    if optimizer_ckpt is not None:
        optimizer_state_dict = torch.load(optimizer_ckpt, map_location='cpu')
        optimizer.load_state_dict(optimizer_state_dict)

    # 准备数据集，训练步数、Epoch预估、accelerator.prepare 等调度
    num_split = accelerator.num_processes * args.gradient_accumulation_steps
    assert args.train_batch_size % num_split == 0, \
        (f"batch_size: {args.train_batch_size} needs to be divisible by "
         f"num_processes*gradient_accumulation_steps={num_split}")

    if args.seed is not None:
        seed = int(args.seed)
    else:
        seed = random.randint(1, 100000)

    # 计算每台设备的 Batch size
    train_batch_size_per_device = args.train_batch_size // num_split

    # 准备数据集
    resolutions = [int(res) for res in args.resolutions.split(',')]
    preproc_type=[res for res in args.preproc_type.split(',')]
    train_dataset = ImageMattingDataset(args.dataset_dir, mode='train', debug=args.debug,preproc_type=preproc_type)
    train_dataloader = torch.utils.data.DataLoader(
        train_dataset,
        shuffle=True,
        batch_size=train_batch_size_per_device,
        num_workers=args.dataloader_num_workers,
        collate_fn=lambda batch: collate_fn(batch, resolutions),
    )

    # 测试数据集
    test_dataset = ImageMattingDataset(args.dataset_dir, mode='test', debug=args.debug)
    test_dataloader = torch.utils.data.DataLoader(
        test_dataset,
        shuffle=False,
        batch_size=8,
        num_workers=args.dataloader_num_workers,
        collate_fn=lambda batch: collate_fn(batch, [512]),
    )

    train_dataloader_total_steps = len(train_dataloader)
    num_update_steps_per_epoch = math.ceil(train_dataloader_total_steps / args.gradient_accumulation_steps)
    max_train_steps = args.num_train_epochs * num_update_steps_per_epoch
    
    lr_scheduler = get_scheduler(
        args.lr_scheduler,
        optimizer=optimizer,
        num_training_steps=max_train_steps,
        num_cycles=args.lr_num_cycles,
        power=args.lr_power,
        num_warmup_steps=args.lr_warmup_steps * accelerator.num_processes,
    )
    if lr_scheduler_ckpt is not None:
        lr_scheduler_state_dict = torch.load(lr_scheduler_ckpt, map_location='cpu')
        lr_scheduler.load_state_dict(lr_scheduler_state_dict)

    val_iou_avg_best, val_f1_avg_best = 0., 0.
    if os.path.exists(best_val_record):
        with open(best_val_record, 'r', encoding='utf-8') as f:
            data = json.load(f)
            val_iou_avg_best = data['val_iou_avg_best']
            val_f1_avg_best = data['val_f1_avg_best']

    # accelerator.prepare
    logger.info("Prepare everything with our accelerator\n")
    model, optimizer, lr_scheduler, train_dataloader = accelerator.prepare(model, optimizer, lr_scheduler, train_dataloader)

    if accelerator.is_main_process:
        tracker_config = dict(vars(args))
        exp_date = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        accelerator.init_trackers(f"{args.tracker_project_name}-{exp_date}")# 设置保存的tensorboard的文件名字

    # 经过prepare之后实际长度会发生变化
    train_dataloader_total_steps = len(train_dataloader)
    num_update_steps_per_epoch = math.ceil(train_dataloader_total_steps / args.gradient_accumulation_steps)
    max_train_steps = args.num_train_epochs * num_update_steps_per_epoch

    if accelerator.is_main_process:
        logger.info("***** Running training *****")
        logger.info(f"  Dataset seed: {seed}, resolutions: {resolutions}")
        logger.info(f"  Starting best IoU: {val_iou_avg_best}, best F1Score: {val_f1_avg_best}")
        logger.info(f"  Num checkpoints to keep: {args.num_ckpt_to_keep}")
        logger.info(f"  Num examples = {len(train_dataset)}")
        logger.info(f"  Num epochs = {args.num_train_epochs}")
        logger.info(f"  Batch size per device = {train_batch_size_per_device}")
        logger.info(f"  Total train batch size (w. parallel, distributed & accumulation) = {args.train_batch_size}")
        logger.info(f"  Gradient Accumulation steps = {args.gradient_accumulation_steps}")
        logger.info(f"  Total optimization steps = {max_train_steps}")

    first_epoch = 0
    exists_epoch_ckpts = list(Path(args.output_dir).glob('model_epoch-*'))
    if len(exists_epoch_ckpts) > 0:
        epoch_numbers = [int(f.stem.split('-')[1]) for f in exists_epoch_ckpts]
        first_epoch = max(epoch_numbers) - 1

    global_step = start_steps
    global_update_steps = 0

    # 初始化进度条并显示当前 epoch
    progress_bar = tqdm(
        range(0, max_train_steps),
        initial=global_step,
        desc=f"Epoch {first_epoch + 1}/{args.num_train_epochs}",
        # Only show the progress bar once on each machine.
        disable=not accelerator.is_local_main_process,
    )

    for epoch in range(first_epoch, args.num_train_epochs):
        # 更新进度条描述，显示当前的epoch
        progress_bar.set_description(f"Epoch {epoch + 1}/{args.num_train_epochs}")

        for step, batch in enumerate(train_dataloader):
            # 这个的封裝方式在  collate_fn 步骤
            images = batch['images']
            masks = batch['masks']

            # 模型前馈输出
            images = images.to(accelerator.device, weight_dtype)
            masks = masks.to(accelerator.device, weight_dtype)
            preds, _ = model(images)

            if is_out_ref:
                (outs_gdt_preds, outs_gdt_masks), preds = preds
                for _idx, (_gdt_pred, _gdt_label) in enumerate(zip(outs_gdt_preds, outs_gdt_masks)):
                    _gdt_pred = nn.functional.interpolate(
                        _gdt_pred, size=_gdt_label.shape[2:], mode='bilinear', align_corners=True
                    ).sigmoid()
                    _gdt_label = _gdt_label.sigmoid()
                    if _idx == 0:
                        loss_gdt = gdt_loss(_gdt_pred, _gdt_label)
                    else:
                        loss_gdt = gdt_loss(_gdt_pred, _gdt_label) + loss_gdt

            backward_loss = pixel_loss(preds, torch.clamp(masks, 0., 1.))
            if is_out_ref:
                backward_loss = backward_loss + loss_gdt

            accelerator.backward(backward_loss)
            # 计算平均权重的目的是什么？
            avg_backward_loss = accelerator.gather(backward_loss.repeat(args.train_batch_size)).mean().item()

            global_update_steps += 1

            # 更新模型权重
            if global_update_steps % args.gradient_accumulation_steps == 0:
                if args.use_clip_grad_norm:
                    accelerator.clip_grad_norm_(params_to_opt, args.clip_grad_norm)
                optimizer.step()
                lr_scheduler.step()
                optimizer.zero_grad()
                progress_bar.update(1)
                global_step += 1

                if accelerator.is_main_process:
                    logs = {
                        "lr": lr_scheduler.get_last_lr()[0],
                        "loss": avg_backward_loss,
                    }
                    progress_bar.set_postfix(**logs)
                    accelerator.log(logs, step=global_step)# step是x坐标 log里面的每个key都是y坐标内容

                if accelerator.is_main_process and global_step % args.save_steps == 0:
                    found_best_model = False
                    if args.validation_image_dir is not None:
                        all_val_images = get_all_image_path(args.validation_image_dir, recursive=True, path_op=Path)
                        all_val_images = path_utils.sort_path_by_name(all_val_images)
                        val_metrics = log_validation(
                            args, all_val_images, model, global_step, accelerator, weight_dtype, None
                        )
                        if val_metrics is not None:
                            _, _, val_iou_avg, val_f1_avg = val_metrics
                            if val_iou_avg >= val_iou_avg_best and val_f1_avg >= val_f1_avg_best:
                                val_iou_avg_best, val_f1_avg_best = val_iou_avg, val_f1_avg
                                with open(best_val_record, 'w', encoding='utf-8') as f:
                                    data = dict()
                                    data['val_iou_avg_best'] = val_iou_avg_best
                                    data['val_f1_avg_best'] = val_f1_avg_best
                                    data['checkpoint_step'] = global_step
                                    json.dump(data, f, ensure_ascii=False, indent=4)
                                found_best_model = True
                                logger.info(f"found best model: {data}")

                    save_path = os.path.join(args.output_dir, f"checkpoint-{global_step}")
                    save_state(save_path, model, optimizer, lr_scheduler, accelerator, args.num_ckpt_to_keep)
                    if found_best_model:
                        best_save_path = os.path.join(args.output_dir, f"best_model")
                        save_state(best_save_path, model, optimizer, lr_scheduler, accelerator)

            if global_step >= max_train_steps:
                break

        # 当前Epoch训练结束，保存当前模型
        if accelerator.is_main_process:
            if args.save_every_n_epoch > 0 and (epoch + 1) % args.save_every_n_epoch == 0:
                save_path = os.path.join(args.output_dir, f"model_epoch-{epoch + 1}")
                save_state(save_path, model, optimizer, lr_scheduler, accelerator)

    # 训练结束，保存最终模型
    accelerator.wait_for_everyone()
    if accelerator.is_main_process:
        save_path = os.path.join(args.output_dir, f"model_final")
        save_state(save_path, model, optimizer, lr_scheduler, accelerator)
    accelerator.end_training()


def manage_checkpoints(output_dir, n_checkpoints_to_keep):
    checkpoints = sorted(
        [d for d in os.listdir(output_dir) if re.match(r'^checkpoint-\d+$', d)],
        key=lambda x: int(x.split('-')[1])
    )

    if len(checkpoints) > n_checkpoints_to_keep:
        checkpoints_to_delete = checkpoints[:-n_checkpoints_to_keep]
        logger.info(f"old version checkpoints to delete: {checkpoints_to_delete}")
        for checkpoint in checkpoints_to_delete:
            checkpoint_path = os.path.join(output_dir, checkpoint)
            if os.path.isdir(checkpoint_path):
                for file in os.listdir(checkpoint_path):
                    file_path = os.path.join(checkpoint_path, file)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                os.rmdir(checkpoint_path)


def save_state(save_dir, model, optimizer, scheduler, accelerator, n_checkpoints_to_keep=None):
    os.makedirs(save_dir, exist_ok=True)

    model = accelerator.unwrap_model(model)
    optimizer = accelerator.unwrap_model(optimizer)
    scheduler = accelerator.unwrap_model(scheduler)

    torch.save(model.state_dict(), os.path.join(save_dir, "model.bin"))
    torch.save(optimizer.state_dict(), os.path.join(save_dir, f"optimizer.bin"))
    torch.save(scheduler.state_dict(), os.path.join(save_dir, f"scheduler.bin"))

    # 保持最新的n个checkpoint
    if n_checkpoints_to_keep is not None:
        manage_checkpoints(os.path.dirname(save_dir), n_checkpoints_to_keep)


def log_validation(args, all_images, model, steps, accelerator, weight_dtype, test_dataloader=None):
    model = accelerator.unwrap_model(model)
    model.eval()

    if test_dataloader is not None:
        logger.info("\nRunning metrics...\n")

        # 测试集指标
        iou_meter_test = AverageMeter()
        f1_meter_test = AverageMeter()

        test_dataloader_total_steps = len(test_dataloader)
        test_pbar = tqdm(total=test_dataloader_total_steps, desc='evaluating...')
        for step, batch in enumerate(test_dataloader):
            # 模型前馈输出
            images = batch['images'].to(accelerator.device, weight_dtype)
            masks = batch['masks'].to(accelerator.device, weight_dtype)

            with torch.no_grad():
                preds = model(images)[0]

            iou = compute_IoU(preds, masks)
            iou_meter_test.update(iou, masks.size(0))
            f1 = FScore(preds, masks).item()
            f1_meter_test.update(f1, masks.size(0))

            logs = {"IoU": iou_meter_test.avg, "F1": f1_meter_test.avg}
            test_pbar.set_postfix(**logs)
            test_pbar.update(1)

        accelerator.log(logs, step=steps)
        logger.info(f"\n{logs}\nn")

    # 验证效果可视化
    logger.info("\nRunning visualized validation...\n")

    # 实测集指标
    iou_meter_real = AverageMeter()
    f1_meter_real = AverageMeter()

    image_latest_log_dir = os.path.join(args.output_dir, "image_logs")
    image_running_steps_log_dir = os.path.join(image_latest_log_dir, "running_steps")
    os.makedirs(image_running_steps_log_dir, exist_ok=True)

    images_to_save = []
    for image_path in all_images:
        image = Image.open(image_path).convert('RGB')
        image = image.resize((512, 512), Image.LANCZOS)
        image_tensor = imagenet_normalize_image(image)
        image_tensor = image_tensor.unsqueeze(0)
        mask_path = os.path.join(Path(image_path).parents[1], "mask", f"{Path(image_path).stem}_mask.png")
        mask_image = Image.open(mask_path).convert('L')
        mask_image = mask_image.resize((512, 512), Image.LANCZOS)
        mask_gt_tensor = normalize_mask(mask_image).unsqueeze(0)
        mask_gt_tensor = mask_gt_tensor.to(accelerator.device, weight_dtype)

        with torch.no_grad():
            image_tensor = image_tensor.to(accelerator.device, weight_dtype)
            image_mask_tensor = model(image_tensor)[0]

        iou = compute_IoU(image_mask_tensor, mask_gt_tensor)
        iou_meter_real.update(iou, mask_gt_tensor.size(0))
        f1 = FScore(image_mask_tensor, mask_gt_tensor).item()
        f1_meter_real.update(f1, mask_gt_tensor.size(0))

        image_input = imagenet_denormalize_image(image_tensor.squeeze(0))
        image_mask = denormalize_mask(image_mask_tensor.squeeze(0)).convert('RGB')

        # 将image_mask转换为灰度图，便于后续用作透明度掩码
        mask_gray = image_mask.convert('L')  # 转换为灰度图像，其中白色区域表示mask
        # 创建一个绿色的遮罩图像，和image_input同样大小
        green_mask = Image.new('RGB', image_input.size, (0, 255, 0))  # 纯绿色
        # 创建一个透明度为30%的绿色遮罩
        transparent_green_mask = Image.blend(image_input, green_mask, alpha=0.3)
        # 将绿色遮罩和原始图像根据mask_gray进行合并
        blended_image = Image.composite(transparent_green_mask, image_input, mask_gray)
        # 拼接三个PIL图像（image_input, image_mask, blended_image）
        width, height = image_input.size

        combined_image = Image.new('RGB', (width * 3, height))  # 创建一个新图像，宽度是三个图像的宽度之和
        combined_image.paste(image_input, (0, 0))  # 将第一个图像粘贴到左边
        combined_image.paste(image_mask, (width, 0))  # 将第二个图像粘贴到中间
        combined_image.paste(blended_image, (width * 2, 0))  # 将混合了mask区域绿色遮罩的图像粘贴到右边

        images_to_save.append(combined_image)

    # 将图像按每N行分组拼接，并保存
    num_images = len(images_to_save)
    group_size = 6  # 每组N行图像
    num_groups = math.ceil(num_images / group_size)  # 计算分组数量

    for group_idx in range(num_groups):
        start_idx = group_idx * group_size
        end_idx = min(start_idx + group_size, num_images)
        group_images = images_to_save[start_idx:end_idx]

        # 获取每行图像的宽度和高度
        image_width, image_height = group_images[0].size

        # 创建一个空白图像，宽度为单行图像宽度，高度为 group_size 行图像高度的总和
        total_height = image_height * len(group_images)
        group_image = Image.new('RGB', (image_width, total_height))

        # 逐行粘贴图像
        for i, img in enumerate(group_images):
            group_image.paste(img, (0, i * image_height))

        # 构建文件名
        part_suffix = f"_part_{group_idx + 1:04d}"
        latest_filename = os.path.join(image_latest_log_dir, f"validation_latest{part_suffix}.png")
        steps_filename = os.path.join(image_running_steps_log_dir, f"validation_{steps:09d}{part_suffix}.jpg")

        # 保存每组拼接后的图像
        group_image.save(latest_filename)
        group_image.save(steps_filename)

    model.train()

    if test_dataloader is not None:
        return iou_meter_test.avg, f1_meter_test.avg, iou_meter_real.avg, f1_meter_real.avg

    return None, None, iou_meter_real.avg, f1_meter_real.avg


if __name__ == '__main__':
    main()
