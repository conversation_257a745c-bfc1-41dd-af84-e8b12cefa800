# Cycle-CenterNet表格结构识别训练系统

## 📋 概述

本系统实现了基于Cycle-CenterNet算法的表格结构识别训练流程，支持分布式训练、混合精度训练和完整的模型管理功能。

### 🎯 主要功能

- **分布式训练**: 基于HuggingFace Accelerate框架，支持多GPU训练
- **混合精度训练**: 支持fp16/bf16混合精度训练，提升训练效率
- **配置管理**: 基于OmegaConf的层级配置系统，支持配置文件和命令行覆盖
- **模型管理**: 支持EMA、检查点恢复、最佳模型保存等功能
- **数据处理**: 表格结构识别专用的数据集加载和预处理流程

## 🚀 快速开始

### 环境要求

```bash
# 基础依赖
torch>=1.12.0
torchvision>=0.13.0
accelerate>=0.20.0
omegaconf>=2.3.0
tqdm
Pillow
opencv-python

# 可选依赖
bitsandbytes  # 用于8-bit AdamW优化器
```

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd train-anything

# 安装依赖
pip install -r requirements.txt
```

### 基本使用

```bash
# 使用默认配置训练
python training_loops/table_structure_recognition/train_cycle_centernet.py \
    -c configs/cycle_centernet_config.yaml

# 覆盖特定参数
python training_loops/table_structure_recognition/train_cycle_centernet.py \
    -c configs/cycle_centernet_config.yaml \
    -o training.epochs=100 \
       training.batch_size=4 \
       data.paths.train_data_dir=/path/to/your/data

# 调试模式（使用少量数据）
python training_loops/table_structure_recognition/train_cycle_centernet.py \
    -c configs/cycle_centernet_config.yaml \
    -o basic.debug=true
```

## 📁 数据准备

### 数据格式

数据应按以下结构组织（支持分part的目录结构）：

```
data/
├── train/
│   ├── images/
│   │   ├── part_0001/
│   │   │   ├── image1.jpg
│   │   │   ├── image2.jpg
│   │   │   └── ...
│   │   ├── part_0002/
│   │   │   └── ...
│   └── labels/
│       ├── part_0001/
│       │   ├── image1.json
│       │   ├── image2.json
│       │   └── ...
│       ├── part_0002/
│       │   └── ...
└── val/
    ├── images/
    │   ├── part_0001/
    │   │   ├── val_image1.jpg
    │   │   └── ...
    └── labels/
        ├── part_0001/
        │   ├── val_image1.json
        │   └── ...
```

### 标注格式

每个图像对应一个JSON标注文件，包含表格单元格的位置信息。支持两种标注格式：

#### 格式1：点坐标数组

```json
{
    "image_path": "images/part_0001/image1.jpg",
    "part_name": "part_0001",
    "cells": [
        {
            "bbox": {
                "p1": [x1, y1],  // 左上角
                "p2": [x2, y2],  // 右上角
                "p3": [x3, y3],  // 右下角
                "p4": [x4, y4]   // 左下角
            }
        }
    ]
}
```

#### 格式2：点坐标对象

```json
{
    "image_path": "images/part_0001/image1.jpg",
    "part_name": "part_0001",
    "cells": [
        {
            "bbox": {
                "p1": {"x": x1, "y": y1},  // 左上角
                "p2": {"x": x2, "y": y2},  // 右上角
                "p3": {"x": x3, "y": y3},  // 右下角
                "p4": {"x": x4, "y": y4}   // 左下角
            }
        }
    ]
}
```

注意：
- 标注文件名应与图像文件名相同（不含扩展名）
- 每个单元格的边界框使用四点坐标表示，而不是简单的矩形坐标
- 系统会自动计算单元格中心点和最小外接矩形

## ⚙️ 配置说明

### 配置文件结构

```yaml
# 基础配置
basic:
  debug: false
  seed: 42
  output_dir: "./outputs/cycle_centernet"

# 数据配置
data:
  paths:
    train_data_dir: "/path/to/train/data"
    val_data_dir: "/path/to/val/data"
  processing:
    image_size: [512, 512]
    max_samples: null  # 调试时可设置为小数值
    normalize:
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]
      to_rgb: true
  loader:
    num_workers: 4
    pin_memory: true

# 模型配置
model:
  backbone:
    depth: 34  # ResNet深度
  head:
    feat_channels: 64
  task:
    num_classes: 1

# 训练配置
training:
  epochs: 150
  batch_size: 8
  optimizer:
    type: "SGD"  # 或 "Adam", "AdamW"
    learning_rate: 0.00125
    weight_decay: 0.0001
    momentum: 0.9
  scheduler:
    type: "step"  # 或 "cosine"
    step:
      step_size: 100
      gamma: 0.1
    warmup:
      steps: 500
  gradient:
    clip_norm: false
    clip_value: 1.0

# EMA配置
ema:
  enabled: true
  decay: 0.999
  start_step: 0
  update_period: 1

# 损失函数配置
loss:
  weights:
    heatmap: 1.0
    offset: 1.0
    center2vertex: 1.0
    vertex2center: 0.5

# 检查点配置
checkpoint:
  save:
    steps: 1000
    every_n_epoch: 10
    keep_num: 5
  resume:
    from_checkpoint: null  # 恢复检查点路径
  validation:
    steps: 1000
    num_batches: 10

# 分布式训练配置
distributed:
  mixed_precision: "fp16"  # 或 "bf16", "no"
  report_to: "tensorboard"
  tracker_project_name: "cycle-centernet-training"
```

### 命令行参数覆盖

支持通过`-o`参数覆盖配置文件中的任意参数：

```bash
# 覆盖单个参数
-o training.epochs=200

# 覆盖多个参数
-o training.epochs=200 training.batch_size=16 basic.debug=true

# 覆盖嵌套参数
-o data.paths.train_data_dir=/new/path model.backbone.depth=50
```

## 🔧 高级功能

### 分布式训练

```bash
# 单机多GPU训练
accelerate launch --num_processes=4 \
    training_loops/table_structure_recognition/train_cycle_centernet.py \
    -c configs/cycle_centernet_config.yaml

# 多机训练
accelerate launch --num_processes=8 --num_machines=2 \
    training_loops/table_structure_recognition/train_cycle_centernet.py \
    -c configs/cycle_centernet_config.yaml
```

### 混合精度训练

在配置文件中设置：

```yaml
distributed:
  mixed_precision: "fp16"  # 或 "bf16"
```

### EMA（指数移动平均）

```yaml
ema:
  enabled: true
  decay: 0.999
  start_step: 0
  update_period: 1
```

### 检查点恢复

```bash
# 从特定检查点恢复
python train_cycle_centernet.py \
    -c configs/cycle_centernet_config.yaml \
    -o checkpoint.resume.from_checkpoint=/path/to/checkpoint

# 自动从最新检查点恢复（如果存在）
python train_cycle_centernet.py \
    -c configs/cycle_centernet_config.yaml
```

## 📊 监控和日志

### 输出目录结构

```
outputs/
├── logs/
│   └── monitors.log          # 详细训练日志
├── checkpoint-1000/          # 定期保存的检查点
├── checkpoint-2000/
├── best_loss_model/          # 最佳模型
│   ├── pytorch_model.bin
│   ├── pytorch_model_ema.bin
│   └── record.json
├── model_epoch-10/           # 每N个epoch保存的模型
└── model_final/              # 最终模型
```

### TensorBoard监控

```bash
# 启动TensorBoard
tensorboard --logdir outputs/runs

# 查看训练指标
http://localhost:6006
```

## 🐛 常见问题

### 1. 内存不足

```bash
# 减少批次大小
-o training.batch_size=4

# 减少工作进程数
-o data.loader.num_workers=2

# 关闭pin_memory
-o data.loader.pin_memory=false
```

### 2. 训练速度慢

```bash
# 启用混合精度训练
-o distributed.mixed_precision=fp16

# 增加批次大小
-o training.batch_size=16

# 增加工作进程数
-o data.loader.num_workers=8
```

### 3. 模型不收敛

```bash
# 调整学习率
-o training.optimizer.learning_rate=0.001

# 使用AdamW优化器
-o training.optimizer.type=AdamW

# 启用梯度裁剪
-o training.gradient.clip_norm=true training.gradient.clip_value=1.0
```

### 4. 调试模式

```bash
# 使用少量数据进行调试
-o basic.debug=true

# 设置固定随机种子
-o basic.seed=42

# 减少验证批次数
-o checkpoint.validation.num_batches=5
```

## 📈 性能优化建议

1. **数据加载优化**:
   - 使用SSD存储数据
   - 适当增加`num_workers`
   - 启用`pin_memory`

2. **训练优化**:
   - 使用混合精度训练
   - 合理设置批次大小
   - 使用多GPU训练

3. **模型优化**:
   - 启用EMA提升模型稳定性
   - 合理设置学习率调度
   - 使用梯度裁剪防止梯度爆炸

## 🤝 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

[MIT License](LICENSE)

## 📞 联系方式

- Email: <EMAIL>
- Issues: [GitHub Issues](https://github.com/your-repo/issues) 