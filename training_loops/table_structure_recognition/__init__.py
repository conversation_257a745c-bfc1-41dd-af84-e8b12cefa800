#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-03 16:30
# <AUTHOR> <EMAIL>
# @FileName: __init__.py

"""
表格结构识别训练循环模块

该模块包含Cycle-CenterNet表格结构识别的训练循环实现，包括：
- train_cycle_centernet.py: 主训练脚本
- 训练和验证函数
- 数据加载和预处理函数
"""

# 导入训练相关模块
from .train_cycle_centernet import (
    prepare_dataloaders,
    save_state,
    log_validation,
    walk_dataloaders,
    main
)

# 从网络模块导入模型
from networks.cycle_centernet import (
    CycleCenterNetModel,
    create_cycle_centernet_model,
    cycle_centernet_default,
    cycle_centernet_table_cell
)

__all__ = [
    # 训练相关函数
    'prepare_dataloaders',
    'save_state',
    'log_validation',
    'walk_dataloaders',
    'main',

    # 模型相关（从网络模块导入）
    'CycleCenterNetModel',
    'create_cycle_centernet_model',
    'cycle_centernet_default',
    'cycle_centernet_table_cell'
]
