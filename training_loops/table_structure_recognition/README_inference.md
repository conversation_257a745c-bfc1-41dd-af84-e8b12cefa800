# Cycle-CenterNet 表格结构识别推理脚本

本文档介绍如何使用 `infer_cycle_centernet.py` 脚本对表格图片进行结构识别推理。

## 功能特性

- 🚀 **纯推理模式**: 专门用于推理，无需训练环境
- 🎯 **ModelScope 兼容**: 完全兼容 ModelScope 版本的 Cycle-CenterNet 模型
- 🖼️ **批量处理**: 支持对目录中的多张图片进行批量推理
- 📊 **可视化输出**: 自动生成包含原图、预测结果和热力图的可视化图片
- ⚙️ **灵活配置**: 支持自定义置信度阈值、NMS阈值等参数
- 💾 **多种检查点格式**: 支持多种模型检查点格式

## 快速开始

### 1. 基础使用

```bash
# 使用示例脚本快速开始
bash cmd_scripts/train_table_structure/cycle_centernet_infer_example.sh
```

### 2. 自定义推理

```bash
# 使用完整的推理脚本
bash cmd_scripts/train_table_structure/cycle_centernet_infer.sh
```

### 3. 直接调用 Python 脚本

```bash
python training_loops/table_structure_recognition/infer_cycle_centernet.py \
    --config configs/table_structure_recognition/cycle_centernet/cycle_centernet_ms_config.yaml \
    --checkpoint /path/to/your/checkpoint \
    --input_dir /path/to/images \
    --output_dir /path/to/output \
    --device cuda:0
```

## 参数说明

### 必需参数

- `--config`: 配置文件路径
- `--checkpoint`: 模型检查点路径
- `--input_dir`: 输入图片目录
- `--output_dir`: 输出结果目录

### 可选参数

- `--device`: 推理设备 (默认: cuda:0)
- `--mixed_precision`: 混合精度类型 (默认: fp16)
- `--max_samples`: 最大处理样本数 (默认: 处理所有图片)
- `--confidence_threshold`: 置信度阈值 (默认: 0.3)
- `--nms_threshold`: NMS阈值 (默认: 0.3)

## 支持的检查点格式

脚本支持以下检查点格式：

1. **单个模型文件**:
   - `.pth` 文件
   - `.pt` 文件

2. **检查点目录**:
   - 包含 `pytorch_model.bin` 的目录
   - 训练输出的 `checkpoint-XXXX` 目录
   - 训练输出的 `model_epoch-XXX` 目录

## 支持的图片格式

- `.jpg`, `.jpeg`
- `.png`
- `.bmp`
- `.tiff`, `.tif`

## 输出结果

推理完成后，输出目录将包含以下内容：

```
output_dir/
├── step_0/                 # 推理结果目录
│   ├── image1_combined.png # 组合可视化图片
│   ├── image2_combined.png
│   └── ...
├── latest/                 # 最新结果的副本
│   ├── image1_combined.png
│   └── ...
└── logs/                   # 推理日志
    └── inference.log
```

每张组合图片包含：
- 原始图片
- 预测的表格结构（边界框和关键点）
- 双通道热力图可视化

## 配置文件

推理脚本支持两种配置方式：

### 1. 使用完整配置文件

使用训练时的完整配置文件，脚本会自动提取推理所需的配置。

### 2. 简化配置模式

如果配置文件加载失败，脚本会自动切换到简化配置模式，使用内置的默认参数。

## 故障排除

### 常见问题

1. **CUDA 内存不足**:
   ```bash
   # 使用 CPU 推理
   --device cpu
   
   # 或减少批次大小
   --batch_size 1
   ```

2. **检查点加载失败**:
   - 确保检查点路径正确
   - 检查检查点文件是否完整
   - 确认模型架构匹配

3. **没有找到图片**:
   - 确保输入目录存在
   - 检查图片格式是否支持
   - 确认目录权限

4. **配置文件错误**:
   - 检查配置文件路径
   - 确认配置文件格式正确
   - 脚本会自动回退到简化配置

### 调试模式

启用详细日志输出：

```bash
export PYTHONPATH=.
python training_loops/table_structure_recognition/infer_cycle_centernet.py \
    --config your_config.yaml \
    --checkpoint your_checkpoint \
    --input_dir your_images \
    --output_dir your_output \
    --device cuda:0 \
    2>&1 | tee inference.log
```

## 性能优化

### GPU 推理优化

```bash
# 使用混合精度加速
--mixed_precision fp16

# 选择合适的设备
--device cuda:0  # 或 cuda:1, cuda:2 等
```

### 批量处理优化

```bash
# 限制处理数量（用于测试）
--max_samples 10

# 调整置信度阈值（过滤低质量预测）
--confidence_threshold 0.5
```

## 集成到其他项目

推理脚本设计为独立模块，可以轻松集成到其他项目中：

```python
from training_loops.table_structure_recognition.infer_cycle_centernet import (
    load_model_from_checkpoint,
    create_simple_config
)

# 加载模型
model = load_model_from_checkpoint(checkpoint_path, config, device, weight_dtype)

# 创建可视化器
visualizer = TableStructureVisualizerMS(config, device, weight_dtype)

# 执行推理
visualizer.visualize_validation_samples(model, 0, accelerator)
```

## 相关文件

- `infer_cycle_centernet.py`: 主推理脚本
- `cycle_centernet_infer.sh`: 完整推理启动脚本
- `cycle_centernet_infer_example.sh`: 快速开始示例脚本
- `train_cycle_centernet_ms.py`: 原始训练脚本（参考）
