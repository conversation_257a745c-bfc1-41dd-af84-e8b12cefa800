#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Time: 2025-07-30
# Author: <EMAIL>
# FileName: infer_cycle_centernet.py

"""
Cycle-CenterNet 表格结构识别纯推理可视化脚本

基于 train_cycle_centernet_ms.py 训练脚本，提取纯推理功能，
使用 TableStructureVisualizerMS 进行推理和可视化。

主要功能:
1. 加载训练好的 Cycle-CenterNet 模型
2. 对指定图片进行推理
3. 生成可视化结果
4. 保存推理结果到指定目录

使用方法:
    python training_loops/table_structure_recognition/infer_cycle_centernet.py \
        --config configs/table_structure_recognition/cycle_centernet/cycle_centernet_ms_config.yaml \
        --checkpoint /path/to/checkpoint \
        --input_dir /path/to/images \
        --output_dir /path/to/output \
        --device cuda:0
"""

import os
import sys
import argparse
from pathlib import Path
from typing import Optional

import torch
from accelerate import Accelerator
from accelerate.utils import ProjectConfiguration

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

# 导入日志和模型相关模块
from modules.utils.log import LOGGER

# 直接导入可视化器，避免 __init__.py 的依赖问题
try:
    from modules.visualization.table_structure_visualizer_ms import TableStructureVisualizerMS
except ImportError as e:
    print(f"导入 TableStructureVisualizerMS 失败: {e}")
    print("请确保 modules/visualization/table_structure_visualizer_ms.py 文件存在")
    sys.exit(1)

try:
    from networks.cycle_centernet_ms import create_cycle_centernet_ms_model
except ImportError as e:
    print(f"导入 create_cycle_centernet_ms_model 失败: {e}")
    print("请确保 networks/cycle_centernet_ms 模块存在")
    sys.exit(1)

logger = LOGGER


def parse_inference_args():
    """解析推理参数"""
    parser = argparse.ArgumentParser(description="Cycle-CenterNet 表格结构识别推理脚本")
    
    # 基础参数
    parser.add_argument("-c", "--config", type=str, required=True,
                       help="配置文件路径")
    parser.add_argument("--checkpoint", type=str, required=True,
                       help="模型检查点路径")
    parser.add_argument("--input_dir", type=str, required=True,
                       help="输入图片目录路径")
    parser.add_argument("--output_dir", type=str, required=True,
                       help="输出结果目录路径")
    
    # 设备参数
    parser.add_argument("--device", type=str, default="cuda:0",
                       help="推理设备 (默认: cuda:0)")
    parser.add_argument("--mixed_precision", type=str, default="fp16",
                       choices=["no", "fp16", "bf16"],
                       help="混合精度类型 (默认: fp16)")
    
    # 推理参数
    parser.add_argument("--max_samples", type=int, default=None,
                       help="最大处理样本数 (默认: 处理所有图片)")
    parser.add_argument("--batch_size", type=int, default=1,
                       help="推理批次大小 (默认: 1)")
    
    # 可视化参数
    parser.add_argument("--confidence_threshold", type=float, default=0.3,
                       help="置信度阈值 (默认: 0.3)")
    parser.add_argument("--nms_threshold", type=float, default=0.3,
                       help="NMS阈值 (默认: 0.3)")
    
    return parser.parse_args()


def load_model_from_checkpoint(checkpoint_path: str, config, device: torch.device, weight_dtype: torch.dtype):
    """
    从检查点加载模型
    
    Args:
        checkpoint_path: 检查点路径
        config: 配置对象
        device: 设备
        weight_dtype: 权重数据类型
        
    Returns:
        加载好的模型
    """
    logger.info(f"从检查点加载模型: {checkpoint_path}")
    
    # 创建模型
    model = create_cycle_centernet_ms_model(
        config={
            'base_name': config.model.get('base_name', 'dla34'),
            'pretrained': config.model.get('pretrained', False),
            'down_ratio': config.model.get('down_ratio', 4),
            'head_conv': config.model.get('head_conv', 256),
            'checkpoint_path': None,  # 不在创建时加载权重
        }
    )
    
    # 加载检查点
    if os.path.isdir(checkpoint_path):
        # 如果是目录，查找最新的检查点
        checkpoint_files = list(Path(checkpoint_path).glob("pytorch_model.bin"))
        if not checkpoint_files:
            checkpoint_files = list(Path(checkpoint_path).glob("*.pth"))
        if not checkpoint_files:
            checkpoint_files = list(Path(checkpoint_path).glob("*.pt"))
        
        if checkpoint_files:
            checkpoint_path = str(checkpoint_files[0])
        else:
            raise FileNotFoundError(f"在目录 {checkpoint_path} 中未找到模型文件")
    
    # 加载权重
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    if 'state_dict' in checkpoint:
        state_dict = checkpoint['state_dict']
    elif 'model' in checkpoint:
        state_dict = checkpoint['model']
    else:
        state_dict = checkpoint
    
    # 移除可能的前缀
    new_state_dict = {}
    for key, value in state_dict.items():
        new_key = key
        if key.startswith('module.'):
            new_key = key[7:]  # 移除 'module.' 前缀
        new_state_dict[new_key] = value
    
    # 加载权重到模型
    missing_keys, unexpected_keys = model.load_state_dict(new_state_dict, strict=False)
    
    if missing_keys:
        logger.warning(f"缺失的键: {missing_keys[:5]}...")
    if unexpected_keys:
        logger.warning(f"意外的键: {unexpected_keys[:5]}...")
    
    # 移动到指定设备并设置数据类型
    model = model.to(device, dtype=weight_dtype)
    model.eval()
    
    logger.info("模型加载完成")
    return model


def create_simple_config(args):
    """
    创建简化的推理配置

    Args:
        args: 命令行参数

    Returns:
        推理配置对象
    """
    from omegaconf import OmegaConf

    # 创建简化的配置结构
    config_dict = {
        'basic': {
            'output_dir': args.output_dir,
            'seed': 42
        },
        'model': {
            'base_name': 'dla34',
            'pretrained': False,
            'down_ratio': 4,
            'head_conv': 256,
            'checkpoint_path': None
        },
        'visualization': {
            'enabled': True,
            'sample_images_dir': args.input_dir,
            'output_dir': args.output_dir,
            'max_samples': args.max_samples or 1000,
            'frequency': 1,
            'style': {
                'bbox_color': [0, 255, 0],
                'keypoint_color': [255, 0, 0],
                'center_color': [255, 0, 0],
                'transparency': 0.8,
                'line_thickness': 2,
                'point_radius': 4
            },
            'heatmap': {
                'colormap': 'jet',
                'normalize': True,
                'threshold': 0.1
            },
            'modelscope': {
                'bbox_k': 1000,
                'gbox_k': 4000,
                'confidence_threshold': args.confidence_threshold,
                'nms_threshold': args.nms_threshold
            }
        }
    }

    return OmegaConf.create(config_dict)


def create_inference_config(args, base_config):
    """
    创建推理专用配置

    Args:
        args: 命令行参数
        base_config: 基础配置

    Returns:
        推理配置对象
    """
    # 复制基础配置
    config = base_config

    # 更新可视化配置
    if not hasattr(config, 'visualization'):
        config.visualization = {}

    # 设置推理特定的可视化配置
    config.visualization.enabled = True
    config.visualization.sample_images_dir = args.input_dir
    config.visualization.output_dir = args.output_dir
    config.visualization.max_samples = args.max_samples or 1000
    config.visualization.frequency = 1

    # 更新 ModelScope 配置
    if not hasattr(config.visualization, 'modelscope'):
        config.visualization.modelscope = {}

    config.visualization.modelscope.confidence_threshold = args.confidence_threshold
    config.visualization.modelscope.nms_threshold = args.nms_threshold
    config.visualization.modelscope.bbox_k = 1000
    config.visualization.modelscope.gbox_k = 4000

    # 设置输出目录
    config.basic.output_dir = args.output_dir

    return config


def setup_accelerator(args):
    """设置 Accelerator"""
    project_config = ProjectConfiguration(
        project_dir=args.output_dir,
        logging_dir=os.path.join(args.output_dir, "logs")
    )
    
    accelerator = Accelerator(
        mixed_precision=args.mixed_precision,
        project_config=project_config,
        log_with=None,  # 推理时不需要日志记录
    )
    
    return accelerator


def main():
    """主推理函数"""
    try:
        # 解析参数
        args = parse_inference_args()

        logger.info("开始 Cycle-CenterNet 表格结构识别推理")
        logger.info(f"配置文件: {args.config}")
        logger.info(f"检查点: {args.checkpoint}")
        logger.info(f"输入目录: {args.input_dir}")
        logger.info(f"输出目录: {args.output_dir}")
        logger.info(f"设备: {args.device}")

        # 检查输入目录
        if not os.path.exists(args.input_dir):
            raise FileNotFoundError(f"输入目录不存在: {args.input_dir}")

        # 检查配置文件
        if not os.path.exists(args.config):
            raise FileNotFoundError(f"配置文件不存在: {args.config}")

        # 检查检查点
        if not os.path.exists(args.checkpoint):
            raise FileNotFoundError(f"检查点不存在: {args.checkpoint}")

        # 创建输出目录
        os.makedirs(args.output_dir, exist_ok=True)

        # 统计输入图片数量
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        image_files = []
        for ext in image_extensions:
            image_files.extend(list(Path(args.input_dir).rglob(f'*{ext}')))
            image_files.extend(list(Path(args.input_dir).rglob(f'*{ext.upper()}')))

        logger.info(f"找到 {len(image_files)} 张图片待处理")
        if len(image_files) == 0:
            logger.warning("输入目录中没有找到支持的图片文件")
            return

        # 尝试加载配置文件，如果失败则使用简化配置
        try:
            from modules.proj_cmd_args.cycle_centernet.args import parse_args
            sys.argv = ['infer_cycle_centernet.py', '-c', args.config]  # 模拟命令行参数
            base_config = parse_args()
            config = create_inference_config(args, base_config)
            logger.info("使用完整配置文件")
        except Exception as e:
            logger.warning(f"加载配置文件失败，使用简化配置: {e}")
            config = create_simple_config(args)

        # 设置 Accelerator
        accelerator = setup_accelerator(args)

        # 设置权重数据类型
        weight_dtype = torch.float32
        if accelerator.mixed_precision == "fp16":
            weight_dtype = torch.float16
        elif accelerator.mixed_precision == "bf16":
            weight_dtype = torch.bfloat16

        logger.info(f"使用权重数据类型: {weight_dtype}")

        # 加载模型
        model = load_model_from_checkpoint(
            args.checkpoint, config, accelerator.device, weight_dtype
        )

        # 创建可视化器
        logger.info("创建可视化器...")
        visualizer = TableStructureVisualizerMS(
            config=config,
            device=accelerator.device,
            weight_dtype=weight_dtype
        )

        # 执行推理和可视化
        logger.info("开始推理和可视化...")

        with torch.no_grad():
            visualizer.visualize_validation_samples(
                model=model,
                global_step=0,  # 推理时步数设为0
                accelerator=accelerator
            )

        logger.info(f"推理完成，结果保存到: {args.output_dir}")

        # 显示输出目录结构
        output_path = Path(args.output_dir)
        if output_path.exists():
            logger.info("输出目录结构:")
            for item in output_path.iterdir():
                if item.is_dir():
                    file_count = len(list(item.glob('*')))
                    logger.info(f"  {item.name}/  ({file_count} 个文件)")
                else:
                    logger.info(f"  {item.name}")

    except Exception as e:
        logger.error(f"推理过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
