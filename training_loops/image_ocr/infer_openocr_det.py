#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/4/1 11:38
# <AUTHOR> <EMAIL>
# @FileName: infer_openocr_det

"""
OpenOCR检测模型推理模块
主要功能：
1. 加载训练好的模型
2. 对输入图像进行推理
3. 可视化检测结果
4. 支持批量处理和单张图像处理
"""

import os
import time
import json
from pathlib import Path

import cv2
import torch
import numpy as np

from third_parties import init_third_parties_env
init_third_parties_env(target=['openocr'])  # 必须初始化OpenOCR环境

from tools.utility import ArgsParser
from tools.engine.config import Config
from tools.utils.logging import get_logger
from tools.utils.utility import get_image_file_list
from tools.utils.ckpt import load_ckpt

from opendet.preprocess import create_operators, transform
from opendet.postprocess import build_post_process
from opendet.modeling import build_model as build_det_model
from modules.utils.image_utils import get_all_image_path

logger = get_logger()


def draw_det_res(
    dt_boxes: np.ndarray,
    config,
    img: np.n<PERSON><PERSON>,
    img_name: str,
    save_path: str
) -> None:
    """
    将检测结果绘制到图像上并保存
    
    Args:
        dt_boxes: 检测框坐标列表
        config: 配置信息
        img: 原始图像
        img_name: 图像文件名
        save_path: 结果保存路径
    """
    src_im = img.copy()
    for box in dt_boxes:
        box = np.array(box).astype(np.int32).reshape((-1, 1, 2))
        cv2.polylines(src_im, [box], True, color=(255, 255, 0), thickness=2)
    
    if not os.path.exists(save_path):
        os.makedirs(save_path)
    save_path = os.path.join(save_path, os.path.basename(img_name))
    cv2.imwrite(save_path, src_im)
    logger.info(f"检测结果已保存至: {save_path}")


def draw_boxes_with_filtering(
    img, 
    all_boxes, 
    all_scores, 
    filtered_boxes, 
    save_path
):
    """
    在输入图像上绘制未过滤和已过滤的box
    
    Args:
        img: 输入图像
        all_boxes: 所有box（未过滤的）
        all_scores: 所有box对应的分数
        filtered_boxes: 过滤后的box（被过滤掉的）
        save_path: 结果保存路径
    
    Returns:
        vis_img: 绘制了box的可视化图像
    """
    vis_img = img.copy()
    
    # 找出未被过滤掉的boxes（保留的boxes）
    # 先将所有box转换为字符串以便比较
    if all_boxes is not None and filtered_boxes is not None:
        all_boxes_str = [str(box) for box in all_boxes]
        filtered_boxes_str = [str(box) for box in filtered_boxes]
        
        # 找出保留的boxes索引
        kept_indices = []
        for i, box_str in enumerate(all_boxes_str):
            if box_str not in filtered_boxes_str:
                kept_indices.append(i)
        
        # 绘制被过滤掉的boxes（红色）
        for idx in kept_indices:
            box = all_boxes[idx]
            score = all_scores[idx] if idx < len(all_scores) else 0.5
            box_np = np.array(box).astype(np.int32).reshape((-1, 1, 2))
            
            # 直接在图像上绘制边界框
            cv2.polylines(vis_img, [box_np], True, color=(0, 0, 255), thickness=2)
            
            # 添加填充效果，透明度根据score映射到0.3~0.7
            alpha = 0.3 + score * 0.4  # 0.3到0.7的透明度
            overlay = vis_img.copy()
            cv2.fillPoly(overlay, [box_np], (0, 0, 255))
            cv2.addWeighted(overlay, alpha, vis_img, 1 - alpha, 0, vis_img)
        
        # 绘制被保留下来的boxes（绿色）
        for box in filtered_boxes:
            box_np = np.array(box).astype(np.int32).reshape((-1, 1, 2))
            
            # 直接在图像上绘制边界框
            cv2.polylines(vis_img, [box_np], True, color=(0, 255, 0), thickness=2)
            
            # 添加填充效果，使用固定透明度
            alpha = 0.5  # 使用中等透明度
            overlay = vis_img.copy()
            cv2.fillPoly(overlay, [box_np], (0, 255, 0))
            cv2.addWeighted(overlay, alpha, vis_img, 1 - alpha, 0, vis_img)

    # 保存结果
    dirname = os.path.dirname(save_path)
    if not os.path.exists(dirname):
        os.makedirs(dirname)
    cv2.imwrite(save_path, vis_img)
    logger.info(f"Box可视化图已保存至: {save_path}")

    return vis_img


def save_debug_images(debug_info, file, save_path_prefix, src_img=None):
    """
    保存调试模式下的可视化图像

    Args:
        debug_info: 调试信息，包含prob_map, binary_map等
        file: 原始图像文件路径
        save_path_prefix: 保存路径前缀
        src_img: 原始图像，用于绘制box
    """
    if not os.path.exists(save_path_prefix):
        os.makedirs(save_path_prefix)

    # 获取文件名（不含扩展名）
    basename = os.path.basename(file)
    filename, _ = os.path.splitext(basename)

    # 保存概率图
    if "prob_map" in debug_info:
        prob_map = debug_info["prob_map"]
        # 转换为彩色热力图以便更好地可视化
        prob_map_vis = cv2.applyColorMap((prob_map * 255).astype(np.uint8), cv2.COLORMAP_JET)
        prob_save_path = os.path.join(save_path_prefix, f"{filename}_prob_map.jpg")
        cv2.imwrite(prob_save_path, prob_map_vis)
        logger.info(f"概率图已保存至: {prob_save_path}")

    # 保存二值图
    if "binary_map" in debug_info:
        binary_map = debug_info["binary_map"]
        binary_save_path = os.path.join(save_path_prefix, f"{filename}_binary_map.jpg")
        cv2.imwrite(binary_save_path, binary_map * 255)
        logger.info(f"二值图已保存至: {binary_save_path}")

    # 绘制并保存Box可视化效果图
    if src_img is not None and "all_boxes" in debug_info and "all_scores" in debug_info and "filtered_boxes" in debug_info:
        vis_save_path = os.path.join(save_path_prefix, f"{filename}_vis_box.jpg")
        draw_boxes_with_filtering(
            src_img,
            debug_info["all_boxes"],
            debug_info["all_scores"],
            debug_info["filtered_boxes"],
            vis_save_path
        )


def set_device(device, numId=0):
    """
    设置推理设备
    """
    import torch
    if device == 'gpu' and torch.cuda.is_available():
        device = torch.device(f'cuda:{numId}')
    else:
        logger.info('GPU is not available, using CPU.')
        device = torch.device('cpu')
    return device


def replace_batchnorm(net):
    """
    替换模型中的BatchNorm层为Identity，用于推理优化
    """
    import torch
    for child_name, child in net.named_children():
        if hasattr(child, 'fuse'):
            fused = child.fuse()
            setattr(net, child_name, fused)
            replace_batchnorm(fused)
        elif isinstance(child, torch.nn.BatchNorm2d):
            setattr(net, child_name, torch.nn.Identity())
        else:
            replace_batchnorm(child)


class OpenDetector(object):
    """OpenOCR文本检测器类"""

    def __init__(self, config=None, numId=0):
        """
        初始化检测器
        
        Args:
            config: 配置信息
            numId: GPU设备编号
        """
        self._init_model(config, numId)

    def _init_model(self, config, numId=0):
        """初始化模型"""
        # 获取全局配置
        global_config = config['Global']
        
        # 创建预处理操作
        self.transform = transform
        transforms = []
        for op in config['Eval']['dataset']['transforms']:
            op_name = list(op)[0]
            if 'Label' in op_name:
                continue
            elif op_name == 'KeepKeys':
                op[op_name]['keep_keys'] = ['image', 'shape']
            transforms.append(op)
        
        self.ops = create_operators(transforms, global_config)
        
        # 创建后处理
        self.post_process_class = build_post_process(config['PostProcess'], global_config)
        # 判断是否为DBPostProcess类型，用于调试
        self.is_db_postprocess = self.post_process_class.__class__.__name__ == "DBPostProcess"

        # 构建模型
        self.model = build_det_model(config['Architecture'])
        self.model.eval()
        load_ckpt(self.model, config)
        
        # 设置计算设备
        self.device = set_device(global_config.get('device', 'cpu'), numId=numId)
        self.model.to(device=self.device)

    def __call__(self, img_path=None, img=None, debug=False):
        """
        执行推理
        
        Args:
            img_path: 图像路径
            img: 图像数据，与img_path二选一
            debug: 是否返回调试信息
            
        Returns:
            list: 包含推理结果的列表
        """
        # 检查输入
        assert img_path is not None or img is not None, "必须提供img_path或img参数"
        
        # 开始计时
        st = time.time()
        
        if img is not None:
            data = {'image': img}
        elif img_path is not None:
            with open(img_path, 'rb') as f:
                img = f.read()
                data = {'image': img}
            # 先进行一次预处理（包含DecodeImage等）
            data = self.transform(data, self.ops[:1])
        
        # 进行剩余的预处理
        batch = self.transform(data, self.ops[1:])
        
        # 转换输入数据
        images = np.expand_dims(batch[0], axis=0)
        shape_list = np.expand_dims(batch[1], axis=0)
        images = torch.from_numpy(images).to(device=self.device)
        
        # 执行前向计算
        with torch.no_grad():
            preds = self.model(images)
        
        # 执行后处理
        debug_mode = debug and self.is_db_postprocess  # 只有当启用debug且是DBPostProcess时才传递debug=True
        if debug_mode:
            post_result, debug_info_batch = self.post_process_class(preds, [None, shape_list], debug=True)
        else:
            post_result = self.post_process_class(preds, [None, shape_list])
            debug_info_batch = None
        
        # 计算耗时
        elapse = time.time() - st
        
        # 整理结果
        info = {'boxes': post_result[0]['points'], 'elapse': elapse}
        if debug_info_batch is not None:
            info['debug_info'] = debug_info_batch[0]
        
        results = [info]
        
        return results


@torch.no_grad()
def main(config, mode="Eval"):
    """
    主函数：执行文本检测推理流程
    """
    global_config = config["Global"]
    
    # 初始化检测器
    model = OpenDetector(config)
    
    # 获取配置
    infer_img = global_config.get('infer_img')
    save_res_dir = global_config.get('save_res_dir', None)
    save_res_path = global_config.get('save_res_path', None)
    debug_mode = global_config.get('debug', True)  # 默认开启debug模式
    assert save_res_dir is not None or save_res_path is not None, "必须指定有效的结果保存路径"
    
    # 开始推理
    if save_res_dir is not None:
        # 创建结果保存目录
        save_res_dir = Path(save_res_dir)
        save_res_dir.mkdir(parents=True, exist_ok=True)
        
        # 遍历所有图像文件
        all_image_files = get_all_image_path(infer_img, path_op=Path, recursive=True)
        for file in all_image_files:
            logger.info(f"正在处理图像：{file}")
            
            # 执行推理
            preds_result = model(img_path=str(file), debug=debug_mode)[0]
            logger.info(f"infer_img: {file}, time cost: {preds_result['elapse']}")
            
            # 获取检测框
            boxes = preds_result['boxes']
            
            # 构建结果JSON
            dt_boxes_json = []
            for box in boxes:
                tmp_json = {"transcription": "待识别", "points": np.array(box).tolist()}
                dt_boxes_json.append(tmp_json)
            
            # 保存可视化结果
            img_rel_path = Path(file).relative_to(infer_img)
            img_dst_path = save_res_dir / img_rel_path
            src_img = cv2.imread(str(file))
            draw_det_res(boxes, config, src_img.copy(), str(file), img_dst_path.parent)
            
            # 如果有debug信息，保存debug可视化图像
            if 'debug_info' in preds_result:
                debug_save_path = save_res_dir / "debug_results"
                save_debug_images(preds_result['debug_info'], str(file), str(debug_save_path), src_img.copy())
            
            # 保存结果到Label.txt
            label_path = save_res_dir / "Label.txt"
            if os.path.exists(label_path):
                with open(label_path, "a") as f:
                    f.write(f"{str(img_rel_path)}\t{json.dumps(dt_boxes_json)}\n")
            else:
                Path(label_path).parent.mkdir(parents=True, exist_ok=True)
                with open(label_path, "w") as f:
                    f.write(f"{str(img_rel_path)}\t{json.dumps(dt_boxes_json)}\n")
    else:
        # 使用单文件保存模式
        if not os.path.exists(os.path.dirname(save_res_path)):
            os.makedirs(os.path.dirname(save_res_path))
        
        with open(save_res_path, "wb") as fout:
            # 遍历所有图像文件
            for file in get_image_file_list(infer_img):
                logger.info(f"正在处理图像: {file}")
                
                # 执行推理
                preds_result = model(img_path=file, debug=debug_mode)[0]
                logger.info(f"infer_img: {file}, time cost: {preds_result['elapse']}")
                
                # 获取检测框
                boxes = preds_result['boxes']
                
                # 构建结果JSON
                dt_boxes_json = []
                for box in boxes:
                    tmp_json = {"transcription": "", "points": np.array(box).tolist()}
                    dt_boxes_json.append(tmp_json)
                
                # 保存可视化结果
                save_det_path = os.path.dirname(save_res_path) + "/det_results/"
                src_img = cv2.imread(file)
                draw_det_res(boxes, config, src_img.copy(), file, save_det_path)
                
                # 如果有debug信息，保存debug可视化图像
                if 'debug_info' in preds_result:
                    debug_save_path = os.path.dirname(save_res_path) + "/debug_results/"
                    save_debug_images(preds_result['debug_info'], file, debug_save_path, src_img.copy())
                
                # 保存检测结果到文件
                otstr = file + "\t" + json.dumps(dt_boxes_json) + "\n"
                fout.write(otstr.encode())
    
    logger.info("推理完成!")


if __name__ == '__main__':
    # 使用ArgsParse解析并合并配置
    FLAGS = ArgsParser().parse_args()
    cfg = Config(FLAGS.config)
    FLAGS = vars(FLAGS)
    opt = FLAGS.pop('opt')
    cfg.merge_dict(FLAGS)
    cfg.merge_dict(opt)
    print(cfg)
        
    # 执行推理
    mode = os.environ.get("MODE", "Eval")  # 从环境变量获取模式设置
    main(cfg.cfg, mode)
