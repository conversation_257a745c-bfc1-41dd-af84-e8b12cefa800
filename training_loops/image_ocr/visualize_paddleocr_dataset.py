#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/2/24 20:41
# <AUTHOR> <EMAIL>
# @FileName: visualize_paddleocr_dataset

"""
PaddleOCR数据集可视化工具
主要功能：
1. 加载训练数据
2. 采样指定数量的batch
3. 将图像和标注信息保存到指定路径，包括：
   - 原始图像
   - 原始标注可视化
   - 训练监督信号可视化
   - 完整的标签数据
"""
from typing import Dict, Optional

from third_parties import init_third_parties_env
init_third_parties_env()

import os
import random
import cv2
import paddle
import shutil
import numpy as np
from tqdm import tqdm

import tools.program as program
from modules.utils.log import LOGGER
from ppocr.data import build_dataloader, set_signal_handlers
from ppocr.data.imaug.make_shrink_map import MakeShrinkMapAdvanced


def denormalize_image(
    image: np.ndarray,
    mean: Optional[np.ndarray] = None,
    std: Optional[np.ndarray] = None
) -> np.ndarray:
    """反归一化图像数据
    
    Args:
        image: 归一化后的图像数据，shape为[C,H,W]或[H,W,C]
        mean: 均值，默认为[0.485, 0.456, 0.406]
        std: 标准差，默认为[0.229, 0.224, 0.225]
        
    Returns:
        反归一化后的图像数据，shape为[H,W,C]，BGR格式
    """
    if mean is None:
        mean = np.array([0.485, 0.456, 0.406])
    if std is None:
        std = np.array([0.229, 0.224, 0.225])
    
    # 确保图像是HWC格式
    if image.shape[0] == 3:  # CHW -> HWC
        image = image.transpose(1, 2, 0)
    
    # 反归一化: (image * std + mean) * 255
    image = image * std + mean
    image = image * 255.
    image = np.clip(image, 0, 255).astype(np.uint8)
    
    # 注意：PaddleOCR中的图像已经是BGR格式，不需要进行颜色转换
    # image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
    
    return image


def draw_detection_results(
    image: np.ndarray,
    polys: np.ndarray,
    ignore_tags: np.ndarray,
    src_h: int,
    src_w: int,
) -> np.ndarray:
    """在图像上绘制检测结果
    
    Args:
        image: 输入图像，BGR格式，已经是原始尺寸
        polys: 多边形坐标点数组，shape为[N,4,2]，坐标是在原始图像尺寸下的坐标
        ignore_tags: 是否忽略的标记列表
        src_h: 原始图像高度
        src_w: 原始图像宽度

    Returns:
        绘制了检测框的图像，保持输入图像的尺寸不变
    """
    # 复制一份图像用于绘制
    vis_image = image.copy()
    ori_h, ori_w = image.shape[:2]
    
    if polys is None:
        return vis_image

    vis_image = cv2.resize(vis_image, (src_w, src_h))
    
    for idx, (poly, tag) in enumerate(zip(polys, ignore_tags)):
        if tag:
            color = (0, 0, 255)  # 红色表示忽略的检测框
        else:
            color = (0, 255, 0)  # 绿色表示有效的检测框
            
        # 坐标已经是在原始尺寸下的，不需要映射
        poly = poly.reshape(-1, 2)
        poly = poly.astype(np.int32)
        
        cv2.polylines(vis_image, [poly], True, color, 2)

    vis_image = cv2.resize(vis_image, (ori_w, ori_h))
    
    return vis_image


def save_supervision_map(
    map_data: np.ndarray,
    save_path: str,
    is_binary: bool = False
) -> None:
    """保存监督信号图
    
    Args:
        map_data: 监督信号数据
        save_path: 保存路径
        is_binary: 是否为二值图（mask）
    """
    if isinstance(map_data, paddle.Tensor):
        map_data = map_data.numpy()
    
    if is_binary:
        # 二值图直接保存
        cv2.imwrite(save_path, (map_data * 255).astype(np.uint8))
    else:
        # 连续值图像需要归一化到0-255
        map_vis = ((map_data - map_data.min()) / (map_data.max() - map_data.min()) * 255).astype(np.uint8)
        cv2.imwrite(save_path, map_vis)


def save_batch(batch: tuple, save_dir: str, batch_idx: int, mode: str) -> None:
    """保存一个batch的数据
    
    Args:
        batch: 数据batch
        save_dir: 保存目录
        batch_idx: batch索引
        mode: 数据集模式，'Train' 或 'Eval'/'Test'
    """
    # 获取图像数据
    images = batch[0]
    
    # 获取标签数据
    if mode == 'Train':
        # 训练模式：包含完整的监督信号
        threshold_maps = batch[1]   # 阈值图（Threshold Map），用于学习文本区域的边界
        shrink_maps = batch[3]      # 概率图（Probability Map）即 Shrink Map，用于学习文本区域的核心部分
        threshold_masks = batch[2]  # 标记有效的阈值图区域，
        shrink_masks = batch[4]     # 标记有效的训练区域，对于太小的文本或者被忽略的文本区域，mask=0

        # 创建子结果保存路径
        image_save_dir = os.path.join(save_dir, 'image')
        shrink_save_dir = os.path.join(save_dir, 'shrink')
        threshold_save_dir = os.path.join(save_dir, 'threshold')
        os.makedirs(image_save_dir, exist_ok=True)
        os.makedirs(shrink_save_dir, exist_ok=True)
        os.makedirs(threshold_save_dir, exist_ok=True)
        
        for idx in range(len(images)):
            # 获取当前样本的所有数据
            image = images[idx]
            shrink_map = shrink_maps[idx]
            shrink_mask = shrink_masks[idx]
            threshold_map = threshold_maps[idx]
            threshold_mask = threshold_masks[idx]
            
            # 转换数据类型
            if isinstance(image, paddle.Tensor):
                image = image.numpy()
            
            # 反归一化图像
            image = denormalize_image(image)
            
            # 生成样本ID
            sample_id = f"{batch_idx:04d}_{idx:04d}"
            
            # 1. 保存原始图像
            image_path = os.path.join(image_save_dir, f'{sample_id}.jpg')
            cv2.imwrite(image_path, image)

            # 2. 分别保存各个监督信号
            # 2.1 保存shrink map
            shrink_map_path = os.path.join(shrink_save_dir, f'{sample_id}_map.jpg')
            save_supervision_map(shrink_map, shrink_map_path)

            # 2.2 保存shrink mask
            shrink_mask_path = os.path.join(shrink_save_dir, f'{sample_id}_mask.jpg')
            save_supervision_map(shrink_mask, shrink_mask_path, is_binary=True)
            
            # 2.3 保存threshold map
            threshold_map_path = os.path.join(threshold_save_dir, f'{sample_id}_map.jpg')
            save_supervision_map(threshold_map, threshold_map_path)
            
            # 2.4 保存threshold mask
            threshold_mask_path = os.path.join(threshold_save_dir, f'{sample_id}_mask.jpg')
            save_supervision_map(threshold_mask, threshold_mask_path, is_binary=True)
            
    else:
        # Eval/Test模式：包含基本的检测标注信息
        # batch = (images, shapes, polys, ignore_tags)
        shapes = batch[1]       # 原始图像尺寸信息：[src_h, src_w, ratio_h, ratio_w]
        polys = batch[2]        # 检测框坐标
        ignore_tags = batch[3]  # 忽略标记

        # 创建子结果保存路径
        image_save_dir = os.path.join(save_dir, 'image')
        det_vis_save_dir = os.path.join(save_dir, 'det_vis')
        os.makedirs(image_save_dir, exist_ok=True)
        os.makedirs(det_vis_save_dir, exist_ok=True)
        
        for idx in range(len(images)):
            image = images[idx]
            shape = shapes[idx] if shapes is not None else None
            poly = polys[idx] if polys is not None else None
            ignore_tag = ignore_tags[idx] if ignore_tags is not None else None
            
            # 转换数据类型
            if isinstance(image, paddle.Tensor):
                image = image.numpy()
                if shape is not None:
                    shape = shape.numpy()
                if poly is not None:
                    poly = poly.numpy()
                if ignore_tag is not None:
                    ignore_tag = ignore_tag.numpy()
            
            # 反归一化图像
            image = denormalize_image(image)
            
            # 获取原始图像尺寸和缩放比例
            src_h, src_w = shape[:2] if shape is not None else (image.shape[0], image.shape[1])
            ratio_h, ratio_w = shape[2:] if shape is not None else (1.0, 1.0)
            
            # 保存原始图像
            sample_id = f'{batch_idx}_{idx}'
            image_path = os.path.join(image_save_dir, f'{sample_id}.jpg')
            # 将图像resize回原始尺寸后保存
            cv2.imwrite(image_path, image)
            
            # 保存可视化结果
            if poly is not None and ignore_tag is not None:
                # 在原始尺寸图像上绘制检测框
                det_vis = draw_detection_results(
                    image, poly, ignore_tag,
                    int(src_h), int(src_w),
                )
                det_vis_path = os.path.join(det_vis_save_dir, f'{sample_id}.jpg')
                cv2.imwrite(det_vis_path, det_vis)
            else:
                LOGGER.warning(f"Sample {sample_id} has no valid detection boxes, saving original image")
                det_vis_path = os.path.join(det_vis_save_dir, f'{sample_id}.jpg')
                cv2.imwrite(det_vis_path, image)


def visualize_dataset(
    config: Dict,
    save_dir: str,
    num_batches: int,
    mode: str,
    epoch: int = 0,
    seed: int = -1,
    random_sampling: bool = False
) -> None:
    """可视化数据集中指定数量的batch
    
    Args:
        config: 配置字典
        save_dir: 保存目录
        num_batches: 要保存的batch数量
        mode: 数据集模式，如'Train'/'Eval'/'Test'
    """
    if seed != -1:
        random.seed(seed)
        np.random.seed(seed)
        paddle.seed(seed)

    # 创建保存目录
    save_dir = os.path.join(save_dir, mode)
    if os.path.exists(save_dir):
        shutil.rmtree(save_dir)
    os.makedirs(save_dir)

    set_signal_handlers()

    if random_sampling:
        config[mode]['loader']['shuffle'] = True
    else:
        config[mode]['loader']['shuffle'] = False

    train_dataloader = build_dataloader(config, mode, 'cpu', LOGGER, seed=42)
    if hasattr(train_dataloader.dataset, "set_epoch"):
        train_dataloader.dataset.set_epoch(epoch)
        print(f"train_dataloader.dataset.set_epoch: {epoch}")
    
    for batch_idx, batch in tqdm(enumerate(train_dataloader), total=num_batches, desc=f'Saving {mode} dataset samples'):
        if num_batches > 0 and batch_idx >= num_batches:
            break
        save_batch(batch, save_dir, batch_idx, mode)
        
    print(f'Successfully saved {num_batches} batches to {save_dir}')


if __name__ == '__main__':
    save_dir = "/aipdf-mlp/xelawk/debug/ppocr_sample_analysis"

    if os.path.exists(save_dir):
        shutil.rmtree(save_dir)

    config, device, logger, vdl_writer = program.preprocess()
    epoch_num = config['Global']['epoch_num']

    for mode, num_batches in zip(['Train', 'Eval'], [4, 1]):
        epochs = list(np.linspace(1, epoch_num, 10, dtype=int))
        for epoch in epochs:
            cur_save_dir = os.path.join(save_dir, f"epoch_{epoch}")
            os.makedirs(cur_save_dir, exist_ok=True)
            visualize_dataset(config, cur_save_dir, num_batches, mode, epoch=epoch, seed=-1, random_sampling=True)
