#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/03/10 11:30
# <AUTHOR> <EMAIL>
# @FileName: grid_search_db_params.py
"""
DBPostProcess参数搜索脚本
主要功能：
1. 针对DBPostProcess的关键参数进行网格搜索
2. 评估每个参数组合的hmean值
3. 找出最佳参数组合

搜索参数：
- thresh：二值化阈值
- box_thresh：文本框置信度阈值
- unclip_ratio：扩展比例
- min_size：最小尺寸
"""

import os
import json
import time
import paddle
import argparse
import itertools

import numpy as np
import matplotlib.pyplot as plt

from tqdm import tqdm
from collections import OrderedDict

# 初始化第三方库环境
from third_parties import init_third_parties_env
init_third_parties_env()  # 必要操作，初始化项目子模块中的第三方库

# 导入ppocr相关模块
import tools.program as program
from ppocr.metrics import build_metric
from ppocr.postprocess import build_post_process
from ppocr.utils.save_load import load_model
from ppocr.modeling.architectures import build_model
from ppocr.data import build_dataloader, set_signal_handlers


def evaluate_with_params(config, model, valid_dataloader, post_process_params):
    """
    使用指定的后处理参数评估模型
    
    Args:
        config: 模型配置字典
        model: 训练好的模型
        valid_dataloader: 验证数据加载器
        post_process_params: 后处理参数字典
        
    Returns:
        hmean: 使用当前参数评估得到的hmean值
    """
    # 备份整个后处理配置
    original_post_process = config["PostProcess"].copy()
    
    # 使用参数替换原始配置，确保所有参数都被应用
    # 注意: 对于每个参数组合，我们都会更新所有相关参数
    for key, value in post_process_params.items():
        config["PostProcess"][key] = value
    
    # 全局生效的参数
    global_config = config["Global"]

    # 构建后处理器
    post_process_class = build_post_process(config["PostProcess"], global_config)
    
    # 创建评估指标
    eval_class = build_metric(config['Metric'])
    
    # 确定是否需要额外输入
    extra_input_models = [
        "SRN", "NRTR", "SAR", "SEED", "SVTR", "SVTR_LCNet",
        "VisionLAN", "RobustScanner", "SVTR_HGNet"
    ]
    extra_input = False
    if config["Architecture"]["algorithm"] == "Distillation":
        for key in config["Architecture"]["Models"]:
            extra_input = (
                extra_input
                or config["Architecture"]["Models"][key]["algorithm"]
                in extra_input_models
            )
    else:
        extra_input = config["Architecture"]["algorithm"] in extra_input_models
        
    # 获取模型类型
    model_type = config["Architecture"].get("model_type", None)
    
    # AMP相关参数
    scaler = None
    amp_level = config["Global"].get("amp_level", "O2")
    amp_custom_black_list = config["Global"].get("amp_custom_black_list", [])
    
    try:
        # 直接使用program.evaluate进行评估
        metrics = program.evaluate(
            model,
            valid_dataloader,
            post_process_class,
            eval_class,
            model_type,
            extra_input,
            scaler,
            amp_level,
            amp_custom_black_list,
        )
        hmean = metrics.get('hmean', 0.0)
    except Exception as e:
        print(f"评估过程出错: {e}")
        hmean = 0.0  # 出错时返回0
    finally:
        # 恢复原始后处理配置
        # 完全替换回原始配置，确保不遗漏任何参数
        config["PostProcess"] = original_post_process
    
    return hmean


def grid_search(config, model, valid_dataloader, param_grid, output_dir='./grid_search_results'):
    """
    网格搜索DBPostProcess的最佳参数
    
    Args:
        config: 模型配置字典
        model: 训练好的模型
        valid_dataloader: 验证数据加载器
        param_grid: 参数网格，字典格式，键为参数名，值为参数可选值列表
        output_dir: 结果输出目录
        
    Returns:
        best_params: 最佳参数组合
        best_hmean: 最佳hmean值
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有参数组合
    param_names = sorted(param_grid.keys())
    param_values = [param_grid[name] for name in param_names]
    param_combinations = list(itertools.product(*param_values))
    
    print(f"开始网格搜索，共 {len(param_combinations)} 个参数组合...")
    
    # 记录搜索结果
    results = []
    best_hmean = 0.0
    best_params = None
    
    # 进度条显示
    pbar = tqdm(param_combinations, desc="参数搜索进度")
    
    # 遍历所有参数组合
    for params in pbar:
        # 构建参数字典
        param_dict = {name: value for name, value in zip(param_names, params)}
        
        # 记录开始时间
        start_time = time.time()
        
        # 使用当前参数组合评估模型
        try:
            hmean = evaluate_with_params(config, model, valid_dataloader, param_dict)
            
            # 记录结果
            elapsed_time = time.time() - start_time
            result = OrderedDict(param_dict)
            result['hmean'] = hmean
            result['time'] = elapsed_time
            results.append(result)
            
            # 更新进度条描述
            pbar.set_description(f"当前hmean: {hmean:.5f}, 最佳hmean: {best_hmean:.5f}")
            
            # 更新最佳结果
            if hmean > best_hmean:
                best_hmean = hmean
                best_params = param_dict.copy()
                
                # 保存当前最佳参数
                with open(os.path.join(output_dir, 'best_params.json'), 'w') as f:
                    json.dump(best_params, f, indent=4)
            
        except Exception as e:
            print(f"参数组合 {param_dict} 评估失败: {e}")
    
    # 保存所有结果
    result_file = os.path.join(output_dir, 'grid_search_results.json')
    with open(result_file, 'w') as f:
        json.dump(results, f, indent=4)
    
    # 可视化主要参数的影响
    visualize_results(results, output_dir)
    
    print(f"\n搜索完成！")
    print(f"最佳参数: {best_params}")
    print(f"最佳hmean: {best_hmean:.5f}")
    print(f"详细结果保存至: {result_file}")
    
    return best_params, best_hmean


def visualize_results(results, output_dir):
    """
    可视化网格搜索结果
    
    Args:
        results: 搜索结果列表
        output_dir: 输出目录
    """
    # 转换为pandas DataFrame便于分析
    import pandas as pd
    df = pd.DataFrame(results)
    
    # 创建可视化目录
    vis_dir = os.path.join(output_dir, 'visualizations')
    os.makedirs(vis_dir, exist_ok=True)
    
    # 针对每个参数创建hmean分布图
    params = ['thresh', 'box_thresh', 'unclip_ratio', 'min_size']
    for param in params:
        if param in df.columns:
            plt.figure(figsize=(10, 6))
            # 按参数值分组并计算平均hmean
            grouped = df.groupby(param)['hmean'].mean().reset_index()
            plt.plot(grouped[param], grouped['hmean'], 'o-', linewidth=2, markersize=8)
            plt.xlabel(param)
            plt.ylabel('Average hmean')
            plt.title(f'Effect of {param} on hmean')
            plt.grid(True)
            plt.savefig(os.path.join(vis_dir, f'{param}_effect.png'))
            plt.close()
    
    # 创建热力图显示参数交互影响
    if len(params) >= 2:
        for i, param1 in enumerate(params):
            for param2 in params[i+1:]:
                if param1 in df.columns and param2 in df.columns:
                    # 创建交叉表
                    pivot = df.pivot_table(values='hmean', index=param1, columns=param2, aggfunc='mean')
                    
                    plt.figure(figsize=(10, 8))
                    plt.imshow(pivot, cmap='viridis', aspect='auto', interpolation='nearest')
                    plt.colorbar(label='hmean')
                    plt.xlabel(param2)
                    plt.ylabel(param1)
                    plt.title(f'Interaction between {param1} and {param2}')
                    
                    # 设置坐标轴标签
                    plt.xticks(range(len(pivot.columns)), pivot.columns)
                    plt.yticks(range(len(pivot.index)), pivot.index)
                    
                    plt.savefig(os.path.join(vis_dir, f'{param1}_{param2}_interaction.png'))
                    plt.close()


def main():
    # 处理命令行参数，获取output_dir参数
    # 注意：这里需要在program.preprocess前处理，因为preprocess会自己解析命令行参数
    import sys
    
    # 默认输出目录
    output_dir = './grid_search_results'
    
    # 遍历命令行参数，查找--output_dir参数
    i = 1
    while i < len(sys.argv):
        if sys.argv[i] == '--output_dir' and i + 1 < len(sys.argv):
            output_dir = sys.argv[i + 1]
            # 从命令行参数中移除output_dir参数，避免干扰program.preprocess
            del sys.argv[i:i+2]
            break
        elif sys.argv[i].startswith('--output_dir='):
            output_dir = sys.argv[i].split('=', 1)[1]
            # 从命令行参数中移除output_dir参数
            del sys.argv[i]
            break
        i += 1
    
    # 加载配置和初始化环境
    config, device, logger, vdl_writer = program.preprocess(is_train=False)
    
    # 设置评估模式
    mode = 'Test'
    os.environ["MODE"] = mode
    
    # 构建验证数据加载器
    set_signal_handlers()
    valid_dataloader = build_dataloader(config, mode, device, logger)
    
    # 构建模型
    model = build_model(config['Architecture'])
    
    # 加载训练好的模型参数
    load_model(config, model, model_type=config["Architecture"]["model_type"])
    model.eval()
    
    # 参数搜索空间定义
    param_grid = {
        'thresh': [0.35, 0.4, 0.45, 0.50],                   # 二值化阈值
        'box_thresh': [0.55, 0.6, 0.65, 0.70, 0.75],         # 文本框置信度阈值
        'unclip_ratio': [1.2, 1.3, 1.4, 1.5, 1.6],           # 扩展比例
        'min_text_size': [1, 2, 3],                          # 最小尺寸
        'min_text_size_delta': [4, 5, 6]                     # 最小尺寸最后一道工序的上限上涨幅度
    }
    
    # 执行网格搜索
    best_params, best_hmean = grid_search(
        config, model, valid_dataloader, param_grid, output_dir=output_dir
    )
    
    # 输出最终结果
    print("\n" + "="*50)
    print("网格搜索完成！最佳参数组合：")
    for key, value in best_params.items():
        print(f"{key}: {value}")
    print(f"最佳hmean: {best_hmean:.5f}")
    print("="*50)


if __name__ == '__main__':
    main()
