#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/04/02 16:30
# <AUTHOR> <EMAIL>
# @FileName: grid_search_openocr_db_params.py
"""
OpenOCR DBPostProcess参数搜索脚本
主要功能：
1. 针对DBPostProcess的关键参数进行网格搜索
2. 评估每个参数组合的hmean值
3. 找出最佳参数组合

搜索参数：
- thresh：二值化阈值
- box_thresh：文本框置信度阈值
- unclip_ratio：扩展比例
- min_size：最小尺寸
"""

# 初始化第三方库环境
from third_parties import init_third_parties_env
init_third_parties_env(target=['openocr'])

import os
import json
import time
import copy
import torch
import traceback
import itertools

import matplotlib.pyplot as plt

from tqdm import tqdm
from collections import OrderedDict

# 导入OpenOCR相关模块
from tools.utility import ArgsParser
from tools.engine.config import Config
from tools.engine.trainer import Trainer


def evaluate_with_params(cfg, post_process_params):
    """
    使用指定的后处理参数评估模型
    
    Args:
        cfg: 模型配置对象
        post_process_params: 后处理参数字典
        
    Returns:
        hmean: 使用当前参数评估得到的hmean值
    """
    # 创建配置的深拷贝，以免影响原始配置
    cfg_copy = copy.deepcopy(cfg)
    
    # 修改PostProcess配置
    if 'PostProcess' not in cfg_copy.cfg:
        cfg_copy.cfg['PostProcess'] = {}
    
    for key, value in post_process_params.items():
        cfg_copy.cfg['PostProcess'][key] = value
    
    try:
        # 为该参数组合创建一个新的trainer实例
        mode = os.environ.get('MODE', 'Test')  # Eval 或 Test
        trainer = Trainer(cfg_copy, mode=mode.lower(), task='det')
        
        # 执行评估
        total_metrics = trainer.evaluate()
        mode = mode.lower()
        metrics = total_metrics[mode]
        hmean = metrics.get('hmean', 0.0)
        
        # 显式销毁trainer，释放资源
        del trainer
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        return hmean

    except Exception as e:
        print(f'评估过程出错: {e}')
        traceback.print_exc()
        return 0.0


def grid_search(cfg, param_grid, output_dir='./grid_search_results'):
    """
    网格搜索DBPostProcess的最佳参数
    
    Args:
        cfg: 模型配置对象
        param_grid: 参数网格，字典格式，键为参数名，值为参数可选值列表
        output_dir: 结果输出目录
        
    Returns:
        best_params: 最佳参数组合
        best_hmean: 最佳hmean值
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有参数组合
    param_names = sorted(param_grid.keys())
    param_values = [param_grid[name] for name in param_names]
    param_combinations = list(itertools.product(*param_values))
    
    print(f"开始网格搜索，共 {len(param_combinations)} 个参数组合...")
    
    # 记录搜索结果
    results = []
    best_hmean = 0.0
    best_params = None  # 确保初始值为None
    
    # 进度条显示
    result_file = os.path.join(output_dir, 'grid_search_results.json')
    
    # 如果结果文件已经存在，则加载已有的结果
    if os.path.exists(result_file):
        try:
            with open(result_file, 'r') as f:
                results = json.load(f)
            print(f"加载了 {len(results)} 条已有的结果")
            
            # 更新最佳结果
            for result in results:
                if 'hmean' in result and result['hmean'] > best_hmean:
                    best_hmean = result['hmean']
                    best_params = {k: v for k, v in result.items() if k in param_names}
                    
            print(f"当前最佳hmean: {best_hmean:.5f}")
        except Exception as e:
            print(f"加载已有结果失败: {e}")
            results = []
    
    # 进度条显示
    pbar = tqdm(param_combinations, desc="参数搜索进度")
    
    # 遍历所有参数组合
    for params in pbar:
        # 构建参数字典
        param_dict = {name: value for name, value in zip(param_names, params)}
        
        # 检查是否已经评估过该参数组合
        already_evaluated = False
        for result in results:
            if all(result.get(name) == value for name, value in param_dict.items()):
                already_evaluated = True
                pbar.set_description(f"跳过已经评估过的参数组合 hmean: {result.get('hmean', 0.0):.5f}")
                break
                
        if already_evaluated:
            continue
        
        # 记录开始时间
        start_time = time.time()
        
        # 使用当前参数组合评估模型
        try:
            hmean = evaluate_with_params(cfg, param_dict)
            
            # 记录结果
            elapsed_time = time.time() - start_time
            result = OrderedDict(param_dict)
            result['hmean'] = hmean
            result['time'] = elapsed_time
            results.append(result)
            
            # 更新进度条描述
            pbar.set_description(f"当前hmean: {hmean:.5f}, 最佳hmean: {best_hmean:.5f}")
            
            # 更新最佳结果
            if hmean > best_hmean:
                best_hmean = hmean
                best_params = param_dict.copy()
                
                # 保存当前最佳参数
                with open(os.path.join(output_dir, 'best_params.json'), 'w') as f:
                    json.dump(best_params, f, indent=4)
            
            # 保存所有结果
            with open(result_file, 'w') as f:
                json.dump(results, f, indent=4)
            
        except Exception as e:
            print(f"参数组合 {param_dict} 评估失败: {e}")
            
            # 保存评估失败的结果
            result = OrderedDict(param_dict)
            result['hmean'] = 0.0
            result['time'] = time.time() - start_time
            result['error'] = str(e)
            results.append(result)
            
            # 保存所有结果
            with open(result_file, 'w') as f:
                json.dump(results, f, indent=4)
    
    # 可视化主要参数的影响
    visualize_results(results, output_dir)
    
    print(f"\n搜索完成！")
    print(f"最佳参数: {best_params}")
    print(f"最佳hmean: {best_hmean:.5f}")
    
    # 确保返回有效的最佳参数组合
    if best_params is None:
        print("注意: 没有找到有效的参数组合，返回默认参数")
        best_params = {name: param_grid[name][0] for name in param_names}
        best_hmean = 0.0
    
    return best_params, best_hmean


def visualize_results(results, output_dir):
    """
    可视化结果
    
    Args:
        results: 结果列表
        output_dir: 输出目录
    """
    # 转换为pandas DataFrame
    import pandas as pd
    df = pd.DataFrame(results)
    
    # 创建可视化目录
    vis_dir = os.path.join(output_dir, 'visualizations')
    os.makedirs(vis_dir, exist_ok=True)
    
    # 可视化每个参数的影响
    params = ['thresh', 'box_thresh', 'unclip_ratio', 'min_text_size', 'min_text_size_delta']
    for param in params:
        if param in df.columns:
            plt.figure(figsize=(10, 6))
            # 按参数分组计算平均hmean
            grouped = df.groupby(param)['hmean'].mean().reset_index()
            plt.plot(grouped[param], grouped['hmean'], 'o-', linewidth=2, markersize=8)
            plt.xlabel(param)
            plt.ylabel('Average hmean')
            plt.title(f'Effect of {param} on hmean')
            plt.grid(True)
            plt.savefig(os.path.join(vis_dir, f'{param}_effect.png'))
            plt.close()
    
    # 可视化参数之间的交互作用
    if len(params) >= 2:
        for i, param1 in enumerate(params):
            for param2 in params[i+1:]:
                if param1 in df.columns and param2 in df.columns:
                    # 创建交互作用图
                    pivot = df.pivot_table(values='hmean', index=param1, columns=param2, aggfunc='mean')
                    
                    plt.figure(figsize=(10, 8))
                    plt.imshow(pivot, cmap='viridis', aspect='auto', interpolation='nearest')
                    plt.colorbar(label='hmean')
                    plt.xlabel(param2)
                    plt.ylabel(param1)
                    plt.title(f'Interaction between {param1} and {param2}')
                    
                    # 设置x轴和y轴的刻度
                    plt.xticks(range(len(pivot.columns)), pivot.columns)
                    plt.yticks(range(len(pivot.index)), pivot.index)
                    
                    plt.savefig(os.path.join(vis_dir, f'{param1}_{param2}_interaction.png'))
                    plt.close()


def parse_args():
    """解析命令行参数"""
    parser = ArgsParser()
    args = parser.parse_args()
    return args


def main():
    # 处理命令行参数，获取output_dir参数
    # 注意：在ArgsParser中需要处理output_dir参数，否则会报错
    import sys
    
    # 默认输出目录
    output_dir = './grid_search_results'
    
    # 处理命令行参数，获取output_dir参数
    i = 1
    while i < len(sys.argv):
        if sys.argv[i] == '--output_dir' and i + 1 < len(sys.argv):
            output_dir = sys.argv[i + 1]
            # 从命令行参数中删除output_dir参数
            del sys.argv[i:i+2]
            break
        elif sys.argv[i].startswith('--output_dir='):
            output_dir = sys.argv[i].split('=', 1)[1]
            # 从命令行参数中删除output_dir参数
            del sys.argv[i]
            break
        i += 1
    
    # 解析命令行参数
    FLAGS = parse_args()
    cfg = Config(FLAGS.config)
    FLAGS = vars(FLAGS)
    opt = FLAGS.pop('opt')
    cfg.merge_dict(FLAGS)
    cfg.merge_dict(opt)
    
    # 定义参数网格
    param_grid = {
        'thresh': [0.17, 0.18, 0.19, 0.20],
        'box_thresh': [0.68, 0.70, 0.72],
        'unclip_ratio': [1.30, 1.35, 1.40, 1.45, 1.50],
        'min_text_size': [1],
        'min_text_size_delta': [6]
    }
    
    # 计算参数组合数
    total_combinations = 1
    for values in param_grid.values():
        total_combinations *= len(values)
    
    print(f"\n参数网格共有 {total_combinations} 个参数组合")
    confirmation = input("是否继续网格搜索? (yes/no): ")
    if confirmation.lower() != 'yes':
        print("网格搜索已取消")
        return
    
    # 执行网格搜索
    best_params, best_hmean = grid_search(cfg, param_grid, output_dir=output_dir)
    
    # 输出结果
    print("\n" + "="*50)
    print("网格搜索完成！")
    print("最佳参数组合：")
    for key, value in best_params.items():
        print(f"{key}: {value}")
    print(f"最佳hmean: {best_hmean:.5f}")
    print("="*50)


if __name__ == '__main__':
    tic = time.time()
    main()
    toc = time.time()
    print(f"网格搜索耗时: {(toc - tic) / 60:.2f} 分钟")
