#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/3/26 11:06
# <AUTHOR> <EMAIL>
# @FileName: visualize_openocr_dataset

"""
OpenOCR数据集可视化工具
主要功能：
1. 加载训练数据
2. 采样指定数量的batch
3. 将图像和标注信息保存到指定路径，包括：
   - 原始图像
   - 原始标注可视化
   - 训练监督信号可视化
   - 完整的标签数据
"""
from typing import Dict, Optional, Union

from third_parties import init_third_parties_env
init_third_parties_env(['openocr'])

import os
import random
import cv2
import torch
import shutil
import numpy as np
from tqdm import tqdm

from tools.utility import ArgsParser
from tools.engine.config import Config
from tools.data import build_dataloader
from tools.utils.logging import get_logger


def denormalize_image(
    image: np.ndarray,
    mean: Optional[np.ndarray] = None,
    std: Optional[np.ndarray] = None
) -> np.ndarray:
    """反归一化图像数据
    
    Args:
        image: 归一化后的图像数据，shape为[C,H,W]或[H,W,C]
        mean: 均值，默认为[0.485, 0.456, 0.406]
        std: 标准差，默认为[0.229, 0.224, 0.225]
        
    Returns:
        反归一化后的图像数据，shape为[H,W,C]，BGR格式
    """
    if mean is None:
        mean = np.array([0.485, 0.456, 0.406])
    if std is None:
        std = np.array([0.229, 0.224, 0.225])
    
    # 确保图像是HWC格式
    if image.shape[0] == 3:  # CHW -> HWC
        image = image.transpose(1, 2, 0)
    
    # 反归一化: (image * std + mean) * 255
    image = image * std + mean
    image = image * 255.
    image = np.clip(image, 0, 255).astype(np.uint8)
    
    # 注意：OpenOCR中的图像规范应与PaddleOCR保持一致，已经是BGR格式，不需要转换
    # image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
    
    return image


def draw_detection_results(
    image: np.ndarray,
    polys: np.ndarray,
    ignore_tags: np.ndarray,
    src_h: int,
    src_w: int,
) -> np.ndarray:
    """在图像上绘制检测结果
    
    Args:
        image: 输入图像，BGR格式
        polys: 多边形坐标点数组
        ignore_tags: 是否忽略的标记数组
        src_h: 原始图像高度
        src_w: 原始图像宽度

    Returns:
        绘制了检测框的图像
    """
    # 复制一份图像用于绘制
    vis_image = image.copy()
    ori_h, ori_w = image.shape[:2]
    
    if polys is None or len(polys) == 0:
        return vis_image

    # 缩放到原始图像尺寸
    vis_image = cv2.resize(vis_image, (src_w, src_h))
    
    for poly, tag in zip(polys, ignore_tags):
        if tag:
            color = (0, 0, 255)  # 红色表示忽略的检测框
        else:
            color = (0, 255, 0)  # 绿色表示有效的检测框
            
        # 检查是否为Tensor类型，若是则转换为NumPy数组
        if isinstance(poly, torch.Tensor):
            poly = poly.detach().cpu().numpy()
            
        # 转换为整数坐标
        poly = poly.reshape(-1, 2)
        poly = poly.astype(np.int32)
        
        cv2.polylines(vis_image, [poly], True, color, 2)

    # 缩放回原始尺寸
    vis_image = cv2.resize(vis_image, (ori_w, ori_h))
    
    return vis_image


def save_supervision_map(
    map_data: Union[np.ndarray, torch.Tensor],
    save_path: str,
    is_binary: bool = False
) -> None:
    """保存监督信号图
    
    Args:
        map_data: 监督信号数据
        save_path: 保存路径
        is_binary: 是否为二值图（mask）
    """
    if isinstance(map_data, torch.Tensor):
        map_data = map_data.detach().cpu().numpy()
    
    if is_binary:
        # 二值图直接保存
        cv2.imwrite(save_path, (map_data * 255).astype(np.uint8))
    else:
        # 连续值图像需要归一化到0-255
        if map_data.min() != map_data.max():
            map_vis = ((map_data - map_data.min()) / (map_data.max() - map_data.min()) * 255).astype(np.uint8)
        else:
            map_vis = (map_data * 255).astype(np.uint8)
        cv2.imwrite(save_path, map_vis)


def save_batch(batch: tuple, save_dir: str, batch_idx: int, mode: str) -> None:
    """保存一个batch的数据
    
    Args:
        batch: 数据batch，元组格式
        save_dir: 保存目录
        batch_idx: batch索引
        mode: 数据集模式，'Train' 或 'Eval'/'Test'
    """
    # 获取图像数据
    images = batch[0]
    
    if images is None:
        print(f"Batch {batch_idx} has no image data, skipping...")
        return
    
    if isinstance(images, torch.Tensor):
        images = images.detach().cpu().numpy()
    
    # 根据模式处理不同的数据
    if mode == 'Train':
        # 训练模式：包含完整的监督信号
        threshold_maps = batch[1]   # 阈值图（Threshold Map），用于学习文本区域的边界
        shrink_maps = batch[3]      # 概率图（Probability Map）即 Shrink Map，用于学习文本区域的核心部分
        threshold_masks = batch[2]  # 标记有效的阈值图区域，
        shrink_masks = batch[4]     # 标记有效的训练区域，对于太小的文本或者被忽略的文本区域，mask=0

        # 创建子结果保存路径
        image_save_dir = os.path.join(save_dir, 'image')
        shrink_save_dir = os.path.join(save_dir, 'shrink')
        threshold_save_dir = os.path.join(save_dir, 'threshold')
        os.makedirs(image_save_dir, exist_ok=True)
        os.makedirs(shrink_save_dir, exist_ok=True)
        os.makedirs(threshold_save_dir, exist_ok=True)

        batch_size = len(images) if isinstance(images, list) else images.shape[0]
        for idx in range(batch_size):
            # 获取当前样本的图像
            if isinstance(images, list):
                image = images[idx]
            else:
                image = images[idx]

            # 反归一化图像
            image = denormalize_image(image)

            # 生成样本ID
            sample_id = f"{batch_idx:04d}_{idx:04d}"

            # 1. 保存原始图像
            image_path = os.path.join(image_save_dir, f'{sample_id}.jpg')
            cv2.imwrite(image_path, image)

            # 2. 保存监督信号
            if threshold_maps is not None and idx < len(threshold_maps):
                threshold_map = threshold_maps[idx]
                threshold_map_path = os.path.join(threshold_save_dir, f'{sample_id}_map.jpg')
                save_supervision_map(threshold_map, threshold_map_path)

            if threshold_masks is not None and idx < len(threshold_masks):
                threshold_mask = threshold_masks[idx]
                threshold_mask_path = os.path.join(threshold_save_dir, f'{sample_id}_mask.jpg')
                save_supervision_map(threshold_mask, threshold_mask_path, is_binary=True)

            if shrink_maps is not None and idx < len(shrink_maps):
                shrink_map = shrink_maps[idx]
                shrink_map_path = os.path.join(shrink_save_dir, f'{sample_id}_map.jpg')
                save_supervision_map(shrink_map, shrink_map_path)

            if shrink_masks is not None and idx < len(shrink_masks):
                shrink_mask = shrink_masks[idx]
                shrink_mask_path = os.path.join(shrink_save_dir, f'{sample_id}_mask.jpg')
                save_supervision_map(shrink_mask, shrink_mask_path, is_binary=True)

    else:
        # Eval/Test模式：包含基本的检测标注信息
        # 预期batch格式: (images, shapes, polys, ignore_tags)
        shapes = batch[1] if len(batch) > 1 else None    # 原始图像尺寸信息：[src_h, src_w, ratio_h, ratio_w]
        polys = batch[2] if len(batch) > 2 else None     # 检测框坐标
        ignore_tags = batch[3] if len(batch) > 3 else None  # 忽略标记
        
        # 创建子结果保存路径
        image_save_dir = os.path.join(save_dir, 'image')
        det_vis_save_dir = os.path.join(save_dir, 'det_vis')
        os.makedirs(image_save_dir, exist_ok=True)
        os.makedirs(det_vis_save_dir, exist_ok=True)
        
        batch_size = len(images) if isinstance(images, list) else images.shape[0]
        for idx in range(batch_size):
            image = images[idx] if isinstance(images, list) else images[idx]
            shape = shapes[idx] if shapes is not None and idx < len(shapes) else None
            poly = polys[idx] if polys is not None and idx < len(polys) else None
            ignore_tag = ignore_tags[idx] if ignore_tags is not None and idx < len(ignore_tags) else None
            
            # 转换数据类型
            if isinstance(image, torch.Tensor):
                image = image.detach().cpu().numpy()
                if shape is not None and isinstance(shape, torch.Tensor):
                    shape = shape.detach().cpu().numpy()
                if poly is not None and isinstance(poly, torch.Tensor):
                    poly = poly.detach().cpu().numpy()
                if ignore_tag is not None and isinstance(ignore_tag, torch.Tensor):
                    ignore_tag = ignore_tag.detach().cpu().numpy()
            
            # 反归一化图像
            image = denormalize_image(image)
            
            # 获取原始图像尺寸和缩放比例
            src_h, src_w = shape[:2] if shape is not None else (image.shape[0], image.shape[1])
            ratio_h, ratio_w = shape[2:] if shape is not None and len(shape) > 2 else (1.0, 1.0)

            if isinstance(src_h, torch.Tensor):
                src_h = int(src_h.detach().cpu().numpy())

            if isinstance(src_w, torch.Tensor):
                src_w = int(src_w.detach().cpu().numpy())

            if isinstance(ratio_h, torch.Tensor):
                ratio_h = float(ratio_h.detach().cpu().numpy())

            if isinstance(ratio_w, torch.Tensor):
                ratio_w = float(ratio_w.detach().cpu().numpy())

            # 保存原始图像
            sample_id = f'{batch_idx:04d}_{idx:04d}'
            image_path = os.path.join(image_save_dir, f'{sample_id}.jpg')
            cv2.imwrite(image_path, image)
            
            # 保存可视化结果
            if poly is not None and ignore_tag is not None:
                # 在原始尺寸图像上绘制检测框
                det_vis = draw_detection_results(
                    image, poly, ignore_tag, src_h, src_w
                )
                det_vis_path = os.path.join(det_vis_save_dir, f'{sample_id}.jpg')
                cv2.imwrite(det_vis_path, det_vis)
            else:
                print(f"Sample {sample_id} has no valid detection boxes, saving original image")
                det_vis_path = os.path.join(det_vis_save_dir, f'{sample_id}.jpg')
                cv2.imwrite(det_vis_path, image)


def visualize_dataset(
    config: Dict,
    save_dir: str,
    num_batches: int,
    mode: str,
    epoch: int = 0,
    seed: int = -1,
    random_sampling: bool = True,
    task: str = 'det'
) -> None:
    """可视化数据集中指定数量的batch
    
    Args:
        config: 配置字典
        save_dir: 保存目录
        num_batches: 要保存的batch数量
        mode: 数据集模式，如'Train'/'Eval'/'Test'
        epoch: 数据集的epoch
        seed: 随机种子
        random_sampling: 是否随机采样
        task: 任务类型，'det'表示检测，'rec'表示识别
    """
    if seed != -1:
        random.seed(seed)
        np.random.seed(seed)
        torch.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)

    # 创建保存目录
    save_dir = os.path.join(save_dir, mode)
    if os.path.exists(save_dir):
        shutil.rmtree(save_dir)
    os.makedirs(save_dir)

    logger = get_logger()
    
    if random_sampling:
        config[mode]['loader']['shuffle'] = True
    else:
        config[mode]['loader']['shuffle'] = False
    
    # 构建数据加载器
    train_dataloader = build_dataloader(config=config, mode=mode, logger=logger, seed=seed, task=task)
    
    # 对于一些实现了set_epoch方法的数据集，设置epoch
    if hasattr(train_dataloader.dataset, "set_epoch"):
        train_dataloader.dataset.set_epoch(epoch)
        print(f"train_dataloader.dataset.set_epoch: {epoch}")
    
    # 遍历指定数量的batch
    for batch_idx, batch in tqdm(enumerate(train_dataloader), total=num_batches, desc=f'Saving {mode} dataset samples'):
        if 0 < num_batches <= batch_idx:
            break
        save_batch(batch, save_dir, batch_idx, mode)
        
    print(f'Successfully saved {min(num_batches, batch_idx+1)} batches to {save_dir}')


def parse_args():
    parser = ArgsParser()
    parser.add_argument(
        '--eval',
        action='store_true',
        help='Whether to perform evaluation in train',
    )
    args = parser.parse_args()
    return args


def init_cfg():
    FLAGS = parse_args()
    cfg = Config(FLAGS.config)
    FLAGS = vars(FLAGS)
    opt = FLAGS.pop('opt')
    cfg.merge_dict(FLAGS)
    cfg.merge_dict(opt)
    return cfg


if __name__ == '__main__':
    save_dir = "/aipdf-mlp/xelawk/debug/openocr_sample_analysis_0430"
    
    if os.path.exists(save_dir):
        shutil.rmtree(save_dir)

    config = init_cfg()
    epoch_num = config.cfg['Global']['epoch_num']

    # 可以根据需要设置不同的模式和epoch
    for mode, num_batches in zip(['Train', 'Eval'], [200, 200]):
        epochs = list(np.linspace(1, epoch_num, 2, dtype=int))
        for epoch in epochs:
            cur_save_dir = os.path.join(save_dir, f"epoch_{epoch}")
            os.makedirs(cur_save_dir, exist_ok=True)
            visualize_dataset(config.cfg, cur_save_dir, num_batches, mode, epoch=epoch, seed=520, random_sampling=True)
