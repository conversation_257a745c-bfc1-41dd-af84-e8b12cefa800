#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/2/23 17:13
# <AUTHOR> <EMAIL>
# @FileName: train_paddleocr

# Copyright (c) 2020 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from third_parties import init_third_parties_env
init_third_parties_env()  # 必要操作，初始化项目子模块中的第三方库

import os
import time

import paddle
import paddle.distributed as dist
import tools.program as program
import tools.naive_sync_bn as naive_sync_bn

# 导入OCR模型训练所需的各个组件
from ppocr.losses import build_loss
from ppocr.metrics import build_metric
from ppocr.optimizer import build_optimizer
from ppocr.utils.utility import set_seed
from ppocr.utils.save_load import load_model
from ppocr.postprocess import build_post_process
from ppocr.modeling.architectures import build_model
from ppocr.modeling.architectures import apply_to_static
from ppocr.data import build_dataloader, set_signal_handlers

# 获取分布式训练环境中的进程数量
WORLD_SIZE = dist.get_world_size()


def main(config, device, logger, vdl_writer, seed):
    """
    训练主函数
    Args:
        config: 配置字典，包含模型、训练、优化器等所有配置信息
        device: 设备信息，指定训练使用的硬件设备
        logger: 日志记录器，用于记录训练过程中的信息
        vdl_writer: VisualDL写入器，用于可视化训练过程
        seed: 随机种子，确保实验可复现性
    """
    # 初始化分布式训练环境
    if config["Global"]["distributed"]:
        dist.init_parallel_env()
    global_config = config["Global"]

    # 设置信号处理器，用于优雅地处理程序中断
    set_signal_handlers()

    # 构建训练数据加载器
    train_dataloader = build_dataloader(config, "Train", device, logger, seed)
    if len(train_dataloader) == 0:
        logger.error(
            "No Images in train dataset, please ensure\n"
            + "\t1. The images num in the train label_file_list should be larger than or equal with batch size.\n"
            + "\t2. The annotation file and path in the configuration file are provided normally."
        )
        return

    # 构建验证数据加载器（如果配置中启用了评估）
    if config["Eval"]:
        valid_dataloader = build_dataloader(config, "Eval", device, logger, seed)
    else:
        valid_dataloader = None
    step_pre_epoch = len(train_dataloader)

    # 构建后处理类，用于处理模型输出
    post_process_class = build_post_process(config["PostProcess"], global_config)

    # 构建模型：根据算法类型进行特殊处理
    # 对于文本识别模型，一般是CTCLabelDecode，需要根据字符表大小设置输出通道数
    if hasattr(post_process_class, "character"):
        char_num = len(getattr(post_process_class, "character"))
        # 处理蒸馏模型的情况
        if config["Architecture"]["algorithm"] in ["Distillation"]:
            for key in config["Architecture"]["Models"]:
                if config["Architecture"]["Models"][key]["Head"]["name"] == "MultiHead":
                    # 根据不同的解码器类型调整字符数量
                    if config["PostProcess"]["name"] == "DistillationSARLabelDecode":
                        char_num = char_num - 2
                    if config["PostProcess"]["name"] == "DistillationNRTRLabelDecode":
                        char_num = char_num - 3
                    out_channels_list = {"CTCLabelDecode": char_num}

                    # 配置SAR损失函数的参数
                    if list(config["Loss"]["loss_config_list"][-1].keys())[0] == "DistillationSARLoss":
                        config["Loss"]["loss_config_list"][-1]["DistillationSARLoss"]["ignore_index"] = (char_num + 1)
                        out_channels_list["SARLabelDecode"] = char_num + 2
                    elif any("DistillationNRTRLoss" in d for d in config["Loss"]["loss_config_list"]):
                        out_channels_list["NRTRLabelDecode"] = char_num + 3
                    config["Architecture"]["Models"][key]["Head"]["out_channels_list"] = out_channels_list

                else:
                    config["Architecture"]["Models"][key]["Head"]["out_channels"] = char_num
        # 多头模型
        elif config["Architecture"]["Head"]["name"] == "MultiHead":
            if config["PostProcess"]["name"] == "SARLabelDecode":
                char_num = char_num - 2
            if config["PostProcess"]["name"] == "NRTRLabelDecode":
                char_num = char_num - 3
            out_channels_list = {"CTCLabelDecode": char_num}

            # 更新SAR损失参数
            if list(config["Loss"]["loss_config_list"][1].keys())[0] == "SARLoss":
                if config["Loss"]["loss_config_list"][1]["SARLoss"] is None:
                    config["Loss"]["loss_config_list"][1]["SARLoss"] = {
                        "ignore_index": char_num + 1
                    }
                else:
                    config["Loss"]["loss_config_list"][1]["SARLoss"]["ignore_index"] = (char_num + 1)
                out_channels_list["SARLabelDecode"] = char_num + 2
            elif list(config["Loss"]["loss_config_list"][1].keys())[0] == "NRTRLoss":
                out_channels_list["NRTRLabelDecode"] = char_num + 3
            config["Architecture"]["Head"]["out_channels_list"] = out_channels_list
        # 基础识别模型
        else:
            config["Architecture"]["Head"]["out_channels"] = char_num

        if config["PostProcess"]["name"] == "SARLabelDecode":  # SAR模型
            config["Loss"]["ignore_index"] = char_num - 1

    # 构建模型架构
    model = build_model(config["Architecture"])

    # 配置同步批归一化，用于多卡训练时同步BN统计信息
    use_sync_bn = config["Global"].get("use_sync_bn", False)
    if use_sync_bn:
        if config["Global"].get("use_npu", False):
            naive_sync_bn.convert_syncbn(model)
        else:
            model = paddle.nn.SyncBatchNorm.convert_sync_batchnorm(model)
        logger.info("convert_sync_batchnorm")

    # 将模型转换为静态图模式（如果配置指定）
    model = apply_to_static(model, config, logger)

    # 构建损失函数
    loss_class = build_loss(config["Loss"])

    # 构建优化器和学习率调度器
    optimizer, lr_scheduler = build_optimizer(
        config["Optimizer"],
        epochs=config["Global"]["epoch_num"],
        step_each_epoch=len(train_dataloader),
        model=model,
    )

    # 构建评估指标
    eval_class = build_metric(config["Metric"])

    logger.info("train dataloader has {} iters".format(len(train_dataloader)))
    if valid_dataloader is not None:
        logger.info("valid dataloader has {} iters".format(len(valid_dataloader)))

    # 配置自动混合精度训练
    use_amp = config["Global"].get("use_amp", False)
    amp_level = config["Global"].get("amp_level", "O2")  # O2级别：权重使用FP32，计算使用FP16
    amp_dtype = config["Global"].get("amp_dtype", "float16")
    amp_custom_black_list = config["Global"].get("amp_custom_black_list", [])
    amp_custom_white_list = config["Global"].get("amp_custom_white_list", [])

    # 清理旧的训练结果文件
    if os.path.exists(os.path.join(config["Global"]["save_model_dir"], "train_result.json")):
        try:
            os.remove(os.path.join(config["Global"]["save_model_dir"], "train_result.json"))
        except:
            pass

    # 配置自动混合精度训练环境
    if use_amp:
        # 设置AMP相关标志
        AMP_RELATED_FLAGS_SETTING = {
            "FLAGS_max_inplace_grad_add": 8,  # 梯度累加的最大次数
        }
        if paddle.is_compiled_with_cuda():
            AMP_RELATED_FLAGS_SETTING.update(
                {
                    "FLAGS_cudnn_batchnorm_spatial_persistent": 1,  # 使用持久化的cudnn batchnorm算法
                    "FLAGS_gemm_use_half_precision_compute_type": 0,  # 矩阵乘法不使用半精度计算
                }
            )
        paddle.set_flags(AMP_RELATED_FLAGS_SETTING)

        # 配置损失缩放器，用于处理FP16计算中的数值溢出
        scale_loss = config["Global"].get("scale_loss", 1.0)
        use_dynamic_loss_scaling = config["Global"].get(
            "use_dynamic_loss_scaling", False
        )
        scaler = paddle.amp.GradScaler(
            init_loss_scaling=scale_loss,
            use_dynamic_loss_scaling=use_dynamic_loss_scaling,
        )

        # 配置O2级别的AMP优化
        if amp_level == "O2":
            model, optimizer = paddle.amp.decorate(
                models=model,
                optimizers=optimizer,
                level=amp_level,
                master_weight=True,
                dtype=amp_dtype,
            )
    else:
        scaler = None

    # 加载预训练模型
    pre_best_model_dict = load_model(config, model, optimizer, config["Architecture"]["model_type"])

    # 分布式训练
    if config["Global"]["distributed"]:
        model = paddle.DataParallel(model)

    # 检查是否存在预训练模型，并在开始训练前进行评估
    pretrained_model = config["Global"].get("pretrained_model")
    eval_pretrained_first = config["Global"].get("eval_pretrained_first", False)
    if eval_pretrained_first and pretrained_model and valid_dataloader is not None and dist.get_rank() == 0:
        logger.info("=" * 50)
        logger.info("发现预训练模型: {}，在开始训练前进行评估...".format(pretrained_model))
        logger.info("Found pretrained model: {}, evaluating before training...".format(pretrained_model))
        try:
            model_type = config["Architecture"]["model_type"]
        except:
            model_type = None

        algorithm = config["Architecture"]["algorithm"]

        extra_input_models = [
            "SRN", "NRTR", "SAR", "SEED", "SVTR", "SVTR_LCNet", "SPIN", 
            "VisionLAN", "RobustScanner", "RFL", "DRRG", "SATRN", 
            "SVTR_HGNet", "ParseQ", "CPPD"
        ]
        
        extra_input = False
        if algorithm == "Distillation":
            for key in config["Architecture"]["Models"]:
                extra_input = (
                    extra_input
                    or config["Architecture"]["Models"][key]["algorithm"]
                    in extra_input_models
                )
        else:
            extra_input = algorithm in extra_input_models
        
        # 评估预训练模型
        cur_metric = program.evaluate(
            model,
            valid_dataloader,
            post_process_class,
            eval_class,
            model_type,
            extra_input=extra_input,
            scaler=scaler,
            amp_level=amp_level,
            amp_custom_black_list=amp_custom_black_list,
            amp_custom_white_list=amp_custom_white_list,
            amp_dtype=amp_dtype,
        )
        
        # 打印预训练模型的指标（中英双语）
        metric_items = ", ".join(["{}: {}".format(k, v) for k, v in cur_metric.items()])
        logger.info("原始预训练模型评估指标 / Pretrained model metrics [{}]: {}".format(
            pretrained_model, metric_items
        ))
        logger.info("=" * 50)

    # 开始训练
    program.train(
        config,
        train_dataloader,
        valid_dataloader,
        device,
        model,
        loss_class,
        optimizer,
        lr_scheduler,
        post_process_class,
        eval_class,
        pre_best_model_dict,
        logger,
        step_pre_epoch,
        vdl_writer,
        scaler,
        amp_level,
        amp_custom_black_list,
        amp_custom_white_list,
        amp_dtype,
    )
    
    # 同步所有进程，确保训练和评估全部完成
    if config["Global"]["distributed"]:
        paddle.device.cuda.synchronize()
        dist.barrier()
        # 优雅地关闭分布式进程组，避免NCCL超时问题
        logger.info("正在关闭分布式进程组...")
        dist.destroy_process_group()
        logger.info("分布式进程组已关闭")


def test_reader(config, device, logger):
    """
    测试数据读取器
    Args:
        config: 配置字典
        device: 设备信息
        logger: 日志记录器
    """
    loader = build_dataloader(config, "Train", device, logger)

    count = 0
    starttime = time.time()
    try:
        for data in loader():
            count += 1
            if count % 1 == 0:
                batch_time = time.time() - starttime
                starttime = time.time()
                logger.info("reader: {}, {}, {}".format(count, len(data[0]), batch_time))
    except Exception as e:
        logger.info(e)

    logger.info("finish reader: {}, Success!".format(count))


if __name__ == "__main__":
    # 预处理配置，初始化设备和日志记录器
    config, device, logger, vdl_writer = program.preprocess(is_train=True)
    # 设置随机种子以确保实验可复现
    seed = config["Global"]["seed"] if "seed" in config["Global"] else 1024
    set_seed(seed)
    main(config, device, logger, vdl_writer, seed)
    # test_reader(config, device, logger)
