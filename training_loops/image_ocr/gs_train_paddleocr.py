#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/03/03 23:44
# <AUTHOR> <EMAIL>
# @FileName: gs_train_paddleocr

from typing import Dict, List, Any

# 必要操作，初始化项目子模块中的第三方库
from third_parties import init_third_parties_env
init_third_parties_env()

import os
import re
import sys
import yaml
import copy
import json
import time
import logging
import argparse
import itertools
import subprocess
from pathlib import Path
from ppocr.utils.utility import set_seed


class GridSearchTrainer:
    """网格搜索训练器，用于PaddleOCR模型参数优化"""
    
    def __init__(self, config_path: str, output_dir: str):
        """
        初始化网格搜索训练器
        
        Args:
            config_path: 配置文件路径
            output_dir: 输出目录，用于保存所有实验结果
        """
        self.config_path = config_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载基础配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.base_config = yaml.safe_load(f)
            
        # 创建日志目录
        self.log_dir = self.output_dir / 'logs'
        self.log_dir.mkdir(exist_ok=True)
        
        # 设置日志
        self.logger = self._setup_logger()
        
        # 搜索结果记录
        self.search_results = []
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('grid_search')
        logger.setLevel(logging.INFO)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 创建文件处理器
        file_handler = logging.FileHandler(self.log_dir / f'grid_search_{time.strftime("%Y%m%d_%H%M%S")}.log')
        file_handler.setLevel(logging.INFO)
        
        # 创建格式器
        formatter = logging.Formatter("[%(levelname)s %(asctime)s] %(message)s")
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)
        
        # 添加处理器到日志记录器
        logger.addHandler(console_handler)
        logger.addHandler(file_handler)
        
        return logger
            
    def parse_metrics_from_log(self, log_file: str) -> List[Dict[str, float]]:
        """
        从训练日志中解析所有评估指标，包括precision, recall, hmean和fps
        
        Args:
            log_file: 日志文件路径
            
        Returns:
            包含所有指标的字典列表
        """
        metrics_list = []
        # 匹配当前epoch的评估指标行，如：
        # cur metric, precision: 0.9058977875842328, recall: 0.7759549815686642, hmean: 0.8359065834544678, fps: 59.845530091815704
        metrics_pattern = r'cur metric, precision: ([\d.]+), recall: ([\d.]+), hmean: ([\d.]+), fps: ([\d.]+)'
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            matches = re.findall(metrics_pattern, content)
            if matches:
                for match in matches:
                    metrics_list.append({
                        'precision': float(match[0]),
                        'recall': float(match[1]),
                        'hmean': float(match[2]),
                        'fps': float(match[3])
                    })
                
            return metrics_list

        except Exception as e:
            self.logger.error(f"解析日志文件失败: {e}")
            return []
    
    def calculate_metrics(self, log_file: str) -> Dict[str, Any]:
        """
        计算实验指标，包括precision, recall, hmean的统计数据
        
        Args:
            log_file: 日志文件路径
            
        Returns:
            包含各项指标统计数据的字典
        """
        # 获取所有评估指标记录
        metrics_list = self.parse_metrics_from_log(log_file)
        
        # 如果没有指标记录，返回空结果
        if not metrics_list:
            return {
                'precision_values': [],
                'recall_values': [],
                'hmean_values': [],
                'fps_values': [],
                'avg_precision': 0.0,
                'avg_recall': 0.0,
                'avg_hmean': 0.0,
                'avg_fps': 0.0,
                'max_precision': 0.0,
                'max_recall': 0.0,
                'max_hmean': 0.0,
                'max_fps': 0.0,
                'min_precision': 0.0,
                'min_recall': 0.0,
                'min_hmean': 0.0,
                'min_fps': 0.0,
                'last_precision': 0.0,
                'last_recall': 0.0,
                'last_hmean': 0.0,
                'last_fps': 0.0
            }
        
        # 提取各个指标的值列表
        precision_values = [m['precision'] for m in metrics_list]
        recall_values = [m['recall'] for m in metrics_list]
        hmean_values = [m['hmean'] for m in metrics_list]
        fps_values = [m['fps'] for m in metrics_list]
        
        # 计算统计数据
        metrics = {
            # 原始值列表
            'precision_values': precision_values,
            'recall_values': recall_values,
            'hmean_values': hmean_values,
            'fps_values': fps_values,
            
            # 平均值
            'avg_precision': sum(precision_values) / len(precision_values),
            'avg_recall': sum(recall_values) / len(recall_values),
            'avg_hmean': sum(hmean_values) / len(hmean_values),
            'avg_fps': sum(fps_values) / len(fps_values),
            
            # 最大值
            'max_precision': max(precision_values),
            'max_recall': max(recall_values),
            'max_hmean': max(hmean_values),
            'max_fps': max(fps_values),
            
            # 最小值
            'min_precision': min(precision_values),
            'min_recall': min(recall_values),
            'min_hmean': min(hmean_values),
            'min_fps': min(fps_values),
            
            # 最后一次的值
            'last_precision': precision_values[-1],
            'last_recall': recall_values[-1],
            'last_hmean': hmean_values[-1],
            'last_fps': fps_values[-1]
        }
        
        return metrics
    
    def run_experiment(
        self, 
        config: Dict[str, Any], 
        exp_name: str, 
        exp_id: int,
        devices: str,
    ) -> Dict[str, Any]:
        """
        运行单次实验
        
        Args:
            config: 配置字典
            exp_name: 实验名称
            exp_id: 实验ID
            
        Returns:
            实验结果信息
        """
        # 创建实验目录
        exp_dir = self.output_dir / f'exp_{exp_id}_{exp_name}'
        exp_dir.mkdir(exist_ok=True)
        
        # 获取模型别名，用于拼接保存路径
        model_alias = config['Global'].get('model_alias', '')
        
        # 更新保存路径
        base_save_model_dir = str(exp_dir / 'models')
        base_save_inference_dir = str(exp_dir / 'inference')
        
        # 如果有模型别名，将其拼接到保存路径中
        if model_alias:
            config['Global']['save_model_dir'] = os.path.join(base_save_model_dir, model_alias)
            config['Global']['save_inference_dir'] = os.path.join(base_save_inference_dir, model_alias)
            config['Global']['save_res_path'] = str(exp_dir / f'predicts_{model_alias}.txt')
        else:
            config['Global']['save_model_dir'] = base_save_model_dir
            config['Global']['save_inference_dir'] = base_save_inference_dir
            config['Global']['save_res_path'] = str(exp_dir / 'predicts.txt')
        
        # 保存配置到实验目录
        config_file = exp_dir / 'config.yml'
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False)
        
        self.logger.info(f"开始实验 {exp_id}: {exp_name}")
        self.logger.info(f"配置保存在: {config_file}")
        self.logger.info(f"模型将保存在: {config['Global']['save_model_dir']}")
        
        # 设置随机种子
        seed = config['Global'].get('seed', 1024)
        set_seed(seed)
        
        try:
            # 构建命令
            cmd = [
                sys.executable,
                "-m", "paddle.distributed.launch",
                f"--devices={devices}",
                "training_loops/image_ocr/train_paddleocr.py",
                "-c", str(config_file),
                "-o", f"Global.seed={seed}"
            ]
            
            # 如果有预训练模型，添加到命令
            if config['Global'].get('pretrained_model'):
                cmd.extend([
                    "-o", f"Global.pretrained_model={config['Global']['pretrained_model']}"
                ])

            # 启动子进程
            self.logger.info(f"执行命令: {' '.join(cmd)}")
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                cwd=os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            )

            # 获取输出
            for line in process.stdout:
                self.logger.info(line.strip())

            # 等待进程结束
            process.wait()

            # 检查子进程是否成功
            if process.returncode != 0:
                raise Exception(f"训练进程返回错误代码: {process.returncode}")

            # 分析日志
            log_file = os.path.join(config['Global']['save_model_dir'], 'train.log')
            metrics = self.calculate_metrics(log_file)

            # 记录结果
            result = {
                'exp_id': exp_id,
                'exp_name': exp_name,
                'metrics': metrics,
                'params': {
                    'total_epoch': config['Global']['epoch_num']
                }
            }
            
            # 查找并记录shrink_ratio参数
            for transform in config['Train']['dataset']['transforms']:
                if isinstance(transform, dict) and 'MakeBorderMapAdvanced' in transform:
                    border_map_key = 'MakeBorderMapAdvanced'
                    result['params']['min_shrink_ratio'] = transform[border_map_key].get('min_shrink_ratio', None)
                    result['params']['max_shrink_ratio'] = transform[border_map_key].get('max_shrink_ratio', None)
                    break

            # 输出该实验的关键指标
            self.logger.info(f"实验 {exp_id} 完成: 平均precision={metrics['avg_precision']:.4f}, "
                            f"平均recall={metrics['avg_recall']:.4f}, "
                            f"平均hmean={metrics['avg_hmean']:.4f}, "
                            f"min_shrink_ratio={result['params'].get('min_shrink_ratio')}, "
                            f"max_shrink_ratio={result['params'].get('max_shrink_ratio')}")

            # 将实验结果保存到单独文件
            result_file = exp_dir / 'result.json'
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            return result

        except Exception as e:
            self.logger.error(f"实验 {exp_id} 失败: {e}")
            return {
                'exp_id': exp_id,
                'exp_name': exp_name,
                'metrics': {},
                'params': {},
                'error': str(e)
            }

    def grid_search(
        self,
        param_grid: Dict[str, List[Any]],
        max_epochs: int = 10,
        devices: str = '0',
    ) -> List[Dict[str, Any]]:
        """
        执行网格搜索

        Args:
            param_grid: 参数网格，格式为 {参数名: [参数值列表]}
            max_epochs: 最大训练轮次，用于加速搜索过程

        Returns:
            搜索结果列表
        """
        # 获取所有参数组合
        param_names = list(param_grid.keys())
        param_values = list(param_grid.values())
        param_combinations = list(itertools.product(*param_values))

        # 打印参数组合数量并等待用户确认
        self.logger.info(f"共计 {len(param_combinations)} 种参数组合")
        self.logger.info("参数组合详情:")
        
        # 打印每种组合的详细信息
        for i, combination in enumerate(param_combinations):
            params = dict(zip(param_names, combination))
            param_info = []
            for name, value in params.items():
                if name == 'pretrained_model':
                    model_path, model_alias = value
                    param_info.append(f'pretrained={model_alias}')
                elif name == 'shrink_combo':
                    min_ratio, max_ratio = value
                    param_info.append(f'min_shrink={min_ratio}, max_shrink={max_ratio}')
                else:
                    param_info.append(f'{name}={value}')
            self.logger.info(f"组合 {i+1}: " + ", ".join(param_info))
        
        # 用户确认
        user_input = input("\n是否开始试验? (y/n): ")
        if user_input.lower() not in ['y', 'yes']:
            self.logger.info("用户取消试验")
            return []

        self.logger.info(f"开始网格搜索，共 {len(param_combinations)} 种参数组合")

        # 记录开始时间
        start_time = time.time()

        # 对每种参数组合执行实验
        for i, combination in enumerate(param_combinations):
            # 创建参数字典
            params = dict(zip(param_names, combination))

            # 构建实验名称
            name_parts = []
            for name, value in params.items():
                if name == 'pretrained_model':
                    # 使用别名而不是完整路径
                    model_path, model_alias = value
                    name_parts.append(f'pretrained_{model_alias}')
                elif name == 'shrink_combo':
                    # 收缩比例组合
                    min_ratio, max_ratio = value
                    name_parts.append(f'min_shrink_{min_ratio}_max_shrink_{max_ratio}')
                elif isinstance(value, (int, float)):
                    name_parts.append(f'{name}_{value}')
                elif isinstance(value, bool):
                    name_parts.append(f'{name}_{value}')
            
            exp_name = '_'.join(name_parts)
            if not exp_name:
                exp_name = f"exp_{i+1}"

            # 创建配置副本
            config = copy.deepcopy(self.base_config)

            # 更新训练轮次
            config['Global']['epoch_num'] = max_epochs
            config['Global']['save_epoch_step'] = 2 * max_epochs  # GS不保存模型

            # 更新预训练模型路径和别名（如果有）
            if 'pretrained_model' in params:
                model_path, model_alias = params['pretrained_model']
                if model_path in ['null', 'None']:
                    config['Global']['pretrained_model'] = None
                else:
                    config['Global']['pretrained_model'] = model_path
                config['Global']['model_alias'] = model_alias

            # 更新MakeBorderMapAdvanced和MakeShrinkMapAdvanced的shrink_ratio
            if 'shrink_combo' in params:
                min_ratio, max_ratio = params['shrink_combo']
                params['min_shrink_ratio'] = min_ratio
                params['max_shrink_ratio'] = max_ratio

            # 遍历查找MakeBorderMapAdvanced和MakeShrinkMapAdvanced配置
            for transform in config['Train']['dataset']['transforms']:
                if isinstance(transform, dict) and 'MakeBorderMapAdvanced' in transform:
                    transform['MakeBorderMapAdvanced']['min_shrink_ratio'] = params['min_shrink_ratio']
                    transform['MakeBorderMapAdvanced']['max_shrink_ratio'] = params['max_shrink_ratio']
                    # 默认动态收缩
                    transform['MakeBorderMapAdvanced']['total_epoch'] = max_epochs
                if isinstance(transform, dict) and 'MakeShrinkMapAdvanced' in transform:
                    transform['MakeShrinkMapAdvanced']['min_shrink_ratio'] = params['min_shrink_ratio']
                    transform['MakeShrinkMapAdvanced']['max_shrink_ratio'] = params['max_shrink_ratio']
                    # 默认动态收缩
                    transform['MakeShrinkMapAdvanced']['total_epoch'] = max_epochs

            # 运行实验
            result = self.run_experiment(config, exp_name, i + 1, devices)
            self.search_results.append(result)

            # 保存中间结果
            self.save_results()

        # 计算搜索耗时
        elapsed_time = time.time() - start_time
        self.logger.info(f"网格搜索完成，共耗时 {elapsed_time:.2f} 秒")

        # 返回按评估指标排序的结果
        sorted_results = sorted(
            self.search_results,
            key=lambda x: x['metrics'].get('avg_hmean', 0.0),
            reverse=True
        )

        return sorted_results

    def save_results(self):
        """保存搜索结果到JSON文件"""
        results_file = self.output_dir / 'search_results.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.search_results, f, ensure_ascii=False, indent=2)

    def print_best_results(self, top_n: int = 5):
        """
        打印最佳结果，分别按precision、recall和hmean排序

        Args:
            top_n: 打印前N个最佳结果
        """
        if not self.search_results:
            self.logger.info("没有搜索结果")
            return

        # 按不同指标排序
        sorted_by_precision = sorted(
            self.search_results,
            key=lambda x: x['metrics'].get('avg_precision', 0.0),
            reverse=True
        )

        sorted_by_recall = sorted(
            self.search_results,
            key=lambda x: x['metrics'].get('avg_recall', 0.0),
            reverse=True
        )

        sorted_by_hmean = sorted(
            self.search_results,
            key=lambda x: x['metrics'].get('avg_hmean', 0.0),
            reverse=True
        )

        # 打印基于hmean的结果
        self.logger.info(f"\n======= 基于平均Hmean的前 {top_n} 个最佳结果 =======")
        for i, result in enumerate(sorted_by_hmean[:top_n]):
            self.logger.info(
                f"{i+1}. 实验: {result['exp_name']}, "
                f"平均hmean: {result['metrics'].get('avg_hmean', 0.0):.4f}, "
                f"最大hmean: {result['metrics'].get('max_hmean', 0.0):.4f}, "
                f"参数: {result['params']}"
            )

        # 打印基于precision的结果
        self.logger.info(f"\n======= 基于平均Precision的前 {top_n} 个最佳结果 =======")
        for i, result in enumerate(sorted_by_precision[:top_n]):
            self.logger.info(
                f"{i+1}. 实验: {result['exp_name']}, "
                f"平均precision: {result['metrics'].get('avg_precision', 0.0):.4f}, "
                f"最大precision: {result['metrics'].get('max_precision', 0.0):.4f}, "
                f"参数: {result['params']}"
            )

        # 打印基于recall的结果
        self.logger.info(f"\n======= 基于平均Recall的前 {top_n} 个最佳结果 =======")
        for i, result in enumerate(sorted_by_recall[:top_n]):
            self.logger.info(
                f"{i+1}. 实验: {result['exp_name']}, "
                f"平均recall: {result['metrics'].get('avg_recall', 0.0):.4f}, "
                f"最大recall: {result['metrics'].get('max_recall', 0.0):.4f}, "
                f"参数: {result['params']}"
            )

        # 将最佳结果保存到单独文件
        best_results = {
            'by_hmean': sorted_by_hmean[:top_n],
            'by_precision': sorted_by_precision[:top_n],
            'by_recall': sorted_by_recall[:top_n]
        }
        best_results_file = self.output_dir / 'best_results.json'
        with open(best_results_file, 'w', encoding='utf-8') as f:
            json.dump(best_results, f, ensure_ascii=False, indent=2)

        self.logger.info(f"\n最佳结果已保存到: {best_results_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PaddleOCR 网格搜索训练')
    parser.add_argument(
        "--devices",
        default='0',
        help='指定GPU设备，例如：`0,1,2,3`'
    )
    parser.add_argument(
        '-c', '--config', 
        required=True,
        help='配置文件路径'
    )
    parser.add_argument(
        '-o', '--output_dir', 
        default='./grid_search_results',
        help='输出目录'
    )
    parser.add_argument(
        '--max_epochs', 
        type=int, 
        default=10,
        help='每次实验的最大训练轮次'
    )
    parser.add_argument(
        '--min_shrink_ratio', 
        type=float, 
        nargs='+',
        default=[0.3, 0.4, 0.5],
        help='MakeBorderMapAdvanced和MakeShrinkMapAdvanced的min_shrink_ratio参数值列表'
    )
    parser.add_argument(
        '--max_shrink_ratio', 
        type=float, 
        nargs='+',
        default=[0.4, 0.5, 0.6],
        help='MakeBorderMapAdvanced和MakeShrinkMapAdvanced的max_shrink_ratio参数值列表，必须大于或等于min_shrink_ratio'
    )
    parser.add_argument(
        '--pretrained_models',
        nargs='+',
        default=[],
        help='要搜索的预训练模型路径列表'
    )
    parser.add_argument(
        '--model_aliases',
        nargs='+',
        default=[],
        help='预训练模型的别名列表，用于拼接保存目录'
    )
    
    args = parser.parse_args()
    
    # 生成有效的参数组合（确保max_shrink_ratio >= min_shrink_ratio）
    valid_shrink_combos = []
    for min_ratio in args.min_shrink_ratio:
        for max_ratio in args.max_shrink_ratio:
            if max_ratio > min_ratio:
                valid_shrink_combos.append((min_ratio, max_ratio))
    
    if not valid_shrink_combos:
        raise ValueError("没有有效的min_shrink_ratio和max_shrink_ratio组合！确保至少有一个max_shrink_ratio >= min_shrink_ratio")
    
    # 参数网格
    param_grid = {'shrink_combo': valid_shrink_combos}
    
    # 如果提供了预训练模型，将其加入搜索网格
    if args.pretrained_models:
        # 如果没有提供足够的别名，则自动生成
        if len(args.model_aliases) < len(args.pretrained_models):
            # 为没有别名的模型生成别名
            missing_aliases = len(args.pretrained_models) - len(args.model_aliases)
            for i in range(missing_aliases):
                model_path = args.pretrained_models[len(args.model_aliases) + i]
                # 从路径中提取文件名作为别名
                alias = os.path.splitext(os.path.basename(model_path))[0]
                args.model_aliases.append(alias)
        
        # 将预训练模型及其别名组合为搜索参数
        pretrained_models_with_aliases = list(zip(args.pretrained_models, args.model_aliases))
        param_grid['pretrained_model'] = pretrained_models_with_aliases
    
    # 创建网格搜索训练器
    trainer = GridSearchTrainer(args.config, args.output_dir)
    
    # 执行网格搜索
    results = trainer.grid_search(param_grid, max_epochs=args.max_epochs, devices=args.devices)
    
    # 打印最佳结果
    trainer.print_best_results(top_n=5)


if __name__ == "__main__":
    main()
