#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/3/25 14:22
# <AUTHOR> <EMAIL>
# @FileName: train_openocr_det

from third_parties import init_third_parties_env
init_third_parties_env(target=['openocr'])

from tools.utility import ArgsParser
from tools.engine.config import Config
from tools.engine.trainer import Trainer


def parse_args():
    parser = ArgsParser()
    args = parser.parse_args()
    return args


def main():
    FLAGS = parse_args()
    cfg = Config(FLAGS.config)
    FLAGS = vars(FLAGS)
    opt = FLAGS.pop('opt')
    cfg.merge_dict(FLAGS)
    cfg.merge_dict(opt)
    trainer = Trainer(cfg, mode='train_eval', task='det')
    trainer.train()


if __name__ == '__main__':
    main()
