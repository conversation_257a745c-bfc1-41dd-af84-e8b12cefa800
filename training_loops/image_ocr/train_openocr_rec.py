from third_parties import init_third_parties_env
init_third_parties_env(target=['openocr'])

from tools.utility import ArgsParser
from tools.engine.config import Config
from tools.engine.trainer import Trainer


def parse_args():
    parser = ArgsParser()
    parser.add_argument(
        '--eval',
        action='store_true',
        help='Whether to perform evaluation in train',
    )
    args = parser.parse_args()
    return args


def main():
    FLAGS = parse_args()
    cfg = Config(FLAGS.config)
    FLAGS = vars(FLAGS)
    opt = FLAGS.pop('opt')
    cfg.merge_dict(FLAGS)
    cfg.merge_dict(opt)
    trainer = Trainer(cfg, mode='train_eval' if FLAGS['eval'] else 'train', task='rec')
    trainer.train()


if __name__ == '__main__':
    main()
