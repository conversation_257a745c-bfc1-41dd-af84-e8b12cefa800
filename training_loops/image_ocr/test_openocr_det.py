#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/4/1 11:37
# <AUTHOR> <EMAIL>
# @FileName: test_openocr_det

"""OpenOCR检测模型评估模块
主要功能：
1. 加载训练好的模型
2. 构建评估数据加载器
3. 执行模型评估
4. 计算并输出评估指标

支持的模型类型：
- 文本检测模型
- 蒸馏模型
"""

from third_parties import init_third_parties_env
init_third_parties_env(target=['openocr'])  # 初始化OpenOCR环境

import os
import time

from tools.utility import ArgsParser
from tools.engine.config import Config
from tools.engine.trainer import Trainer


def parse_args():
    """解析命令行参数"""
    parser = ArgsParser()
    args = parser.parse_args()
    return args


def main():
    """评估主函数：执行模型评估流程
    包括：数据加载、模型构建、评估执行等步骤
    """
    FLAGS = parse_args()
    cfg = Config(FLAGS.config)
    FLAGS = vars(FLAGS)
    opt = FLAGS.pop('opt')
    cfg.merge_dict(FLAGS)
    cfg.merge_dict(opt)
    print(cfg)
    
    # 使用OpenOCR的Trainer进行评估
    mode = os.environ.get('MODE', 'Test')  # Eval or Test
    mode = mode.lower()
    trainer = Trainer(cfg, mode=mode, task='det')

    # 仅执行评估
    total_metrics = trainer.evaluate()
    metrics = total_metrics[mode]
    print("\n======= 评估指标 =======")
    for k, v in metrics.items():
        print(f"{k}: {v}")


if __name__ == '__main__':
    tic = time.time()
    main()
    toc = time.time()
    print(f"评估用时: {(toc - tic) / 60:.2f} 分钟")
