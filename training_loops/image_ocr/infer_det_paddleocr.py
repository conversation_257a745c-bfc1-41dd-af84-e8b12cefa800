#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/2/24 11:12
# <AUTHOR> <EMAIL>
# @FileName: infer_det_paddleocr

# Copyright (c) 2020 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from third_parties import init_third_parties_env
init_third_parties_env()  # 必要操作，初始化项目子模块中的第三方库

import os
os.environ["FLAGS_allocator_strategy"] = "auto_growth"  # 设置PaddlePaddle内存分配策略

import cv2
import json
import paddle
import numpy as np
from pathlib import Path

import tools.program as program
from ppocr.utils.save_load import load_model
from ppocr.postprocess import build_post_process
from ppocr.utils.utility import get_image_file_list
from ppocr.data import create_operators, transform
from ppocr.modeling.architectures import build_model

from modules.utils.image_utils import get_all_image_path

def draw_det_res(
    dt_boxes: np.ndarray,
    config: dict,
    img: np.ndarray,
    img_name: str,
    save_path: str
) -> None:
    """
    将检测结果绘制到图像上并保存
    
    Args:
        dt_boxes: 检测框坐标列表
        config: 配置信息
        img: 原始图像
        img_name: 图像文件名
        save_path: 结果保存路径
    """
    src_im = img
    for box in dt_boxes:
        box = np.array(box).astype(np.int32).reshape((-1, 1, 2))
        cv2.polylines(src_im, [box], True, color=(255, 255, 0), thickness=2)
    
    if not os.path.exists(save_path):
        os.makedirs(save_path)
    save_path = os.path.join(save_path, os.path.basename(img_name))
    cv2.imwrite(save_path, src_im)
    logger.info("检测结果已保存至: {}".format(save_path))


def draw_boxes_with_filtering(
    img, 
    all_boxes, 
    all_scores, 
    filtered_boxes, 
    save_path
):
    """
    在输入图像上绘制未过滤和已过滤的box
    
    Args:
        img: 输入图像
        all_boxes: 所有box（未过滤的）
        all_scores: 所有box对应的分数
        filtered_boxes: 过滤后的box（被过滤掉的）
        save_path: 结果保存路径
    
    Returns:
        vis_img: 绘制了box的可视化图像
    """
    vis_img = img.copy()
    
    # 找出未被过滤掉的boxes（保留的boxes）
    # 先将所有box转换为字符串以便比较
    if all_boxes is not None and filtered_boxes is not None:
        all_boxes_str = [str(box) for box in all_boxes]
        filtered_boxes_str = [str(box) for box in filtered_boxes]
        
        # 找出保留的boxes索引
        kept_indices = []
        for i, box_str in enumerate(all_boxes_str):
            if box_str not in filtered_boxes_str:
                kept_indices.append(i)
        
        # 绘制被过滤掉的boxes（红色）
        for idx in kept_indices:
            box = all_boxes[idx]
            score = all_scores[idx]
            box_np = np.array(box).astype(np.int32).reshape((-1, 1, 2))
            
            # 直接在图像上绘制边界框
            cv2.polylines(vis_img, [box_np], True, (0, 0, 255), 2)
            
            # 添加填充效果，透明度根据score映射到0.3~0.7
            alpha = 0.3 + score * 0.4  # 0.3到0.7的透明度
            overlay = vis_img.copy()
            cv2.fillPoly(overlay, [box_np], (0, 0, 255))
            cv2.addWeighted(overlay, alpha, vis_img, 1 - alpha, 0, vis_img)
        
        # 绘制被保留下来的boxes（绿色）
        for box in filtered_boxes:
            box_np = np.array(box).astype(np.int32).reshape((-1, 1, 2))
            
            # 直接在图像上绘制边界框
            cv2.polylines(vis_img, [box_np], True, (0, 255, 0), 2)
            
            # 添加填充效果，使用固定透明度
            alpha = 0.5  # 使用中等透明度
            overlay = vis_img.copy()
            cv2.fillPoly(overlay, [box_np], (0, 255, 0))
            cv2.addWeighted(overlay, alpha, vis_img, 1 - alpha, 0, vis_img)

    # 保存结果
    dirname = os.path.dirname(save_path)
    if not os.path.exists(dirname):
        os.makedirs(dirname)
    cv2.imwrite(save_path, vis_img)
    logger.info(f"Box可视化图已保存至: {save_path}")

    return vis_img


def save_debug_images(debug_info, file, save_path_prefix, src_img=None):
    """
    保存调试模式下的可视化图像

    Args:
        debug_info: 调试信息，包含prob_map, binary_map等
        file: 原始图像文件路径
        save_path_prefix: 保存路径前缀
        src_img: 原始图像，用于绘制box
    """
    if not os.path.exists(save_path_prefix):
        os.makedirs(save_path_prefix)

    # 获取文件名（不含扩展名）
    basename = os.path.basename(file)
    filename, _ = os.path.splitext(basename)

    # 保存概率图
    if "prob_map" in debug_info:
        prob_map = debug_info["prob_map"]
        # 转换为彩色热力图以便更好地可视化
        prob_map_vis = cv2.applyColorMap((prob_map * 255).astype(np.uint8), cv2.COLORMAP_JET)
        prob_save_path = os.path.join(save_path_prefix, f"{filename}_prob_map.jpg")
        cv2.imwrite(prob_save_path, prob_map_vis)
        logger.info(f"概率图已保存至: {prob_save_path}")

    # 保存二值图
    if "binary_map" in debug_info:
        binary_map = debug_info["binary_map"]
        binary_save_path = os.path.join(save_path_prefix, f"{filename}_binary_map.jpg")
        cv2.imwrite(binary_save_path, binary_map * 255)
        logger.info(f"二值图已保存至: {binary_save_path}")

    # 绘制并保存Box可视化效果图
    if src_img is not None and "all_boxes" in debug_info and "all_scores" in debug_info and "filtered_boxes" in debug_info:
        vis_save_path = os.path.join(save_path_prefix, f"{filename}_vis_box.jpg")
        draw_boxes_with_filtering(
            src_img,
            debug_info["all_boxes"],
            debug_info["all_scores"],
            debug_info["filtered_boxes"],
            vis_save_path
        )


@paddle.no_grad()
def main(config, mode):
    """
    主函数：执行文本检测推理流程
    包括：模型加载、数据预处理、推理、后处理、结果保存等步骤
    """
    global_config = config["Global"]

    # 构建模型
    model = build_model(config["Architecture"])
    load_model(config, model)

    # 构建后处理器
    post_process_class = build_post_process(config["PostProcess"])
    # 判断是否为DBPostProcess类型，用于调试
    class_name = post_process_class.__class__.__name__
    is_db_postprocess = class_name == "DBPostProcess"

    # 创建数据预处理操作
    transforms = []
    for op in config[mode]["dataset"]["transforms"]:
        op_name = list(op)[0]
        if "Label" in op_name:
            continue
        elif op_name == "KeepKeys":
            op[op_name]["keep_keys"] = ["image", "shape"]
        transforms.append(op)
    ops = create_operators(transforms, global_config)

    # 创建结果保存目录
    save_res_dir = config["Global"].get("save_res_dir", None)
    save_res_path = config["Global"].get("save_res_path", None)

    # 开始推理
    model.eval()

    if save_res_dir is not None:
        save_res_dir = Path(save_res_dir)
        save_res_dir.mkdir(parents=True, exist_ok=True)
        all_image_files = get_all_image_path(config["Global"]["infer_img"], path_op=Path, recursive=True)
        for file in all_image_files:
            logger.info(f"正在处理图像：{file}")

            # 读取并预处理图像
            with open(file, "rb") as f:
                img = f.read()
                data = {"image": img}
            batch = transform(data, ops)

            # 模型推理
            images = np.expand_dims(batch[0], axis=0)
            shape_list = np.expand_dims(batch[1], axis=0)
            images = paddle.to_tensor(images)
            preds = model(images)

            # 后处理获取检测框，如果是DBPostProcess类型则使用debug模式
            if is_db_postprocess:
                post_result, debug_info_batch = post_process_class(preds, shape_list, debug=True)
            else:
                post_result = post_process_class(preds, shape_list)
                debug_info_batch = None

            src_img = cv2.imread(str(file))
            if isinstance(post_result, dict):
                # 处理多任务模型的检测结果
                det_box_json = {}
                for k in post_result.keys():
                    dt_boxes_list = []
                    boxes = post_result[k][0]["points"]
                    for box in boxes:
                        tmp_json = {"transcription": "待识别", "points": np.array(box).tolist()}
                        dt_boxes_list.append(tmp_json)
                    det_box_json[k] = dt_boxes_list

                    # 保存可视化结果
                    img_rel_path = Path(file).relative_to(config["Global"]["infer_img"])
                    img_dst_path = save_res_dir / img_rel_path
                    draw_det_res(boxes, config, src_img.copy(), file, img_dst_path.parent)

                dt_boxes_json = det_box_json

            else:
                # 处理单任务模型的检测结果
                dt_boxes_json = []
                boxes = post_result[0]["points"]
                for box in boxes:
                    tmp_json = {"transcription": "待识别", "points": np.array(box).tolist()}
                    dt_boxes_json.append(tmp_json)

                # 保存可视化结果
                img_rel_path = Path(file).relative_to(config["Global"]["infer_img"])
                img_dst_path = save_res_dir / img_rel_path
                draw_det_res(boxes, config, src_img.copy(), file, img_dst_path.parent)

            label_path = save_res_dir / "Label.txt"
            if os.path.exists(label_path):
                with open(label_path, "a") as f:
                    f.write(f"{str(img_rel_path)}\t{json.dumps(dt_boxes_json)}\n")
            else:
                Path(label_path).parent.mkdir(parents=True, exist_ok=True)
                with open(label_path, "w") as f:
                    f.write(f"{str(img_rel_path)}\t{json.dumps(dt_boxes_json)}\n")

    else:
        if not os.path.exists(os.path.dirname(save_res_path)):
            os.makedirs(os.path.dirname(save_res_path))

        with open(save_res_path, "wb") as fout:
            for file in get_image_file_list(config["Global"]["infer_img"]):
                logger.info("正在处理图像: {}".format(file))

                # 读取并预处理图像
                with open(file, "rb") as f:
                    img = f.read()
                    data = {"image": img}
                batch = transform(data, ops)

                # 模型推理
                images = np.expand_dims(batch[0], axis=0)
                shape_list = np.expand_dims(batch[1], axis=0)
                images = paddle.to_tensor(images)
                preds = model(images)

                # 后处理获取检测框，如果是DBPostProcess类型则使用debug模式
                if is_db_postprocess:
                    post_result, debug_info_batch = post_process_class(preds, shape_list, debug=True)
                else:
                    post_result = post_process_class(preds, shape_list)
                    debug_info_batch = None

                src_img = cv2.imread(file)

                dt_boxes_json = []
                # 处理不同类型的后处理结果
                if isinstance(post_result, dict):
                    # 处理多任务模型的检测结果
                    det_box_json = {}
                    for k in post_result.keys():
                        boxes = post_result[k][0]["points"]
                        dt_boxes_list = []
                        for box in boxes:
                            tmp_json = {"transcription": "", "points": np.array(box).tolist()}
                            dt_boxes_list.append(tmp_json)
                        det_box_json[k] = dt_boxes_list

                        # 保存可视化结果
                        save_det_path = os.path.dirname(config["Global"]["save_res_path"]) + "/det_results_{}/".format(k)
                        draw_det_res(boxes, config, src_img.copy(), file, save_det_path)

                        # 如果有debug信息，保存debug可视化图像
                        if debug_info_batch and k in debug_info_batch:
                            debug_save_path = os.path.dirname(config["Global"]["save_res_path"]) + "/debug_results_{}/".format(k)
                            save_debug_images(debug_info_batch[k][0], file, debug_save_path, src_img.copy())

                    dt_boxes_json = det_box_json
                else:
                    # 处理单任务模型的检测结果
                    boxes = post_result[0]["points"]
                    dt_boxes_json = []
                    for box in boxes:
                        tmp_json = {"transcription": "", "points": np.array(box).tolist()}
                        dt_boxes_json.append(tmp_json)

                    # 保存可视化结果
                    save_det_path = os.path.dirname(config["Global"]["save_res_path"]) + "/det_results/"
                    draw_det_res(boxes, config, src_img.copy(), file, save_det_path)

                    # 如果有debug信息，保存debug可视化图像
                    if debug_info_batch:
                        debug_save_path = os.path.dirname(config["Global"]["save_res_path"]) + "/debug_results/"
                        save_debug_images(debug_info_batch[0], file, debug_save_path, src_img.copy())

                # 保存检测结果到文件
                otstr = file + "\t" + json.dumps(dt_boxes_json) + "\n"
                fout.write(otstr.encode())

    logger.info("推理完成!")


if __name__ == "__main__":
    config, device, logger, vdl_writer = program.preprocess()
    mode = os.environ.get("MODE", "Eval")
    main(config, mode)
