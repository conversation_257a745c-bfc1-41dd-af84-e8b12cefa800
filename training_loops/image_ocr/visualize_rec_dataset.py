#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/2/24 20:41
# <AUTHOR> <EMAIL>
# @FileName: visualize_paddleocr_dataset

"""
PaddleOCR数据集可视化工具
主要功能：
1. 加载训练数据
2. 采样指定数量的batch
3. 将图像和标注信息保存到指定路径，包括：
   - 原始图像
   - 原始标注可视化
   - 训练监督信号可视化
   - 完整的标签数据
"""
from typing import Dict, Optional

from third_parties import init_third_parties_env
init_third_parties_env()

import os
import random
import cv2
import math
import paddle
import shutil
import numpy as np
from tqdm import tqdm

import tools.program as program
from modules.utils.log import LOGGER
from ppocr.data import build_dataloader
from ppocr.data.imaug import transform
from ppocr.data.simple_dataset import SimpleDataSet
from paddle.io import Dataset, DataLoader
from ppocr.data.multi_scale_sampler import MultiScaleSampler
from ppocr.data.multi_scale_adaptive_sampler import MultiScaleAdaptiveSampler


def denormalize_image(
        image: np.ndarray,
        mean: Optional[np.ndarray] = None,
        std: Optional[np.ndarray] = None
) -> np.ndarray:
    """反归一化图像数据

    Args:
        image: 归一化后的图像数据，shape为[C,H,W]或[H,W,C]
        mean: 均值，默认为[0.485, 0.456, 0.406]
        std: 标准差，默认为[0.229, 0.224, 0.225]

    Returns:
        反归一化后的图像数据，shape为[H,W,C]，BGR格式
    """
    if mean is None:
        mean = np.array([0.485, 0.456, 0.406])
    if std is None:
        std = np.array([0.229, 0.224, 0.225])

    # 确保图像是HWC格式
    if image.shape[0] == 3:  # CHW -> HWC
        image = image.transpose(1, 2, 0)

    # 反归一化: (image * std + mean) * 255
    image = image * std + mean
    image = image * 255.
    image = np.clip(image, 0, 255).astype(np.uint8)

    # 注意：PaddleOCR中的图像已经是BGR格式，不需要进行颜色转换
    # image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)

    return image


class MultiScaleDataSet(SimpleDataSet):
    def __init__(self, config, mode, logger, seed=None):
        super(MultiScaleDataSet, self).__init__(config, mode, logger, seed)
        self.ds_width = config[mode]["dataset"].get("ds_width", False)
        if self.ds_width:
            self.wh_aware()

    def wh_aware(self):
        data_line_new = []
        wh_ratio = []
        for lins in self.data_lines:
            data_line_new.append(lins)
            lins = lins.decode("utf-8")
            name, label, w, h = lins.strip("\n").split(self.delimiter)
            wh_ratio.append(float(w) / float(h))

        self.data_lines = data_line_new
        self.wh_ratio = np.array(wh_ratio)
        self.wh_ratio_sort = np.argsort(self.wh_ratio)
        self.data_idx_order_list = list(range(len(self.data_lines)))

    def resize_norm_img(self, data, imgW, imgH, padding=True):
        img = data["image"]
        h = img.shape[0]
        w = img.shape[1]
        if not padding:
            resized_image = cv2.resize(
                img, (imgW, imgH), interpolation=cv2.INTER_LINEAR
            )
            resized_w = imgW
        else:
            ratio = w / float(h)
            if math.ceil(imgH * ratio) > imgW:
                resized_w = imgW
            else:
                resized_w = int(math.ceil(imgH * ratio))
            resized_image = cv2.resize(img, (resized_w, imgH))
        resized_image = resized_image.astype("float32")

        resized_image = resized_image.transpose((2, 0, 1)) / 255
        resized_image -= 0.5
        resized_image /= 0.5
        padding_im = np.zeros((3, imgH, imgW), dtype=np.float32)
        padding_im[:, :, :resized_w] = resized_image
        valid_ratio = min(1.0, float(resized_w / imgW))
        data["image"] = padding_im
        data["valid_ratio"] = valid_ratio
        return data

    def __getitem__(self, properties):
        # properites is a tuple, contains (width, height, index)
        img_height = properties[1]
        idx = properties[2]
        if self.ds_width and properties[3] is not None:
            wh_ratio = properties[3]
            img_width = img_height * (
                1 if int(round(wh_ratio)) == 0 else int(round(wh_ratio))
            )
            file_idx = self.wh_ratio_sort[idx]
        else:
            file_idx = self.data_idx_order_list[idx]
            img_width = properties[0]
            wh_ratio = None

        data_line = self.data_lines[file_idx]
        try:
            data_line = data_line.decode("utf-8")
            substr = data_line.strip("\n").split(self.delimiter)
            file_name = substr[0]
            file_name = self._try_parse_filename_list(file_name)
            label = substr[1]
            img_path = os.path.join(self.data_dir, file_name)
            data = {"img_path": img_path, "label": label}
            if not os.path.exists(img_path):
                raise Exception("{} does not exist!".format(img_path))
            with open(data["img_path"], "rb") as f:
                img = f.read()
                data["image"] = img
                data["ext_data"] = self.get_ext_data()
            outs = transform(data, self.ops[:-1])
            if outs is not None:
                outs = self.resize_norm_img(outs, img_width, img_height)
                outs = transform(outs, self.ops[-1:])
        except:
            outs = None
        if outs is None:
            # during evaluation, we should fix the idx to get same results for many times of evaluation.
            rnd_idx = (idx + 1) % self.__len__()
            return self.__getitem__([img_width, img_height, rnd_idx, wh_ratio])
        return outs


if __name__ == '__main__':
    config, device, logger, vdl_writer = program.preprocess()
    seed = 1024
    mode = "Train"

    # train_dataloader = build_dataloader(config, "Train", device, logger, seed)
    dataset = MultiScaleDataSet(config, mode, logger, seed)

    config_sampler = config[mode]["sampler"]
    sampler_name = config_sampler.pop("name")
    batch_sampler = eval(sampler_name)(dataset, **config_sampler)

    loader_config = config[mode]["loader"]
    num_workers = loader_config["num_workers"]
    if "use_shared_memory" in loader_config.keys():
        use_shared_memory = loader_config["use_shared_memory"]
    else:
        use_shared_memory = True
    collate_fn = None

    data_loader = DataLoader(
        dataset=dataset,
        batch_sampler=batch_sampler,
        places=device,
        num_workers=num_workers,
        return_list=True,
        use_shared_memory=use_shared_memory,
        collate_fn=collate_fn,
    )

    # data_loader = DataLoader(dataset, batch_size=1, shuffle=True)

    image_save_dir = '/aipdf-mlp/jiacheng/exp/dataloader_vis'
    for epoch in range(5):
        print(f'Epoch {epoch + 1}:')
        iter_num = 0
        for outs in data_loader:
            batch_num = outs[0].shape[0]
            for idx in range(batch_num):
                image = outs[0][idx]
                if isinstance(image, paddle.Tensor):
                    image = image.numpy()

                image = denormalize_image(image)

                image_path = os.path.join(image_save_dir, f'{iter_num}_{idx}.jpg')
                cv2.imwrite(image_path, image)

            iter_num += 1

            if iter_num > 9:
                break


        break

    print("End")
