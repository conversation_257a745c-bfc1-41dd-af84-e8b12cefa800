#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/2/24 14:54
# <AUTHOR> <EMAIL>
# @FileName: eval_paddleocr
"""
PaddleOCR模型评估模块
主要功能：
1. 加载训练好的模型
2. 构建评估数据加载器
3. 执行模型评估
4. 计算并输出评估指标

支持的模型类型：
- 文本检测模型
- 文本识别模型（CTC、SAR、NRTR等）
- 蒸馏模型
- 数学公式识别模型（CAN、LaTeXOCR、UniMERNet等）
"""

# Copyright (c) 2020 PaddlePaddle Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from third_parties import init_third_parties_env
init_third_parties_env()  # 必要操作，初始化项目子模块中的第三方库

import os
import time
import paddle
import tools.program as program
from ppocr.metrics import build_metric
from ppocr.postprocess import build_post_process
from ppocr.utils.save_load import load_model
from ppocr.modeling.architectures import build_model
from ppocr.data import build_dataloader, set_signal_handlers


def configure_recognition_model(config: dict, post_process_class) -> dict:
    """
    配置文本识别模型的输出通道数
    
    Args:
        config: 模型配置字典
        post_process_class: 后处理类实例
    
    Returns:
        更新后的配置字典
    """
    if not hasattr(post_process_class, "character"):
        return config
        
    # 获取字符集大小
    char_num = len(getattr(post_process_class, "character"))
    
    # 处理蒸馏模型
    if config["Architecture"]["algorithm"] == "Distillation":
        for key in config["Architecture"]["Models"]:
            if config["Architecture"]["Models"][key]["Head"]["name"] == "MultiHead":
                # 多头模型配置
                out_channels_list = {}
                # 根据解码器类型调整字符数
                if config["PostProcess"]["name"] == "DistillationSARLabelDecode":
                    char_num = char_num - 2
                elif config["PostProcess"]["name"] == "DistillationNRTRLabelDecode":
                    char_num = char_num - 3
                
                # 设置不同解码器的输出通道
                out_channels_list.update({
                    "CTCLabelDecode": char_num,
                    "SARLabelDecode": char_num + 2,
                    "NRTRLabelDecode": char_num + 3
                })
                config["Architecture"]["Models"][key]["Head"]["out_channels_list"] = out_channels_list
            else:
                config["Architecture"]["Models"][key]["Head"]["out_channels"] = char_num
    
    # 处理多头模型
    elif config["Architecture"]["Head"]["name"] == "MultiHead":
        out_channels_list = {}
        # 根据解码器类型调整字符数
        if config["PostProcess"]["name"] == "SARLabelDecode":
            char_num = char_num - 2
        elif config["PostProcess"]["name"] == "NRTRLabelDecode":
            char_num = char_num - 3
            
        # 设置不同解码器的输出通道
        out_channels_list.update({
            "CTCLabelDecode": char_num,
            "SARLabelDecode": char_num + 2,
            "NRTRLabelDecode": char_num + 3
        })
        config["Architecture"]["Head"]["out_channels_list"] = out_channels_list
    
    # 处理基础识别模型
    else:
        config["Architecture"]["Head"]["out_channels"] = char_num
        
    return config


def determine_model_type(config: dict) -> str:
    """
    确定模型类型并更新相关配置
    
    Args:
        config: 模型配置字典
    
    Returns:
        模型类型字符串
    """
    if "model_type" not in config["Architecture"].keys():
        return None
        
    algorithm = config["Architecture"]["algorithm"]
    model_type_map = {
        "CAN": "can",
        "LaTeXOCR": "latexocr",
        "UniMERNet": "unimernet",
        "PP-FormulaNet-S": "pp_formulanet",
        "PP-FormulaNet-L": "pp_formulanet"
    }
    
    # 对于特殊的公式识别模型，设置BLEU评分
    if algorithm in ["LaTeXOCR", "UniMERNet", "PP-FormulaNet-S", "PP-FormulaNet-L"]:
        config["Metric"]["cal_blue_score"] = True
    
    return model_type_map.get(algorithm, config["Architecture"]["model_type"])


def setup_amp_training(config: dict, model) -> tuple:
    """
    设置自动混合精度训练
    
    Args:
        config: 模型配置字典
        model: 模型实例
    
    Returns:
        (model, scaler) 元组
    """
    use_amp = config["Global"].get("use_amp", False)
    if not use_amp:
        return model, None
        
    # 设置AMP相关标志
    amp_flags = {
        "FLAGS_cudnn_batchnorm_spatial_persistent": 1,
        "FLAGS_max_inplace_grad_add": 8,
    }
    paddle.set_flags(amp_flags)
    
    # 配置损失缩放器
    scale_loss = config["Global"].get("scale_loss", 1.0)
    use_dynamic_loss_scaling = config["Global"].get("use_dynamic_loss_scaling", False)
    scaler = paddle.amp.GradScaler(
        init_loss_scaling=scale_loss,
        use_dynamic_loss_scaling=use_dynamic_loss_scaling,
    )
    
    # O2级别优化
    amp_level = config["Global"].get("amp_level", "O2")
    if amp_level == "O2":
        model = paddle.amp.decorate(models=model, level=amp_level, master_weight=True)
        
    return model, scaler


def main(config, mode='Eval'):
    """
    评估主函数：执行模型评估流程
    包括：数据加载、模型构建、评估执行等步骤
    """
    global_config = config["Global"]
    
    # 构建评估数据加载器
    set_signal_handlers()
    valid_dataloader = build_dataloader(config, mode, device, logger)

    # 构建后处理器
    post_process_class = build_post_process(config["PostProcess"], global_config)

    # 配置模型结构
    config = configure_recognition_model(config, post_process_class)
    model = build_model(config["Architecture"])
    
    # 检查是否需要额外输入
    extra_input_models = [
        "SRN", "NRTR", "SAR", "SEED", "SVTR", "SVTR_LCNet",
        "VisionLAN", "RobustScanner", "SVTR_HGNet"
    ]
    extra_input = False
    if config["Architecture"]["algorithm"] == "Distillation":
        for key in config["Architecture"]["Models"]:
            extra_input = (
                extra_input
                or config["Architecture"]["Models"][key]["algorithm"]
                in extra_input_models
            )
    else:
        extra_input = config["Architecture"]["algorithm"] in extra_input_models
    
    # 确定模型类型
    model_type = determine_model_type(config)
    
    # 构建评估指标
    eval_class = build_metric(config["Metric"])
    
    # 设置自动混合精度训练
    model, scaler = setup_amp_training(config, model)
    amp_level = config["Global"].get("amp_level", "O2")
    amp_custom_black_list = config["Global"].get("amp_custom_black_list", [])

    # 加载模型权重
    best_model_dict = load_model(config, model, model_type=config["Architecture"]["model_type"])
    if best_model_dict:
        logger.info("*************** 检查点中的评估指标 ***************")
        for k, v in best_model_dict.items():
            logger.info("{}:{}".format(k, v))

    # 开始评估
    logger.info("开始模型评估...")
    metric = program.evaluate(
        model,
        valid_dataloader,
        post_process_class,
        eval_class,
        model_type,
        extra_input,
        scaler,
        amp_level,
        amp_custom_black_list,
    )
    
    # 输出评估结果
    logger.info("*************** 最终评估指标 ***************")
    for k, v in metric.items():
        logger.info("{}:{}".format(k, v))


if __name__ == "__main__":
    tic = time.time()
    config, device, logger, vdl_writer = program.preprocess()
    mode = os.environ.get("MODE", "Eval")
    main(config, mode)  # 'Eval' or 'Test'
    toc = time.time()

    logger.info(f"评估用时: {(toc - tic) / 60: 2f} 分钟")
