#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/03/17 11:23
# <AUTHOR> <EMAIL>
# @FileName: evaluate_models.py
"""
模型批量评估工具

该脚本用于批量评估指定目录下的所有模型文件(iter_epoch_xxx.pdparams)，
并找出hmean、precision和recall指标最佳的模型。
"""

import os
import re
import sys
import datetime
import subprocess
from typing import Dict, List, Tuple


def find_model_files(model_dir: str) -> List[str]:
    """查找目录中所有的iter_epoch_xxx.pdparams文件
    
    Args:
        model_dir: 模型文件目录
        
    Returns:
        包含所有模型文件路径的列表
    """
    model_files = []
    pattern = re.compile(r'iter_epoch_(\d+)\.pdparams$')
    
    for file in os.listdir(model_dir):
        if pattern.search(file):
            model_files.append(os.path.join(model_dir, file))
    
    return model_files


def extract_epoch_number(model_file: str) -> int:
    """从模型文件名提取epoch编号
    
    Args:
        model_file: 模型文件路径
        
    Returns:
        epoch编号
    """
    match = re.search(r'iter_epoch_(\d+)\.pdparams$', model_file)
    if match:
        return int(match.group(1))
    return -1


def evaluate_model(model_file: str, config_file: str) -> Dict[str, float]:
    """评估单个模型文件
    
    Args:
        model_file: 模型文件路径
        config_file: 配置文件路径
        
    Returns:
        包含评估指标的字典
    """
    epoch = extract_epoch_number(model_file)
    print(f"\n正在评估模型: {os.path.basename(model_file)} (Epoch {epoch})")
    
    try:
        # 设置环境变量
        env = os.environ.copy()
        env["PYTHONPATH"] = "." + (":" + env["PYTHONPATH"] if "PYTHONPATH" in env else "")
        env["MODE"] = "Eval"  # 可以是 'Eval' 或 'Test'
        
        # 获取当前Python解释器路径
        python_executable = sys.executable
        
        # 运行评估脚本
        cmd = [
            python_executable, "training_loops/image_ocr/test_paddleocr.py",
            "-c",
            config_file,
            "-o",
            f"Global.checkpoints={model_file}",
            "Global.distributed=false",
            "Metric.name=ParallelDetMetric",
            "Metric.main_indicator=hmean",
            "Metric.workers=20",
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 运行命令
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,  # 获取输出
            stderr=subprocess.STDOUT,  # 将错误输出重定向到标准输出
            cwd=os.getcwd(),  # 在当前目录下运行
            env=env,
            text=True,
            bufsize=1  # 设置缓冲区大小为1
        )
        
        # 实时打印控制台输出
        full_output = ""
        for line in iter(process.stdout.readline, ""):
            # print(line, end="")  # 实时打印
            full_output += line
        
        # 等待进程结束
        process.stdout.close()
        return_code = process.wait()
        
        # 检查进程是否正常结束
        if return_code != 0:
            print(f"命令执行失败，返回代码: {return_code}")
            return {}
        
        # 解析评估结果
        metrics = {}
        
        # 使用正则表达式匹配评估指标的模式
        precision_pattern = re.compile(r'\[.*?\] ppocr INFO: precision:(\d+\.\d+)')
        recall_pattern = re.compile(r'\[.*?\] ppocr INFO: recall:(\d+\.\d+)')
        hmean_pattern = re.compile(r'\[.*?\] ppocr INFO: hmean:(\d+\.\d+)')
        fps_pattern = re.compile(r'\[.*?\] ppocr INFO: fps:(\d+\.\d+)')
        time_pattern = re.compile(r'\[.*?\] ppocr INFO: 评估用时： (\d+\.\d+) 分钟')
        
        # 直接使用正则表达式从全部输出中提取评估指标
        precision_match = precision_pattern.search(full_output)
        if precision_match:
            metrics['precision'] = float(precision_match.group(1))
            print(f"precision: {metrics['precision']}")
        
        recall_match = recall_pattern.search(full_output)
        if recall_match:
            metrics['recall'] = float(recall_match.group(1))
            print(f"recall: {metrics['recall']}")
        
        hmean_match = hmean_pattern.search(full_output)
        if hmean_match:
            metrics['hmean'] = float(hmean_match.group(1))
            print(f"hmean: {metrics['hmean']}")
        
        fps_match = fps_pattern.search(full_output)
        if fps_match:
            metrics['fps'] = float(fps_match.group(1))
            print(f"fps: {metrics['fps']}")
        
        time_match = time_pattern.search(full_output)
        if time_match:
            metrics['评估用时'] = float(time_match.group(1))
            print(f"评估用时: {metrics['评估用时']} 分钟")
        
        # 如果没有成功解析指标，打印原始输出以便调试
        if not metrics:
            print("警告：未能解析评估指标，原始输出：")
            print(full_output)
        
        return metrics
    
    except Exception as e:
        print(f"评估失败，异常信息: {str(e)}")
        return {}


def find_best_models(evaluation_results: Dict[str, Dict[str, float]]) -> Dict[str, Tuple[str, float]]:
    """根据不同指标找出最佳模型
    
    Args:
        evaluation_results: 模型评估结果字典，格式为{模型文件: {指标名: 指标值}}
        
    Returns:
        包含每个指标的最佳模型和对应值的字典
    """
    metrics_of_interest = ['hmean', 'precision', 'recall']
    best_models = {}
    
    for metric in metrics_of_interest:
        best_score = -1
        best_model = None
        
        for model_file, metrics in evaluation_results.items():
            if metric in metrics and metrics[metric] > best_score:
                best_score = metrics[metric]
                best_model = model_file
        
        if best_model:
            best_models[metric] = (best_model, best_score)
    
    return best_models


def main():
    """主函数"""
    # 直接设置参数，而不是通过命令行参数解析
    model_dir = '/aipdf-mlp/xelawk/training_outputs/20250316/debug'
    config_file = os.path.join(model_dir, 'config.yml')
    
    # 确认模型目录存在
    if not os.path.isdir(model_dir):
        print(f"错误：模型目录 {model_dir} 不存在")
        return
    
    # 确认配置文件存在
    if not os.path.isfile(config_file):
        print(f"错误：配置文件 {config_file} 不存在")
        return
    
    # 查找所有模型文件
    model_files = find_model_files(model_dir)
    if not model_files:
        print(f"错误：在目录 {model_dir} 中未找到任何iter_epoch_*.pdparams文件")
        return
    
    print(f"找到 {len(model_files)} 个模型文件待评估")
    
    # 按epoch排序模型文件
    model_files.sort(key=extract_epoch_number)
    
    # 评估所有模型
    results = {}
    for model_file in model_files:
        metrics = evaluate_model(model_file, config_file)
        results[model_file] = metrics
        
        # 打印当前模型的评估结果
        epoch = extract_epoch_number(model_file)
        print(f"Epoch {epoch} 评估结果:")
        for metric, value in metrics.items():
            print(f"  {metric}: {value}")
    
    # 找出最佳模型
    best_models = find_best_models(results)
    
    # 打印最佳模型信息
    print("\n最佳模型汇总:")
    for metric, (model_file, score) in best_models.items():
        epoch = extract_epoch_number(model_file)
        print(f"最佳 {metric}: Epoch {epoch}, 得分: {score:.4f}, 文件: {os.path.basename(model_file)}")
    
    # 将评估结果保存到文件
    evaluation_file = os.path.join(model_dir, "evaluation_results.txt")
    print(f"\n将评估结果保存到 {evaluation_file}")

    with open(evaluation_file, 'w', encoding='utf-8') as f:
        # 写入评估时间
        f.write(f"评估时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 写入所有模型的评估结果
        f.write("各模型评估结果:\n")
        f.write("=" * 80 + "\n")
        for model_file in model_files:
            epoch = extract_epoch_number(model_file)
            f.write(f"Epoch {epoch} ({os.path.basename(model_file)}):\n")
            if model_file in results:
                for metric, value in results[model_file].items():
                    f.write(f"  {metric}: {value}\n")
            else:
                f.write("  无有效评估结果\n")
            f.write("\n")
        
        # 写入最佳模型汇总
        f.write("\n最佳模型汇总:\n")
        f.write("=" * 80 + "\n")
        for metric, (model_file, score) in best_models.items():
            epoch = extract_epoch_number(model_file)
            f.write(f"最佳 {metric}: Epoch {epoch}, 得分: {score:.4f}, 文件: {os.path.basename(model_file)}\n")

    print(f"评估结果已保存到 {evaluation_file}")


if __name__ == "__main__":
    main()
