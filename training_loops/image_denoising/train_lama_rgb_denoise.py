#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/11/23 20:11
# <AUTHOR> <EMAIL>
# @FileName: train_lama_denoise

import os
import re
import math
import json
import random
import datetime
import warnings
from pathlib import Path
from itertools import islice
from omegaconf import OmegaConf

import torch
import numpy as np
import torch.utils.data
from tqdm import tqdm
from PIL import Image
from accelerate.logging import get_logger
from diffusers.optimization import get_scheduler

import modules.utils.torch_utils as torch_utils
import modules.utils.image_utils as image_utils
from modules.utils.log import create_file_logger
from modules.utils.torch_utils import EMAHandler
from modules.proj_cmd_args.lama_denoise.args_v2 import parse_args
from modules.utils.train_utils import prepare_training_enviornment

from my_datasets.image_denoising.base_dataset_v1 import walk_dataloaders
from my_datasets.image_denoising.base_dataset_v1 import MyCustomDataset as MyClearDocDataset
from my_datasets.image_denoising.base_dataset_clean import MyCustomDataset as MyCleanDocDataset
from my_datasets.image_denoising.base_dataset_deblack import MyCustomDataset as MyDeblackDataset
from my_datasets.image_denoising.base_dataset_v1 import dynamic_collate_fn as clear_collate_fn
from my_datasets.image_denoising.base_dataset_clean import dynamic_collate_fn as clean_collate_fn
from my_datasets.image_denoising.base_dataset_deblack import dynamic_collate_fn as deblack_collate_fn
from networks.lama.metrics import SSIMEvaluator
from networks.lama.training_loss import TrainingLossV2
from networks.lama.denoise_lama import make_generator, make_discriminator

args = parse_args()
logger = get_logger(__name__)
file_logger_path = Path(os.path.join(args.output_dir, "logs", "monitors.log"))
os.makedirs(file_logger_path.parent, exist_ok=True)
file_logger = create_file_logger(file_logger_path, "DEBUG")
torch.autograd.set_detect_anomaly(True)

# 忽略特定警告
warnings.filterwarnings("ignore", message="torch.meshgrid", category=UserWarning)
warnings.filterwarnings("ignore", message="Grad strides do not match bucket view strides", category=UserWarning)
warnings.filterwarnings("ignore", message="On January 1, 2023, MMCV will release v2.0.0", category=UserWarning, )


def prepare_dataloaders(args, mode, train_batch_size_per_device, seed=-1):
    debug = args.debug
    if debug:
        msg = "Using debug mode to load dataset with small size"
        logger.info(msg)
        file_logger.info(msg)

    datasets, loaders = [], []
    deblack_doc_dataset_dir = args.deblack_doc_dataset_dir
    deblack_doc_dataset_dir2 = args.deblack_doc_dataset_dir2
    clear_doc_dataset_dir = args.clear_doc_dataset_dir
    clean_doc_dataset_dir = args.clean_doc_dataset_dir

    # 训练集才会用到去黑底的数据集
    deblack_doc_dataset = None
    if deblack_doc_dataset_dir is not None and mode == "train":
        deblack_doc_dataset = MyDeblackDataset(deblack_doc_dataset_dir, mode, seed, debug=debug)
        datasets.append(deblack_doc_dataset)

    deblack_doc_dataset2 = None
    if deblack_doc_dataset_dir2 is not None and mode == "train":
        deblack_doc_dataset2 = MyDeblackDataset(deblack_doc_dataset_dir2, mode, seed, debug=debug, apply_degrade=False)
        datasets.append(deblack_doc_dataset2)

    clear_doc_dataset = None
    if clear_doc_dataset_dir is not None:
        clear_doc_dataset = MyClearDocDataset(clear_doc_dataset_dir, mode, seed, debug=debug)
        datasets.append(clear_doc_dataset)

    clean_doc_dataset = None
    if clean_doc_dataset_dir is not None:
        clean_doc_dataset = MyCleanDocDataset(clean_doc_dataset_dir, mode, seed, debug=debug)
        datasets.append(clean_doc_dataset)

    assert len(datasets) > 0, ("No datasets were loaded! Please ensure at least one dataset directory")

    def dynamic_resolution_for_deblack_doc():
        if mode == "train":
            if not args.res4deblackdoc:
                raise ValueError("args.res4deblackdoc list is empty.")
            return random.choice(args.res4deblackdoc)
        else:
            return 1024

    def dynamic_resolution_for_deblack_doc2():
        if mode == "train":
            if not args.res4deblackdoc2:
                raise ValueError("args.res4deblackdoc2 list is empty.")
            return random.choice(args.res4deblackdoc2)
        else:
            return 512

    def dynamic_resolution_for_clear_doc():
        if mode == "train":
            if not args.res4cleardoc:
                raise ValueError("args.res4cleardoc list is empty.")
            return random.choice(args.res4cleardoc)
        else:
            return 1024

    def dynamic_resolution_for_clean_doc():
        if mode == "train":
            if not args.res4cleandoc:
                raise ValueError("args.res4cleandoc list is empty.")
            return random.choice(args.res4cleandoc)
        else:
            return 1024

    if deblack_doc_dataset is not None:
        deblack_doc_data_loader = torch.utils.data.DataLoader(
            deblack_doc_dataset,
            shuffle=True if mode == "train" else False,
            batch_size=train_batch_size_per_device,
            num_workers=args.dataloader_num_workers,
            collate_fn=lambda batch: deblack_collate_fn(batch, dynamic_resolution_for_deblack_doc)
        )
        loaders.append(("DeblackDoc", deblack_doc_data_loader))

    if deblack_doc_dataset2 is not None:
        deblack_doc_data_loader2 = torch.utils.data.DataLoader(
            deblack_doc_dataset2,
            shuffle=True if mode == "train" else False,
            batch_size=train_batch_size_per_device,
            num_workers=args.dataloader_num_workers,
            collate_fn=lambda batch: deblack_collate_fn(batch, dynamic_resolution_for_deblack_doc2)
        )
        loaders.append(("DeblackDoc2", deblack_doc_data_loader2))

    if clear_doc_dataset is not None:
        clear_doc_data_loader = torch.utils.data.DataLoader(
            clear_doc_dataset,
            shuffle=True if mode == "train" else False,
            batch_size=train_batch_size_per_device,
            num_workers=args.dataloader_num_workers,
            collate_fn=lambda batch: clear_collate_fn(batch, dynamic_resolution_for_clear_doc)
        )
        loaders.append(("ClearDoc", clear_doc_data_loader))

    if clean_doc_dataset is not None:
        clean_doc_data_loader = torch.utils.data.DataLoader(
            clean_doc_dataset,
            shuffle=True if mode == "train" else False,
            batch_size=train_batch_size_per_device,
            num_workers=args.dataloader_num_workers,
            collate_fn=lambda batch: clean_collate_fn(batch, dynamic_resolution_for_clean_doc)
        )
        loaders.append(("CleanDoc", clean_doc_data_loader))

    return datasets, loaders


def is_train_generator(step, gen_step_interval=1, disc_step_interval=1):
    """
    控制生成器和判别器的训练频率。
    """
    generator_phase = step % (gen_step_interval + disc_step_interval) < gen_step_interval
    return generator_phase


def generator_forward(generator, noised_image):
    # 使用生成器生成预测的图像
    predicted_image = generator(noised_image)
    return predicted_image


def save_best_checkpoints(
    args,
    accelerator,
    generator_model,
    generator_ema_handler,
    generator_optimizer,
    generator_lr_scheduler,
    discriminator_model,
    discriminator_optimizer,
    discriminator_lr_scheduler,
    global_step,
    val_metrics,
    record_dump_path,
    current_best_record
):
    found_best_model = False
    avg_ssim = val_metrics
    tmp_record = {"avg_ssim": avg_ssim}

    if avg_ssim >= current_best_record["avg_ssim"]:
        found_best_model = True

    if found_best_model:
        current_best_record.update(tmp_record)
        with open(record_dump_path, 'w', encoding='utf-8') as f:
            data = dict()
            data.update(current_best_record)
            data['checkpoint_step'] = global_step
            json.dump(data, f, ensure_ascii=False, indent=4)

        msg = f"Found best model: {data}"
        logger.info(msg)
        file_logger.info(msg)

        save_state(
            Path(record_dump_path).parent,
            generator_model,
            generator_optimizer,
            generator_lr_scheduler,
            discriminator_model,
            discriminator_optimizer,
            discriminator_lr_scheduler,
            accelerator,
            generator_ema_handler=generator_ema_handler,
        )


def get_optimizer(args, params_to_opt, optimizer_ckpt, opt_type="generator"):
    if args.use_adam:
        optimizer_class = torch.optim.Adam
    else:
        if args.use_8bit_adamw:
            try:
                import bitsandbytes as bnb
            except ImportError:
                raise ImportError(
                    "To use 8-bit Adam, please install the bitsandbytes library: `pip install bitsandbytes`."
                )
            optimizer_class = bnb.optim.AdamW8bit
        else:
            optimizer_class = torch.optim.AdamW

    if opt_type == "generator":
        learning_rate = args.gen_lr
    elif opt_type == "discriminator":
        learning_rate = args.disc_lr
    else:
        raise ValueError(f"Unrecognized optimizer type: {opt_type}")

    optimizer = optimizer_class(
        params_to_opt,
        lr=learning_rate,
        betas=(args.adam_beta1, args.adam_beta2),
        weight_decay=args.adam_weight_decay,
        eps=args.adam_epsilon,
    )

    if optimizer_ckpt is not None:
        optimizer_state_dict = torch.load(optimizer_ckpt, map_location='cpu')
        optimizer.load_state_dict(optimizer_state_dict)

    return optimizer


def manage_checkpoints(output_dir, n_checkpoints_to_keep):
    checkpoints = sorted(
        [d for d in os.listdir(output_dir) if re.match(r'^checkpoint-\d+$', d)],
        key=lambda x: int(x.split('-')[1])
    )

    if len(checkpoints) > n_checkpoints_to_keep:
        checkpoints_to_delete = checkpoints[:-n_checkpoints_to_keep]
        msg = f"Old version checkpoints to delete: {checkpoints_to_delete}"
        logger.info(msg)
        file_logger.info(msg)

        for checkpoint in checkpoints_to_delete:
            checkpoint_path = os.path.join(output_dir, checkpoint)
            if os.path.isdir(checkpoint_path):
                for file in os.listdir(checkpoint_path):
                    file_path = os.path.join(checkpoint_path, file)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                os.rmdir(checkpoint_path)


def save_state(
    save_dir,
    generator_model,
    generator_optimizer,
    generator_lr_scheduler,
    discriminator_model,
    discriminator_optimizer,
    discriminator_lr_scheduler,
    accelerator,
    n_checkpoints_to_keep=None,
    generator_ema_handler=None,
):
    os.makedirs(save_dir, exist_ok=True)

    generator_model = accelerator.unwrap_model(generator_model)
    discriminator_model = accelerator.unwrap_model(discriminator_model)
    generator_optimizer = accelerator.unwrap_model(generator_optimizer)
    generator_lr_scheduler = accelerator.unwrap_model(generator_lr_scheduler)
    discriminator_optimizer = accelerator.unwrap_model(discriminator_optimizer)
    discriminator_lr_scheduler = accelerator.unwrap_model(discriminator_lr_scheduler)

    torch.save(generator_model.state_dict(), os.path.join(save_dir, "generator_model.bin"))
    torch.save(generator_optimizer.state_dict(), os.path.join(save_dir, "generator_optimizer.bin"))
    torch.save(generator_lr_scheduler.state_dict(), os.path.join(save_dir, "generator_scheduler.bin"))
    torch.save(discriminator_model.state_dict(), os.path.join(save_dir, "discriminator_model.bin"))
    torch.save(discriminator_optimizer.state_dict(), os.path.join(save_dir, "discriminator_optimizer.bin"))
    torch.save(discriminator_lr_scheduler.state_dict(), os.path.join(save_dir, "discriminator_scheduler.bin"))

    if generator_ema_handler is not None:
        generator_ema_handler.save(os.path.join(save_dir, "generator_model_ema.bin"))

    # 保持最新的n个checkpoint
    if n_checkpoints_to_keep is not None:
        manage_checkpoints(os.path.dirname(save_dir), n_checkpoints_to_keep)


def save_grouped_images(images, latest_dir, running_steps_dir, steps, group_size=10):
    """
    将图像分组，每组拼接成一张大图，并保存到指定目录。
    """
    total_parts = (len(images) + group_size - 1) // group_size  # 计算总组数
    for part_idx in range(total_parts):
        # 获取当前组的图像
        start_idx = part_idx * group_size
        end_idx = min(start_idx + group_size, len(images))
        grouped_images = images[start_idx:end_idx]

        # 将组内图像拼接成一张大图
        grouped_image = np.concatenate(grouped_images, axis=0)
        grouped_image = Image.fromarray(grouped_image)

        # 生成带后缀的文件名
        part_suffix = f"part{part_idx + 1}"
        latest_file_name = f"validation_latest_{part_suffix}.png"
        running_steps_file_name = f"validation_{steps:09d}_{part_suffix}.jpg"

        # 保存图像
        grouped_image.save(os.path.join(latest_dir, latest_file_name))
        grouped_image.save(os.path.join(running_steps_dir, running_steps_file_name))


def log_validation(args, generator_model, ema_handler, steps, accelerator, weight_dtype, val_dataloaders):
    generator_model = accelerator.unwrap_model(generator_model)
    if args.use_ema:
        ema_handler.store(generator_model)
        ema_handler.apply_to(generator_model)
    generator_model.eval()

    print()
    msg = f"Running metrics for step-{steps}..."
    logger.info(msg)
    file_logger.info(msg)
    evaluator = SSIMEvaluator().to(accelerator.device, weight_dtype)

    # TODO: 验证评估会比较耗时, 可以指定数量
    if args.num_val_batch is not None and isinstance(args.num_val_batch, int):
        num_batch_to_eval = args.num_val_batch
        test_pbar = tqdm(total=num_batch_to_eval, desc='evaluating...')
        dataloader_steps = islice(walk_dataloaders(val_dataloaders), 0, num_batch_to_eval)
    else:
        test_dataloader_total_steps = sum(len(loader) for flag, loader in val_dataloaders)
        test_pbar = tqdm(total=test_dataloader_total_steps, desc='evaluating...')
        dataloader_steps = walk_dataloaders(val_dataloaders)

    for step, (flag, batch) in enumerate(dataloader_steps):
        noised, target = batch['noised'], batch['target']
        noised = noised.to(accelerator.device, weight_dtype)
        target = target.to(accelerator.device, weight_dtype)

        with torch.no_grad():
            pred = generator_forward(generator_model, noised)

        evaluator(pred, target)
        test_pbar.update(1)

    test_pbar.close()
    eval_results = evaluator.compute()
    accelerator.log(eval_results, step=steps)

    # 输出最终的综合指标
    msg = (f"\nTotal Metrics\n"
           f"AvgSSIM: {eval_results['avg_ssim']:.4f}")
    logger.info(msg)
    file_logger.info(msg)

    # 可视化真实数据效果
    validation_image_dir = args.validation_image_dir
    if validation_image_dir is not None and os.path.exists(validation_image_dir):
        logger.info("\nRunning visualized validation...\n")
        image_latest_log_dir = os.path.join(args.output_dir, "image_logs")
        image_running_steps_log_dir = os.path.join(image_latest_log_dir, "running_steps")
        os.makedirs(image_running_steps_log_dir, exist_ok=True)

        images_to_save = []
        all_val_image_path = image_utils.get_all_image_path(validation_image_dir, recursive=False, path_op=Path)
        vis_pbar = tqdm(total=len(all_val_image_path), desc='visualized validation...')
        for image_path in all_val_image_path:
            image = Image.open(image_path).convert('RGB')
            image = image.resize((1920, 1920), Image.BILINEAR)
            image_tensor = image_utils.normalize_image_rgb(image)
            image_tensor = image_tensor.unsqueeze(0)

            with torch.no_grad():
                pred_img = generator_forward(
                    generator_model,
                    image_tensor.to(accelerator.device, weight_dtype),
                )

            image_input = image_utils.denormalize_tensor_image_rgb(image_tensor.squeeze(0))
            image_pred = image_utils.denormalize_tensor_image_rgb(pred_img.squeeze(0))
            row_images = [image_input, image_pred]

            # 将这一行的图像拼接起来
            row_image = np.concatenate([np.array(img) for img in row_images], axis=1)
            images_to_save.append(row_image)
            vis_pbar.update(1)

        vis_pbar.close()

        # 分组拼接并保存
        save_grouped_images(
            images_to_save,
            image_latest_log_dir,
            image_running_steps_log_dir,
            steps
        )

    if args.use_ema:
        ema_handler.restore(generator_model)
    generator_model.train()

    return eval_results['avg_ssim']


def adjust_pretrained_state_dict(state_dict, generator_model):
    # Get the weights that need to be adjusted, take the first 3 channels of the official input layer weights
    key = "model.1.ffc.convl2l.weight"
    if key in state_dict:
        pretrained_weight = state_dict[key]
        msg = f"Original weight shape: {pretrained_weight.shape}"
        logger.info(msg)
        file_logger.info(msg)

        # Check if the number of channels matches
        if pretrained_weight.shape[1] == 4 and generator_model.state_dict()[key].shape[1] == 3:
            # Adjust weight shape: only take weights of first 3 channels
            adjusted_weight = pretrained_weight[:, :3, :, :]
            # Replace weights
            state_dict[key] = adjusted_weight
            msg = f"Adjusted weight shape: {adjusted_weight.shape}"
            logger.info(msg)
            file_logger.info(msg)
        else:
            msg = "Weight channels already match, no adjustment needed."
            logger.info(msg)
            file_logger.info(msg)

    return state_dict


def main():
    accelerator, weight_dtype = prepare_training_enviornment(args, logger)

    # 初始化模型、checkpoint复用等
    start_steps = 0
    generator_ema_path = None
    generator_state_dict = None
    discriminator_state_dict = None
    generator_optimizer_ckpt = None
    generator_lr_scheduler_ckpt = None
    discriminator_optimizer_ckpt = None
    discriminator_lr_scheduler_ckpt = None

    best_ssim_model_record = os.path.join(args.output_dir, "best_ssim_model", "record.json")
    os.makedirs(Path(best_ssim_model_record).parent, exist_ok=True)

    exists_checkpoints = list(Path(args.output_dir).glob('checkpoint-*'))
    if len(exists_checkpoints) > 0:
        args.reuse_checkpoint = None

    if args.reuse_checkpoint is not None and os.path.exists(args.reuse_checkpoint):
        generator_state_dict = torch.load(
            os.path.join(args.reuse_checkpoint, "generator_model.bin"),
            map_location='cpu'
        )

        tmp_generator_ema_path = os.path.join(args.reuse_checkpoint, "generator_model_ema.bin")
        if args.use_ema and os.path.exists(tmp_generator_ema_path):
            generator_ema_path = tmp_generator_ema_path

        discriminator_state_dict = torch.load(
            os.path.join(args.reuse_checkpoint, "discriminator_model.bin"),
            map_location='cpu'
        )
        load_state_dict_msg = f"load state dict from {args.reuse_checkpoint}"
        if accelerator.is_main_process:
            msg = f"Reusing checkpoint from {args.reuse_checkpoint}"
            logger.info(msg)
            file_logger.info(msg)

    elif len(exists_checkpoints) > 0:
        checkpoint_numbers = [int(f.stem.split('-')[1]) for f in exists_checkpoints]
        resume_steps = max(checkpoint_numbers)
        resume_ckpt_dir = os.path.join(args.output_dir, f'checkpoint-{resume_steps}')
        generator_state_dict = torch.load(
            os.path.join(resume_ckpt_dir, "generator_model.bin"),
            map_location='cpu'
        )

        tmp_generator_ema_path = os.path.join(resume_ckpt_dir, "generator_model_ema.bin")
        if args.use_ema and os.path.exists(tmp_generator_ema_path):
            generator_ema_path = tmp_generator_ema_path

        discriminator_state_dict = torch.load(
            os.path.join(resume_ckpt_dir, "discriminator_model.bin"),
            map_location='cpu'
        )
        load_state_dict_msg = f"load state dict from {resume_ckpt_dir}"
        start_steps = resume_steps
        generator_optimizer_ckpt = os.path.join(resume_ckpt_dir, "generator_optimizer.bin")
        discriminator_optimizer_ckpt = os.path.join(resume_ckpt_dir, "discriminator_optimizer.bin")
        generator_lr_scheduler_ckpt = os.path.join(resume_ckpt_dir, "generator_scheduler.bin")
        discriminator_lr_scheduler_ckpt = os.path.join(resume_ckpt_dir, "discriminator_scheduler.bin")
        if accelerator.is_main_process:
            msg = f"Reusing running checkpoint from {resume_ckpt_dir}"
            logger.info(msg)
            file_logger.info(msg)

    # 模型定义初始化, 解决分布式训练BatchNorm2D引起的梯度计算异常
    generator_cfg = OmegaConf.load(args.generator_cfg_yaml)
    assert generator_cfg.input_nc == 3, "input channels must be 3, RGB"
    generator_model = make_generator(**generator_cfg)
    generator_model = torch_utils.convert_batchnorm_to_apex_sync_batchnorm(generator_model)
    if generator_state_dict is not None:
        generator_state_dict = adjust_pretrained_state_dict(generator_state_dict, generator_model)
        generator_model.load_state_dict(generator_state_dict, strict=True)
        logger.info(f"generator_model: {load_state_dict_msg}")
        file_logger.info(f"generator_model: {load_state_dict_msg}")
    generator_model.to(accelerator.device, weight_dtype)
    generator_model.train()

    ema_handler = None
    if args.use_ema:
        ema_handler = EMAHandler(
            model=generator_model,
            decay=args.ema_decay,
            device=accelerator.device,
            weight_dtype=weight_dtype,
            start_step=args.ema_start_step,
            update_period=args.ema_period,
            log_updates=False,
        )
        if generator_ema_path is not None:
            ema_handler.load(generator_ema_path)

    discriminator_cfg = OmegaConf.load(args.discriminator_cfg_yaml)
    discriminator_model = make_discriminator(**discriminator_cfg)
    discriminator_model = torch_utils.convert_batchnorm_to_apex_sync_batchnorm(discriminator_model)
    if discriminator_state_dict is not None:
        discriminator_model.load_state_dict(discriminator_state_dict, strict=True)
        logger.info(f"discriminator_model: {load_state_dict_msg}")
        file_logger.info(f"discriminator_model: {load_state_dict_msg}")
    discriminator_model.to(accelerator.device, weight_dtype)
    discriminator_model.train()

    # 初始化优化策略
    generator_params_to_opt = generator_model.parameters()
    discriminator_params_to_opt =discriminator_model.parameters()

    loss_cfg = OmegaConf.load(args.loss_cfg_yaml)
    loss_criterion = TrainingLossV2(config_yaml=args.loss_cfg_yaml)
    loss_criterion = loss_criterion.to(accelerator.device, weight_dtype)
    generator_optimizer = get_optimizer(
        args=args,
        params_to_opt=generator_params_to_opt,
        optimizer_ckpt=generator_optimizer_ckpt,
        opt_type="generator"
    )
    discriminator_optimizer = get_optimizer(
        args=args,
        params_to_opt=discriminator_params_to_opt,
        optimizer_ckpt=discriminator_optimizer_ckpt,
        opt_type="discriminator"
    )

    # 准备数据集，训练步数、Epoch预估、accelerator.prepare 等调度
    num_split = accelerator.num_processes
    assert args.train_batch_size % num_split == 0, \
        (f"batch_size: {args.train_batch_size} needs to be divisible by num_processes={num_split}")

    if args.seed is not None:
        seed = int(args.seed)
    else:
        seed = random.randint(1, 100000)

    # 准备数据集
    train_batch_size_per_device = args.train_batch_size // num_split
    all_train_datasets, all_train_loaders = prepare_dataloaders(args, 'train', train_batch_size_per_device, seed=seed)
    all_val_datasets, all_val_loaders = prepare_dataloaders(args, 'val', train_batch_size_per_device, seed=seed)

    train_dataloader_total_steps = sum(len(loader) for _, loader in all_train_loaders)
    num_update_steps_per_epoch = math.ceil(train_dataloader_total_steps)
    max_train_steps = args.num_train_epochs * num_update_steps_per_epoch

    generator_lr_scheduler = get_scheduler(
        args.lr_scheduler,
        optimizer=generator_optimizer,
        num_training_steps=max_train_steps,
        num_cycles=args.lr_num_cycles,
        power=args.lr_power,
        num_warmup_steps=args.lr_warmup_steps * accelerator.num_processes,
    )
    if generator_lr_scheduler_ckpt is not None:
        lr_scheduler_state_dict = torch.load(generator_lr_scheduler_ckpt, map_location='cpu')
        generator_lr_scheduler.load_state_dict(lr_scheduler_state_dict)

    discriminator_lr_scheduler = get_scheduler(
        args.lr_scheduler,
        optimizer=discriminator_optimizer,
        num_training_steps=max_train_steps,
        num_cycles=args.lr_num_cycles,
        power=args.lr_power,
        num_warmup_steps=args.lr_warmup_steps * accelerator.num_processes,
    )
    if discriminator_lr_scheduler_ckpt is not None:
        lr_scheduler_state_dict = torch.load(discriminator_lr_scheduler_ckpt, map_location='cpu')
        discriminator_lr_scheduler.load_state_dict(lr_scheduler_state_dict)

    # 最佳模型保存指标的初始化，包含 fid, ssim, ssim_fid100_f1
    best_ssim_record_data = {"avg_ssim": 0.}
    if os.path.exists(best_ssim_model_record):
        with open(best_ssim_model_record, 'r', encoding='utf-8') as f:
            data = json.load(f)
            best_ssim_record_data["avg_ssim"] = data["avg_ssim"]

    # accelerator.prepare
    logger.info("Prepare everything with our accelerator\n")
    generator_model, generator_optimizer, generator_lr_scheduler = accelerator.prepare(
        generator_model, generator_optimizer, generator_lr_scheduler
    )
    discriminator_model, discriminator_optimizer, discriminator_lr_scheduler = accelerator.prepare(
        discriminator_model, discriminator_optimizer, discriminator_lr_scheduler
    )
    for i, loader in enumerate(all_train_loaders):
        flag, loader = loader
        loader = accelerator.prepare(loader)
        all_train_loaders[i] = (flag, loader)

    if accelerator.is_main_process:
        exp_date = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        accelerator.init_trackers(f"{args.tracker_project_name}-{exp_date}")

    # 经过prepare之后实际长度会发生变化
    train_dataloader_total_steps = sum(len(loader) for _, loader in all_train_loaders)
    num_update_steps_per_epoch = math.ceil(train_dataloader_total_steps)
    max_train_steps = args.num_train_epochs * num_update_steps_per_epoch

    if accelerator.is_main_process:
        msg = "\n"
        msg += "***** Running training *****\n"
        msg += f"  Num checkpoints to keep: {args.num_ckpt_to_keep}\n"
        msg += f"  Enable EMA model: {args.use_ema}\n"
        msg += f"  Dataset seed: {seed}\n"
        msg += (f"  Resolution set, "
                f"res4cleardoc: {args.res4cleardoc} "
                f"res4cleandoc: {args.res4cleandoc} "
                f"res4deblackdoc: {args.res4deblackdoc}\n")
        msg += f"  Loss cfg: {loss_cfg}\n"
        msg += f"  best_ssim_model_record_data: {best_ssim_record_data}\n"
        msg += f"  Num examples = {sum(len(dataset) for dataset in all_train_datasets)}\n"
        msg += f"  Num epochs = {args.num_train_epochs}\n"
        msg += f"  Batch size per device = {train_batch_size_per_device}\n"
        msg += f"  Total train batch size (w. parallel, distributed & accumulation) = {args.train_batch_size}\n"
        msg += f"  Total optimization steps = {max_train_steps}\n"
        msg += "\n"
        logger.info(msg)
        file_logger.info(msg)

    first_epoch = 0
    global_step = start_steps
    exists_epoch_ckpts = list(Path(args.output_dir).glob('model_epoch-*'))
    if len(exists_epoch_ckpts) > 0:
        epoch_numbers = [int(f.stem.split('-')[1]) for f in exists_epoch_ckpts]
        first_epoch = max(epoch_numbers) - 1

    progress_bar = tqdm(
        range(0, max_train_steps),
        initial=global_step,
        desc="Steps",
        # Only show the progress bar once on each machine.
        disable=not accelerator.is_local_main_process,
    )

    for epoch in range(first_epoch, args.num_train_epochs):
        for step, (flag, batch) in enumerate(walk_dataloaders(all_train_loaders)):
            noised, target = batch['noised'], batch['target']
            noised = noised.to(accelerator.device, weight_dtype)
            target = target.to(accelerator.device, weight_dtype)

            # 交替训练
            if is_train_generator(step):
                torch_utils.set_requires_grad(accelerator.unwrap_model(generator_model), True)
                torch_utils.set_requires_grad(accelerator.unwrap_model(discriminator_model), False)

            else:
                torch_utils.set_requires_grad(accelerator.unwrap_model(generator_model), False)
                torch_utils.set_requires_grad(accelerator.unwrap_model(discriminator_model), True)

            pred = generator_forward(generator_model, noised)

            if is_train_generator(step):
                total_loss, metrics = loss_criterion.forward_generator_loss(
                    pred, target, generator_model, discriminator_model
                )
            else:
                total_loss, metrics = loss_criterion.forward_discriminator_loss(
                    pred, target, generator_model, discriminator_model
                )

            accelerator.backward(total_loss)
            avg_loss = accelerator.gather(total_loss.repeat(args.train_batch_size)).mean().item()

            if args.use_clip_grad_norm:
                if is_train_generator(step):
                    accelerator.clip_grad_norm_(generator_params_to_opt, args.clip_grad_norm)
                else:
                    accelerator.clip_grad_norm_(discriminator_params_to_opt, args.clip_grad_norm)

            if is_train_generator(step):
                generator_optimizer.step()
                generator_lr_scheduler.step()
                generator_optimizer.zero_grad()
            else:
                discriminator_optimizer.step()
                discriminator_lr_scheduler.step()
                discriminator_optimizer.zero_grad()

            if args.use_ema and is_train_generator(step):
                ema_handler.update(accelerator.unwrap_model(generator_model), global_step)

            progress_bar.update(1)
            global_step += 1

            if accelerator.is_main_process and is_train_generator(step):
                logs = {
                    "dflag": flag,
                    "loss": total_loss.detach().item(),
                    "avg_loss": avg_loss,
                    # "gen_lr": generator_lr_scheduler.get_last_lr()[0],
                    # "disc_lr": discriminator_lr_scheduler.get_last_lr()[0],
                }
                progress_bar.set_postfix(**logs)
                accelerator.log(logs, step=global_step)

            if accelerator.is_main_process and global_step % args.save_steps == 0:
                save_path = os.path.join(args.output_dir, f"checkpoint-{global_step}")
                save_state(
                    save_path,
                    generator_model,
                    generator_optimizer,
                    generator_lr_scheduler,
                    discriminator_model,
                    discriminator_optimizer,
                    discriminator_lr_scheduler,
                    accelerator,
                    args.num_ckpt_to_keep,
                    generator_ema_handler=ema_handler,
                )

                # 下面是根据不同记录保存最佳模型
                val_metrics = log_validation(
                    args, generator_model, ema_handler, global_step, accelerator, weight_dtype, all_val_loaders
                )
                save_best_checkpoints(
                    args,
                    accelerator,
                    generator_model,
                    ema_handler,
                    generator_optimizer,
                    generator_lr_scheduler,
                    discriminator_model,
                    discriminator_optimizer,
                    discriminator_lr_scheduler,
                    global_step,
                    val_metrics,
                    best_ssim_model_record,
                    best_ssim_record_data,
                )

            if global_step >= max_train_steps:
                break

        # 当前Epoch训练结束，保存当前模型
        if accelerator.is_main_process and args.save_every_n_epoch > 0 and (epoch + 1) % args.save_every_n_epoch == 0:
            save_path = os.path.join(args.output_dir, f"model_epoch-{epoch + 1}")
            save_state(
                save_path,
                generator_model,
                generator_optimizer,
                generator_lr_scheduler,
                discriminator_model,
                discriminator_optimizer,
                discriminator_lr_scheduler,
                accelerator,
                generator_ema_handler=ema_handler,
            )

    # 训练结束，保存最终模型
    accelerator.wait_for_everyone()
    if accelerator.is_main_process:
        save_path = os.path.join(args.output_dir, f"model_final")
        save_state(
            save_path,
            generator_model,
            generator_optimizer,
            generator_lr_scheduler,
            discriminator_model,
            discriminator_optimizer,
            discriminator_lr_scheduler,
            accelerator,
            generator_ema_handler=ema_handler,
        )
    accelerator.end_training()


if __name__ == '__main__':
    main()
