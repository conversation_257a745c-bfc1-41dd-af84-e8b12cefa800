#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/22 14:50
# <AUTHOR> <EMAIL>
# @FileName: train_kantu_classifier

import os
import re
import math
import json
import random
import datetime
import warnings
from enum import Enum
from pathlib import Path

import torch
import torch.utils.data
from tqdm import tqdm
from accelerate.logging import get_logger
from diffusers.optimization import get_scheduler
from sklearn.metrics import roc_auc_score, f1_score

import modules.utils.torch_utils as torch_utils
from modules.utils.log import create_file_logger
from networks.utils.metrics.eval import AverageMeter
from networks.mobilenet.loss import binary_classification_loss
from modules.utils.train_utils import prepare_training_enviornment
from modules.proj_cmd_args.kantu_classifiers.args import parse_args
from modules.utils.train_utils import calculate_fpr_recall_at_thresholds
from my_datasets.kantu_classifier.base_dataset_v2 import MyCustomDataset, walk_dataloaders
from networks.mobilenet.model import MultiTaskMobileNetV3Small, MultiTaskMobileNetV3Large

args = parse_args()
logger = get_logger(__name__)
file_logger_path = Path(os.path.join(args.output_dir, "logs", "monitors.log"))
os.makedirs(file_logger_path.parent, exist_ok=True)
file_logger = create_file_logger(file_logger_path, "DEBUG")
torch.autograd.set_detect_anomaly(True)

# 忽略特定警告
warnings.filterwarnings("ignore", message="torch.meshgrid", category=UserWarning)
warnings.filterwarnings("ignore", message="Grad strides do not match bucket view strides", category=UserWarning)
warnings.filterwarnings("ignore", message="On January 1, 2023, MMCV will release v2.0.0", category=UserWarning, )


class DatasetFlag(Enum):
    CLS_TEXED = 'cls_texted'
    CLS_CLEAR_BLUR = 'cls_clear_blur'
    CLS_WATERMARKED = 'cls_watermarked'
    CLS_HUMAN = 'cls_human'
    CLS_BLACK_NOBLACK = 'cls_black_noblack'
    CLS_DOC_BLUR_AND_NOBLUR = 'cls_doc_blur_and_noblur'
    CLS_DOC_NODOC = 'cls_doc_nodoc'
    CLS_SHADOW_AND_NOSHADOW = 'cls_shadow_and_noshadow'
    CLS_SNAP_CAPTURED = 'cls_snap_captured'
    CLS_CERT_DOC = 'cls_cert_doc'


def prepare_dataloaders(args, mode, train_batch_size_per_device):
    dataset_dir = []
    for _dir in args.train_data_dir:
        # dataset_dir.append((_dir, -1))
        dataset_dir = _dir

    all_datasets = []
    for flag in DatasetFlag:
        all_datasets.append(MyCustomDataset(dataset_dir, flag.value, mode))

    all_loaders = []
    for flag, cur_dataset in zip(DatasetFlag, all_datasets):
        cur_loader = torch.utils.data.DataLoader(
            cur_dataset,
            shuffle=True,
            batch_size=train_batch_size_per_device,
            num_workers=args.dataloader_num_workers,
        )
        all_loaders.append((flag.value, cur_loader))

    return all_datasets, all_loaders


def main():
    accelerator, weight_dtype = prepare_training_enviornment(args, logger)

    # 初始化模型、checkpoint复用等
    state_dict = None
    start_steps = 0
    optimizer_ckpt = None
    lr_scheduler_ckpt = None

    best_auc_f1_model_record = os.path.join(args.output_dir, "best_auc_f1_model", "record.json")
    os.makedirs(Path(best_auc_f1_model_record).parent, exist_ok=True)
    best_auc_model_record = os.path.join(args.output_dir, "best_auc_model", "record.json")
    os.makedirs(Path(best_auc_model_record).parent, exist_ok=True)
    best_f1_model_record = os.path.join(args.output_dir, "best_f1_model", "record.json")
    os.makedirs(Path(best_f1_model_record).parent, exist_ok=True)

    exists_checkpoints = list(Path(args.output_dir).glob('checkpoint-*'))
    if len(exists_checkpoints) > 0:
        args.reuse_checkpoint = None

    if args.reuse_checkpoint is not None and os.path.exists(args.reuse_checkpoint):
        state_dict = torch.load(os.path.join(args.reuse_checkpoint, "model.bin"), map_location='cpu')
        if accelerator.is_main_process:
            msg = f"Reusing checkpoint from {args.reuse_checkpoint}"
            logger.info(msg)
            file_logger.info(msg)

    elif len(exists_checkpoints) > 0:
        checkpoint_numbers = [int(f.stem.split('-')[1]) for f in exists_checkpoints]
        resume_steps = max(checkpoint_numbers)
        resume_ckpt_dir = os.path.join(args.output_dir, f'checkpoint-{resume_steps}')
        state_dict = torch.load(os.path.join(resume_ckpt_dir, 'model.bin'), map_location='cpu')
        start_steps = resume_steps
        optimizer_ckpt = os.path.join(resume_ckpt_dir, "optimizer.bin")
        lr_scheduler_ckpt = os.path.join(resume_ckpt_dir, "scheduler.bin")
        if accelerator.is_main_process:
            msg = f"Reusing running checkpoint from {resume_ckpt_dir}"
            logger.info(msg)
            file_logger.info(msg)

    # 模型定义初始化
    if args.model_type == "mbv3_small":
        model = MultiTaskMobileNetV3Small(args.dropout)
    elif args.model_type == "mbv3_large":
        model = MultiTaskMobileNetV3Large(args.dropout)
    else:
        raise ValueError(f"Unsupported model type: {args.model_type}")
    model = torch_utils.convert_batchnorm_to_sync_batchnorm(model)  # 解决分布式训练BatchNorm2D引起的梯度计算异常
    if state_dict is not None:
        model.load_state_dict(state_dict, strict=True)
    model.to(accelerator.device, weight_dtype)
    model.train()

    # 初始化优化策略
    params_to_opt = model.parameters()
    loss_criterion = binary_classification_loss
    optimizer = get_optimizer(args, params_to_opt, optimizer_ckpt)

    # 准备数据集，训练步数、Epoch预估、accelerator.prepare 等调度
    num_split = accelerator.num_processes * args.gradient_accumulation_steps
    assert args.train_batch_size % num_split == 0, \
        (f"batch_size: {args.train_batch_size} needs to be divisible by "
         f"num_processes*gradient_accumulation_steps={num_split}")

    if args.seed is not None:
        seed = int(args.seed)
    else:
        seed = random.randint(1, 100000)

    # 准备数据集
    train_batch_size_per_device = args.train_batch_size // num_split
    all_train_datasets, all_train_loaders = prepare_dataloaders(args, 'train', train_batch_size_per_device)
    all_test_datasets, all_test_loaders = prepare_dataloaders(args, 'test', train_batch_size_per_device)

    train_dataloader_total_steps = sum(len(loader) for _, loader in all_train_loaders)
    num_update_steps_per_epoch = math.ceil(train_dataloader_total_steps / args.gradient_accumulation_steps)
    max_train_steps = args.num_train_epochs * num_update_steps_per_epoch

    lr_scheduler = get_scheduler(
        args.lr_scheduler,
        optimizer=optimizer,
        num_training_steps=max_train_steps,
        num_cycles=args.lr_num_cycles,
        power=args.lr_power,
        num_warmup_steps=args.lr_warmup_steps * accelerator.num_processes,
    )
    if lr_scheduler_ckpt is not None:
        lr_scheduler_state_dict = torch.load(lr_scheduler_ckpt, map_location='cpu')
        lr_scheduler.load_state_dict(lr_scheduler_state_dict)

    # 最佳模型保存指标的初始化，包含 auc+f1, auc, f1
    best_auc_f1_model_record_data = {"val_auc_avg_best": 0., "val_f1_avg_best": 0.}
    if os.path.exists(best_auc_f1_model_record):
        with open(best_auc_f1_model_record, 'r', encoding='utf-8') as f:
            data = json.load(f)
            best_auc_f1_model_record_data['val_auc_avg_best'] = data['val_auc_avg_best']
            best_auc_f1_model_record_data['val_f1_avg_best'] = data['val_f1_avg_best']

    best_auc_model_record_data = {"val_auc_avg_best": 0.}
    if os.path.exists(best_auc_model_record):
        with open(best_auc_model_record, 'r', encoding='utf-8') as f:
            data = json.load(f)
            best_auc_model_record_data['val_auc_avg_best'] = data['val_auc_avg_best']

    best_f1_model_record_data = {"val_f1_avg_best": 0.}
    if os.path.exists(best_f1_model_record):
        with open(best_f1_model_record, 'r', encoding='utf-8') as f:
            data = json.load(f)
            best_f1_model_record_data['val_f1_avg_best'] = data['val_f1_avg_best']

    # accelerator.prepare
    logger.info("Prepare everything with our accelerator\n")
    model, optimizer, lr_scheduler = accelerator.prepare(model, optimizer, lr_scheduler)
    for i, loader in enumerate(all_train_loaders):
        flag, loader = loader
        loader = accelerator.prepare(loader)
        all_train_loaders[i] = (flag, loader)

    if accelerator.is_main_process:
        tracker_config = dict(vars(args))
        tracker_config.pop('train_data_dir')
        exp_date = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        accelerator.init_trackers(f"{args.tracker_project_name}-{exp_date}", config=tracker_config)

    # 经过prepare之后实际长度会发生变化
    train_dataloader_total_steps = sum(len(loader) for _, loader in all_train_loaders)
    num_update_steps_per_epoch = math.ceil(train_dataloader_total_steps / args.gradient_accumulation_steps)
    max_train_steps = args.num_train_epochs * num_update_steps_per_epoch

    if accelerator.is_main_process:
        msg = "\n"
        msg += "***** Running training *****\n"
        msg += f"  Model type: {args.model_type}\n"
        msg += f"  Num checkpoints to keep: {args.num_ckpt_to_keep}\n"
        msg += f"  Dataset seed: {seed}\n"
        msg += f"  best_auc_f1_model_record_data: {best_auc_f1_model_record_data}\n"
        msg += f"  best_auc_model_record_data: {best_auc_model_record_data}\n"
        msg += f"  best_f1_model_record_data: {best_f1_model_record_data}\n"
        msg += f"  Num examples = {sum(len(dataset) for dataset in all_train_datasets)}\n"
        msg += f"  Num epochs = {args.num_train_epochs}\n"
        msg += f"  Batch size per device = {train_batch_size_per_device}\n"
        msg += f"  Total train batch size (w. parallel, distributed & accumulation) = {args.train_batch_size}\n"
        msg += f"  Gradient Accumulation steps = {args.gradient_accumulation_steps}\n"
        msg += f"  Total optimization steps = {max_train_steps}\n"
        msg += "\n"
        logger.info(msg)
        file_logger.info(msg)

    first_epoch = 0
    exists_epoch_ckpts = list(Path(args.output_dir).glob('model_epoch-*'))
    if len(exists_epoch_ckpts) > 0:
        epoch_numbers = [int(f.stem.split('-')[1]) for f in exists_epoch_ckpts]
        first_epoch = max(epoch_numbers) - 1

    global_step = start_steps
    global_update_steps = 0

    progress_bar = tqdm(
        range(0, max_train_steps),
        initial=global_step,
        desc="Steps",
        # Only show the progress bar once on each machine.
        disable=not accelerator.is_local_main_process,
    )

    for epoch in range(first_epoch, args.num_train_epochs):
        for step, (flag, batch) in enumerate(walk_dataloaders(all_train_loaders)):
            images, labels = batch['image'], batch['label']
            images = images.to(accelerator.device, weight_dtype)

            # 根据 flag 决定前馈模型
            if flag == DatasetFlag.CLS_CLEAR_BLUR.value:
                preds = model.cls_blur_forward(images)
            elif flag == DatasetFlag.CLS_HUMAN.value:
                preds = model.cls_human_forward(images)
            elif flag == DatasetFlag.CLS_WATERMARKED.value:
                preds = model.cls_watermarked_forward(images)
            elif flag == DatasetFlag.CLS_TEXED.value:
                preds = model.cls_texted_forward(images)
            elif flag == DatasetFlag.CLS_BLACK_NOBLACK.value:
                preds = model.cls_doc_black_forward(images)
            elif flag == DatasetFlag.CLS_DOC_BLUR_AND_NOBLUR.value:
                preds = model.cls_doc_blur_forward(images)
            elif flag == DatasetFlag.CLS_DOC_NODOC.value:
                preds = model.cls_doc_scene_forward(images)
            elif flag == DatasetFlag.CLS_SHADOW_AND_NOSHADOW.value:
                preds = model.cls_doc_shadow_forward(images)
            elif flag == DatasetFlag.CLS_SNAP_CAPTURED.value:
                preds = model.cls_doc_capture_forward(images)
            elif flag == DatasetFlag.CLS_CERT_DOC.value:
                preds = model.cls_cert_doc_forward(images)
            else:
                raise ValueError(f"Unrecognized flag: {flag}")

            # 分布式训练为了统计方便，把多任务的准确率综合在一起
            acc = ((preds > 0.5) == labels).sum().item() / labels.size(0)
            acc = torch.tensor(acc).to(accelerator.device)

            loss = loss_criterion(preds, labels)
            accelerator.backward(loss)

            avg_acc = accelerator.gather(acc.repeat(args.train_batch_size)).mean().item()
            avg_loss = accelerator.gather(loss.repeat(args.train_batch_size)).mean().item()

            # 更新模型权重
            global_update_steps += 1
            if global_update_steps % args.gradient_accumulation_steps == 0:
                if args.use_clip_grad_norm:
                    accelerator.clip_grad_norm_(params_to_opt, args.clip_grad_norm)
                optimizer.step()
                lr_scheduler.step()
                optimizer.zero_grad()
                progress_bar.update(1)
                global_step += 1

                if accelerator.is_main_process:
                    logs = {
                        "loss": loss.detach().item(),
                        "avg_loss": avg_loss,
                        "avg_acc": avg_acc,
                        "lr": lr_scheduler.get_last_lr()[0],
                    }
                    progress_bar.set_postfix(**logs)
                    accelerator.log(logs, step=global_step)

                if accelerator.is_main_process and global_step % args.save_steps == 0:
                    save_path = os.path.join(args.output_dir, f"checkpoint-{global_step}")
                    save_state(save_path, model, optimizer, lr_scheduler, accelerator, args.num_ckpt_to_keep)

                    # 下面是根据不同记录保存最佳模型
                    val_metrics = log_validation(args, model, global_step, accelerator, weight_dtype, all_test_loaders)
                    best_record_path = [best_auc_f1_model_record, best_auc_model_record, best_f1_model_record]
                    best_record_data = [best_auc_f1_model_record_data, best_auc_model_record_data, best_f1_model_record_data]
                    for record_path, record in zip(best_record_path, best_record_data):
                        save_best_checkpoints(
                            args, accelerator, model, optimizer, lr_scheduler,
                            global_step, val_metrics, record_path, record
                        )

            if global_step >= max_train_steps:
                break

        # 当前Epoch训练结束，保存当前模型
        if accelerator.is_main_process and args.save_every_n_epoch > 0 and (epoch + 1) % args.save_every_n_epoch == 0:
            save_path = os.path.join(args.output_dir, f"model_epoch-{epoch + 1}")
            save_state(save_path, model, optimizer, lr_scheduler, accelerator)

    # 训练结束，保存最终模型
    accelerator.wait_for_everyone()
    if accelerator.is_main_process:
        save_path = os.path.join(args.output_dir, f"model_final")
        save_state(save_path, model, optimizer, lr_scheduler, accelerator)
    accelerator.end_training()


def save_best_checkpoints(
    args,
    accelerator,
    model,
    optimizer,
    lr_scheduler,
    global_step,
    val_metrics,
    record_dump_path,
    current_best_record
):
    tmp_record = dict()
    found_best_model = False
    is_reach_best_records = []
    val_acc_avg, val_auc_avg, val_f1_avg = val_metrics
    if "val_acc_avg_best" in current_best_record:
        tmp_record['val_acc_avg_best'] = val_acc_avg
        is_reach_best_records.append(val_acc_avg >= current_best_record["val_acc_avg_best"])
    if "val_auc_avg_best" in current_best_record:
        tmp_record['val_auc_avg_best'] = val_auc_avg
        is_reach_best_records.append(val_auc_avg >= current_best_record["val_auc_avg_best"])
    if "val_f1_avg_best" in current_best_record:
        tmp_record['val_f1_avg_best'] = val_f1_avg
        is_reach_best_records.append(val_f1_avg >= current_best_record["val_f1_avg_best"])

    if all(is_reach_best_records):
        current_best_record.update(tmp_record)
        with open(record_dump_path, 'w', encoding='utf-8') as f:
            data = dict()
            data.update(current_best_record)
            data['checkpoint_step'] = global_step
            json.dump(data, f, ensure_ascii=False, indent=4)

        found_best_model = True
        msg = f"Found best model: {data}"
        logger.info(msg)
        file_logger.info(msg)

    if found_best_model:
        best_save_path = Path(record_dump_path).parent
        save_state(best_save_path, model, optimizer, lr_scheduler, accelerator)


def get_optimizer(args, params_to_opt, optimizer_ckpt):
    if args.use_8bit_adam:
        try:
            import bitsandbytes as bnb
        except ImportError:
            raise ImportError(
                "To use 8-bit Adam, please install the bitsandbytes library: `pip install bitsandbytes`."
            )
        optimizer_class = bnb.optim.AdamW8bit
    else:
        optimizer_class = torch.optim.AdamW

    optimizer = optimizer_class(
        params_to_opt,
        lr=args.learning_rate,
        betas=(args.adam_beta1, args.adam_beta2),
        weight_decay=args.adam_weight_decay,
        eps=args.adam_epsilon,
    )

    if optimizer_ckpt is not None:
        optimizer_state_dict = torch.load(optimizer_ckpt, map_location='cpu')
        optimizer.load_state_dict(optimizer_state_dict)

    return optimizer


def manage_checkpoints(output_dir, n_checkpoints_to_keep):
    checkpoints = sorted(
        [d for d in os.listdir(output_dir) if re.match(r'^checkpoint-\d+$', d)],
        key=lambda x: int(x.split('-')[1])
    )

    if len(checkpoints) > n_checkpoints_to_keep:
        checkpoints_to_delete = checkpoints[:-n_checkpoints_to_keep]
        msg = f"Old version checkpoints to delete: {checkpoints_to_delete}"
        logger.info(msg)
        file_logger.info(msg)
        for checkpoint in checkpoints_to_delete:
            checkpoint_path = os.path.join(output_dir, checkpoint)
            if os.path.isdir(checkpoint_path):
                for file in os.listdir(checkpoint_path):
                    file_path = os.path.join(checkpoint_path, file)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                os.rmdir(checkpoint_path)


def save_state(save_dir, model, optimizer, scheduler, accelerator, n_checkpoints_to_keep=None):
    os.makedirs(save_dir, exist_ok=True)

    model = accelerator.unwrap_model(model)
    optimizer = accelerator.unwrap_model(optimizer)
    scheduler = accelerator.unwrap_model(scheduler)

    torch.save(model.state_dict(), os.path.join(save_dir, "model.bin"))
    torch.save(optimizer.state_dict(), os.path.join(save_dir, f"optimizer.bin"))
    torch.save(scheduler.state_dict(), os.path.join(save_dir, f"scheduler.bin"))

    # 保持最新的n个checkpoint
    if n_checkpoints_to_keep is not None:
        manage_checkpoints(os.path.dirname(save_dir), n_checkpoints_to_keep)


def log_validation(args, model, steps, accelerator, weight_dtype, test_dataloaders):
    model = accelerator.unwrap_model(model)
    model.eval()

    # 初始化存储所有样本的预测值和标签的列表
    all_preds = {flag.value: [] for flag in DatasetFlag}
    all_labels = {flag.value: [] for flag in DatasetFlag}

    print()
    msg = f"Running metrics for step-{steps}..."
    logger.info(msg)
    file_logger.info(msg)
    test_dataloader_total_steps = sum(len(loader) for flag, loader in test_dataloaders)
    test_pbar = tqdm(total=test_dataloader_total_steps, desc='evaluating...')

    for step, (flag, batch) in enumerate(walk_dataloaders(test_dataloaders)):
        images, labels = batch['image'], batch['label']
        images = images.to(accelerator.device, weight_dtype)

        # 根据flag选择前馈模型
        with torch.no_grad():
            if flag == DatasetFlag.CLS_CLEAR_BLUR.value:
                preds = model.cls_blur_forward(images)
            elif flag == DatasetFlag.CLS_HUMAN.value:
                preds = model.cls_human_forward(images)
            elif flag == DatasetFlag.CLS_WATERMARKED.value:
                preds = model.cls_watermarked_forward(images)
            elif flag == DatasetFlag.CLS_TEXED.value:
                preds = model.cls_texted_forward(images)
            elif flag == DatasetFlag.CLS_BLACK_NOBLACK.value:
                preds = model.cls_doc_black_forward(images)
            elif flag == DatasetFlag.CLS_DOC_BLUR_AND_NOBLUR.value:
                preds = model.cls_doc_blur_forward(images)
            elif flag == DatasetFlag.CLS_DOC_NODOC.value:
                preds = model.cls_doc_scene_forward(images)
            elif flag == DatasetFlag.CLS_SHADOW_AND_NOSHADOW.value:
                preds = model.cls_doc_shadow_forward(images)
            elif flag == DatasetFlag.CLS_SNAP_CAPTURED.value:
                preds = model.cls_doc_capture_forward(images)
            elif flag == DatasetFlag.CLS_CERT_DOC.value:
                preds = model.cls_cert_doc_forward(images)
            else:
                raise ValueError(f"Unrecognized flag: {flag}")

        # 将预测值和标签存储下来
        all_preds[flag].append(preds.cpu())
        all_labels[flag].append(labels.cpu())

        test_pbar.update(1)

    test_pbar.close()

    # 初始化总计量器
    total_acc_meter = AverageMeter()
    total_auc_meter = AverageMeter()
    total_f1_meter = AverageMeter()

    # 初始化按flag分的计量器
    flag_acc_meters = {flag.value: AverageMeter() for flag in DatasetFlag}
    flag_auc_meters = {flag.value: AverageMeter() for flag in DatasetFlag}
    flag_f1_meters = {flag.value: AverageMeter() for flag in DatasetFlag}

    # 计算每个flag的指标以及总的指标
    for flag in all_preds:
        preds = torch.cat(all_preds[flag], dim=0)
        labels = torch.cat(all_labels[flag], dim=0)

        # 打印ROC曲线
        print("\n")
        fpr, tpr, thresholds = calculate_fpr_recall_at_thresholds(labels.numpy(), preds.numpy(), n_thresholds=10)
        msg = f"**************** ROC of {flag} ****************"
        logger.info(msg)
        file_logger.info(msg)
        for i in range(len(thresholds)):
            msg = f"{flag} threshold: {thresholds[i]:.2f}, fpr: {fpr[i]:.2f}, recall: {tpr[i]:.2f}"
            logger.info(msg)
            file_logger.info(msg)
        print("\n")

        # 计算准确率、AUC 和 F1
        acc = ((preds > 0.5).float() == labels).sum().item() / labels.size(0)
        auc = roc_auc_score(labels.numpy(), preds.numpy())
        f1 = f1_score(labels.numpy(), (preds > 0.5).numpy(), average='macro')

        # 更新总的计量器
        total_acc_meter.update(acc, n=labels.size(0))
        total_auc_meter.update(auc, n=labels.size(0))
        total_f1_meter.update(f1, n=labels.size(0))

        # 更新flag对应的计量器
        flag_acc_meters[flag].update(acc, n=labels.size(0))
        flag_auc_meters[flag].update(auc, n=labels.size(0))
        flag_f1_meters[flag].update(f1, n=labels.size(0))

    logs = {
        "val_acc": total_acc_meter.avg,
        "val_auc": total_auc_meter.avg,
        "val_f1": total_f1_meter.avg
    }
    accelerator.log(logs, step=steps)

    # 输出最终的综合指标
    msg = (
        f"\nTotal Metrics\n"
        f"ACC: {total_acc_meter.avg:.4f}, "
        f"AUC: {total_auc_meter.avg:.4f}, "
        f"F1: {total_f1_meter.avg:.4f}"
    )
    logger.info(msg)
    file_logger.info(msg)

    for flag in flag_acc_meters:
        msg = (
            f"Metrics of {flag}\n"
            f"ACC: {flag_acc_meters[flag].avg:.4f}, "
            f"AUC: {flag_auc_meters[flag].avg:.4f}, "
            f"F1: {flag_f1_meters[flag].avg:.4f}"
        )
        logger.info(msg)
        file_logger.info(msg)

    model.train()

    return total_acc_meter.avg, total_auc_meter.avg, total_f1_meter.avg


if __name__ == '__main__':
    main()