#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/10/10 11:14
# <AUTHOR> <EMAIL>
# @FileName: eval.py

import os
import json
import torch
import warnings
from enum import Enum
from tqdm import tqdm
from torch.utils.data import DataLoader
from sklearn.metrics import roc_auc_score, f1_score

import modules.utils.torch_utils as torch_utils
from modules.utils.log import create_file_logger
from networks.utils.metrics.eval import AverageMeter
from modules.utils.train_utils import calculate_fpr_recall_at_thresholds
from my_datasets.kantu_classifier import MyCustomDataset, walk_dataloaders
from networks.mobilenet.model import MultiTaskMobileNetV3Small, MultiTaskMobileNetV3Large

# 忽略特定警告
warnings.filterwarnings("ignore", category=UserWarning)


# 数据集标识枚举
class DatasetFlag(Enum):
    CLS_BLACK_NOBLACK = 'cls_black_noblack'
    CLS_DOC_BLUR_AND_NOBLUR = 'cls_doc_blur_and_noblur'
    CLS_DOC_NODOC = 'cls_doc_nodoc'
    CLS_SHADOW_AND_NOSHADOW = 'cls_shadow_and_noshadow'
    CLS_SNAP_CAPTURED = 'cls_snap_captured'
    CLS_CLEAR_BLUR = 'cls_clear_blur'
    CLS_HUMAN = 'cls_human'
    CLS_TEXED = 'cls_texted'
    CLS_WATERMARKED = 'cls_watermarked'


# 准备数据集加载器
def prepare_dataloaders(train_data_dirs, mode, batch_size_per_device, num_workers):
    dataset_dir = []
    for _dir in train_data_dirs:
        dataset_dir.append((_dir, -1))

    all_datasets = []
    for flag in DatasetFlag:
        all_datasets.append(MyCustomDataset(dataset_dir, flag.value, mode))

    all_loaders = []
    for flag, cur_dataset in zip(DatasetFlag, all_datasets):
        cur_loader = DataLoader(
            cur_dataset,
            shuffle=False,
            batch_size=batch_size_per_device,
            num_workers=num_workers,
        )
        all_loaders.append((flag.value, cur_loader))

    return all_datasets, all_loaders


# 模型验证部分
def validate_model(model, device, test_loaders, output_dir, logger):
    model.eval()  # 设置为验证模式，自动关闭 dropout 和 batchnorm 的训练行为

    all_preds = {flag.value: [] for flag in DatasetFlag}
    all_labels = {flag.value: [] for flag in DatasetFlag}

    logger.info(f"Running evaluation for model")
    test_dataloader_total_steps = sum(len(loader) for _, loader in test_loaders)
    test_pbar = tqdm(total=test_dataloader_total_steps, desc='Evaluating...')

    for step, (flag, batch) in enumerate(walk_dataloaders(test_loaders)):
        images, labels = batch['image'], batch['label']
        images = images.to(device)

        with torch.no_grad():  # 禁用梯度计算，节省内存和加速推理
            preds = model_forward_by_flag(model, flag, images)

        all_preds[flag].append(preds.cpu())
        all_labels[flag].append(labels.cpu())

        test_pbar.update(1)

    test_pbar.close()

    total_acc_meter, total_auc_meter, total_f1_meter = AverageMeter(), AverageMeter(), AverageMeter()
    flag_acc_meters = {flag.value: AverageMeter() for flag in DatasetFlag}
    flag_auc_meters = {flag.value: AverageMeter() for flag in DatasetFlag}
    flag_f1_meters = {flag.value: AverageMeter() for flag in DatasetFlag}

    # 遍历每个数据集标识，计算指标
    for flag in all_preds:
        preds = torch.cat(all_preds[flag], dim=0)
        labels = torch.cat(all_labels[flag], dim=0)

        # 计算ROC, FPR, TPR
        fpr, tpr, thresholds = calculate_fpr_recall_at_thresholds(labels.numpy(), preds.numpy(), n_thresholds=10)
        log_roc_curve(flag, fpr, tpr, thresholds, logger)

        # 计算准确率、AUC 和 F1
        acc = ((preds > 0.5).float() == labels).sum().item() / labels.size(0)
        auc = roc_auc_score(labels.numpy(), preds.numpy())
        f1 = f1_score(labels.numpy(), (preds > 0.5).numpy(), average='macro')

        total_acc_meter.update(acc, n=labels.size(0))
        total_auc_meter.update(auc, n=labels.size(0))
        total_f1_meter.update(f1, n=labels.size(0))

        flag_acc_meters[flag].update(acc, n=labels.size(0))
        flag_auc_meters[flag].update(auc, n=labels.size(0))
        flag_f1_meters[flag].update(f1, n=labels.size(0))

    logger.info(
        f"Validation Results - ACC: {total_acc_meter.avg:.4f}, AUC: {total_auc_meter.avg:.4f}, F1: {total_f1_meter.avg:.4f}")
    save_metrics_to_json(output_dir, total_acc_meter.avg, total_auc_meter.avg, total_f1_meter.avg, logger)

    return total_acc_meter.avg, total_auc_meter.avg, total_f1_meter.avg


# 根据标识选择模型前馈
def model_forward_by_flag(model, flag, images):
    if flag == DatasetFlag.CLS_CLEAR_BLUR.value:
        return model.cls_blur_forward(images)
    elif flag == DatasetFlag.CLS_HUMAN.value:
        return model.cls_human_forward(images)
    elif flag == DatasetFlag.CLS_WATERMARKED.value:
        return model.cls_watermarked_forward(images)
    elif flag == DatasetFlag.CLS_TEXED.value:
        return model.cls_texted_forward(images)
    elif flag == DatasetFlag.CLS_BLACK_NOBLACK.value:
        return model.cls_doc_black_forward(images)
    elif flag == DatasetFlag.CLS_DOC_BLUR_AND_NOBLUR.value:
        return model.cls_doc_blur_forward(images)
    elif flag == DatasetFlag.CLS_DOC_NODOC.value:
        return model.cls_doc_scene_forward(images)
    elif flag == DatasetFlag.CLS_SHADOW_AND_NOSHADOW.value:
        return model.cls_doc_shadow_forward(images)
    elif flag == DatasetFlag.CLS_SNAP_CAPTURED.value:
        return model.cls_doc_capture_forward(images)
    else:
        raise ValueError(f"Unrecognized flag: {flag}")


# 保存指标到JSON
def save_metrics_to_json(output_dir, acc, auc, f1, logger):
    metrics = {
        'accuracy': acc,
        'auc': auc,
        'f1_score': f1,
    }
    metrics_path = os.path.join(output_dir, 'eval_metrics.json')
    with open(metrics_path, 'w', encoding='utf-8') as f:
        json.dump(metrics, f, indent=4)
    logger.info(f"Metrics saved to {metrics_path}")


# 打印ROC曲线
def log_roc_curve(flag, fpr, tpr, thresholds, logger):
    logger.info(f"**************** ROC of {flag} ****************")
    for i in range(len(thresholds)):
        msg = f"{flag} threshold: {thresholds[i]:.2f}, fpr: {fpr[i]:.2f}, recall: {tpr[i]:.2f}"
        logger.info(msg)
    logger.info("\n")


def main():
    # 显式指定参数
    reuse_checkpoint = "/aicamera-mlp-old/xelawk_train_space/training_outputs/release_202410/large_kantu_classifier_distributed_v202410102206/best_f1_model"  # 模型检查点路径
    output_dir = reuse_checkpoint  # 输出目录
    model_type = "mbv3_large"  # 模型类型 ('mbv3_small' 或 'mbv3_large')
    train_data_dirs = ["/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/release_hf/v202410091945"]  # 训练数据路径（可以是多个路径）
    batch_size_per_device = 32  # 每个设备的批量大小
    num_workers = 4  # DataLoader 的工作线程数

    # 创建日志
    logger = create_file_logger(os.path.join(output_dir, "eval.log"))

    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 加载模型
    if os.path.exists(reuse_checkpoint):
        state_dict = torch.load(os.path.join(reuse_checkpoint, "model.bin"), map_location='cpu')
        logger.info(f"Loading model from {reuse_checkpoint}")
    else:
        raise FileNotFoundError(f"Checkpoint not found at {reuse_checkpoint}")

    # 初始化模型
    if model_type == "mbv3_small":
        model = MultiTaskMobileNetV3Small()
    elif model_type == "mbv3_large":
        model = MultiTaskMobileNetV3Large()
    else:
        raise ValueError(f"Unsupported model type: {model_type}")

    model = torch_utils.convert_batchnorm_to_sync_batchnorm(model)
    model.load_state_dict(state_dict, strict=True)
    model.to(device)

    # 准备数据集和验证
    all_test_datasets, all_test_loaders = prepare_dataloaders(
        train_data_dirs, 'test', batch_size_per_device, num_workers
    )
    validate_model(model, device, all_test_loaders, output_dir, logger)


if __name__ == '__main__':
    main()
