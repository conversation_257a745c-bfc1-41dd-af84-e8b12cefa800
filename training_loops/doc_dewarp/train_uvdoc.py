#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/10/15 14:58
# <AUTHOR> <EMAIL>
# @FileName: train_uvdoc

import os
import re
import sys

import accelerate.data_loader
import math
import json
import random
import datetime
import warnings
from pathlib import Path

import torch
import numpy as np
import torch.utils.data
from omegaconf import OmegaConf

from tqdm import tqdm
from PIL import Image
from accelerate.logging import get_logger
from diffusers.optimization import get_scheduler

import modules.utils.log
import modules.utils.path_utils as path_utils
import modules.utils.torch_utils as torch_utils
from networks.lama.saicinpainting.modules import make_generator
from networks.utils.metrics.eval import AverageMeter
from modules.utils.train_utils import prepare_training_enviornment
from modules.utils.image_utils import get_all_image_path, normalize_image_rgb

from networks.uvdoc.models import UVDocNet
# from networks.uvdoc.models import UVDocNet_biref2
from networks.uvdoc.losses import TrainingLoss,TrainingLoss2
from modules.doc_dewrap.dto import IMG_SIZE, GRID_SIZE
from modules.doc_dewrap.data_utils import bilinear_unwarping
from modules.doc_dewrap.image_utils import get_visualized_results
from my_datasets.doc_dewarp.doc3d_dataset_v1 import Doc3DDataset
from my_datasets.doc_dewarp.uvdoc_dataset_v1 import UVDocDataset
from my_datasets.doc_dewarp.plane_dataset_v1 import PlaneDataset
from modules.proj_cmd_args.doc_dewarp.train_uvdoc import parse_args
logger = get_logger(__name__)
torch.autograd.set_detect_anomaly(True)

# 忽略特定警告
warnings.filterwarnings("ignore", message="torch.meshgrid", category=UserWarning)
warnings.filterwarnings("ignore", message="Grad strides do not match bucket view strides", category=UserWarning)
warnings.filterwarnings("ignore", message="On January 1, 2023, MMCV will release v2.0.0", category=UserWarning, )

import os
# 指定accerate 在那个cuda上进行
# os.environ['ACCELERATE_TORCH_DEVICE']='cuda:1'
def walk_dataloaders(loaders):
    if isinstance(loaders,accelerate.data_loader.DataLoaderShard):
        # 单个数据集的情况
        doing = [iter(loaders)]
    else:
        # 多个数据集的情况
        doing = [iter(loader) for loader in loaders]

    random.shuffle(doing) #  随机打乱 list 的顺序
    i = 0
    while doing:
        i = i % len(doing)
        it = doing[i]
        try:
            batch = next(it)
            yield batch
            i += 1
        except StopIteration:
            del doing[i]



def namespace_to_flat_dict(namespace):
    return {key: value for key, value in vars(namespace).items()}
def save_args(name_dict,save_path):
    with open(save_path,'w',encoding='utf-8') as f:
        for key,value in name_dict.items():
            f.writelines(f'{key}:{value}\n')

# 针对lama调整预训练权重 让4通道变成3通道
def adjust_pretrained_state_dict(state_dict, generator_model):
    # Get the weights that need to be adjusted, take the first 3 channels of the official input layer weights
    key = "model.1.ffc.convl2l.weight"
    if key in state_dict:
        pretrained_weight = state_dict[key]
        msg = f"Original weight shape: {pretrained_weight.shape}"
        logger.info(msg)

        # Check if the number of channels matches
        if pretrained_weight.shape[1] == 4 and generator_model.state_dict()[key].shape[1] == 3:
            # Adjust weight shape: only take weights of first 3 channels
            adjusted_weight = pretrained_weight[:, :3, :, :]
            # Replace weights
            state_dict[key] = adjusted_weight
            msg = f"Adjusted weight shape: {adjusted_weight.shape}"
            logger.info(msg)
        else:
            msg = "Weight channels already match, no adjustment needed."
            logger.info(msg)

    return state_dict
def main():
    args = parse_args()
    accelerator, weight_dtype, = prepare_training_enviornment(args, logger)
    # 记录日志
    file_logger=modules.utils.log.create_file_logger(log_path=os.path.join(args.output_dir,'train_log.log'))
    name_dict=namespace_to_flat_dict(args)

    # 初始化模型、checkpoint复用等
    state_dict = None
    start_steps = 0
    optimizer_ckpt = None
    lr_scheduler_ckpt = None
    best_val_record = os.path.join(args.output_dir, "best_val_record.json")
    # biref_net_权重
    birefNet_state_dict=None
    # 存在检查点 优先加载检查点 检查点里面没有数据 那就加载reuse_checkpoint 参数
    exists_checkpoints = list(Path(args.output_dir).glob('checkpoint-*'))
    if len(exists_checkpoints) > 0:
        args.reuse_checkpoint = None

    if args.reuse_checkpoint is not None and os.path.exists(args.reuse_checkpoint):
        state_dict = torch.load(os.path.join(args.reuse_checkpoint, "model.bin"), map_location='cpu')
        logger.info(f"Reusing checkpoint from {args.reuse_checkpoint}")

    elif len(exists_checkpoints) > 0:
        checkpoint_numbers = [int(f.stem.split('-')[1]) for f in exists_checkpoints]
        resume_steps = max(checkpoint_numbers)
        resume_ckpt_dir = os.path.join(args.output_dir, f'checkpoint-{resume_steps}')
        state_dict = torch.load(os.path.join(resume_ckpt_dir, 'model.bin'), map_location='cpu')
        start_steps = resume_steps
        optimizer_ckpt = os.path.join(resume_ckpt_dir, "optimizer.bin")
        lr_scheduler_ckpt = os.path.join(resume_ckpt_dir, "scheduler.bin")
        logger.info(f"Reusing running checkpoint from {resume_ckpt_dir}")
    
    # 是否使用use_bf_backbone
    if args.load_birefNet is not None and os.path.exists(args.load_birefNet) and args.use_bf_backbone:
        birefNet_state_dict=torch.load(args.load_birefNet,map_location='cpu')

    # 是否使用lama 作为backbone
    generator_model=None
    use_lama_backbone=False if args.use_lama_backbone==0 else True
    if args.generator_cfg_yaml is not None and use_lama_backbone:
        generator_cfg = OmegaConf.load(args.generator_cfg_yaml)  # 模型配置差异
        generator_model = make_generator(**generator_cfg)  # 设置模块
        lama_state_dict=torch.load(r'/aicamera-mlp/xelawk_train_space/aicache/torch/lama_pretrained/generator_model.bin')
        lama_state_dict=adjust_pretrained_state_dict(state_dict=lama_state_dict,generator_model=generator_model)
        generator_model.load_state_dict(lama_state_dict,strict=False)# 加载lama的预训练权重
        
    use_grid_3d=False if args.use_grid_3d==0 else True
    # 切换不同的模型结构
    resnet_size=args.resnet_size# 切换resnet的不同模型大小
    model = UVDocNet(use_grid=args.use_grid,use_bf_backbone=False,use_lama_backbone=use_lama_backbone,lama_backbone=generator_model,use_grid_3d=use_grid_3d,resnet_size=resnet_size)
    # torch.save(model,'/mnt/aicamera-mlp/hz_datasets/tmp/test_hz/uvdoc_lama.pth')
    # torch.save(model.state_dict(),'/mnt/aicamera-mlp/hz_datasets/tmp/test_hz/uvdoc_lama_state_dict.pth')
    # model=UVDocNet_biref2()
    # for k,v in model.named_parameters():
    #     print(k)
    # 解决分布式训练BatchNorm2D引起的梯度计算异常
    model = torch_utils.convert_batchnorm_to_sync_batchnorm(model)
    
    if state_dict is not None:
        # 官方权重存在参数bug 不是直接存在参数 而是 用model_state 去引导的
        # model.load_state_dict(state_dict['model_state'], strict=True)
        # model.load_state_dict(state_dict, strict=True)
        model.load_state_dict(state_dict, strict=False)

    # 将birefNet 权重加载到 UVDoc 里面 use_bf_backbone
    if birefNet_state_dict is not None and args.use_bf_backbone:
        logger.info(f"birefNet_state_dict running checkpoint from {args.load_birefNet}")
        model.load_state_dict(birefNet_state_dict,strict=False)
    
    model.to(accelerator.device, weight_dtype)
    model.train()

    # 初始化优化策略
    if use_grid_3d:
        loss_criterion = TrainingLoss(
            lambda_img=args.lambda_img,
            lambda_2d=args.lambda_2d,
            lambda_3d=args.lambda_3d,
        )
    else:
        loss_criterion = TrainingLoss2(
            lambda_img=args.lambda_img,
            lambda_2d=args.lambda_2d,
        )

    params_to_opt = model.parameters()
    if args.use_8bit_adam:
        try:
            import bitsandbytes as bnb
        except ImportError:
            raise ImportError(
                "To use 8-bit Adam, please install the bitsandbytes library: `pip install bitsandbytes`."
            )
        optimizer_class = bnb.optim.AdamW8bit
    else:
        optimizer_class = torch.optim.AdamW

    optimizer = optimizer_class(
        params_to_opt,# 模型参数
        lr=args.learning_rate,# 初始学习率
        betas=(args.adam_beta1, args.adam_beta2),
        weight_decay=args.adam_weight_decay,
        eps=args.adam_epsilon,
    )

    if optimizer_ckpt is not None:
        optimizer_state_dict = torch.load(optimizer_ckpt, map_location='cpu')
        optimizer.load_state_dict(optimizer_state_dict)

    # 准备数据集，训练步数、Epoch预估、accelerator.prepare 等调度
    num_split = accelerator.num_processes * args.gradient_accumulation_steps
    assert args.train_batch_size % num_split == 0, \
        (f"batch_size: {args.train_batch_size} needs to be divisible by "
         f"num_processes*gradient_accumulation_steps={num_split}")

    if args.seed is not None:
        seed = int(args.seed)
    else:
        seed = random.randint(1, 100000)

    # 计算每台设备的 Batch size
    train_batch_size_per_device = args.train_batch_size // num_split

    # 准备数据集
    # 训练集
    train_datasets = []
    if args.doc3d_data_dir:
        doc3d_dataset_train = Doc3DDataset(# 7w
            data_path=args.doc3d_data_dir,
            split='train',
            img_size=IMG_SIZE,
            grid_size=GRID_SIZE,
            use_grid=args.use_grid,
            crop_tight_p=args.crop_tight_p,
            normalize_grid3d=True,
            grid_locally_enhanced=True,
            grid_locally_enhanced_method=args.grid_locally_enhanced_method,
            p_grid_locally_enhanced=args.p_grid_locally_enhanced,
            appearance_augmentation=args.a_aug,
            seed=seed,
            resample_num=-1
        )
        train_datasets.append(doc3d_dataset_train)
    if args.Doc3D_grid_perspect:
        doc3d_perspect_dataset_train = Doc3DDataset(  # 7w
            data_path=args.Doc3D_grid_perspect,
            split='train',
            img_size=IMG_SIZE,
            grid_size=GRID_SIZE,
            use_grid=args.use_grid,
            crop_tight_p=args.crop_tight_p,
            normalize_grid3d=True,
            grid_locally_enhanced=True,
            grid_locally_enhanced_method=args.grid_locally_enhanced_method,
            p_grid_locally_enhanced=args.p_grid_locally_enhanced,
            appearance_augmentation=args.a_aug,
            seed=seed,
            resample_num=-1
        )
        train_datasets.append(doc3d_perspect_dataset_train)

    if args.perspect_data_dir:
        # 加载透视类型的数据集 加载格式是doc3d 1w
        perspect_dataset_train = Doc3DDataset(
            data_path=args.perspect_data_dir,
            split='train',
            img_size=IMG_SIZE,
            grid_size=GRID_SIZE,
            use_grid=args.use_grid,
            crop_tight_p=args.crop_tight_p,
            normalize_grid3d=True,
            grid_locally_enhanced=True,
            p_grid_locally_enhanced=args.p_grid_locally_enhanced,
            grid_locally_enhanced_method=args.grid_locally_enhanced_method,
            appearance_augmentation=args.a_aug,
            seed=seed,
            resample_num=len(doc3d_dataset_train)
        )
        train_datasets.append(perspect_dataset_train)
    if args.uvdoc_data_dir:
        uvdoc_dataset_train = UVDocDataset(#7w
            data_path=args.uvdoc_data_dir,
            split='train',
            img_size=IMG_SIZE,
            grid_size=GRID_SIZE,
            use_grid=args.use_grid,
            crop_tight_p=args.crop_tight_p,
            normalize_grid3d=True,
            grid_locally_enhanced=True,
            p_grid_locally_enhanced=args.p_grid_locally_enhanced,
            grid_locally_enhanced_method=args.grid_locally_enhanced_method,
            appearance_augmentation=args.a_aug,
            geometric_augmentations=args.g_uvdoc_aug,
            seed=seed,
            resample_num=len(doc3d_dataset_train)
        )
        train_datasets.append(uvdoc_dataset_train)
    if args.user_data_uvdoc_final:
        user_data_uvdoc_final = UVDocDataset(  # 7w
            data_path=args.user_data_uvdoc_final,
            split='train',
            img_size=IMG_SIZE,
            grid_size=GRID_SIZE,
            use_grid=args.use_grid,
            crop_tight_p=args.crop_tight_p,
            normalize_grid3d=True,
            grid_locally_enhanced=True,
            p_grid_locally_enhanced=args.p_grid_locally_enhanced,
            grid_locally_enhanced_method=args.grid_locally_enhanced_method,
            appearance_augmentation=args.a_aug,
            geometric_augmentations=args.g_uvdoc_aug,
            seed=seed,
            resample_num=len(doc3d_dataset_train)
        )
        train_datasets.append(user_data_uvdoc_final)

    if args.plane_data_dir:
        plane_dataset_train = PlaneDataset(
            data_path=args.plane_data_dir,
            split='train',
            img_size=IMG_SIZE,
            grid_size=GRID_SIZE,
            seed=seed,
            resample_num=len(doc3d_dataset_train)
        )
        train_datasets.append(plane_dataset_train)

    assert len(train_datasets) > 0
    train_dataloaders = []
    for train_dataset in train_datasets:
        train_dataloader = torch.utils.data.DataLoader(
            train_dataset,
            shuffle=True,
            batch_size=train_batch_size_per_device,
            num_workers=args.dataloader_num_workers,
        )
        train_dataloaders.append(train_dataloader)

    # 测试数据集
    test_datasets = []
    if args.doc3d_data_dir:
        doc3d_dataset_test = Doc3DDataset(
            data_path=args.doc3d_data_dir,
            split='val',
            img_size=IMG_SIZE,
            grid_size=GRID_SIZE,
            use_grid=args.use_grid,
            crop_tight_p=args.crop_tight_p,
            normalize_grid3d=True,
            grid_locally_enhanced=True,
            p_grid_locally_enhanced=args.p_grid_locally_enhanced,
            grid_locally_enhanced_method=args.grid_locally_enhanced_method,
            appearance_augmentation=args.a_aug,
            seed=seed
        )
        test_datasets.append(doc3d_dataset_test)

    if args.perspect_data_dir:
        # 加载透视数据集是验证集
        perspect_dataset_test = Doc3DDataset(
            data_path=args.perspect_data_dir,
            split='val',
            img_size=IMG_SIZE,
            grid_size=GRID_SIZE,
            use_grid=args.use_grid,
            crop_tight_p=args.crop_tight_p,
            normalize_grid3d=True,
            grid_locally_enhanced=True,
            p_grid_locally_enhanced=args.p_grid_locally_enhanced,
            grid_locally_enhanced_method=args.grid_locally_enhanced_method,
            appearance_augmentation=args.a_aug,
            seed=seed
        )
        test_datasets.append(perspect_dataset_test)
    if args.Doc3D_grid_perspect:
        # 加载透视数据集是验证集
        Doc3D_grid_perspect = Doc3DDataset(
            data_path=args.Doc3D_grid_perspect,
            split='val',
            img_size=IMG_SIZE,
            grid_size=GRID_SIZE,
            use_grid=args.use_grid,
            crop_tight_p=args.crop_tight_p,
            normalize_grid3d=True,
            grid_locally_enhanced=True,
            p_grid_locally_enhanced=args.p_grid_locally_enhanced,
            grid_locally_enhanced_method=args.grid_locally_enhanced_method,
            appearance_augmentation=args.a_aug,
            seed=seed
        )
        test_datasets.append(Doc3D_grid_perspect)
    if args.uvdoc_data_dir:
        uvdoc_dataset_test = UVDocDataset(
            data_path=args.uvdoc_data_dir,
            split='val',
            img_size=IMG_SIZE,
            grid_size=GRID_SIZE,
            use_grid=args.use_grid,
            crop_tight_p=args.crop_tight_p,
            normalize_grid3d=True,
            grid_locally_enhanced=True,
            p_grid_locally_enhanced=args.p_grid_locally_enhanced,
            grid_locally_enhanced_method=args.grid_locally_enhanced_method,
            appearance_augmentation=args.a_aug,
            geometric_augmentations=args.g_uvdoc_aug,
            seed=seed,
            resample_num=len(doc3d_dataset_test)
        )
        test_datasets.append(uvdoc_dataset_test)
    if args.user_data_uvdoc_final:
        user_data_uvdoc_final = UVDocDataset(
            data_path=args.user_data_uvdoc_final,
            split='val',
            img_size=IMG_SIZE,
            grid_size=GRID_SIZE,
            use_grid=args.use_grid,
            crop_tight_p=args.crop_tight_p,
            normalize_grid3d=True,
            grid_locally_enhanced=True,
            p_grid_locally_enhanced=args.p_grid_locally_enhanced,
            grid_locally_enhanced_method=args.grid_locally_enhanced_method,
            appearance_augmentation=args.a_aug,
            geometric_augmentations=args.g_uvdoc_aug,
            seed=seed,
            resample_num=len(doc3d_dataset_test)
        )
        test_datasets.append(user_data_uvdoc_final)
    if args.plane_data_dir:
        plane_dataset_test = PlaneDataset(
            data_path=args.plane_data_dir,
            split='val',
            img_size=IMG_SIZE,
            grid_size=GRID_SIZE,
            seed=seed,
            resample_num=len(doc3d_dataset_test)
        )
        test_datasets.append(plane_dataset_test)

    assert len(test_datasets) > 0
    test_dataloaders = []
    for test_dataset in test_datasets:
        test_dataloader = torch.utils.data.DataLoader(
            test_dataset,
            shuffle=True,
            batch_size=train_batch_size_per_device,
            num_workers=args.dataloader_num_workers,
        )
        test_dataloaders.append(test_dataloader)

    train_dataloader_total_steps = sum(len(loader) for loader in train_dataloaders)
    num_update_steps_per_epoch = math.ceil(train_dataloader_total_steps / args.gradient_accumulation_steps)
    max_train_steps = args.num_train_epochs * num_update_steps_per_epoch # 在整个迭代周期上是逐渐余弦相似的降低

    lr_scheduler = get_scheduler(
        args.lr_scheduler,
        optimizer=optimizer,
        num_training_steps=max_train_steps,
        num_cycles=args.lr_num_cycles,
        power=args.lr_power,
        num_warmup_steps=args.lr_warmup_steps * accelerator.num_processes,
    )
    if lr_scheduler_ckpt is not None:# 学习率加载地方
        lr_scheduler_state_dict = torch.load(lr_scheduler_ckpt, map_location='cpu')
        lr_scheduler.load_state_dict(lr_scheduler_state_dict)

    val_grid_l1_avg_best, val_img_mse_best = sys.float_info.max, sys.float_info.max
    if os.path.exists(best_val_record):
        with open(best_val_record, 'r', encoding='utf-8') as f:
            data = json.load(f)
            val_grid_l1_avg_best = data['val_grid_l1_avg_best']
            val_img_mse_best = data['val_img_mse_best']

    # accelerator.prepare
    logger.info("Prepare everything with our accelerator\n")
    model, optimizer, lr_scheduler = accelerator.prepare(model, optimizer, lr_scheduler)
    # # 启用未使用参数检测
    # accelerator.state.distributed_type = "DDP"
    # accelerator.state._ddp_kwargs = {"find_unused_parameters": True}
    # 要是多个数据集就会把多个数据集list变成元组 要是单个数据集 输入list返回该数据集
    train_dataloaders = accelerator.prepare(*train_dataloaders)
    # 若是单个数据集就直接把数据集封裝成元组

    if accelerator.is_main_process:
        tracker_config = dict(vars(args))
        exp_date = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        accelerator.init_trackers(f"{args.tracker_project_name}-{exp_date}")
        save_args(name_dict, save_path=os.path.join(args.output_dir, 'args.txt'))

    # 经过prepare之后实际长度会发生变化
    train_dataloader_total_steps = sum(len(loader) for loader in train_dataloaders)
    num_update_steps_per_epoch = math.ceil(train_dataloader_total_steps / args.gradient_accumulation_steps)
    max_train_steps = args.num_train_epochs * num_update_steps_per_epoch

    logger.info("***** Running training *****")
    logger.info(f"  Dataset seed: {seed}")
    logger.info(f"  Starting best Grid L1: {val_grid_l1_avg_best}, best Img MSE: {val_img_mse_best}")
    logger.info(f"  Num checkpoints to keep: {args.num_ckpt_to_keep}")
    logger.info(f"  Appearance augmentation: {args.a_aug}")
    logger.info(f"  Geometric UVDoc augmentation: {args.g_uvdoc_aug}")
    logger.info(f"  Num examples = {len(train_dataset)}")
    logger.info(f"  Num epochs = {args.num_train_epochs}")
    logger.info(f"  Batch size per device = {train_batch_size_per_device}")
    logger.info(f"  Total train batch size (w. parallel, distributed & accumulation) = {args.train_batch_size}")
    logger.info(f"  Gradient Accumulation steps = {args.gradient_accumulation_steps}")
    logger.info(f"  Total optimization steps = {max_train_steps}")

    first_epoch = 0
    exists_epoch_ckpts = list(Path(args.output_dir).glob('model_epoch-*'))
    if len(exists_epoch_ckpts) > 0:
        epoch_numbers = [int(f.stem.split('-')[1]) for f in exists_epoch_ckpts]
        first_epoch = max(epoch_numbers) - 1

    global_step = start_steps
    global_update_steps = 0

    # 初始化进度条并显示当前 epoch
    progress_bar = tqdm(
        range(0, max_train_steps),
        initial=global_step,
        desc=f"Epoch {first_epoch + 1}/{args.num_train_epochs}",
        # Only show the progress bar once on each machine.
        disable=not accelerator.is_local_main_process,
    )
    # 多个训练集 和 单个训练集的区分
    sum_iter=0
    if isinstance(train_dataloaders,tuple):
        n_loaders=len(train_dataloaders)
        for i in range(n_loaders):
            sum_iter+=len(train_dataloaders[i])
    elif isinstance(train_dataloaders,accelerate.data_loader.DataLoaderShard):
        sum_iter=len(train_dataloaders)

    sum_iter=sum_iter*args.num_train_epochs
    # sum_iter=(len(train_dataloaders[0])+len(train_dataloaders[1]))*args.num_train_epochs# 总的迭代次数
    for epoch in range(first_epoch, args.num_train_epochs):
        # 更新进度条描述，显示当前的epoch
        progress_bar.set_description(f"Epoch {epoch + 1}/{args.num_train_epochs}")

        for step, batch in enumerate(walk_dataloaders(train_dataloaders)):
            grid2D = batch['grid2D']
            grid3D = batch['grid3D']
            img_RGB = batch['img_RGB']
            img_RGB_unwarped = batch['img_RGB_unwarped']

            # 模型前馈输出
            img_RGB = img_RGB.to(accelerator.device, weight_dtype)
            img_RGB_unwarped = img_RGB_unwarped.to(accelerator.device, weight_dtype)
            grid2D = grid2D.to(accelerator.device, weight_dtype)
            if use_grid_3d:
                grid3D = grid3D.to(accelerator.device, weight_dtype)
                grid2D_pred, grid3D_pred = model(img_RGB)# 模型前馈网络
            else:
                grid2D_pred=model(img_RGB)
            img_RGB_unwarped_pred = bilinear_unwarping(img_RGB, grid2D_pred, IMG_SIZE)

            # 指定Epoch开始生效图像L1损失的Backward
            disable_lambda_img = epoch + 1 < args.ep_lambda_img_start
            if use_grid_3d:
                backward_loss, recon_mse_loss = loss_criterion(
                    img_RGB_unwarped_pred, grid2D_pred, grid3D_pred,
                    img_RGB_unwarped, grid2D, grid3D, disable_lambda_img=disable_lambda_img
                )
            else:
                backward_loss, recon_mse_loss = loss_criterion(
                    img_RGB_unwarped_pred, grid2D_pred,
                    img_RGB_unwarped, grid2D, disable_lambda_img=disable_lambda_img
                )
            accelerator.backward(backward_loss)
            
            # 判断哪些参数在模型定义了但是没有在forward使用
            # unused_parameters = []
            # for name, param in model.named_parameters():
            #     if param.grad is None:
            #         unused_parameters.append(name)
            # print(unused_parameters)
            global_update_steps += 1
            avg_backward_loss = accelerator.gather(backward_loss.repeat(args.train_batch_size)).mean().item()
            avg_recon_mse_loss = accelerator.gather(recon_mse_loss.repeat(args.train_batch_size)).mean().item()

            # 更新模型权重
            if global_update_steps % args.gradient_accumulation_steps == 0:
                if args.use_clip_grad_norm:
                    accelerator.clip_grad_norm_(params_to_opt, args.clip_grad_norm)
                optimizer.step()
                lr_scheduler.step()
                optimizer.zero_grad()
                progress_bar.update(1)
                global_step += 1

                if accelerator.is_main_process:
                    # 保存该日志信息
                    logs = {
                        "lr": lr_scheduler.get_last_lr()[0],
                        "back_loss": avg_backward_loss,
                        "mse_loss": avg_recon_mse_loss,
                        'epoch':epoch,
                        'step':f'{step+1}/{sum_iter}'
                    }
                    # 训练集部分
                    file_logger.info(f'train: lr:{format(logs["lr"],".3e")}\t back_loss:{format(logs["back_loss"],".7f")} \t mse_loss:{format(logs["mse_loss"],".7f")}\t'
                                     f'epoch:{logs["epoch"]}\t step:{logs["step"]}')
                    progress_bar.set_postfix(**logs)
                    accelerator.log(logs, step=global_step)
                if accelerator.is_main_process and global_step % args.save_steps == 0:
                    found_best_model = False
                    if args.validation_image_dir is not None:
                        all_val_images = get_all_image_path(args.validation_image_dir, recursive=True, path_op=Path)
                        all_val_images = path_utils.sort_path_by_name(all_val_images)

                        # 保存验证集的图片加载顺序
                        # with open(r'/mnt/aicamera-mlp/hz_datasets/tmp/test_hz/test_sample_path.txt','w',encoding='utf-8') as f:
                        #     for val_img_path in all_val_images:
                        #         f.writelines(f'{str(val_img_path)}\n')

                        # 验证集效果评测
                        val_metrics = log_validation(
                            args, all_val_images, model, global_step, accelerator, weight_dtype, test_dataloaders,use_grid_3d
                        )
                        if val_metrics is not None:
                            val_grid_l1_avg, val_img_mse_avg = val_metrics
                            # 验证集日志
                            file_logger.info(f'val:val_grid_l1_avg={val_grid_l1_avg},val_img_mse_avg={val_img_mse_avg}')
                            if val_grid_l1_avg <= val_grid_l1_avg_best and val_img_mse_avg <= val_img_mse_best:
                                val_grid_l1_avg_best, val_img_mse_best = val_grid_l1_avg, val_img_mse_avg
                                with open(best_val_record, 'w', encoding='utf-8') as f:
                                    data = dict()
                                    data['val_grid_l1_avg_best'] = val_grid_l1_avg_best
                                    data['val_img_mse_best'] = val_img_mse_best
                                    data['checkpoint_step'] = global_step
                                    json.dump(data, f, ensure_ascii=False, indent=4)
                                found_best_model = True
                                logger.info(f"found best model: {data}")

                    save_path = os.path.join(args.output_dir, f"checkpoint-{global_step}")
                    save_state(save_path, model, optimizer, lr_scheduler, accelerator, args.num_ckpt_to_keep)
                    if found_best_model:
                        best_save_path = os.path.join(args.output_dir, f"best_model")
                        save_state(best_save_path, model, optimizer, lr_scheduler, accelerator)

            if global_step >= max_train_steps:
                break

        # 当前Epoch训练结束，保存当前模型
        if accelerator.is_main_process:
            if args.save_every_n_epoch > 0 and (epoch + 1) % args.save_every_n_epoch == 0:
                save_path = os.path.join(args.output_dir, f"model_epoch-{epoch + 1}")
                save_state(save_path, model, optimizer, lr_scheduler, accelerator)

    # 训练结束，保存最终模型
    accelerator.wait_for_everyone()
    if accelerator.is_main_process:
        save_path = os.path.join(args.output_dir, f"model_final")
        save_state(save_path, model, optimizer, lr_scheduler, accelerator)
    accelerator.end_training()


def manage_checkpoints(output_dir, n_checkpoints_to_keep):
    checkpoints = sorted(
        [d for d in os.listdir(output_dir) if re.match(r'^checkpoint-\d+$', d)],
        key=lambda x: int(x.split('-')[1])
    )

    if len(checkpoints) > n_checkpoints_to_keep:
        checkpoints_to_delete = checkpoints[:-n_checkpoints_to_keep]
        logger.info(f"old version checkpoints to delete: {checkpoints_to_delete}")
        for checkpoint in checkpoints_to_delete:
            checkpoint_path = os.path.join(output_dir, checkpoint)
            if os.path.isdir(checkpoint_path):
                for file in os.listdir(checkpoint_path):
                    file_path = os.path.join(checkpoint_path, file)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                os.rmdir(checkpoint_path)


def save_state(save_dir, model, optimizer, scheduler, accelerator, n_checkpoints_to_keep=None):
    os.makedirs(save_dir, exist_ok=True)

    model = accelerator.unwrap_model(model)
    optimizer = accelerator.unwrap_model(optimizer)
    scheduler = accelerator.unwrap_model(scheduler)

    torch.save(model.state_dict(), os.path.join(save_dir, "model.bin"))
    torch.save(optimizer.state_dict(), os.path.join(save_dir, f"optimizer.bin"))
    torch.save(scheduler.state_dict(), os.path.join(save_dir, f"scheduler.bin"))

    # 保持最新的n个checkpoint
    if n_checkpoints_to_keep is not None:
        manage_checkpoints(os.path.dirname(save_dir), n_checkpoints_to_keep)


def log_validation(args, all_images, model, steps, accelerator, weight_dtype, test_dataloaders=None,use_grid_3d=True):
    model = accelerator.unwrap_model(model)
    model.eval()
    if use_grid_3d:
        loss_criterion = TrainingLoss(
            lambda_img=args.lambda_img,
            lambda_2d=args.lambda_2d,
            lambda_3d=args.lambda_3d,
        )
    else:
        loss_criterion = TrainingLoss2(
            lambda_img=args.lambda_img,
            lambda_2d=args.lambda_2d,
        )

    grid_l1_meter = AverageMeter()
    img_mse_meter = AverageMeter()

    if test_dataloaders is not None:
        logger.info("\nRunning metrics...\n")
        test_dataloader_total_steps = sum(len(loader) for loader in test_dataloaders)
        test_pbar = tqdm(total=test_dataloader_total_steps, desc='evaluating...')
        for step, batch in enumerate(walk_dataloaders(test_dataloaders)):
            grid2D = batch['grid2D']
            grid3D = batch['grid3D']
            img_RGB = batch['img_RGB']
            img_RGB_unwarped = batch['img_RGB_unwarped']

            # 模型前馈输出
            img_RGB = img_RGB.to(accelerator.device, weight_dtype)
            img_RGB_unwarped = img_RGB_unwarped.to(accelerator.device, weight_dtype)
            grid2D = grid2D.to(accelerator.device, weight_dtype)
            grid3D = grid3D.to(accelerator.device, weight_dtype)

            with torch.no_grad():
                if use_grid_3d:
                    grid2D_pred, grid3D_pred = model(img_RGB)
                else:
                    grid2D_pred = model(img_RGB)
                img_RGB_unwarped_pred = bilinear_unwarping(img_RGB, grid2D_pred, IMG_SIZE)

                # 指定Epoch开始生效图像L1损失的Backward
                if use_grid_3d:
                    grid_l1_loss, img_mse_loss = loss_criterion(
                        img_RGB_unwarped_pred, grid2D_pred, grid3D_pred,
                        img_RGB_unwarped, grid2D, grid3D, disable_lambda_img=True
                    )
                else:
                    grid_l1_loss, img_mse_loss = loss_criterion(
                        img_RGB_unwarped_pred, grid2D_pred,
                        img_RGB_unwarped, grid2D, disable_lambda_img=True
                    )

            grid_l1_meter.update(grid_l1_loss.detach().item(), 1)
            img_mse_meter.update(img_mse_loss.detach().item(), 1)
            logs = {"grid_l1": grid_l1_meter.avg, "img_mse_meter": img_mse_meter.avg}
            test_pbar.set_postfix(**logs)
            test_pbar.update(1)

        accelerator.log(logs, step=steps)
        logger.info(f"\n{logs}\nn")

    # 验证效果可视化
    logger.info("\nRunning visualized validation...\n")
    image_latest_log_dir = os.path.join(args.output_dir, "image_logs")
    image_running_steps_log_dir = os.path.join(image_latest_log_dir, "running_steps")
    os.makedirs(image_running_steps_log_dir, exist_ok=True)

    images_to_save = []
    for image_path in all_images:
        image = Image.open(image_path).convert('RGB')
        image = image.resize((IMG_SIZE[1], IMG_SIZE[0]), Image.LANCZOS)
        image_tensor = normalize_image_rgb(image)
        image_tensor = image_tensor.unsqueeze(0)

        with torch.no_grad():
            image_tensor = image_tensor.to(accelerator.device, weight_dtype)
            if use_grid_3d:
                grid2D_pred, grid3D_pred = model(image_tensor)
            else:
                grid2D_pred = model(image_tensor)
            image_unwarped_tensor = bilinear_unwarping(image_tensor, grid2D_pred, IMG_SIZE)
        if use_grid_3d:
            data_list=[[image_tensor, image_unwarped_tensor, grid2D_pred, grid3D_pred]]
        else:
            data_list=[[image_tensor, image_unwarped_tensor, grid2D_pred]]
        visualized_result = get_visualized_results(
            data_list,
            dpi=200,
            figsize=6,
            sample_idx=0,
            use_grid_3d=use_grid_3d
        )[0]
        images_to_save.append(visualized_result)

    # 将图像按每N行分组拼接，并保存
    num_images = len(images_to_save)
    group_size = 6  # 每组N行图像
    num_groups = math.ceil(num_images / group_size)  # 计算分组数量

    for group_idx in range(num_groups):
        start_idx = group_idx * group_size
        end_idx = min(start_idx + group_size, num_images)
        group_images = images_to_save[start_idx:end_idx]

        # 将每组的图像拼接成一张大图
        group_image = np.concatenate(group_images, axis=0)
        group_image_pil = Image.fromarray(group_image)

        # 构建文件名
        part_suffix = f"_part_{group_idx + 1:04d}"
        latest_filename = os.path.join(image_latest_log_dir, f"validation_latest{part_suffix}.png")
        steps_filename = os.path.join(image_running_steps_log_dir, f"validation_{steps:09d}{part_suffix}.jpg")

        # 保存每组拼接后的图像
        group_image_pil.save(latest_filename)
        group_image_pil.save(steps_filename)

    model.train()

    if test_dataloaders is not None:
        return grid_l1_meter.avg, img_mse_meter.avg


if __name__ == '__main__':
    main()
