#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/7/18 19:35
# <AUTHOR> <EMAIL>
# @FileName: train_wm_mvanet

import os
import re
import math
import json
import random
import datetime
import warnings
from pathlib import Path

import torch
import torch.nn.functional as F
import numpy as np
import torch.utils.data
from tqdm import tqdm
from PIL import Image
from accelerate.logging import get_logger
from diffusers.optimization import get_scheduler

from networks.mvanet.model.MVANetOld import MVANet, MVANetInfer
from networks.mvanet.losses import structure_loss
from networks.utils.metrics.eval import AverageMeter, compute_IoU, FScore

import modules.utils.torch_utils as torch_utils
import modules.utils.path_utils as path_utils
from modules.utils.train_utils import prepare_training_enviornment
from modules.proj_cmd_args.watermark_removal_ai.train_mvanet_args import parse_args
from my_datasets.watermark_removal_ai.custom_watermark_dataset_v2 import WatermarkDataset, walk_dataloaders

from modules.utils.image_utils import (
    normalize_image_rgb,
    denormalize_tensor_image_rgb,
    get_all_image_path,
    denormalize_mask,
)

logger = get_logger(__name__)
torch.autograd.set_detect_anomaly(True)

# 忽略特定警告
warnings.filterwarnings("ignore", message="torch.meshgrid", category=UserWarning)
warnings.filterwarnings("ignore", message="Grad strides do not match bucket view strides", category=UserWarning)
warnings.filterwarnings("ignore", message="On January 1, 2023, MMCV will release v2.0.0", category=UserWarning, )


def main():
    args = parse_args()
    accelerator, weight_dtype = prepare_training_enviornment(args, logger)

    # 初始化模型、checkpoint复用等
    state_dict = None
    start_steps = 0
    optimizer_ckpt = None
    lr_scheduler_ckpt = None
    best_val_record = os.path.join(args.output_dir, "best_val_record.json")

    exists_checkpoints = list(Path(args.output_dir).glob('checkpoint-*'))
    if len(exists_checkpoints) > 0:
        args.reuse_checkpoint = None

    if args.reuse_checkpoint is not None and os.path.exists(args.reuse_checkpoint):
        state_dict = torch.load(os.path.join(args.reuse_checkpoint, "model.bin"), map_location='cpu')
        logger.info(f"Reusing checkpoint from {args.reuse_checkpoint}")

    elif len(exists_checkpoints) > 0:
        checkpoint_numbers = [int(f.stem.split('-')[1]) for f in exists_checkpoints]
        resume_steps = max(checkpoint_numbers)
        resume_ckpt_dir = os.path.join(args.output_dir, f'checkpoint-{resume_steps}')
        state_dict = torch.load(os.path.join(resume_ckpt_dir, 'model.bin'), map_location='cpu')
        start_steps = resume_steps
        optimizer_ckpt = os.path.join(resume_ckpt_dir, "optimizer.bin")
        lr_scheduler_ckpt = os.path.join(resume_ckpt_dir, "scheduler.bin")
        logger.info(f"Reusing running checkpoint from {resume_ckpt_dir}")

    model = MVANet()

    # 解决分布式训练BatchNorm2D引起的梯度计算异常
    model = torch_utils.convert_batchnorm_to_sync_batchnorm(model)

    if state_dict is not None:
        model.load_state_dict(state_dict, strict=False)

    model.to(accelerator.device, weight_dtype)
    model.train()

    # 初始化优化策略
    loss_criterion = structure_loss
    params_to_opt = model.parameters()

    if args.use_8bit_adam:
        try:
            import bitsandbytes as bnb
        except ImportError:
            raise ImportError(
                "To use 8-bit Adam, please install the bitsandbytes library: `pip install bitsandbytes`."
            )
        optimizer_class = bnb.optim.AdamW8bit
    else:
        optimizer_class = torch.optim.AdamW

    optimizer = optimizer_class(
        params_to_opt,
        lr=args.learning_rate,
        betas=(args.adam_beta1, args.adam_beta2),
        weight_decay=args.adam_weight_decay,
        eps=args.adam_epsilon,
    )

    if optimizer_ckpt is not None:
        optimizer_state_dict = torch.load(optimizer_ckpt, map_location='cpu')
        optimizer.load_state_dict(optimizer_state_dict)

    # 准备数据集，训练步数、Epoch预估、accelerator.prepare 等调度
    num_split = accelerator.num_processes * args.gradient_accumulation_steps
    assert args.train_batch_size % num_split == 0, \
        (f"batch_size: {args.train_batch_size} needs to be divisible by "
         f"num_processes*gradient_accumulation_steps={num_split}")

    if args.seed is not None:
        seed = int(args.seed)
    else:
        seed = random.randint(1, 100000)

    resolutions = [int(res) for res in args.resolutions.strip().split(',')]

    # 准备数据集
    train_batch_size_per_device = args.train_batch_size // num_split
    train_datasets = []
    for res in resolutions:
        train_dataset = WatermarkDataset(args.dataset_dir, res, mode='train', seed=seed)
        train_datasets.append(train_dataset)

    train_dataloaders = []
    for train_dataset in train_datasets:
        train_dataloader = torch.utils.data.DataLoader(
            train_dataset,
            shuffle=True,
            batch_size=train_batch_size_per_device,
            num_workers=args.dataloader_num_workers,
        )
        train_dataloaders.append(train_dataloader)

    # 测试数据集
    test_datasets = []
    for res in resolutions:
        test_dataset = WatermarkDataset(args.dataset_dir, res, mode='test', seed=seed)
        test_datasets.append(test_dataset)

    test_dataloaders = []
    for test_dataset in test_datasets:
        test_dataloader = torch.utils.data.DataLoader(
            test_dataset,
            shuffle=True,
            batch_size=train_batch_size_per_device,
            num_workers=args.dataloader_num_workers,
        )
        test_dataloaders.append(test_dataloader)

    train_dataloader_total_steps = sum(len(loader) for loader in train_dataloaders)
    num_update_steps_per_epoch = math.ceil(train_dataloader_total_steps / args.gradient_accumulation_steps)
    max_train_steps = args.num_train_epochs * num_update_steps_per_epoch

    lr_scheduler = get_scheduler(
        args.lr_scheduler,
        optimizer=optimizer,
        num_training_steps=max_train_steps,
        num_cycles=args.lr_num_cycles,
        power=args.lr_power,
        num_warmup_steps=args.lr_warmup_steps * accelerator.num_processes,
    )
    if lr_scheduler_ckpt is not None:
        lr_scheduler_state_dict = torch.load(lr_scheduler_ckpt, map_location='cpu')
        lr_scheduler.load_state_dict(lr_scheduler_state_dict)

    val_iou_avg_best, val_f1_avg_best = 0., 0.
    if os.path.exists(best_val_record):
        with open(best_val_record, 'r', encoding='utf-8') as f:
            data = json.load(f)
            val_iou_avg_best = data['val_iou_avg_best']
            val_f1_avg_best = data['val_f1_avg_best']

    # accelerator.prepare
    logger.info("Prepare everything with our accelerator\n")
    model, optimizer, lr_scheduler = accelerator.prepare(model, optimizer, lr_scheduler)
    train_dataloaders = accelerator.prepare(*train_dataloaders)

    if accelerator.is_main_process:
        tracker_config = dict(vars(args))
        exp_date = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        accelerator.init_trackers(f"{args.tracker_project_name}-{exp_date}", config=tracker_config)

    # 经过prepare之后实际长度会发生变化
    train_dataloader_total_steps = sum(len(loader) for loader in train_dataloaders)
    num_update_steps_per_epoch = math.ceil(train_dataloader_total_steps / args.gradient_accumulation_steps)
    max_train_steps = args.num_train_epochs * num_update_steps_per_epoch

    print("\n")
    logger.info("***** Running training *****")
    logger.info(f"  Num checkpoints to keep: {args.num_ckpt_to_keep}")
    logger.info(f"  Dataset seed: {seed}")
    logger.info(f"  Starting best IoU: {val_iou_avg_best}, best F1: {val_f1_avg_best}")
    logger.info(f"  Resolutions: {resolutions}")
    logger.info(f"  Num examples = {sum(len(dataset) for dataset in train_datasets)}")
    logger.info(f"  Num epochs = {args.num_train_epochs}")
    logger.info(f"  Batch size per device = {train_batch_size_per_device}")
    logger.info(f"  Total train batch size (w. parallel, distributed & accumulation) = {args.train_batch_size}")
    logger.info(f"  Gradient Accumulation steps = {args.gradient_accumulation_steps}")
    logger.info(f"  Total optimization steps = {max_train_steps}")
    print("\n")

    first_epoch = 0
    exists_epoch_ckpts = list(Path(args.output_dir).glob('model_epoch-*'))
    if len(exists_epoch_ckpts) > 0:
        epoch_numbers = [int(f.stem.split('-')[1]) for f in exists_epoch_ckpts]
        first_epoch = max(epoch_numbers) - 1
    global_step = start_steps
    global_update_steps = 0

    progress_bar = tqdm(
        range(0, max_train_steps),
        initial=global_step,
        desc="Steps",
        # Only show the progress bar once on each machine.
        disable=not accelerator.is_local_main_process,
    )

    for epoch in range(first_epoch, args.num_train_epochs):
        for step, batch in enumerate(walk_dataloaders(train_dataloaders)):
            watermarked_images = batch['watermarked_images']
            watermark_masks = batch['watermark_masks']

            # 模型前馈输出
            watermarked_images = watermarked_images.to(accelerator.device, weight_dtype)
            watermark_masks = watermark_masks.to(accelerator.device, weight_dtype)

            watermarked_images = F.upsample(watermarked_images, size=(1024, 1024), mode='bilinear', align_corners=True)
            watermark_masks = F.upsample(watermark_masks, size=(1024, 1024), mode='bilinear', align_corners=True)

            b, c, h, w = watermark_masks.size()
            target_1 = F.upsample(watermark_masks, size=h // 4, mode='nearest').to(accelerator.device, weight_dtype)
            target_2 = F.upsample(watermark_masks, size=h // 8, mode='nearest').to(accelerator.device, weight_dtype)
            target_3 = F.upsample(watermark_masks, size=h // 16, mode='nearest').to(accelerator.device, weight_dtype)
            target_4 = F.upsample(watermark_masks, size=h // 32, mode='nearest').to(accelerator.device, weight_dtype)
            target_5 = F.upsample(watermark_masks, size=h // 64, mode='nearest').to(accelerator.device, weight_dtype)

            results = model(watermarked_images)
            (sideout5, sideout4, sideout3, sideout2,
             sideout1, final, glb5, glb4, glb3, glb2, glb1,
             tokenattmap4, tokenattmap3, tokenattmap2, tokenattmap1) = results

            loss1 = loss_criterion(sideout5.to(accelerator.device, weight_dtype), target_4)
            loss2 = loss_criterion(sideout4.to(accelerator.device, weight_dtype), target_3)
            loss3 = loss_criterion(sideout3.to(accelerator.device, weight_dtype), target_2)
            loss4 = loss_criterion(sideout2.to(accelerator.device, weight_dtype), target_1)
            loss5 = loss_criterion(sideout1.to(accelerator.device, weight_dtype), target_1)
            loss6 = loss_criterion(final.to(accelerator.device, weight_dtype), watermark_masks)
            loss7 = loss_criterion(glb5.to(accelerator.device, weight_dtype), target_5)
            loss8 = loss_criterion(glb4.to(accelerator.device, weight_dtype), target_4)
            loss9 = loss_criterion(glb3.to(accelerator.device, weight_dtype), target_3)
            loss10 = loss_criterion(glb2.to(accelerator.device, weight_dtype), target_2)
            loss11 = loss_criterion(glb1.to(accelerator.device, weight_dtype), target_2)
            loss12 = loss_criterion(tokenattmap4.to(accelerator.device, weight_dtype), target_3)
            loss13 = loss_criterion(tokenattmap3.to(accelerator.device, weight_dtype), target_2)
            loss14 = loss_criterion(tokenattmap2.to(accelerator.device, weight_dtype), target_1)
            loss15 = loss_criterion(tokenattmap1.to(accelerator.device, weight_dtype), target_1)

            loc_loss = loss1 + loss2 + loss3 + loss4 + loss5 + loss6
            glb_loss = loss7 + loss8 + loss9 + loss10 + loss11
            map_loss = loss12 + loss13 + loss14 + loss15

            loss = args.lambda_loc_loss * loc_loss + args.lambda_glb_loss * glb_loss + args.lambda_map_loss * map_loss
            accelerator.backward(loss)

            global_update_steps += 1
            avg_loss = accelerator.gather(loss.repeat(args.train_batch_size)).mean().item()

            # 更新模型权重
            if global_update_steps % args.gradient_accumulation_steps == 0:
                if args.use_clip_grad_norm:
                    accelerator.clip_grad_norm_(params_to_opt, args.clip_grad_norm)
                optimizer.step()
                lr_scheduler.step()
                optimizer.zero_grad()
                progress_bar.update(1)
                global_step += 1

                if accelerator.is_main_process:
                    logs = {"lr": lr_scheduler.get_last_lr()[0], "loss": loss.detach().item(), "avg_loss": avg_loss}
                    progress_bar.set_postfix(**logs)
                    accelerator.log(logs, step=global_step)

                if accelerator.is_main_process and global_step % args.save_steps == 0:
                    found_best_model = False
                    if args.validation_image_dir is not None:
                        all_val_images = get_all_image_path(args.validation_image_dir, recursive=False, path_op=Path)
                        all_val_images = path_utils.sort_path_by_name(all_val_images)
                        val_metrics = log_validation(
                            args, all_val_images, model, global_step, accelerator, weight_dtype, test_dataloaders
                        )
                        if val_metrics is not None:
                            val_iou_avg, val_f1_avg = val_metrics
                            if val_iou_avg >= val_iou_avg_best and val_f1_avg >= val_f1_avg_best:
                                val_iou_avg_best, val_f1_avg_best = val_iou_avg, val_f1_avg
                                with open(best_val_record, 'w', encoding='utf-8') as f:
                                    data = dict()
                                    data['val_iou_avg_best'] = val_iou_avg_best
                                    data['val_f1_avg_best'] = val_f1_avg_best
                                    data['checkpoint_step'] = global_step
                                    json.dump(data, f, ensure_ascii=False, indent=4)
                                found_best_model = True
                                logger.info(f"found best model: {data}")

                    save_path = os.path.join(args.output_dir, f"checkpoint-{global_step}")
                    save_state(save_path, model, optimizer, lr_scheduler, accelerator, args.num_ckpt_to_keep)
                    if found_best_model:
                        best_save_path = os.path.join(args.output_dir, f"best_model")
                        save_state(best_save_path, model, optimizer, lr_scheduler, accelerator)

            if global_step >= max_train_steps:
                break

        # 当前Epoch训练结束，保存当前模型
        if accelerator.is_main_process:
            save_path = os.path.join(args.output_dir, f"model_epoch-{epoch + 1}")
            save_state(save_path, model, optimizer, lr_scheduler, accelerator)

    # 训练结束，保存最终模型
    accelerator.wait_for_everyone()
    if accelerator.is_main_process:
        save_path = os.path.join(args.output_dir, f"model_final")
        save_state(save_path, model, optimizer, lr_scheduler, accelerator)
    accelerator.end_training()


def manage_checkpoints(output_dir, n_checkpoints_to_keep):
    checkpoints = sorted(
        [d for d in os.listdir(output_dir) if re.match(r'^checkpoint-\d+$', d)],
        key=lambda x: int(x.split('-')[1])
    )

    if len(checkpoints) > n_checkpoints_to_keep:
        checkpoints_to_delete = checkpoints[:-n_checkpoints_to_keep]
        logger.info(f"old version checkpoints to delete: {checkpoints_to_delete}")
        for checkpoint in checkpoints_to_delete:
            checkpoint_path = os.path.join(output_dir, checkpoint)
            if os.path.isdir(checkpoint_path):
                for file in os.listdir(checkpoint_path):
                    file_path = os.path.join(checkpoint_path, file)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                os.rmdir(checkpoint_path)


def save_state(save_dir, model, optimizer, scheduler, accelerator, n_checkpoints_to_keep=None):
    os.makedirs(save_dir, exist_ok=True)

    model = accelerator.unwrap_model(model)
    optimizer = accelerator.unwrap_model(optimizer)
    scheduler = accelerator.unwrap_model(scheduler)

    torch.save(model.state_dict(), os.path.join(save_dir, "model.bin"))
    torch.save(optimizer.state_dict(), os.path.join(save_dir, f"optimizer.bin"))
    torch.save(scheduler.state_dict(), os.path.join(save_dir, f"scheduler.bin"))

    # 保持最新的n个checkpoint
    if n_checkpoints_to_keep is not None:
        manage_checkpoints(os.path.dirname(save_dir), n_checkpoints_to_keep)


def log_validation(args, all_images, model, steps, accelerator, weight_dtype, test_dataloaders=None):
    model = accelerator.unwrap_model(model)
    model.eval()
    train_state_dict = model.state_dict()

    model_infer = MVANetInfer()
    infer_state_dict = model_infer.state_dict()

    update_state_dict = {k: v for k, v in train_state_dict.items() if k in infer_state_dict}
    infer_state_dict.update(update_state_dict)
    model_infer.load_state_dict(infer_state_dict)
    model_infer.to(accelerator.device, weight_dtype)
    model_infer.eval()

    iou_meter = AverageMeter()
    f1_meter = AverageMeter()
    if test_dataloaders is not None:
        logger.info("\nRunning metrics...\n")

        test_dataloader_total_steps = sum(len(loader) for loader in test_dataloaders)
        test_pbar = tqdm(total=test_dataloader_total_steps, desc='evaluating...')
        for step, batch in enumerate(walk_dataloaders(test_dataloaders)):
            watermarked_images = batch['watermarked_images']
            watermark_masks = batch['watermark_masks']

            # 模型前馈输出
            watermarked_images = F.upsample(watermarked_images, size=(1024, 1024), mode='bilinear', align_corners=True)
            watermark_masks = F.upsample(watermark_masks, size=(1024, 1024), mode='bilinear', align_corners=True)

            watermarked_images = watermarked_images.to(accelerator.device, weight_dtype)
            watermark_masks = watermark_masks.to(accelerator.device, weight_dtype)

            with torch.no_grad():
                predicted_masks = model_infer(watermarked_images)

            iou = compute_IoU(predicted_masks, watermark_masks)
            iou_meter.update(iou, watermark_masks.size(0))
            f1 = FScore(predicted_masks, watermark_masks).item()
            f1_meter.update(f1, watermark_masks.size(0))

            logs = {"val_IoU": iou_meter.avg, "val_F1": f1_meter.avg}
            test_pbar.set_postfix(**logs)
            test_pbar.update(1)

        accelerator.log(logs, step=steps)
        logger.info(f"\n{logs}\nn")

    # 验证效果可视化
    logger.info("\nRunning visualized validation...\n")
    image_latest_log_dir = os.path.join(args.output_dir, "image_logs")
    image_running_steps_log_dir = os.path.join(image_latest_log_dir, "running_steps")
    os.makedirs(image_running_steps_log_dir, exist_ok=True)

    current_image_set = []
    for idx, image_path in enumerate(all_images):
        image = Image.open(image_path).convert('RGB')
        image = image.resize((1024, 1024), Image.LANCZOS)
        image_tensor = normalize_image_rgb(image)
        image_tensor = image_tensor.unsqueeze(0)

        with torch.no_grad():
            predicted_masks = model_infer(image_tensor.to(accelerator.device, weight_dtype))

        image_input = denormalize_tensor_image_rgb(image_tensor.squeeze(0))
        predicted_masks = denormalize_mask(predicted_masks.squeeze(0)).convert('RGB')
        display_results = [image_input, predicted_masks]

        # 将这一行的图像拼接起来
        current_image_set.append(np.concatenate([np.array(img) for img in display_results], axis=1))

        if (idx + 1) % 5 == 0 or idx + 1 == len(all_images):
            # 将5行图像拼接成一张大图
            combined_image = np.concatenate(current_image_set, axis=0)
            combined_image = Image.fromarray(combined_image)

            # 保存图像
            image_idx = idx // 5
            save_path1 = os.path.join(image_latest_log_dir, f"val_{image_idx:03d}_latest.png")
            os.makedirs(Path(save_path1).parent, exist_ok=True)
            save_path2 = os.path.join(image_running_steps_log_dir, f"part_{image_idx:03d}", f"val_{steps:09d}.jpg")
            os.makedirs(Path(save_path2).parent, exist_ok=True)
            combined_image.save(save_path1)
            combined_image.save(save_path2)
            current_image_set = []

    model.train()

    if test_dataloaders is not None:
        return iou_meter.avg, f1_meter.avg


if __name__ == '__main__':
    main()