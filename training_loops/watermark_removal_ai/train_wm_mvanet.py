#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/7/18 19:35
# <AUTHOR> <EMAIL>
# @FileName: train_wm_mvanet

import os
import math
import random
import datetime
import warnings
from pathlib import Path

import torch
import torch.nn.functional as F
import numpy as np
import torch.utils.data
from tqdm import tqdm
from PIL import Image
from accelerate.logging import get_logger
from diffusers.optimization import get_scheduler

from networks.mvanet.model.MVANet import MVANet
from networks.mvanet.losses import structure_loss
from networks.utils.metrics.eval import AverageMeter, compute_IoU, FScore

import modules.utils.torch_utils as torch_utils
from modules.utils.train_utils import prepare_training_enviornment
from modules.proj_cmd_args.watermark_removal_ai.train_mvanet_args import parse_args
from my_datasets.watermark_removal_ai.custom_watermark_dataset import WatermarkDataset, walk_dataloaders

from modules.utils.image_utils import (
    normalize_image_rgb,
    denormalize_tensor_image_rgb,
    get_all_image_path,
    denormalize_mask,
)

logger = get_logger(__name__)
torch.autograd.set_detect_anomaly(True)

# 忽略特定警告
warnings.filterwarnings("ignore", message="torch.meshgrid", category=UserWarning)
warnings.filterwarnings("ignore", message="Grad strides do not match bucket view strides", category=UserWarning)
warnings.filterwarnings("ignore", message="On January 1, 2023, MMCV will release v2.0.0", category=UserWarning, )


def main():
    args = parse_args()
    accelerator, weight_dtype = prepare_training_enviornment(args, logger)

    # 初始化模型、checkpoint复用等
    state_dict = None
    start_steps = 0
    optimizer_ckpt = None
    lr_scheduler_ckpt = None

    exists_checkpoints = list(Path(args.output_dir).glob('checkpoint-*'))
    if len(exists_checkpoints) > 0:
        args.reuse_checkpoint = None

    if args.reuse_checkpoint is not None and os.path.exists(args.reuse_checkpoint):
        state_dict = torch.load(os.path.join(args.reuse_checkpoint, "model.bin"), map_location='cpu')
        logger.info(f"Reusing checkpoint from {args.reuse_checkpoint}")

    elif len(exists_checkpoints) > 0:
        checkpoint_numbers = [int(f.stem.split('-')[1]) for f in exists_checkpoints]
        resume_steps = max(checkpoint_numbers)
        resume_ckpt_dir = os.path.join(args.output_dir, f'checkpoint-{resume_steps}')
        state_dict = torch.load(os.path.join(resume_ckpt_dir, 'model.bin'), map_location='cpu')
        start_steps = resume_steps
        optimizer_ckpt = os.path.join(resume_ckpt_dir, "optimizer.bin")
        lr_scheduler_ckpt = os.path.join(resume_ckpt_dir, "scheduler.bin")
        logger.info(f"Reusing running checkpoint from {resume_ckpt_dir}")

    model = MVANet()

    # 解决分布式训练BatchNorm2D引起的梯度计算异常
    model = torch_utils.convert_batchnorm_to_sync_batchnorm(model)

    if state_dict is not None:
        model.load_state_dict(state_dict, strict=False)

    model.to(accelerator.device, weight_dtype)
    model.train()

    # 初始化优化策略
    loss_criterion = structure_loss
    params_to_opt = model.parameters()

    if args.use_8bit_adam:
        try:
            import bitsandbytes as bnb
        except ImportError:
            raise ImportError(
                "To use 8-bit Adam, please install the bitsandbytes library: `pip install bitsandbytes`."
            )
        optimizer_class = bnb.optim.AdamW8bit
    else:
        optimizer_class = torch.optim.AdamW

    optimizer = optimizer_class(
        params_to_opt,
        lr=args.learning_rate,
        betas=(args.adam_beta1, args.adam_beta2),
        weight_decay=args.adam_weight_decay,
        eps=args.adam_epsilon,
    )

    if optimizer_ckpt is not None:
        optimizer_state_dict = torch.load(optimizer_ckpt, map_location='cpu')
        optimizer.load_state_dict(optimizer_state_dict)

    # 准备数据集，训练步数、Epoch预估、accelerator.prepare 等调度
    num_split = accelerator.num_processes * args.gradient_accumulation_steps
    assert args.train_batch_size % num_split == 0, \
        (f"batch_size: {args.train_batch_size} needs to be divisible by "
         f"num_processes*gradient_accumulation_steps={num_split}")

    if args.seed is not None:
        seed = int(args.seed)
    else:
        seed = random.randint(1, 100000)

    resolutions = [int(res) for res in args.resolutions.strip().split(',')]

    # 准备数据集
    train_batch_size_per_device = args.train_batch_size // num_split
    train_datasets = []
    for res in resolutions:
        train_dataset = WatermarkDataset(args.dataset_dir, res, mode='train', seed=seed)
        train_datasets.append(train_dataset)

    train_dataloaders = []
    for train_dataset in train_datasets:
        train_dataloader = torch.utils.data.DataLoader(
            train_dataset,
            shuffle=True,
            batch_size=train_batch_size_per_device,
            num_workers=args.dataloader_num_workers,
        )
        train_dataloaders.append(train_dataloader)

    # 测试数据集
    test_datasets = []
    for res in resolutions:
        test_dataset = WatermarkDataset(args.dataset_dir, res, mode='test', seed=seed)
        test_datasets.append(test_dataset)

    test_dataloaders = []
    for test_dataset in test_datasets:
        test_dataloader = torch.utils.data.DataLoader(
            test_dataset,
            shuffle=True,
            batch_size=train_batch_size_per_device,
            num_workers=args.dataloader_num_workers,
        )
        test_dataloaders.append(test_dataloader)

    train_dataloader_total_steps = sum(len(loader) for loader in train_dataloaders)
    num_update_steps_per_epoch = math.ceil(train_dataloader_total_steps / args.gradient_accumulation_steps)
    max_train_steps = args.num_train_epochs * num_update_steps_per_epoch

    lr_scheduler = get_scheduler(
        args.lr_scheduler,
        optimizer=optimizer,
        num_training_steps=max_train_steps,
        num_cycles=args.lr_num_cycles,
        power=args.lr_power,
        num_warmup_steps=args.lr_warmup_steps * accelerator.num_processes,
    )
    if lr_scheduler_ckpt is not None:
        lr_scheduler_state_dict = torch.load(lr_scheduler_ckpt, map_location='cpu')
        lr_scheduler.load_state_dict(lr_scheduler_state_dict)

    # accelerator.prepare
    logger.info("Prepare everything with our accelerator\n")
    model, optimizer, lr_scheduler = accelerator.prepare(model, optimizer, lr_scheduler)
    train_dataloaders = accelerator.prepare(*train_dataloaders)

    if accelerator.is_main_process:
        tracker_config = dict(vars(args))
        exp_date = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        accelerator.init_trackers(f"{args.tracker_project_name}-{exp_date}", config=tracker_config)

    # 经过prepare之后实际长度会发生变化
    train_dataloader_total_steps = sum(len(loader) for loader in train_dataloaders)
    num_update_steps_per_epoch = math.ceil(train_dataloader_total_steps / args.gradient_accumulation_steps)
    max_train_steps = args.num_train_epochs * num_update_steps_per_epoch

    logger.info("***** Running training *****")
    logger.info(f"  Dataset seed: {seed}")
    logger.info(f"  Resolutions: {resolutions}")
    logger.info(f"  Num examples = {len(train_dataset)}")
    logger.info(f"  Num epochs = {args.num_train_epochs}")
    logger.info(f"  Batch size per device = {train_batch_size_per_device}")
    logger.info(f"  Total train batch size (w. parallel, distributed & accumulation) = {args.train_batch_size}")
    logger.info(f"  Gradient Accumulation steps = {args.gradient_accumulation_steps}")
    logger.info(f"  Total optimization steps = {max_train_steps}")

    first_epoch = 0
    global_step = start_steps
    global_update_steps = 0

    progress_bar = tqdm(
        range(0, max_train_steps),
        initial=global_step,
        desc="Steps",
        # Only show the progress bar once on each machine.
        disable=not accelerator.is_local_main_process,
    )

    for epoch in range(first_epoch, args.num_train_epochs):
        for step, batch in enumerate(walk_dataloaders(train_dataloaders)):
            watermarked_images = batch['watermarked_images']
            watermark_masks = batch['watermark_masks']

            # 模型前馈输出
            watermarked_images = watermarked_images.to(accelerator.device, weight_dtype)
            watermark_masks = watermark_masks.to(accelerator.device, weight_dtype)

            watermarked_images = F.upsample(watermarked_images, size=(1024, 1024), mode='bilinear', align_corners=True)
            watermark_masks = F.upsample(watermark_masks, size=(1024, 1024), mode='bilinear', align_corners=True)

            b, c, h, w = watermark_masks.size()
            target_1 = F.upsample(watermark_masks, size=h // 4, mode='nearest').to(accelerator.device, weight_dtype)
            target_2 = F.upsample(watermark_masks, size=h // 8, mode='nearest').to(accelerator.device, weight_dtype)
            target_3 = F.upsample(watermark_masks, size=h // 16, mode='nearest').to(accelerator.device, weight_dtype)
            target_4 = F.upsample(watermark_masks, size=h // 32, mode='nearest').to(accelerator.device, weight_dtype)
            target_5 = F.upsample(watermark_masks, size=h // 64, mode='nearest').to(accelerator.device, weight_dtype)

            results = model(watermarked_images)
            (sideout5, sideout4, sideout3, sideout2,
             sideout1, final, glb5, glb4, glb3, glb2, glb1,
             tokenattmap4, tokenattmap3, tokenattmap2, tokenattmap1) = results

            loss1 = loss_criterion(sideout5.to(accelerator.device, weight_dtype), target_4)
            loss2 = loss_criterion(sideout4.to(accelerator.device, weight_dtype), target_3)
            loss3 = loss_criterion(sideout3.to(accelerator.device, weight_dtype), target_2)
            loss4 = loss_criterion(sideout2.to(accelerator.device, weight_dtype), target_1)
            loss5 = loss_criterion(sideout1.to(accelerator.device, weight_dtype), target_1)
            loss6 = loss_criterion(final.to(accelerator.device, weight_dtype), watermark_masks)
            loss7 = loss_criterion(glb5.to(accelerator.device, weight_dtype), target_5)
            loss8 = loss_criterion(glb4.to(accelerator.device, weight_dtype), target_4)
            loss9 = loss_criterion(glb3.to(accelerator.device, weight_dtype), target_3)
            loss10 = loss_criterion(glb2.to(accelerator.device, weight_dtype), target_2)
            loss11 = loss_criterion(glb1.to(accelerator.device, weight_dtype), target_2)
            loss12 = loss_criterion(tokenattmap4.to(accelerator.device, weight_dtype), target_3)
            loss13 = loss_criterion(tokenattmap3.to(accelerator.device, weight_dtype), target_2)
            loss14 = loss_criterion(tokenattmap2.to(accelerator.device, weight_dtype), target_1)
            loss15 = loss_criterion(tokenattmap1.to(accelerator.device, weight_dtype), target_1)

            loc_loss = loss1 + loss2 + loss3 + loss4 + loss5 + loss6
            glb_loss = loss7 + loss8 + loss9 + loss10 + loss11
            map_loss = loss12 + loss13 + loss14 + loss15

            loss = args.lambda_loc_loss * loc_loss + args.lambda_glb_loss * glb_loss + args.lambda_map_loss * map_loss
            accelerator.backward(loss)

            global_update_steps += 1
            avg_loss = accelerator.gather(loss.repeat(args.train_batch_size)).mean().item()

            # 更新模型权重
            if global_update_steps % args.gradient_accumulation_steps == 0:
                if args.use_clip_grad_norm:
                    accelerator.clip_grad_norm_(params_to_opt, args.clip_grad_norm)
                optimizer.step()
                lr_scheduler.step()
                optimizer.zero_grad()
                progress_bar.update(1)
                global_step += 1

                if accelerator.is_main_process:
                    logs = {"lr": lr_scheduler.get_last_lr()[0], "loss": loss.detach().item(), "avg_loss": avg_loss}
                    progress_bar.set_postfix(**logs)
                    accelerator.log(logs, step=global_step)

                if accelerator.is_main_process and global_step % args.save_steps == 0:
                    save_path = os.path.join(args.output_dir, f"checkpoint-{global_step}")
                    save_state(save_path, model, optimizer, lr_scheduler, accelerator)

                if accelerator.is_main_process and args.validation_image_dir is not None \
                        and global_step % args.validation_steps == 0:
                    all_val_images = get_all_image_path(args.validation_image_dir, recursive=False, path_op=Path)
                    log_validation(args, all_val_images, model, global_step, accelerator, weight_dtype, test_dataloader)

            if global_step >= max_train_steps:
                break

        # 当前Epoch训练结束，保存当前模型
        if accelerator.is_main_process:
            save_path = os.path.join(args.output_dir, f"model_epoch_{epoch + 1}")
            save_state(save_path, model, optimizer, lr_scheduler, accelerator)

    # 训练结束，保存最终模型
    accelerator.wait_for_everyone()
    if accelerator.is_main_process:
        save_path = os.path.join(args.output_dir, f"model_final")
        save_state(save_path, model, optimizer, lr_scheduler, accelerator)
    accelerator.end_training()


def save_state(save_dir, model, optimizer, scheduler, accelerator):
    os.makedirs(save_dir, exist_ok=True)

    model = accelerator.unwrap_model(model)
    optimizer = accelerator.unwrap_model(optimizer)
    scheduler = accelerator.unwrap_model(scheduler)

    torch.save(model.state_dict(), os.path.join(save_dir, "model.bin"))
    torch.save(optimizer.state_dict(), os.path.join(save_dir, f"optimizer.bin"))
    torch.save(scheduler.state_dict(), os.path.join(save_dir, f"scheduler.bin"))


def log_validation(args, all_images, model, steps, accelerator, weight_dtype, test_dataloader=None):
    model = accelerator.unwrap_model(model)
    model.eval()
    model_infer = model

    if test_dataloader is not None:
        logger.info("\nRunning metrics...\n")
        iou_meter = AverageMeter()
        f1_meter = AverageMeter()

        test_pbar = tqdm(total=len(test_dataloader), desc='evaluating...')
        for step, batch in enumerate(test_dataloader):
            watermarked_images = batch['watermarked_images']
            watermark_masks = batch['watermark_masks']

            # 模型前馈输出
            watermarked_images = F.upsample(watermarked_images, size=(1024, 1024), mode='bilinear', align_corners=True)
            watermark_masks = F.upsample(watermark_masks, size=(1024, 1024), mode='bilinear', align_corners=True)

            watermarked_images = watermarked_images.to(accelerator.device, weight_dtype)
            watermark_masks = watermark_masks.to(accelerator.device, weight_dtype)

            with torch.no_grad():
                predicted_masks = model_infer(watermarked_images)

            iou = compute_IoU(predicted_masks, watermark_masks)
            iou_meter.update(iou, watermark_masks.size(0))
            f1 = FScore(predicted_masks, watermark_masks).item()
            f1_meter.update(f1, watermark_masks.size(0))

            logs = {"IoU": iou_meter.avg, "F1": f1_meter.avg}
            test_pbar.set_postfix(**logs)
            test_pbar.update(1)

        logger.info(f"\n{logs}\nn")

    # 验证效果可视化
    logger.info("\nRunning visualized validation...\n")
    image_latest_log_dir = os.path.join(args.output_dir, "image_logs")
    image_running_steps_log_dir = os.path.join(image_latest_log_dir, "running_steps")
    os.makedirs(image_running_steps_log_dir, exist_ok=True)

    images_to_save = []
    for image_path in all_images:
        image = Image.open(image_path).convert('RGB')
        image = image.resize((1024, 1024), Image.LANCZOS)
        image_tensor = normalize_image_rgb(image)
        image_tensor = image_tensor.unsqueeze(0)

        with torch.no_grad():
            predicted_masks = model_infer(image_tensor.to(accelerator.device, weight_dtype))

        image_input = denormalize_tensor_image_rgb(image_tensor.squeeze(0))
        predicted_masks = denormalize_mask(predicted_masks.squeeze(0)).convert('RGB')
        row_images = [image_input, predicted_masks]

        # 将这一行的图像拼接起来
        row_image = np.concatenate([np.array(img) for img in row_images], axis=1)
        images_to_save.append(row_image)

    # 将所有行的图像拼接成一张大图
    final_image = np.concatenate(images_to_save, axis=0)
    final_image = Image.fromarray(final_image)
    final_image.save(os.path.join(image_latest_log_dir, f"validation_latest.png"))
    final_image.save(os.path.join(image_running_steps_log_dir, f"validation_{steps:09d}.jpg"))

    model.train()


if __name__ == '__main__':
    main()