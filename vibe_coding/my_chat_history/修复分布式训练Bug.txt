这个脚本 @cmd_scripts/train_table_structure/cycle_centernet_train.sh 在做分布式多节点多卡训练时，会遇上以下问题，需要解决：

```
07/10/2025 18:17:11 - INFO - __main__ - 训练步数计算:
07/10/2025 18:17:11 - INFO - __main__ -   prepare前: 969 steps/epoch, 总计 484500 steps
07/10/2025 18:17:11 - INFO - __main__ -   prepare后: 122 steps/epoch, 总计 61000 steps
07/10/2025 18:17:11 - INFO - __main__ -   最终使用: 484500 steps
07/10/2025 18:17:11 - INFO - __main__ -
***** Running training *****
  Num checkpoints to keep: 100
  Enable EMA model: False
  Dataset seed: 42
  best_loss_model_record_data: {'avg_loss': inf}
  Num examples = 7750
  Num epochs = 500
  Batch size per device = 8
  Total train batch size (w. parallel, distributed & accumulation) = 64
  Total optimization steps = 484500
***** Running training *****
Steps:   0%|          | 0/484500 [00:00<?, ?it/s]
libpng warning: iCCP: known incorrect sRGB profile
Steps:   0%|          | 1/484500 [00:16<2255:33:32, 16.76s/it, avg_loss=24.7, loss=25.6, lr=2.5e-6]Traceback (most recent call last):
  File "/aipdf-mlp/mlp_code/xelawk/v202507092200/train-anything/training_loops/table_structure_recognition/train_cycle_centernet.py", line 794, in <module>
    main()
  File "/aipdf-mlp/mlp_code/xelawk/v202507092200/train-anything/training_loops/table_structure_recognition/train_cycle_centernet.py", line 665, in main
    predictions = model(images)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1518, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1527, in _call_impl
    return forward_call(*args, **kwargs)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/parallel/distributed.py", line 1515, in forward
    inputs, kwargs = self._pre_forward(*inputs, **kwargs)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/parallel/distributed.py", line 1409, in _pre_forward
    if torch.is_grad_enabled() and self.reducer._rebuild_buckets():
RuntimeError: Expected to have finished reduction in the prior iteration before starting a new one. This error indicates that your module has parameters that were not used in producing loss. You can enable unused parameter detection by passing the keyword argument `find_unused_parameters=True` to `torch.nn.parallel.DistributedDataParallel`, and by
making sure all `forward` function outputs participate in calculating loss.
If you already have done the above, then the distributed data parallel module wasn't able to locate the output tensors in the return value of your module's `forward` function. Please include the loss function and the structure of the return value of `forward` of your module when reporting this issue (e.g. list, dict, iterable).
Parameter indices which did not receive grad for rank 3: 60 61 62 96 97 98
 In addition, you can set the environment variable TORCH_DISTRIBUTED_DEBUG to either INFO or DETAIL to print out information about which particular parameters did not receive gradient on this rank as part of this error
Traceback (most recent call last):
  File "/aipdf-mlp/mlp_code/xelawk/v202507092200/train-anything/training_loops/table_structure_recognition/train_cycle_centernet.py", line 794, in <module>
    main()
  File "/aipdf-mlp/mlp_code/xelawk/v202507092200/train-anything/training_loops/table_structure_recognition/train_cycle_centernet.py", line 665, in main
    predictions = model(images)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1518, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1527, in _call_impl
    return forward_call(*args, **kwargs)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/parallel/distributed.py", line 1515, in forward
    inputs, kwargs = self._pre_forward(*inputs, **kwargs)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/parallel/distributed.py", line 1409, in _pre_forward
    if torch.is_grad_enabled() and self.reducer._rebuild_buckets():
RuntimeError: Expected to have finished reduction in the prior iteration before starting a new one. This error indicates that your module has parameters that were not used in producing loss. You can enable unused parameter detection by passing the keyword argument `find_unused_parameters=True` to `torch.nn.parallel.DistributedDataParallel`, and by
making sure all `forward` function outputs participate in calculating loss.
If you already have done the above, then the distributed data parallel module wasn't able to locate the output tensors in the return value of your module's `forward` function. Please include the loss function and the structure of the return value of `forward` of your module when reporting this issue (e.g. list, dict, iterable).
Parameter indices which did not receive grad for rank 2: 60 61 62 96 97 98
 In addition, you can set the environment variable TORCH_DISTRIBUTED_DEBUG to either INFO or DETAIL to print out information about which particular parameters did not receive gradient on this rank as part of this error
Traceback (most recent call last):
  File "/aipdf-mlp/mlp_code/xelawk/v202507092200/train-anything/training_loops/table_structure_recognition/train_cycle_centernet.py", line 794, in <module>
    main()
  File "/aipdf-mlp/mlp_code/xelawk/v202507092200/train-anything/training_loops/table_structure_recognition/train_cycle_centernet.py", line 665, in main
    predictions = model(images)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1518, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1527, in _call_impl
    return forward_call(*args, **kwargs)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/parallel/distributed.py", line 1515, in forward
    inputs, kwargs = self._pre_forward(*inputs, **kwargs)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/parallel/distributed.py", line 1409, in _pre_forward
    if torch.is_grad_enabled() and self.reducer._rebuild_buckets():
RuntimeError: Expected to have finished reduction in the prior iteration before starting a new one. This error indicates that your module has parameters that were not used in producing loss. You can enable unused parameter detection by passing the keyword argument `find_unused_parameters=True` to `torch.nn.parallel.DistributedDataParallel`, and by
making sure all `forward` function outputs participate in calculating loss.
If you already have done the above, then the distributed data parallel module wasn't able to locate the output tensors in the return value of your module's `forward` function. Please include the loss function and the structure of the return value of `forward` of your module when reporting this issue (e.g. list, dict, iterable).
Parameter indices which did not receive grad for rank 1: 60 61 62 96 97 98
 In addition, you can set the environment variable TORCH_DISTRIBUTED_DEBUG to either INFO or DETAIL to print out information about which particular parameters did not receive gradient on this rank as part of this error
Traceback (most recent call last):
  File "/aipdf-mlp/mlp_code/xelawk/v202507092200/train-anything/training_loops/table_structure_recognition/train_cycle_centernet.py", line 794, in <module>
    main()
  File "/aipdf-mlp/mlp_code/xelawk/v202507092200/train-anything/training_loops/table_structure_recognition/train_cycle_centernet.py", line 665, in main
    predictions = model(images)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1518, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1527, in _call_impl
    return forward_call(*args, **kwargs)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/parallel/distributed.py", line 1515, in forward
    inputs, kwargs = self._pre_forward(*inputs, **kwargs)
  File "/opt/miniforge3/lib/python3.10/site-packages/torch/nn/parallel/distributed.py", line 1409, in _pre_forward
    if torch.is_grad_enabled() and self.reducer._rebuild_buckets():
RuntimeError: Expected to have finished reduction in the prior iteration before starting a new one. This error indicates that your module has parameters that were not used in producing loss. You can enable unused parameter detection by passing the keyword argument `find_unused_parameters=True` to `torch.nn.parallel.DistributedDataParallel`, and by
making sure all `forward` function outputs participate in calculating loss.
If you already have done the above, then the distributed data parallel module wasn't able to locate the output tensors in the return value of your module's `forward` function. Please include the loss function and the structure of the return value of `forward` of your module when reporting this issue (e.g. list, dict, iterable).
Parameter indices which did not receive grad for rank 0: 60 61 62 96 97 98
 In addition, you can set the environment variable TORCH_DISTRIBUTED_DEBUG to either INFO or DETAIL to print out information about which particular parameters did not receive gradient on this rank as part of this error
Steps:   0%|          | 1/484500 [00:21<2862:04:50, 21.27s/it, avg_loss=24.7, loss=25.6, lr=2.5e-6]
```