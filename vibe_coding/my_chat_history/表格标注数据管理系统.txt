我需要创建一个 WebUI，用于管理：下载原数据、上传标注后的数据，以及展示统计信息

页面分两个 Tab，分别为：待标注数据下载，已标注数据上传。拉下分两个tab讨论：

待标注数据下载：
- 指定一个下载目录，包含数据源，强制为 tar.gz 格式，目录可以为空（但需要提示为空，无文件）
- 需要有一个文件列表（初始高度合适，当只有一个文件时，需要完整可见），旁边提供下载连接的按钮（显示文件名，文件大小，创建时间）
- 底部需要有一个占位用于展示 总文件数，待标注总数，已标注总数，百分比

已标注数据上传：
- 指定一个上传目录，注意强制为 tar.gz 格式才可上传，目录可以为空（但需要提示为空，无文件）
- 上传文件时需要校验以下信息：
  需要为 tar.gz
  解压 tar.gz 到临时目录上，然后迭归遍历目录下所有图片，过滤隐藏图片后作为样本总数
  统计有效样本数，当图片在所在目录层级下有一个对应的`xxx_table_annotation.json`文件，则记为一个有效样本
  经检验后合格的tar.gz文件所统计的有效样本与总样本信息，需要更新持久化到目标上传目录上，通过json目录托管统计状态。重启webui会恢复加载状态信息
- 底部需要有一个占位用于展示 总样本，已标注样本，百分比

需要注意的是，已标注数据上传 上传文件名必须 在 待标注数据下载的文件列表中。这样做的好处是，当标注后的所有文件上传后，那么 待标注数据下载 的文件列表为  已标注数据上传 的一个镜像
同时，也方便统计 待标注数据下载 底部下面 已标注总数的信息

---

1. 数据源和目录结构相关
下载目录：这个"指定下载目录"是指用户在WebUI界面上输入/选择的本地目录路径吗？还是服务器上的固定目录？answer: 配置中固定目录

数据源：tar.gz文件是从哪里来的？是管理员预先上传到服务器的，还是从外部数据源自动同步的？answer: 预先上传

目录层级：待标注数据解压后的目录结构是怎样的？是平铺的图片文件，还是有特定的子目录结构？平铺的 tar.gz 文件


2. 标注文件格式和验证规则
标注文件命名：xxx_table_annotation.json中的"xxx"是对应图片的文件名（不含扩展名）吗？xxx对应图片的文件名

标注文件内容：xxx_table_annotation.json文件内部需要包含哪些必要字段才算"有效样本"？仅仅存在文件就够了，还是需要验证内容结构？
答：必须解析到 字段  "cells"，且该字段的值为列表，列表不能为空

图片格式：支持哪些图片格式？jpg、png、bmp等？答：IMAGE_FMT = ['.jpg', '.jpeg', '.JPEG', '.JPG', '.png', '.PNG']
隐藏图片：如何定义"隐藏图片"？是以点开头的文件名，还是有其他规则？以点开头

3. 统计信息的具体含义
待标注数据下载页面的"待标注总数"和"已标注总数"：
"待标注总数"是指所有下载目录中tar.gz文件包含的图片总数？所有tar.gz 减去 已上传中的文件名能对应的 tar.gz
"已标注总数"是如何统计的？是基于已上传的标注文件数量吗？ 已上传中所有 tar.gz, 文件名必须被包含于 待标注数据文件列表

数据同步问题：当用户下载了数据但还未上传标注结果时，系统如何知道哪些数据正在被标注？答：不需要你知道正在被标注，你只关心待标注，未标注的

4. 用户权限和并发处理
多用户场景：多个用户同时使用这个系统时，如何避免重复下载同一个文件进行标注？不需要你关心
任务分配：是否需要任务分配机制，确保每个待标注文件只被一个人处理？不需要你关心

5. 持久化存储细节
状态存储：您提到"通过json目录托管统计状态"，这个json文件的具体结构和存储位置是？记录 累计解压后的有效图片即总的样本总数，有效标注的样本总数。存在在上传目录根目录中
数据备份：上传的标注数据是否需要备份机制？不需要你关心

6. 用户体验细节
文件大小限制：tar.gz文件的大小是否有限制？无
上传进度：大文件上传时是否需要显示进度条？无
错误处理：当上传的tar.gz文件格式不正确或验证失败时，如何给用户反馈？直接告诉用户，提示压缩为tar.gz，按照原文件命名，不要篡改