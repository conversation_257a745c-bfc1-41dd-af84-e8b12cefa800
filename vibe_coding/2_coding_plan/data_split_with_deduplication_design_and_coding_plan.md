# 数据集划分工具详细设计与编码计划

## 1. 系统架构概览

### 1.1 架构设计

数据集划分工具采用简洁的分层架构，包含以下三层：

1. **命令行接口层**：处理用户输入参数，显示进度和结果
2. **核心处理层**：实现数据划分的核心逻辑
3. **文件操作层**：处理文件系统相关操作

```mermaid
sequenceDiagram
    participant CLI as 命令行接口
    participant Core as 核心处理模块
    participant FileOps as 文件操作模块
    
    CLI->>Core: 传递源目录和目标目录路径
    Core->>FileOps: 请求扫描源目录结构
    FileOps-->>Core: 返回文件列表
    Core->>Core: 解析manifest.json
    Core->>Core: 执行数据划分算法
    Core->>FileOps: 请求创建目标目录结构
    FileOps-->>Core: 确认目录创建完成
    Core->>FileOps: 请求创建硬链接/复制文件
    FileOps-->>Core: 返回操作结果
    Core-->>CLI: 返回处理统计信息
    CLI->>CLI: 显示结果报告
```

### 1.2 组件拆分

1. **命令行接口组件**
   - 职责：解析命令行参数，显示进度和结果
   - 核心功能：参数验证、进度显示、结果输出

2. **数据处理核心组件**
   - 职责：实现数据划分的核心逻辑
   - 核心功能：manifest解析、数据集划分算法、统计信息生成

3. **文件操作组件**
   - 职责：处理文件系统相关操作
   - 核心功能：目录扫描、目录创建、硬链接创建、文件复制

## 2. 目录结构设计

```
data_split_tool/
├── __init__.py
├── main.py                 # 入口点，命令行接口
├── core/
│   ├── __init__.py
│   ├── splitter.py         # 数据集划分核心逻辑
│   └── manifest.py         # manifest.json解析器
├── utils/
│   ├── __init__.py
│   ├── file_ops.py         # 文件操作工具
│   └── validators.py       # 输入验证工具
└── tests/                  # 单元测试
    ├── __init__.py
    ├── test_splitter.py
    ├── test_manifest.py
    └── test_file_ops.py
```

## 3. 数据流

### 3.1 数据集划分流程

1. 用户通过命令行提供源目录和目标目录路径
2. 程序扫描源目录，获取所有图片和对应标注文件
3. 解析manifest.json，识别所有重复组及其包含的图片
4. 根据规则选择验证集图片（不在manifest.json中的图片）
5. 将剩余图片划分为训练集
6. 创建目标目录结构，并使用硬链接（或复制）将文件放入对应位置
7. 生成并显示统计报告

```mermaid
sequenceDiagram
    participant User
    participant CLI
    participant Splitter
    participant ManifestParser
    participant FileOps
    
    User->>CLI: 输入源目录和目标目录
    CLI->>Splitter: 初始化划分器
    Splitter->>FileOps: 扫描源目录
    FileOps-->>Splitter: 返回文件列表
    Splitter->>ManifestParser: 解析manifest.json
    ManifestParser-->>Splitter: 返回重复组信息
    Splitter->>Splitter: 执行数据划分算法
    Splitter->>FileOps: 创建目标目录结构
    Splitter->>FileOps: 创建硬链接/复制文件
    FileOps-->>Splitter: 返回操作结果
    Splitter-->>CLI: 返回统计信息
    CLI-->>User: 显示结果报告
```

### 3.2 数据模型设计

```mermaid
erDiagram
    ImageFile {
        string path
        string name
        string part_id
        bool in_manifest
    }
    
    LabelFile {
        string path
        string name
        string part_id
    }
    
    DuplicateGroup {
        int group_id
        string group_name
        int size
    }
    
    DatasetSplit {
        string split_name
        int image_count
        float percentage
    }
    
    ImageFile ||--|| LabelFile : "对应"
    DuplicateGroup ||--o{ ImageFile : "包含"
    DatasetSplit ||--o{ ImageFile : "包含"
    DatasetSplit ||--o{ LabelFile : "包含"
```

## 4. API接口定义

### 4.1 命令行接口

```
python -m data_split_tool.main --src-dir <源目录路径> --dst-dir <目标目录路径> [--val-ratio <验证集比例>] [--copy-mode]
```

参数说明：
- `--src-dir`: 源数据集目录路径（必需）
- `--dst-dir`: 目标数据集目录路径（必需）
- `--val-ratio`: 验证集比例，默认为0.1（可选）
- `--copy-mode`: 强制使用复制模式而非硬链接（可选）

### 4.2 核心模块API

#### DatasetSplitter类

```python
class DatasetSplitter:
    def __init__(self, src_dir, dst_dir, val_ratio=0.1, use_copy=False):
        """初始化数据集划分器"""
        
    def split(self):
        """执行数据集划分"""
        
    def generate_report(self):
        """生成统计报告"""
```

#### ManifestParser类

```python
class ManifestParser:
    def __init__(self, manifest_path):
        """初始化manifest解析器"""
        
    def parse(self):
        """解析manifest.json文件"""
        
    def get_duplicate_groups(self):
        """获取所有重复组"""
        
    def is_image_in_manifest(self, image_path):
        """检查图片是否在manifest中"""
```

#### FileOperations类

```python
class FileOperations:
    def scan_directory(self, directory):
        """扫描目录，返回文件列表"""
        
    def create_directory_structure(self, structure):
        """创建目录结构"""
        
    def create_hard_link(self, src, dst):
        """创建硬链接"""
        
    def copy_file(self, src, dst):
        """复制文件"""
```

## 5. 迭代演进依据

1. **模块化设计**：各组件职责明确，便于单独升级或替换
2. **配置驱动**：关键参数可通过命令行配置，未来可扩展为配置文件
3. **扩展点预留**：
   - 数据划分算法可扩展为多种策略
   - 文件操作可支持更多模式（如软链接、云存储等）
   - 报告生成可扩展为多种格式（如JSON、CSV等）

## 6. 渐进式小步迭代编码计划

### 步骤1：项目结构搭建与基础功能实现

**目标**：创建项目基本结构，实现命令行参数解析和基础验证

**任务**：
1. 创建项目目录结构
2. 实现命令行参数解析
3. 实现基本的输入验证（源目录和目标目录）

**验证方式**：
- 运行程序，验证命令行参数解析是否正确
- 测试无效输入时的错误提示

### 步骤2：文件操作模块实现

**目标**：实现文件操作相关功能

**任务**：
1. 实现目录扫描功能，获取源目录中的图片和标注文件
2. 实现目录结构创建功能
3. 实现硬链接和复制文件功能

**验证方式**：
- 扫描测试目录，验证是否能正确获取文件列表
- 创建测试目录结构，验证是否成功
- 测试硬链接和复制功能，验证文件是否正确链接/复制

### 步骤3：Manifest解析模块实现

**目标**：实现manifest.json解析功能

**任务**：
1. 实现manifest.json文件读取
2. 实现重复组信息解析
3. 实现图片查询功能（检查图片是否在manifest中）

**验证方式**：
- 解析测试manifest.json，验证是否能正确提取重复组信息
- 测试图片查询功能，验证是否能正确判断图片是否在manifest中

### 步骤4：数据集划分核心逻辑实现

**目标**：实现数据集划分的核心算法

**任务**：
1. 实现验证集选择逻辑（不在manifest中的图片）
2. 实现训练集选择逻辑（剩余图片）
3. 实现part分区维护逻辑

**验证方式**：
- 使用测试数据执行划分，验证是否符合规则
- 检查验证集是否不包含manifest中的图片
- 检查是否保持了part分区结构

### 步骤5：文件链接/复制与统计报告生成

**目标**：实现文件链接/复制和统计报告生成

**任务**：
1. 实现文件链接/复制功能，将划分后的数据集写入目标目录
2. 实现统计报告生成功能，包括数据集大小、比例等信息
3. 实现异常处理和日志记录

**验证方式**：
- 执行完整流程，验证文件是否正确链接/复制
- 检查生成的统计报告是否准确
- 测试异常情况（如硬链接失败），验证是否正确处理

### 步骤6：集成测试与优化

**目标**：进行集成测试，优化性能和用户体验

**任务**：
1. 编写集成测试，验证完整流程
2. 优化性能，特别是大数据集处理
3. 改进用户体验，包括进度显示和错误提示

**验证方式**：
- 使用真实数据集执行完整流程，验证结果
- 测量性能指标，确保满足要求
- 收集用户反馈，改进用户体验

## 7. 受影响的现有模块与适配说明

由于这是一个新项目，不存在需要适配的现有模块。然而，需要注意以下几点：

1. **文件系统兼容性**：确保在不同操作系统上的硬链接功能正常工作
2. **大数据集处理**：对于大型数据集，需要注意内存使用和处理效率
3. **错误处理**：需要妥善处理各种异常情况，如文件不存在、权限不足等

## 8. 代码复用策略

1. **标准库优先**：优先使用Python标准库（如os, shutil, argparse等）
2. **常用工具库**：对于复杂功能，可考虑使用成熟的第三方库（如tqdm用于进度显示）
3. **自定义工具类**：封装常用功能为工具类，便于复用 