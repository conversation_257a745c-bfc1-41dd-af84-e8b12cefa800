# 基于深度视觉模型的图片去重系统 PRD

## 1. 项目概述

### 1.1 项目背景
表格识别数据集中存在大量内容重复的图片，包括完全相同的图片以及经过轻微压缩、尺寸变化、移位、轻微旋转等变换后的相似图片。需要通过深度视觉模型对这些重复图片进行识别和整理。

### 1.2 项目目标
- 基于ResNet-52深度视觉模型提取图片特征向量
- 使用FAISS库进行高效的相似度检索和匹配
- 将所有重复图片移动到指定目录进行分组存放
- 提供详细的去重日志和统计报告

### 1.3 数据规模
- 预计处理图片数量: ~10万张
- 原始数据组织形式: part_xxxx 分组目录结构
- 计算环境: 支持GPU加速

## 2. 功能需求

### 2.1 核心功能模块

#### 2.1.1 特征提取与索引构建模块 (脚本1)
**功能描述**: 
- 遍历原始图片目录，提取每张图片的深度视觉特征向量
- 构建FAISS索引库并持久化到磁盘

**具体要求**:
- 使用ResNet-52模型进行特征提取
- 按照ResNet-52推荐的预处理方式处理图片(resize、归一化等)
- 特征向量维度按照ResNet-52模型输出维度
- 支持GPU加速的批处理
- 批处理大小根据ResNet-52模型和GPU内存自适应调整
- 索引库使用FAISS格式存储
- 支持断点续传功能
- 生成特征提取进度日志

**输入**:
- 原始图片目录路径
- FAISS索引库保存路径

**输出**:
- FAISS索引库文件
- 图片路径到索引ID的映射文件
- 特征提取日志文件

#### 2.1.2 重复检测与去重模块 (脚本2)
**功能描述**:
- 基于已构建的FAISS索引库进行图片相似度检索
- 识别重复图片组并移动到指定目录

**具体要求**:
- 加载已构建的FAISS索引库
- 使用实践推荐的相似度阈值进行重复判定
- 对于重复图片组(如A、B、C三张图片彼此重复)，将所有重复图片移动到重复目录
- 重复图片按组存放在不同子目录中
- 支持断点续传功能
- 生成详细的去重操作日志

**输入**:
- FAISS索引库路径
- 图片路径映射文件
- 重复图片存放根目录路径

**输出**:
- 重复图片分组目录
- 去重操作日志
- 统计报告

### 2.2 重复判定标准
- **相似度阈值**: 采用实践中推荐的阈值设置
- **重复定义**: 包括完全相同图片以及经过轻微压缩、尺寸变化、移位、轻微旋转等变换的相似图片
- **检索方式**: 根据性能需求选择FAISS索引类型(精确搜索IndexFlatIP或近似搜索IndexIVFFlat)

### 2.3 目录结构组织

#### 2.3.1 重复图片目录结构
```
{用户指定根目录}/
├── dup_0001/
│   ├── image1.jpg
│   ├── image2.png
│   └── image3.jpeg
├── dup_0002/
│   ├── image4.jpg
│   └── image5.png
└── ...
```

#### 2.3.2 日志文件结构
```
{原始数据目录}/
├── dedup_logs/
│   ├── feature_extraction.log
│   ├── deduplication_operations.log
│   └── final_statistics.json
└── ...
```

### 2.4 日志记录要求

#### 2.4.1 特征提取日志
- 处理进度信息
- 每张图片的特征提取状态
- 错误图片记录
- 处理耗时统计

#### 2.4.2 去重操作日志
- 每张图片的移动操作记录
- 重复组识别信息
- 文件移动成功/失败状态
- 操作时间戳

#### 2.4.3 统计报告
- 总图片数量
- 重复组数量
- 移动文件数量
- 重复率统计
- 处理耗时报告

### 2.5 断点续传功能
- 特征提取过程支持中断后继续
- 去重过程支持中断后继续
- 进度状态持久化到文件
- 重启时自动检测并从中断点继续

## 3. 技术规格

### 3.1 深度学习模型
- **模型选择**: ResNet-52
- **特征维度**: 按照ResNet-52模型实际输出维度
- **预处理**: 按照ResNet-52模型推荐方式(resize、归一化等)
- **推理框架**: PyTorch或TensorFlow(根据实现选择)

### 3.2 相似度检索
- **检索库**: FAISS
- **索引类型**: 根据性能需求选择(IndexFlatIP或IndexIVFFlat)
- **相似度度量**: 余弦相似度或欧氏距离
- **阈值设置**: 采用实践推荐值

### 3.3 性能要求
- **批处理**: 支持GPU批处理加速
- **内存管理**: 高效的内存使用，支持大规模数据处理
- **处理速度**: 充分利用GPU加速能力
- **存储效率**: FAISS索引库的高效存储和加载

### 3.4 支持的图片格式
- 支持常见图片格式: .jpg, .jpeg, .png, .JPEG, .JPG, .PNG
- 自动跳过损坏或无法读取的图片文件

## 4. 输入输出规格

### 4.1 脚本1 - 特征提取与索引构建
**输入参数**:
- `--input_dir`: 原始图片目录路径
- `--index_output_dir`: FAISS索引库保存目录
- `--batch_size`: 批处理大小(可选，自动调整)
- `--resume`: 是否从断点继续(可选)

**输出文件**:
- `faiss_index.bin`: FAISS索引库文件
- `image_paths.json`: 图片路径到索引ID的映射
- `feature_extraction.log`: 特征提取日志

### 4.2 脚本2 - 重复检测与去重
**输入参数**:
- `--index_dir`: FAISS索引库目录路径
- `--dup_output_dir`: 重复图片存放根目录
- `--similarity_threshold`: 相似度阈值(可选，使用推荐值)
- `--resume`: 是否从断点继续(可选)

**输出文件**:
- `dup_xxxx/`: 重复图片分组目录
- `deduplication_operations.log`: 去重操作日志
- `final_statistics.json`: 最终统计报告

## 5. 质量标准

### 5.1 功能质量
- 正确识别内容重复的图片(包括轻微变换)
- 准确移动重复图片到指定目录
- 完整的操作日志记录
- 可靠的断点续传功能

### 5.2 性能质量
- 充分利用GPU加速能力
- 支持大规模数据处理(10万张图片)
- 合理的内存使用和处理速度
- FAISS索引的高效检索性能

### 5.3 可维护性
- 清晰的代码结构和注释
- 详细的错误处理和日志记录
- 易于调试和问题定位
- 模块化的设计便于后续扩展

## 6. 实现优先级

### 6.1 第一优先级 (核心功能)
- 基于ResNet-52的特征提取功能
- FAISS索引库构建和存储
- 重复图片检测和移动功能
- 基础的日志记录功能

### 6.2 第二优先级 (辅助功能)
- 详细的操作日志和统计报告
- 断点续传功能
- 错误处理和异常恢复
- 性能优化和内存管理

## 7. 验收标准

### 7.1 功能验收
- [ ] 成功提取所有图片的特征向量并构建FAISS索引
- [ ] 正确识别重复图片组并按要求移动到指定目录
- [ ] 生成完整的操作日志和统计报告
- [ ] 断点续传功能正常工作

### 7.2 性能验收
- [ ] 10万张图片的处理时间在合理范围内
- [ ] GPU资源得到充分利用
- [ ] 内存使用稳定，无内存泄漏
- [ ] FAISS索引检索效率符合预期

### 7.3 质量验收
- [ ] 代码结构清晰，注释完整
- [ ] 错误处理完善，异常情况有合理提示
- [ ] 日志信息详细且格式规范
- [ ] 支持的图片格式完整覆盖 