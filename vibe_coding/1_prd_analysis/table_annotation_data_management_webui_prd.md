# 表格标注数据管理WebUI系统 PRD

- **Time**: 2025-01-28
- **Author**: AI Assistant
- **FileName**: table_annotation_data_management_webui_prd.md

## 1. 产品概述

### 1.1 产品定义
表格标注数据管理WebUI系统是一个用于管理表格标注任务的Web界面工具，支持原始数据下载和标注数据上传管理，并提供实时的标注进度统计功能。

### 1.2 核心价值
- 统一管理表格标注任务的数据流转
- 实时跟踪标注进度和完成情况
- 确保数据格式规范和质量控制
- 提供直观的Web界面操作体验

## 2. 功能需求

### 2.1 整体界面结构
- **主界面布局**：双Tab设计
  - Tab1：待标注数据下载
  - Tab2：已标注数据上传

### 2.2 Tab1：待标注数据下载

#### 2.2.1 目录配置
- **下载目录**：配置文件中指定的固定服务器目录
- **数据来源**：管理员预先上传的tar.gz格式文件
- **目录状态**：支持空目录显示（需提示"目录为空，无文件"）

#### 2.2.2 文件列表展示
- **列表组件**：
  - 初始高度适中，单文件时完整可见
  - 多文件时支持滚动查看
- **文件信息显示**：
  - 文件名
  - 文件大小
  - 创建时间
- **下载功能**：每个文件提供独立的下载按钮

#### 2.2.3 统计信息展示
- **位置**：页面底部
- **统计项目**：
  - 总文件数：下载目录中tar.gz文件总数
  - 待标注总数：总文件数 - 已标注总数
  - 已标注总数：上传目录中存在的同名tar.gz文件数
  - 完成百分比：已标注总数 / 总文件数 * 100%

### 2.3 Tab2：已标注数据上传

#### 2.3.1 目录配置
- **上传目录**：配置文件中指定的固定服务器目录
- **目录状态**：支持空目录显示（需提示"目录为空，无文件"）

#### 2.3.2 已上传文件列表展示
- **列表组件**：
  - 显示已上传的tar.gz文件信息
  - 支持滚动查看多个文件
- **文件信息显示**：
  - 文件名
  - 文件大小
  - 上传时间
  - 样本统计（已标注样本数/总样本数）
- **功能限制**：仅展示信息，不提供下载功能

#### 2.3.3 文件上传功能
- **格式限制**：仅接受tar.gz格式文件
- **文件名验证**：上传文件名必须存在于待标注数据下载的文件列表中
- **上传流程**：
  1. 用户选择本地tar.gz文件
  2. 系统验证文件格式和文件名
  3. 执行内容验证（解压到临时目录）
  4. 验证通过后移动到上传目录
  5. 更新统计状态

#### 2.3.4 内容验证规则
- **如何解析压缩包数据**：解压后遍历到所有图片文件
- **图片格式支持**：['.jpg', '.jpeg', '.JPEG', '.JPG', '.png', '.PNG']
- **隐藏文件过滤**：排除以点(.)开头的文件
- **样本统计**：
  - 总样本数：解压后符合格式的图片文件数量
  - 有效样本数：存在对应`xxx_table_annotation.json`文件的图片数量
- **标注文件验证**：
  - 文件命名：`{图片文件名}_table_annotation.json`
  - 内容验证：必须包含"cells"字段，且值为非空列表

#### 2.3.5 状态持久化
- **存储位置**：上传目录根目录
- **文件名**：`annotation_stats.json`
- **数据结构**：
```json
{
  "total_samples": "累计解压后的图片总数",
  "valid_annotated_samples": "有效标注的样本总数", 
  "files_info": [
    {
      "filename": "文件名.tar.gz",
      "total_images": "图片总数",
      "valid_annotations": "有效标注数"
    }
  ],
  "last_updated": "最后更新时间"
}
```

#### 2.3.6 统计信息展示
- **位置**：页面底部
- **统计项目**：
  - 总样本：所有已上传文件的图片总数
  - 已标注样本：所有已上传文件的有效标注总数
  - 标注百分比：已标注样本 / 总样本 * 100%

## 3. 技术规格

### 3.1 数据格式规范
- **压缩格式**：tar.gz（强制要求）
- **图片格式**：['.jpg', '.jpeg', '.JPEG', '.JPG', '.png', '.PNG']
- **标注文件**：JSON格式，必须包含非空"cells"字段

### 3.2 目录结构
- **下载目录**：配置文件指定的服务器固定路径
- **上传目录**：配置文件指定的服务器固定路径
- **临时目录**：用于解压验证，验证完成后清理

### 3.3 状态管理
- **系统启动**：自动加载`annotation_stats.json`恢复状态
- **实时更新**：每次上传成功后更新统计状态
- **数据一致性**：确保统计数据与实际文件状态同步

## 4. 用户体验规范

### 4.1 错误处理
- **格式错误**：提示"请上传tar.gz格式文件"
- **文件名不匹配**：提示"上传文件名必须在待标注列表中存在"
- **内容验证失败**：提示"文件内容不符合标注规范，请检查标注文件格式"
- **解压失败**：提示"文件损坏或格式错误，请重新压缩为tar.gz格式"

### 4.2 操作反馈
- **上传成功**：显示验证结果和统计更新
- **空目录**：明确提示"目录为空，无文件"
- **加载状态**：文件列表加载时显示loading状态

### 4.3 界面交互
- **Tab切换**：支持快速切换，保持各Tab状态
- **文件列表**：支持排序（按时间、大小、名称）
- **统计信息**：实时更新，无需手动刷新

## 5. 业务规则

### 5.1 数据流转规则
1. 管理员上传原始数据到下载目录
2. 用户从下载Tab获取待标注数据
3. 用户完成标注后上传到上传Tab
4. 系统验证并更新统计状态
5. 已上传文件不再出现在待标注列表中

### 5.2 文件命名规则
- **下载文件**：保持原始文件名
- **上传文件**：必须与下载列表中的文件名完全一致
- **标注文件**：`{图片文件名}_table_annotation.json`

### 5.3 验证规则
- **文件格式**：严格验证tar.gz格式
- **内容结构**：验证解压后的目录结构和文件格式
- **标注完整性**：验证标注文件的存在性和内容有效性

## 6. 非功能性需求

### 6.1 性能要求
- **文件上传**：支持大文件上传（无大小限制）
- **解压验证**：高效的临时目录处理
- **状态加载**：系统启动时快速恢复状态

### 6.2 可靠性要求
- **数据一致性**：确保统计数据准确性
- **错误恢复**：上传失败时不影响现有数据
- **状态持久化**：系统重启后状态完整恢复

### 6.3 可维护性要求
- **配置管理**：目录路径通过配置文件管理
- **日志记录**：关键操作记录日志
- **模块化设计**：便于功能扩展和维护 