# 表格识别数据管理WebUI需求文档

## 1. 产品概述

### 1.1 产品背景

表格识别数据管理WebUI是一个用于管理表格识别相关数据文件的Web应用程序。该应用程序允许用户查看、下载和上传表格识别相关的数据文件，并对上传的文件进行分析和统计。

### 1.2 产品目标

- 提供直观的文件浏览和下载功能
- 支持压缩文件上传并保存
- 自动分析上传文件中的有效样本和未打标样本
- 确保数据管理过程的高效性和可靠性

## 2. 功能需求

### 2.1 文件浏览与下载

#### 2.1.1 文件列表显示

**优先级：** 高

**功能描述：**
- 以列表形式展示指定目录中的所有文件
- 显示文件的基本信息，包括文件名、文件大小和修改时间
- 支持文件列表的刷新功能
- 当目录为空时，显示明确的提示信息

**交互流程：**
1. 用户访问WebUI的"数据下载"页面
2. 系统自动加载并显示指定目录中的文件列表
3. 用户可点击"刷新文件列表"按钮更新显示内容

#### 2.1.2 文件下载

**优先级：** 高

**功能描述：**
- 支持单个文件下载
- 支持批量文件下载
- 提供直观的下载按钮和操作提示

**交互流程：**
1. 用户在文件列表中选择一个或多个文件
2. 用户点击"下载选中文件"按钮
3. 系统生成下载链接或直接触发下载过程
4. 用户成功下载所选文件

### 2.2 文件上传与分析

#### 2.2.1 文件上传

**优先级：** 高

**功能描述：**
- 支持tar或tar.gz格式压缩文件的上传
- 保存原始压缩文件，而非解压后的内容
- 使用原文件名保存，并防止重名覆盖
- 提供上传进度和状态反馈

**交互流程：**
1. 用户访问WebUI的"数据上传"页面
2. 用户选择要上传的压缩文件
3. 用户点击"上传并验证"按钮
4. 系统接收并保存文件，同时显示上传进度
5. 上传完成后，系统显示上传状态信息

#### 2.2.2 文件内容分析

**优先级：** 高

**功能描述：**
- 临时解压上传的压缩文件进行内容分析
- 识别压缩文件中的有效样本和未打标样本
- 有效样本定义：图片文件+对应的_table_annotation.json文件
- 未打标样本定义：只有图片文件，没有对应的标注文件
- 显示分析结果，包括有效样本数量和未打标样本数量

**交互流程：**
1. 用户上传压缩文件后，系统自动进行内容分析
2. 系统临时解压文件并扫描内容
3. 系统统计有效样本和未打标样本的数量
4. 系统在界面上显示分析结果
5. 分析完成后，系统清理临时解压的文件

## 3. 非功能需求

### 3.1 性能需求

- 文件列表加载时间不超过3秒
- 支持至少100MB大小的文件上传
- 文件分析过程中提供进度反馈
- 系统应能同时处理多个用户的请求

### 3.2 安全需求

- 上传文件前进行格式验证，只接受tar和tar.gz格式
- 解压文件时进行安全检查，防止路径遍历攻击
- 防止文件名冲突导致的数据覆盖

### 3.3 可用性需求

- 界面设计简洁明了，操作流程直观
- 提供清晰的操作反馈和错误提示
- 支持主流浏览器（Chrome、Firefox、Safari、Edge）
- 适配不同屏幕尺寸，支持响应式设计

## 4. 界面设计

### 4.1 整体布局

WebUI采用标签页布局，包含两个主要标签页：
- 数据下载：用于浏览和下载文件
- 数据上传：用于上传和分析文件

### 4.2 数据下载页面

- 顶部：标题和刷新按钮
- 中部：文件列表表格，显示文件名、大小和修改时间
- 底部：文件选择区域和下载按钮

### 4.3 数据上传页面

- 左侧：文件上传区域和上传按钮
- 右侧：上传状态和分析结果显示区域
- 底部：分析结果统计表格

## 5. 技术要求

### 5.1 开发环境

- 编程语言：Python
- Web框架：支持快速原型开发的框架（如Gradio、Streamlit等）
- 运行环境：支持Python的服务器或容器

### 5.2 依赖项

- 文件操作库：用于处理文件读写和压缩文件操作
- Web服务库：用于提供HTTP服务
- 数据处理库：用于分析和统计文件内容

## 6. 约束与限制

- 系统只处理指定目录中的文件，不支持目录树浏览
- 仅支持tar和tar.gz格式的压缩文件上传
- 有效样本的判定仅基于文件名模式匹配
- 不提供文件内容预览功能
- 不包含用户认证和权限控制

## 7. 未来扩展

以下功能可能在未来版本中考虑实现：
- 文件内容预览功能
- 更复杂的文件过滤和搜索功能
- 用户认证和权限控制
- 支持更多压缩文件格式
- 文件批量处理操作 