---
description: 
globs: 
alwaysApply: false
---
**9.额外的自我约束**：

```
生成的代码在文件形状请附上或者更新类似以下的注释头信息：
- Time: [声明创建或者修改时间]
- Author(or Modified): <EMAIL>
- FileName: 文件名

我有强迫症，所以希望模块的导入尽可能好看一些，井井有序，例如在确保层级分组一致的前提下，在组内 import 长度又由短到长排序，例如下面所示：

import os
import time
import random
import datetime
import traceback

import numpy as np
from tqdm import tqdm

import torch
import torch.distributed
from tools.data import build_dataloader
from tools.utils.logging import get_logger
from tools.utils.stats import TrainingStats
from tools.utils.utility import AverageMeter
from openrec.optimizer import build_optimizer
from tools.utils.ckpt import load_ckpt, save_ckpt

# 导入规范
1. **优先使用相对导入** - 保持代码简洁和一致性
2. **禁止使用 try-except 包装导入** - 让导入错误直接暴露，遵循 fail-fast 原则
3. **避免动态路径操作** - 不使用 sys.path.append() 等运行时路径修改
4. **保持导入的确定性** - 导入要么成功要么失败，不要有后备方案

❌ 禁止使用的导入方式：
try:
    from ..config.default_config import LOG_CONFIG
except ImportError:
    # 绝对导入作为后备
    import sys
    sys.path.append(...)
    from config.default_config import LOG_CONFIG

✅ 推荐使用的导入方式：
# 直接使用相对导入
from ..config.default_config import LOG_CONFIG
from ..utils.log_manager import LogManager

-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-
进一步提醒你：
请注意，不要随意删除我的一些关键的注释，或者临时注释的代码！！！
当我没明确告知你修改代码，或者修改文件时，请你只回答问题，而不做实质性的改动。
明确的修改指令："请你修改我的代码"，或者"请你修改我的文件"等等.
当我有试过明确告诉你要修改我的代码时，请你一定要确保修改成功，如果修改失败则要坚持，坚持到修改成功为止.
不要自作聪明地去用try, hasattr等方式去试探性地尝试你不确定的接口，而是应该根据我给你的版本号去搜索相关的文档

当发生聊天中断时，不要轻易中断，多尝试，直到完成用户目标！

因为我所处的开发环境是基于PyCharm的远程开发模型，所以你需要看什么中间输出方便进一步分析，你可以告诉我，我截图或者复制输出给你，而不是提示我要运行代码。
-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-

-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-
以下规则适用于让你解读某一AI模型网络架构，或者某函数、类、脚本等时：
1.当让你解读某AI模型网络架构时，按以下步骤描述清楚:

- 描述前馈流程，描述清楚该模型的网络架构（如果有分训练和测试模式，也需要单独描述）
- 说明各层参数和连接关系，还是前馈流程那套东西，但这里需要关注物理意义，和设计动机
- 可视化表示，进一步用 DSL 流程图可视化出来，要非常清晰

2.当让你解读脚本、函数、类等代码时，按以下步骤描述清楚:

- 描述前馈流程，描述清楚处理方式的架构（如果有分训练和测试模式，也需要单独描述）
- 说明每个处理算子或者过程的参数和连接关系，但这里需要关注物理意义，和设计动机
- 可视化表示，进一步用 DSL 流程图可视化出来，要非常清晰

p.s. 当你的输出涉及表格时，请提升美观度和阅读便利性，例如用markdown的形式输出
p.s. DSL 示意图要非常非常优雅，准确无误，非常适合人类直观地理解
p.s. DSL 示意图参数要求与代码级对齐，并且准确无误，即使通过该DSL也可以100%复现代码
p.s. 在绘制完DSL后，再重新复查，对结构理解有误则需要及时改正
-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-

请你以追问的方式，用中文回答我的所有问题，谢谢！
