#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/10/15 15:07
# <AUTHOR> <EMAIL>
# @FileName: losses

import torch.nn as nn


class TrainingLoss(nn.Module):
    def __init__(self, lambda_img=1., lambda_2d=5., lambda_3d=5.):
        super(TrainingLoss, self).__init__()
        self.l1_loss = nn.L1Loss()
        self.l2_loss = nn.MSELoss()
        self.lambda_img = lambda_img
        self.lambda_2d = lambda_2d
        self.lambda_3d = lambda_3d

    def forward(self, img_unwarped, grid2D, grid3D, img_gt, grid2D_gt, grid3D_gt, disable_lambda_img=False):
        recon_l2_loss = self.l2_loss(img_unwarped, img_gt)
        grid2D_loss = self.l2_loss(grid2D, grid2D_gt)
        grid3D_loss = self.l2_loss(grid3D, grid3D_gt)

        if disable_lambda_img:
            backward_loss = self.lambda_2d * grid2D_loss + self.lambda_3d * grid3D_loss
        else:
            recon_l1_loss = self.l1_loss(img_unwarped, img_gt)
            backward_loss = self.lambda_img * recon_l1_loss + self.lambda_2d * grid2D_loss + self.lambda_3d * grid3D_loss

        return backward_loss, recon_l2_loss


class TrainingLoss2(nn.Module):
    def __init__(self, lambda_img=1., lambda_2d=5.):
        super(TrainingLoss2, self).__init__()
        self.l1_loss = nn.L1Loss()
        self.l2_loss = nn.MSELoss()
        self.lambda_img = lambda_img
        self.lambda_2d = lambda_2d

    def forward(self, img_unwarped, grid2D,  img_gt, grid2D_gt,  disable_lambda_img=False):
        recon_l2_loss = self.l2_loss(img_unwarped, img_gt)
        grid2D_loss = self.l2_loss(grid2D, grid2D_gt)

        if disable_lambda_img:
            backward_loss = self.lambda_2d * grid2D_loss
        else:
            recon_l1_loss = self.l1_loss(img_unwarped, img_gt)
            backward_loss = self.lambda_img * recon_l1_loss + self.lambda_2d * grid2D_loss

        return backward_loss, recon_l2_loss
