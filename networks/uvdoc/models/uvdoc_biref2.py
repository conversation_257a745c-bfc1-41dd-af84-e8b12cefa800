#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/10/15 17:08
# <AUTHOR> <EMAIL>
# @FileName: uvdoc

import torch
import torch.nn as nn
import torch
import torch.nn as nn
import torch.nn.functional as F
from kornia.filters import laplacian

# birefnet
from networks.birefnet.dto.config import Config
from networks.birefnet.refinement.stem_layer import StemLayer
from networks.birefnet.modules.aspp import ASPP, ASPPDeformable
from networks.birefnet.modules.lateral_blocks import BasicLatBlk
from my_datasets.image_matting.dto import class_labels_TR_sorted
from networks.birefnet.backbones.build_backbone import build_backbone
from networks.birefnet.modules.decoder_blocks import BasicDecBlk, ResBlk
from networks.birefnet.refinement.refiner import Refiner, RefinerPVTInChannels4, RefUNet


def conv3x3(in_channels, out_channels, kernel_size, stride=1):
    return nn.Conv2d(
        in_channels,
        out_channels,
        kernel_size=kernel_size,
        stride=stride,
        padding=kernel_size // 2,
    )


def dilated_conv_bn_act(in_channels, out_channels, act_fn, BatchNorm, dilation):
    model = nn.Sequential(
        nn.Conv2d(
            in_channels,
            out_channels,
            bias=False,
            kernel_size=3,
            stride=1,
            padding=dilation,
            dilation=dilation,
        ),
        BatchNorm(out_channels),
        act_fn,
    )
    return model


def dilated_conv(in_channels, out_channels, kernel_size, dilation, stride=1):
    model = nn.Sequential(
        nn.Conv2d(
            in_channels,
            out_channels,
            kernel_size=kernel_size,
            stride=stride,
            padding=dilation * (kernel_size // 2),
            dilation=dilation,
        )
    )
    return model


class ResidualBlockWithDilation(nn.Module):
    def __init__(
            self,
            in_channels,
            out_channels,
            BatchNorm,
            kernel_size,
            stride=1,
            downsample=None,
            is_activation=True,
            is_top=False,
    ):
        super(ResidualBlockWithDilation, self).__init__()
        self.stride = stride
        self.downsample = downsample
        self.is_activation = is_activation
        self.is_top = is_top
        if self.stride != 1 or self.is_top:
            self.conv1 = conv3x3(in_channels, out_channels, kernel_size, self.stride)
            self.conv2 = conv3x3(out_channels, out_channels, kernel_size)
        else:
            self.conv1 = dilated_conv(in_channels, out_channels, kernel_size, dilation=3)
            self.conv2 = dilated_conv(out_channels, out_channels, kernel_size, dilation=3)
        
        self.bn1 = BatchNorm(out_channels)
        self.relu = nn.ReLU(inplace=True)
        self.bn2 = BatchNorm(out_channels)
    
    def forward(self, x):
        residual = x
        if self.downsample is not None:
            residual = self.downsample(x)
        
        out1 = self.relu(self.bn1(self.conv1(x)))
        out2 = self.bn2(self.conv2(out1))
        
        out2 += residual
        out = self.relu(out2)
        return out


class ResnetStraight(nn.Module):
    def __init__(
            self,
            num_filter,
            map_num,
            BatchNorm,
            block_nums=(3, 4, 6, 3),
            block=ResidualBlockWithDilation,
            kernel_size=5,
            stride=(1, 1, 2, 2),
    ):
        super(ResnetStraight, self).__init__()
        self.in_channels = num_filter * map_num[0]
        self.stride = stride
        self.relu = nn.ReLU(inplace=True)
        self.block_nums = block_nums
        self.kernel_size = kernel_size
        
        self.layer1 = self.blocklayer(
            block,
            num_filter * map_num[0],
            self.block_nums[0],
            BatchNorm,
            kernel_size=self.kernel_size,
            stride=self.stride[0],
        )
        self.layer2 = self.blocklayer(
            block,
            num_filter * map_num[1],
            self.block_nums[1],
            BatchNorm,
            kernel_size=self.kernel_size,
            stride=self.stride[1],
        )
        self.layer3 = self.blocklayer(
            block,
            num_filter * map_num[2],
            self.block_nums[2],
            BatchNorm,
            kernel_size=self.kernel_size,
            stride=self.stride[2],
        )
    
    def blocklayer(self, block, out_channels, block_nums, BatchNorm, kernel_size, stride=1):
        downsample = None
        if (stride != 1) or (self.in_channels != out_channels):
            downsample = nn.Sequential(
                conv3x3(
                    self.in_channels,
                    out_channels,
                    kernel_size=kernel_size,
                    stride=stride,
                ),
                BatchNorm(out_channels),
            )
        
        layers = []
        layers.append(
            block(
                self.in_channels,
                out_channels,
                BatchNorm,
                kernel_size,
                stride,
                downsample,
                is_top=True,
            )
        )
        self.in_channels = out_channels
        for i in range(1, block_nums):
            layers.append(
                block(
                    out_channels,
                    out_channels,
                    BatchNorm,
                    kernel_size,
                    is_activation=True,
                    is_top=False,
                )
            )
        
        return nn.Sequential(*layers)
    
    def forward(self, x):
        out1 = self.layer1(x)
        out2 = self.layer2(out1)
        out3 = self.layer3(out2)
        return out3


# 这边加入birefnet 部分
class UVDocNet_biref2(nn.Module):
    def __init__(self, in_channels=3, kernel_size=5, num_filter=32, stride=(1, 2, 2, 2), map_num=(1, 2, 4, 8, 16)):
        super(UVDocNet_biref2, self).__init__()
        # 加载birefNet encoder 部分的模型权重
        self.initial_birefNet()
        self.stride = stride
        self.num_filter = num_filter
        self.in_channels = in_channels
        self.kernel_size = kernel_size
        
        BatchNorm = nn.BatchNorm2d
        act_fn = nn.ReLU(inplace=True)
        
        # self.resnet_head = nn.Sequential(
        #     nn.Conv2d(
        #         self.in_channels,
        #         self.num_filter * map_num[0],
        #         bias=False,
        #         kernel_size=self.kernel_size,
        #         stride=2,
        #         padding=self.kernel_size // 2,
        #     ),
        #     BatchNorm(self.num_filter * map_num[0]),
        #     act_fn,
        #     nn.Conv2d(
        #         self.num_filter * map_num[0],
        #         self.num_filter * map_num[0],
        #         bias=False,
        #         kernel_size=self.kernel_size,
        #         stride=2,
        #         padding=self.kernel_size // 2,
        #     ),
        #     BatchNorm(self.num_filter * map_num[0]),
        #     act_fn,
        # )
        #
        # self.resnet_down = ResnetStraight(
        #     self.num_filter,
        #     map_num,
        #     BatchNorm,
        #     block_nums=[3, 4, 6, 3],
        #     block=ResidualBlockWithDilation,
        #     kernel_size=self.kernel_size,
        #     stride=self.stride,
        # )
        
        map_num_i = 2
        
        # biref_net cat uvdoc 用来将backbne和后续连接
        self.biref_uvdoc_channel = nn.Sequential(
            nn.Conv2d(sum(self.biref_channels), sum(self.biref_channels)//2, kernel_size=1),
            nn.BatchNorm2d(sum(self.biref_channels)//2),
            nn.ReLU(),
            nn.Conv2d(sum(self.biref_channels)//2, self.num_filter * map_num[map_num_i], kernel_size=1),
            nn.BatchNorm2d(self.num_filter * map_num[map_num_i]),
            nn.ReLU()
        )
        self.bridge_1 = nn.Sequential(
            dilated_conv_bn_act(
                self.num_filter * map_num[map_num_i],
                self.num_filter * map_num[map_num_i],
                act_fn,
                BatchNorm,
                dilation=1,
            )
        )
        
        self.bridge_2 = nn.Sequential(
            dilated_conv_bn_act(
                self.num_filter * map_num[map_num_i],
                self.num_filter * map_num[map_num_i],
                act_fn,
                BatchNorm,
                dilation=2,
            )
        )
        
        self.bridge_3 = nn.Sequential(
            dilated_conv_bn_act(
                self.num_filter * map_num[map_num_i],
                self.num_filter * map_num[map_num_i],
                act_fn,
                BatchNorm,
                dilation=5,
            )
        )
        
        self.bridge_4 = nn.Sequential(
            *[
                dilated_conv_bn_act(
                    self.num_filter * map_num[map_num_i],
                    self.num_filter * map_num[map_num_i],
                    act_fn,
                    BatchNorm,
                    dilation=d,
                )
                for d in [8, 3, 2]
            ]
        )
        
        self.bridge_5 = nn.Sequential(
            *[
                dilated_conv_bn_act(
                    self.num_filter * map_num[map_num_i],
                    self.num_filter * map_num[map_num_i],
                    act_fn,
                    BatchNorm,
                    dilation=d,
                )
                for d in [12, 7, 4]
            ]
        )
        
        self.bridge_6 = nn.Sequential(
            *[
                dilated_conv_bn_act(
                    self.num_filter * map_num[map_num_i],
                    self.num_filter * map_num[map_num_i],
                    act_fn,
                    BatchNorm,
                    dilation=d,
                )
                for d in [18, 12, 6]
            ]
        )
        
        self.bridge_concat = nn.Sequential(
            nn.Conv2d(
                self.num_filter * map_num[map_num_i] * 6,
                self.num_filter * map_num[2],
                bias=False,
                kernel_size=1,
                stride=1,
                padding=0,
            ),
            BatchNorm(self.num_filter * map_num[2]),
            act_fn,
        )
        
        self.out_point_positions2D = nn.Sequential(
            nn.Conv2d(
                self.num_filter * map_num[2],
                self.num_filter * map_num[0],
                bias=False,
                kernel_size=self.kernel_size,
                stride=1,
                padding=self.kernel_size // 2,
                padding_mode="reflect",
            ),
            BatchNorm(self.num_filter * map_num[0]),
            nn.PReLU(),
            nn.Conv2d(
                self.num_filter * map_num[0],
                2,
                kernel_size=self.kernel_size,
                stride=1,
                padding=self.kernel_size // 2,
                padding_mode="reflect",
            ),
        )
        
        self.out_point_positions3D = nn.Sequential(
            nn.Conv2d(
                self.num_filter * map_num[2],
                self.num_filter * map_num[0],
                bias=False,
                kernel_size=self.kernel_size,
                stride=1,
                padding=self.kernel_size // 2,
                padding_mode="reflect",
            ),
            BatchNorm(self.num_filter * map_num[0]),
            nn.PReLU(),
            nn.Conv2d(
                self.num_filter * map_num[0],
                3,
                kernel_size=self.kernel_size,
                stride=1,
                padding=self.kernel_size // 2,
                padding_mode="reflect",
            ),
        )
        
        self._initialize_weights()
    
    def initial_birefNet(self):
        # birefnet-encoder -----------------------------------------------------
        self.config = Config()  # 加载配置文件 进行模型的初步定义
        self.biref_channels = self.config.lateral_channels_in_collection  # [3072, 1536, 768, 384]
        
        self.bb = build_backbone(self.config.bb, pretrained=False)
        
        if self.config.auxiliary_classification:
            self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
            self.cls_head = nn.Sequential(
                nn.Linear(self.biref_channels[0], len(class_labels_TR_sorted))
            )
        
        if self.config.squeeze_block:
            self.squeeze_module = nn.Sequential(*[
                eval(self.config.squeeze_block.split('_x')[0])(self.biref_channels[0] + sum(self.config.cxt),
                                                               self.biref_channels[0])
                for _ in range(eval(self.config.squeeze_block.split('_x')[1]))
            ])
        
        # self.decoder = Decoder(channels)
        
        if self.config.ender:
            self.dec_end = nn.Sequential(
                nn.Conv2d(1, 16, 3, 1, 1),
                nn.Conv2d(16, 1, 3, 1, 1),
                nn.ReLU(inplace=True),
            )
        
        # refine patch-level segmentation
        if self.config.refine:
            if self.config.refine == 'itself':
                self.stem_layer = StemLayer(in_channels=3 + 1, inter_channels=48, out_channels=3, norm_layer='BN')
            else:
                self.refiner = eval('{}({})'.format(self.config.refine, 'in_channels=3+1'))
        
        # # 冻结birefnet部分权重 全参数重新训练
        # for key, value in self.named_parameters():
        #     if 'bb.' in key and 'refiner.' not in key:
        #         value.requires_grad = False
        #     if 'squeeze_module' in key:
        #         value.requires_grad=False
        
        # birefnet-encoder-----------------------------------------------------------------------
    
    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.xavier_normal_(m.weight, gain=0.2)
            if isinstance(m, nn.ConvTranspose2d):
                assert m.kernel_size[0] == m.kernel_size[1]
                nn.init.xavier_normal_(m.weight, gain=0.2)
    
    # biref_net-enocder-------------------------
    def forward_enc(self, x):
        if self.config.bb in ['vgg16', 'vgg16bn', 'resnet50']:
            x1 = self.bb.conv1(x);
            x2 = self.bb.conv2(x1);
            x3 = self.bb.conv3(x2);
            x4 = self.bb.conv4(x3)
        else:
            x1, x2, x3, x4 = self.bb(x)
            if self.config.mul_scl_ipt == 'cat':
                B, C, H, W = x.shape
                x1_, x2_, x3_, x4_ = self.bb(
                    F.interpolate(x, size=(H // 2, W // 2), mode='bilinear', align_corners=True))
                x1 = torch.cat([x1, F.interpolate(x1_, size=x1.shape[2:], mode='bilinear', align_corners=True)], dim=1)
                x2 = torch.cat([x2, F.interpolate(x2_, size=x2.shape[2:], mode='bilinear', align_corners=True)], dim=1)
                x3 = torch.cat([x3, F.interpolate(x3_, size=x3.shape[2:], mode='bilinear', align_corners=True)], dim=1)
                x4 = torch.cat([x4, F.interpolate(x4_, size=x4.shape[2:], mode='bilinear', align_corners=True)], dim=1)
            elif self.config.mul_scl_ipt == 'add':
                B, C, H, W = x.shape
                x1_, x2_, x3_, x4_ = self.bb(
                    F.interpolate(x, size=(H // 2, W // 2), mode='bilinear', align_corners=True))
                x1 = x1 + F.interpolate(x1_, size=x1.shape[2:], mode='bilinear', align_corners=True)
                x2 = x2 + F.interpolate(x2_, size=x2.shape[2:], mode='bilinear', align_corners=True)
                x3 = x3 + F.interpolate(x3_, size=x3.shape[2:], mode='bilinear', align_corners=True)
                x4 = x4 + F.interpolate(x4_, size=x4.shape[2:], mode='bilinear', align_corners=True)
        
        class_preds = self.cls_head(
            self.avgpool(x4).view(x4.shape[0], -1)) if self.training and self.config.auxiliary_classification else None
        if self.config.cxt:
            x4 = torch.cat(
                (
                    *[
                         F.interpolate(x1, size=x4.shape[2:], mode='bilinear', align_corners=True),
                         F.interpolate(x2, size=x4.shape[2:], mode='bilinear', align_corners=True),
                         F.interpolate(x3, size=x4.shape[2:], mode='bilinear', align_corners=True),
                     ][-len(self.config.cxt):],
                    x4
                ),
                dim=1
            )
        
        return (x1, x2, x3, x4), class_preds
    
    def forward(self, x):
        (x1, x2, x3, x4), class_preds = self.forward_enc(x)
        if self.config.squeeze_block:
            x4 = self.squeeze_module(x4)# ASPP 用不同大小卷积核的可变形卷积进行特征提取
        # x: [1, 3, 712, 488] 4 倍下采样
        # 这部分用birefnet 代替
        # resnet_head = self.resnet_head(x)  # 俩次下采样           # [1, 32, 178, 122]
        # resnet_down = self.resnet_down(resnet_head)  # 4倍下采样 # [1, 128, 45, 31]
        # 把x1,x2,x3,x4 全部融到一起 [3072, 1536, 768, 384]
        x4_=F.interpolate(x4,x3.shape[2:],mode='bilinear',align_corners=False)
        x2_=F.interpolate(x2,x3.shape[2:],mode='bilinear',align_corners=False)
        x1_=F.interpolate(x1,x3.shape[2:],mode='bilinear',align_corners=False)
        x3_=torch.cat([x1_,x2_,x3,x4_],dim=1)
        
        resnet_down = self.biref_uvdoc_channel(x3_)
        bridge_1 = self.bridge_1(resnet_down)  # [1, 128, 45, 31]
        bridge_2 = self.bridge_2(resnet_down)  # [1, 128, 45, 31]
        bridge_3 = self.bridge_3(resnet_down)  # [1, 128, 45, 31]
        bridge_4 = self.bridge_4(resnet_down)  # [1, 128, 45, 31]
        bridge_5 = self.bridge_5(resnet_down)  # [1, 128, 45, 31]
        bridge_6 = self.bridge_6(resnet_down)  # [1, 128, 45, 31]
        # [1, 768, 45, 31]
        bridge_concat = torch.cat([bridge_1, bridge_2, bridge_3, bridge_4, bridge_5, bridge_6], dim=1)
        bridge = self.bridge_concat(bridge_concat)
        
        out_point_positions2D = self.out_point_positions2D(bridge)  # [1, 2, 45, 31]
        out_point_positions3D = self.out_point_positions3D(bridge)  # [1, 3, 45, 31]
        
        return out_point_positions2D, out_point_positions3D

# if __name__=='__main__':
#     birefNet_path=r'/aicamera-mlp/xelawk_train_space/aicache/torch/birefnet/general_e244/model.bin'
#     stat_dict=torch.load(birefNet_path,map_location='cuda:0')
#     model=UVDocNet()
#     model.load_state_dict(stat_dict,strict=False)
#     print('sa')
#     pass
