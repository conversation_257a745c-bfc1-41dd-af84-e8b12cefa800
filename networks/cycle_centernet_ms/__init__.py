# Time: 2025-07-13
# Author: <EMAIL>
# FileName: __init__.py

"""
ModelScope Cycle-CenterNet 模块

这个模块包含了从 ModelScope 迁移的 Cycle-CenterNet 表格结构识别模型实现。
与原有的 cycle_centernet 模块相比，这个版本严格遵循 ModelScope 的实现细节，
包括双通道热力图、亚像素精度回归等关键特性。

主要组件:
- DLA34BackboneMS: DLA-34 骨干网络 (ModelScope 版本)
- CycleCenterNetHeadMS: 双通道检测头
- CycleCenterNetModelMS: 完整模型
- CycleCenterNetLossMS: 损失函数
"""

from .cycle_centernet_model_ms import CycleCenterNetModelMS, create_cycle_centernet_ms_model, TableRecModelMS
from .cycle_centernet_loss_ms import CycleCenterNetLossMS, create_cycle_centernet_ms_loss


# create_cycle_centernet_ms_model 函数已在 cycle_centernet_model_ms 模块中实现


# create_cycle_centernet_ms_loss 函数已在 cycle_centernet_loss_ms 模块中实现


__all__ = [
    'CycleCenterNetModelMS',
    'CycleCenterNetLossMS',
    'create_cycle_centernet_ms_model',
    'create_cycle_centernet_ms_loss',
    'TableRecModelMS'
]
