#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/12/8 20:34
# <AUTHOR> <EMAIL>
# @FileName: init_networks

import warnings
from .network_swinir import SwinIR as net

warnings.filterwarnings(
    "ignore",
    message="torch.meshgrid: in an upcoming release, it will be required to pass the indexing argument"
)


def init_net_from_cfg(cfg):
    model = net(
        upscale=cfg['upscale'],
        in_chans=cfg['in_chans'],
        img_size=cfg['img_size'],
        window_size=cfg['window_size'],
        img_range=cfg['img_range'],
        depths=cfg['depths'],
        embed_dim=cfg['embed_dim'],
        num_heads=cfg['num_heads'],
        mlp_ratio=cfg['mlp_ratio'],
        upsampler=cfg['upsampler'],
        resi_connection=cfg['resi_connection']
    )
    return model
