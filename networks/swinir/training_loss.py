#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/12/8 22:19
# <AUTHOR> <EMAIL>
# @FileName: training_loss

import torch.nn as nn
from omegaconf import OmegaConf
from .losses.loss_ssim import SSIMLoss
from .losses.loss import CharbonnierLoss


class TrainingLoss(nn.Module):
    def __init__(self, config_yaml):
        super(TrainingLoss, self).__init__()

        # 加载配置文件
        config = OmegaConf.load(config_yaml)

        # 初始化 L1 损失
        if "l1" in config and config.l1.weight > 0:
            self.l1_loss = nn.L1Loss()
            self.l1_weight = config.l1.weight
        else:
            self.l1_loss = None

        # 初始化 MSE 损失
        if "mse" in config and config.mse.weight > 0:
            self.mse_loss = nn.MSELoss()
            self.mse_weight = config.mse.weight
        else:
            self.mse_loss = None

        # 初始化 SSIM 损失
        if "ssim" in config and config.ssim.weight > 0:
            self.ssim_loss = SSIMLoss()
            self.ssim_weight = config.ssim.weight
        else:
            self.ssim_loss = None

        # 初始化 Charbonnier 损失
        if "charbonnier" in config and config.charbonnier.weight > 0:
            self.charbonnier_loss = CharbonnierLoss(eps=1e-9)
            self.charbonnier_weight = config.charbonnier.weight
        else:
            self.charbonnier_loss = None

    def forward(self, preds, targets):
        """
        前向计算总损失。
        :param preds: 模型的预测值 (Tensor)
        :param targets: 真实值 (Tensor)
        :return: total_loss (标量), metrics (字典，记录各项损失)
        """
        total_loss = 0.0  # 初始化总损失
        metrics = dict()  # 初始化损失度量字典

        # 计算 L1 损失
        if self.l1_loss is not None:
            l1_loss = self.l1_loss(preds, targets) * self.l1_weight
            metrics['l1_loss'] = l1_loss.item()  # 保存损失值（标量）
            total_loss = total_loss + l1_loss

        # 计算 MSE 损失
        if self.mse_loss is not None:
            mse_loss = self.mse_loss(preds, targets) * self.mse_weight
            metrics['mse_loss'] = mse_loss.item()
            total_loss = total_loss + mse_loss

        # 计算 SSIM 损失
        if self.ssim_loss is not None:
            ssim_loss = self.ssim_loss(preds, targets) * self.ssim_weight
            metrics['ssim_loss'] = ssim_loss.item()
            total_loss = total_loss + ssim_loss

        # 计算 Charbonnier 损失
        if self.charbonnier_loss is not None:
            charbonnier_loss = self.charbonnier_loss(preds, targets) * self.charbonnier_weight
            metrics['charbonnier_loss'] = charbonnier_loss.item()
            total_loss = total_loss + charbonnier_loss

        # 返回总损失和损失度量字典
        return total_loss, metrics