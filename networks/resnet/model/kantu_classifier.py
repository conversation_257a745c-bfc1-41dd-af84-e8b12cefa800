#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/11/8 10:31
# <AUTHOR> <EMAIL>
# @FileName: kantu_classifier

import torch.nn as nn
from .kantu_classifier_backbone import ResNet50Backbone


class MultiTaskResNet50(nn.Module):
    def __init__(self, dropout: float = 0.2):
        super(MultiTaskResNet50, self).__init__()

        # 使用 ResNet52 作为 backbone
        self.backbone = ResNet50Backbone(dropout=dropout)

        # 假设 ResNet52 的输出 feature 大小是 2048
        # 替换所有分类器的输入维度为 2048
        self.cls_texted = nn.Linear(2048, 1)
        self.cls_blur = nn.Linear(2048, 1)
        self.cls_watermarked = nn.Linear(2048, 1)
        self.cls_human = nn.Linear(2048, 1)

        self.cls_doc_black = nn.Linear(2048, 1)
        self.cls_doc_blur = nn.Linear(2048, 1)
        self.cls_doc_scene = nn.Linear(2048, 1)
        self.cls_doc_shadow = nn.Linear(2048, 1)
        self.cls_doc_capture = nn.Linear(2048, 1)
        self.cls_cert_doc = nn.Linear(2048, 1)

        self.classifiers = nn.ModuleList([
            self.cls_texted,
            self.cls_blur,
            self.cls_watermarked,
            self.cls_human,
            self.cls_doc_black,
            self.cls_doc_blur,
            self.cls_doc_scene,
            self.cls_doc_shadow,
            self.cls_doc_capture,
            self.cls_cert_doc,
        ])

        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # 通过 ResNet52 backbone 提取特征
        x = self.backbone(x)
        # 对每个分类器进行前向传播并应用 sigmoid
        output = [self.sigmoid(classifier(x)) for classifier in self.classifiers]
        return output

    def cls_blur_forward(self, x):
        x = self.backbone(x)
        output = self.cls_blur(x)
        output = self.sigmoid(output)
        return output

    def cls_texted_forward(self, x):
        x = self.backbone(x)
        output = self.cls_texted(x)
        output = self.sigmoid(output)
        return output

    def cls_watermarked_forward(self, x):
        x = self.backbone(x)
        output = self.cls_watermarked(x)
        output = self.sigmoid(output)
        return output

    def cls_human_forward(self, x):
        x = self.backbone(x)
        output = self.cls_human(x)
        output = self.sigmoid(output)
        return output

    # 新增的 forward 函数
    def cls_doc_black_forward(self, x):
        x = self.backbone(x)
        output = self.cls_doc_black(x)
        output = self.sigmoid(output)
        return output

    def cls_doc_blur_forward(self, x):
        x = self.backbone(x)
        output = self.cls_doc_blur(x)
        output = self.sigmoid(output)
        return output

    def cls_doc_scene_forward(self, x):
        x = self.backbone(x)
        output = self.cls_doc_scene(x)
        output = self.sigmoid(output)
        return output

    def cls_doc_shadow_forward(self, x):
        x = self.backbone(x)
        output = self.cls_doc_shadow(x)
        output = self.sigmoid(output)
        return output

    def cls_doc_capture_forward(self, x):
        x = self.backbone(x)
        output = self.cls_doc_capture(x)
        output = self.sigmoid(output)
        return output

    def cls_cert_doc_forward(self, x):
        x = self.backbone(x)
        output = self.cls_cert_doc(x)
        output = self.sigmoid(output)
        return output
