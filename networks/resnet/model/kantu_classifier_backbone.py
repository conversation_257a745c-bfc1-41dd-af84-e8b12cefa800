#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/11/8 10:34
# <AUTHOR> <EMAIL>
# @FileName: kantu_classifier_backbone

import torch
import torch.nn as nn
from torchvision.models import resnet50, ResNet50_Weights


class ResNet50Backbone(nn.Module):
    def __init__(self, dropout: float = 0.2):
        super(ResNet50Backbone, self).__init__()

        # 加载预训练的resnet50模型
        self.backbone = resnet50(weights=ResNet50_Weights.IMAGENET1K_V2)

        # 去掉最后的全连接层，保留特征提取部分
        self.backbone = nn.Sequential(*list(self.backbone.children())[:-2])

        # 添加一个自定义的全局平均池化层
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))

        # 共享的全连接层
        self.shared_fc = nn.Sequential(
            nn.Linear(2048, 2048),  # ResNet50的输出特征维度是2048
            nn.ReLU(inplace=True),
            nn.Dropout(p=dropout)
        )

    def forward(self, x):
        # 通过ResNet提取特征
        x = self.backbone(x)
        # 进行全局平均池化
        x = self.avgpool(x)
        x = torch.flatten(x, 1)
        # 通过共享的全连接层
        x = self.shared_fc(x)
        return x