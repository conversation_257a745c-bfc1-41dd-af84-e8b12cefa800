import torch.nn as nn
from .large_backbone import MobileNetV3BackboneLarge


class MultiTaskMobileNetV3Large(nn.Module):
    def __init__(self, dropout: float = 0.2):
        super(MultiTaskMobileNetV3Large, self).__init__()

        self.backbone = MobileNetV3BackboneLarge(dropout=dropout)

        self.cls_texted = nn.Linear(1280, 1)
        self.cls_blur = nn.Linear(1280, 1)
        self.cls_watermarked = nn.Linear(1280, 1)
        self.cls_human = nn.Linear(1280, 1)

        self.cls_doc_black = nn.Linear(1280, 1)
        self.cls_doc_blur = nn.Linear(1280, 1)
        self.cls_doc_scene = nn.Linear(1280, 1)
        self.cls_doc_shadow = nn.Linear(1280, 1)
        self.cls_doc_capture = nn.Linear(1280, 1)
        self.cls_cert_doc = nn.Linear(1280, 1)

        self.classifiers = nn.ModuleList([
            self.cls_texted,
            self.cls_blur,
            self.cls_watermarked,
            self.cls_human,
            self.cls_doc_black,
            self.cls_doc_blur,
            self.cls_doc_scene,
            self.cls_doc_shadow,
            self.cls_doc_capture,
            self.cls_cert_doc,
        ])

        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        x = self.backbone(x)
        output = [self.sigmoid(classifier(x)) for classifier in self.classifiers]
        return output

    def cls_blur_forward(self, x):
        x = self.backbone(x)
        output = self.cls_blur(x)
        output = self.sigmoid(output)
        return output

    def cls_texted_forward(self, x):
        x = self.backbone(x)
        output = self.cls_texted(x)
        output = self.sigmoid(output)
        return output

    def cls_watermarked_forward(self, x):
        x = self.backbone(x)
        output = self.cls_watermarked(x)
        output = self.sigmoid(output)
        return output

    def cls_human_forward(self, x):
        x = self.backbone(x)
        output = self.cls_human(x)
        output = self.sigmoid(output)
        return output

    # 新增的 forward 函数
    def cls_doc_black_forward(self, x):
        x = self.backbone(x)
        output = self.cls_doc_black(x)
        output = self.sigmoid(output)
        return output

    def cls_doc_blur_forward(self, x):
        x = self.backbone(x)
        output = self.cls_doc_blur(x)
        output = self.sigmoid(output)
        return output

    def cls_doc_scene_forward(self, x):
        x = self.backbone(x)
        output = self.cls_doc_scene(x)
        output = self.sigmoid(output)
        return output

    def cls_doc_shadow_forward(self, x):
        x = self.backbone(x)
        output = self.cls_doc_shadow(x)
        output = self.sigmoid(output)
        return output

    def cls_doc_capture_forward(self, x):
        x = self.backbone(x)
        output = self.cls_doc_capture(x)
        output = self.sigmoid(output)
        return output

    def cls_cert_doc_forward(self, x):
        x = self.backbone(x)
        output = self.cls_cert_doc(x)
        output = self.sigmoid(output)
        return output


class MultiTaskMobileNetV3LargeOldVersion(nn.Module):
    def __init__(self, dropout: float = 0.2, pretrained: bool = True):
        super(MultiTaskMobileNetV3LargeOldVersion, self).__init__()

        self.backbone = MobileNetV3BackboneLarge(dropout=dropout)

        self.cls_texted = nn.Linear(1280, 1)
        self.cls_blur = nn.Linear(1280, 1)
        self.cls_watermarked = nn.Linear(1280, 1)
        self.cls_human = nn.Linear(1280, 1)

        self.cls_doc_black = nn.Linear(1280, 1)
        self.cls_doc_blur = nn.Linear(1280, 1)
        self.cls_doc_scene = nn.Linear(1280, 1)
        self.cls_doc_shadow = nn.Linear(1280, 1)
        self.cls_doc_capture = nn.Linear(1280, 1)

        self.classifiers = nn.ModuleList([
            self.cls_texted,
            self.cls_blur,
            self.cls_watermarked,
            self.cls_human,
            self.cls_doc_black,
            self.cls_doc_blur,
            self.cls_doc_scene,
            self.cls_doc_shadow,
            self.cls_doc_capture,
        ])

        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        x = self.backbone(x)
        output = [self.sigmoid(classifier(x)) for classifier in self.classifiers]
        return output

    def cls_blur_forward(self, x):
        x = self.backbone(x)
        output = self.cls_blur(x)
        output = self.sigmoid(output)
        return output

    def cls_texted_forward(self, x):
        x = self.backbone(x)
        output = self.cls_texted(x)
        output = self.sigmoid(output)
        return output

    def cls_watermarked_forward(self, x):
        x = self.backbone(x)
        output = self.cls_watermarked(x)
        output = self.sigmoid(output)
        return output

    def cls_human_forward(self, x):
        x = self.backbone(x)
        output = self.cls_human(x)
        output = self.sigmoid(output)
        return output

    # 新增的 forward 函数
    def cls_doc_black_forward(self, x):
        x = self.backbone(x)
        output = self.cls_doc_black(x)
        output = self.sigmoid(output)
        return output

    def cls_doc_blur_forward(self, x):
        x = self.backbone(x)
        output = self.cls_doc_blur(x)
        output = self.sigmoid(output)
        return output

    def cls_doc_scene_forward(self, x):
        x = self.backbone(x)
        output = self.cls_doc_scene(x)
        output = self.sigmoid(output)
        return output

    def cls_doc_shadow_forward(self, x):
        x = self.backbone(x)
        output = self.cls_doc_shadow(x)
        output = self.sigmoid(output)
        return output

    def cls_doc_capture_forward(self, x):
        x = self.backbone(x)
        output = self.cls_doc_capture(x)
        output = self.sigmoid(output)
        return output