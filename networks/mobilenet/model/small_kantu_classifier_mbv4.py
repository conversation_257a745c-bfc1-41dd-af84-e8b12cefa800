#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/16 11:16
# <AUTHOR> <EMAIL>
# @FileName: small_kantu_classifier_mbv4

import torch.nn as nn
from .small_backbone_mbv4 import MobileNetV4BackboneSmall


class MultiTaskMobileNetV4Small(nn.Module):
    def __init__(self, dropout: float = 0.2):
        super(MultiTaskMobileNetV4Small, self).__init__()

        self.backbone = MobileNetV4BackboneSmall(dropout=dropout)
        self.cls_texted = nn.Linear(1280, 1)
        self.cls_blur = nn.Linear(1280, 1)
        self.cls_watermarked = nn.Linear(1280, 1)
        self.cls_human = nn.Linear(1280, 1)

        self.classifiers = nn.ModuleList([
            self.cls_texted,
            self.cls_blur,
            self.cls_watermarked,
            self.cls_human,
        ])

        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        x = self.backbone(x)
        output = [self.sigmoid(classifier(x)) for classifier in self.classifiers]
        return output

    def cls_blur_forward(self, x):
        x = self.backbone(x)
        output = self.cls_blur(x)
        output = self.sigmoid(output)
        return output

    def cls_texted_forward(self, x):
        x = self.backbone(x)
        output = self.cls_texted(x)
        output = self.sigmoid(output)
        return output

    def cls_watermarked_forward(self, x):
        x = self.backbone(x)
        output = self.cls_watermarked(x)
        output = self.sigmoid(output)
        return output

    def cls_human_forward(self, x):
        x = self.backbone(x)
        output = self.cls_human(x)
        output = self.sigmoid(output)
        return output
