#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/21 20:18
# <AUTHOR> <EMAIL>
# @FileName: large_backbone

import torch
import torch.nn as nn
from torchvision.models import mobilenet_v3_large, MobileNet_V3_Large_Weights


class MobileNetV3BackboneLarge(nn.Module):
    def __init__(self, dropout: float = 0.2):
        super(MobileNetV3BackboneLarge, self).__init__()

        # 加载预训练的mobilenet_v3_small模型
        self.backbone = mobilenet_v3_large(weights=MobileNet_V3_Large_Weights.IMAGENET1K_V2)

        # 共享的全连接层
        self.shared_fc = nn.Sequential(
            nn.Linear(self.backbone.classifier[0].in_features, 1280),
            nn.Hardswish(),
            nn.Dropout(p=dropout)
        )

    def forward(self, x):
        x = self.backbone.features(x)
        x = self.backbone.avgpool(x)
        x = torch.flatten(x, 1)
        x = self.shared_fc(x)
        return x
