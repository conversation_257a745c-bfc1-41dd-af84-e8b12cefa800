#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/21 20:18
# <AUTHOR> <EMAIL>
# @FileName: small_backbone_mbv4

import timm
import torch.nn as nn

class MobileNetV4BackboneSmall(nn.Module):
    def __init__(self, dropout: float = 0.2):
        super(MobileNetV4BackboneSmall, self).__init__()

        # 加载预训练的mobilenetv4_small模型
        self.backbone = timm.create_model(
            'mobilenetv4_conv_small.e2400_r224_in1k',
            pretrained=True,
            drop_rate=dropout,
            num_classes=0,  # remove classifier nn.Linear
        )

    def forward(self, x):
        x = self.backbone(x)
        return x  # [bs, 1280]
