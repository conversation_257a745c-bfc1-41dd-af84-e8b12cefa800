#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-07-28
# <AUTHOR> <EMAIL>
# @FileName: metrics.py

"""
TableBorderNet分割任务评估指标模块

封装现有的分割评估指标，包括：
- IoU (Intersection over Union): 交并比
- F1 Score: F1分数
- Dice Coefficient: Dice系数
- Pixel Accuracy: 像素准确率
- SegmentationMetrics: 综合评估指标类
"""

import torch
import torch.nn.functional as F
from typing import Dict, Any, Optional, Tuple
from networks.utils.metrics.eval import compute_IoU, compute_IoU_single, FScore


class SegmentationMetrics:
    """
    分割任务评估指标计算器
    
    提供完整的分割任务评估指标计算，包括：
    - Mean IoU: 平均交并比
    - Pixel Accuracy: 像素准确率
    - F1 Score: F1分数
    - Dice Coefficient: Dice系数
    - Class-wise metrics: 各类别指标
    """
    
    def __init__(self, num_classes: int, ignore_index: int = -100, eps: float = 1e-6):
        """
        初始化评估指标计算器
        
        Args:
            num_classes: 类别数量
            ignore_index: 忽略的类别索引
            eps: 数值稳定性参数
        """
        self.num_classes = num_classes
        self.ignore_index = ignore_index
        self.eps = eps
        
        # 初始化累积统计
        self.reset()
    
    def reset(self):
        """重置累积统计"""
        self.total_samples = 0
        self.total_correct = 0
        self.total_pixels = 0
        
        # 每个类别的统计
        self.class_iou_sum = torch.zeros(self.num_classes)
        self.class_f1_sum = torch.zeros(self.num_classes)
        self.class_dice_sum = torch.zeros(self.num_classes)
        self.class_count = torch.zeros(self.num_classes)
    
    def update(
        self,
        pred: torch.Tensor,
        target: torch.Tensor,
        mask: Optional[torch.Tensor] = None
    ):
        """
        更新评估指标
        
        Args:
            pred: 预测结果 [B, C, H, W] (logits) 或 [B, H, W] (类别索引)
            target: 真实标签 [B, H, W] (类别索引)
            mask: 可选的掩码 [B, H, W]
        """
        # 如果pred是logits，转换为类别索引
        if pred.dim() == 4:  # [B, C, H, W]
            pred = torch.argmax(pred, dim=1)  # [B, H, W]
        
        # 确保在同一设备上
        pred = pred.to(target.device)
        
        # 应用掩码
        if mask is not None:
            valid_mask = (mask > 0) & (target != self.ignore_index)
        else:
            valid_mask = (target != self.ignore_index)
        
        # 只计算有效像素
        pred_valid = pred[valid_mask]
        target_valid = target[valid_mask]
        
        if len(pred_valid) == 0:
            return
        
        # 更新像素准确率统计
        correct = (pred_valid == target_valid).sum().item()
        total = len(pred_valid)
        
        self.total_correct += correct
        self.total_pixels += total
        self.total_samples += pred.size(0)
        
        # 计算各类别指标
        for class_id in range(self.num_classes):
            # 获取当前类别的预测和真实标签
            pred_class = (pred_valid == class_id).float()
            target_class = (target_valid == class_id).float()
            
            if target_class.sum() > 0:  # 只有当真实标签中存在该类别时才计算
                # IoU
                intersection = (pred_class * target_class).sum()
                union = pred_class.sum() + target_class.sum() - intersection
                iou = intersection / (union + self.eps)
                
                # F1 Score
                precision = intersection / (pred_class.sum() + self.eps)
                recall = intersection / (target_class.sum() + self.eps)
                f1 = 2 * precision * recall / (precision + recall + self.eps)
                
                # Dice Coefficient
                dice = 2 * intersection / (pred_class.sum() + target_class.sum() + self.eps)
                
                # 累积统计
                self.class_iou_sum[class_id] += iou.item()
                self.class_f1_sum[class_id] += f1.item()
                self.class_dice_sum[class_id] += dice.item()
                self.class_count[class_id] += 1
    
    def compute(self) -> Dict[str, float]:
        """
        计算最终的评估指标
        
        Returns:
            评估指标字典
        """
        if self.total_samples == 0:
            return {}
        
        metrics = {}
        
        # 像素准确率
        pixel_accuracy = self.total_correct / self.total_pixels if self.total_pixels > 0 else 0.0
        metrics['pixel_accuracy'] = pixel_accuracy
        
        # 各类别指标
        class_metrics = {}
        valid_classes = self.class_count > 0
        
        if valid_classes.any():
            # 平均IoU
            class_ious = self.class_iou_sum[valid_classes] / self.class_count[valid_classes]
            mean_iou = class_ious.mean().item()
            metrics['mean_iou'] = mean_iou
            
            # 平均F1
            class_f1s = self.class_f1_sum[valid_classes] / self.class_count[valid_classes]
            mean_f1 = class_f1s.mean().item()
            metrics['mean_f1'] = mean_f1
            
            # 平均Dice
            class_dices = self.class_dice_sum[valid_classes] / self.class_count[valid_classes]
            mean_dice = class_dices.mean().item()
            metrics['mean_dice'] = mean_dice
            
            # 各类别详细指标
            for i, class_id in enumerate(torch.where(valid_classes)[0]):
                class_id = class_id.item()
                class_metrics[f'class_{class_id}_iou'] = class_ious[i].item()
                class_metrics[f'class_{class_id}_f1'] = class_f1s[i].item()
                class_metrics[f'class_{class_id}_dice'] = class_dices[i].item()
        
        metrics.update(class_metrics)
        return metrics


def compute_segmentation_metrics(
    pred: torch.Tensor,
    target: torch.Tensor,
    num_classes: int,
    mask: Optional[torch.Tensor] = None,
    ignore_index: int = -100
) -> Dict[str, float]:
    """
    计算单批次的分割评估指标
    
    Args:
        pred: 预测结果 [B, C, H, W] (logits) 或 [B, H, W] (类别索引)
        target: 真实标签 [B, H, W] (类别索引)
        num_classes: 类别数量
        mask: 可选的掩码 [B, H, W]
        ignore_index: 忽略的类别索引
        
    Returns:
        评估指标字典
    """
    metrics_calculator = SegmentationMetrics(num_classes, ignore_index)
    metrics_calculator.update(pred, target, mask)
    return metrics_calculator.compute()


def compute_border_segmentation_metrics(
    pred: torch.Tensor,
    target: torch.Tensor,
    threshold: float = 0.5
) -> Dict[str, float]:
    """
    计算表格边框分割的专用评估指标
    
    针对TableBorderNet的双通道输出（实线/虚线）进行评估
    
    Args:
        pred: 预测结果 [B, 2, H, W] (概率)
        target: 真实标签 [B, 2, H, W] (0/1 mask)
        threshold: 二值化阈值
        
    Returns:
        评估指标字典
    """
    metrics = {}
    
    # 确保pred是概率形式
    if pred.dim() == 4 and pred.size(1) == 2:
        pred_prob = F.softmax(pred, dim=1)  # [B, 2, H, W]
    else:
        pred_prob = pred
    
    # 分别计算实线和虚线的指标
    channel_names = ['solid_line', 'dashed_line']
    
    for i, channel_name in enumerate(channel_names):
        pred_channel = pred_prob[:, i:i+1, :, :]  # [B, 1, H, W]
        target_channel = target[:, i:i+1, :, :]   # [B, 1, H, W]
        
        # 使用现有的评估函数
        iou = compute_IoU(pred_channel, target_channel, threshold)
        f1 = FScore(pred_channel, target_channel, threshold=threshold).item()
        
        metrics[f'{channel_name}_iou'] = iou
        metrics[f'{channel_name}_f1'] = f1
    
    # 计算整体指标（所有通道的平均）
    all_pred = pred_prob.view(pred_prob.size(0), -1)  # [B, 2*H*W]
    all_target = target.view(target.size(0), -1)      # [B, 2*H*W]
    
    # 重新reshape为单通道形式计算整体指标
    all_pred = all_pred.unsqueeze(1)    # [B, 1, 2*H*W]
    all_target = all_target.unsqueeze(1) # [B, 1, 2*H*W]
    
    overall_iou = compute_IoU(all_pred, all_target, threshold)
    overall_f1 = FScore(all_pred, all_target, threshold=threshold).item()
    
    metrics['overall_iou'] = overall_iou
    metrics['overall_f1'] = overall_f1
    metrics['mean_iou'] = (metrics['solid_line_iou'] + metrics['dashed_line_iou']) / 2
    metrics['mean_f1'] = (metrics['solid_line_f1'] + metrics['dashed_line_f1']) / 2
    
    return metrics


def create_tablebordernet_metrics(num_classes: int = 2) -> SegmentationMetrics:
    """
    创建TableBorderNet专用的评估指标计算器
    
    Args:
        num_classes: 类别数量，默认为2（实线、虚线）
        
    Returns:
        配置好的评估指标计算器
    """
    return SegmentationMetrics(num_classes=num_classes, ignore_index=-100)
