#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/11/21 19:57
# @Modified: <EMAIL>
# @FileName: __init__

import logging

from .ffc import FFCResNetGenerator
from .pix2pixhd import (
    GlobalGenerator,
    NLayerDiscriminator,
    MultiDilatedGlobalGenerator,
    MultidilatedNLayerDiscriminator
)


def make_generator(kind, **kwargs):
    logging.info(f'Make generator {kind}')

    if kind == 'pix2pixhd_multidilated':
        return MultiDilatedGlobalGenerator(**kwargs)
    
    if kind == 'pix2pixhd_global':
        return GlobalGenerator(**kwargs)

    if kind == 'ffc_resnet':
        return FFCResNetGenerator(**kwargs)

    raise ValueError(f'Unknown generator kind {kind}')


def make_discriminator(kind, **kwargs):
    logging.info(f'Make discriminator {kind}')

    if kind == 'pix2pixhd_nlayer_multidilated':
        return MultidilatedNLayerDiscriminator(**kwargs)

    if kind == 'pix2pixhd_nlayer':
        return NLayerDiscriminator(**kwargs)

    raise ValueError(f'Unknown discriminator kind {kind}')
