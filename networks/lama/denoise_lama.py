#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/11/23 20:20
# <AUTHOR> <EMAIL>
# @FileName: denoise_lama

import torch
from omegaconf import OmegaConf
from .saicinpainting.modules import make_generator, make_discriminator


class LamaInpaintDenoiseTrainModel(torch.nn.Module):
    def __init__(self, generator_cfg_yaml, discriminator_cfg_yaml):
        super().__init__()
        self.generator_cfg = OmegaConf.load(generator_cfg_yaml)
        self.generator = make_generator(**self.generator_cfg)
        self.discriminator_cfg = OmegaConf.load(discriminator_cfg_yaml)
        self.discriminator = make_discriminator(**self.discriminator_cfg)

    def forward(self, noised_image):
        # 确保 denoise_mask 和 noised_image 在同一设备上
        denoise_mask = self.create_inner_denoise_mask(noised_image).to(noised_image.device)
        # 将 noised_image 和 denoise_mask 拼接
        x = torch.cat([noised_image, denoise_mask], dim=1)
        # 使用生成器生成预测的图像
        predicted_image = self.generator(x)
        return predicted_image, denoise_mask

    def create_inner_denoise_mask(self, noised_image):
        """创建全为1的 denoise mask"""
        b, _, height, width = noised_image.shape
        return torch.ones((b, 1, height, width), dtype=torch.float32)