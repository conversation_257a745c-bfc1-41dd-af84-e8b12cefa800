#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/11/25 15:13
# <AUTHOR> <EMAIL>
# @FileName: training_loss

import torch.nn as nn
from omegaconf import OmegaConf

from .losses.base import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .losses.adversarial import make_discrim_loss
from .losses.perceptual import Perceptual<PERSON>oss, ResNetPL
from .losses.feature_matching import feature_matching_loss, masked_l1_loss, masked_l2_loss


class TrainingLoss(nn.Module):
    def __init__(self, config_yaml):
        super(TrainingLoss, self).__init__()

        config = OmegaConf.load(config_yaml)

        if "adversarial" in config and config.adversarial.weight > 0:
            self.adversarial_loss = make_discrim_loss(**config.adversarial)
        else:
            self.adversarial_loss = None

        if "l1" in config and (config.l1.weight_known > 0 or config.l1.weight_missing > 0):
            self.l1_loss = masked_l1_loss
            self.l1_weight_known = config.l1.weight_known
            self.l1_weight_missing = config.l1.weight_missing
        else:
            self.l1_loss = None

        if "mse" in config and (config.mse.weight_known > 0 or config.mse.weight_missing > 0):
            self.mse_loss = masked_l2_loss
            self.mse_weight_known = config.mse.weight_known
            self.mse_weight_missing = config.mse.weight_missing
        else:
            self.mse_loss = None

        if "perceptual" in config and config.perceptual.weight > 0:
            self.loss_pl = PerceptualLoss()
            self.loss_pl_weight = config.perceptual.weight
        else:
            self.loss_pl = None

        if "feature_matching" in config and config.feature_matching.weight > 0:
            self.feature_match_loss = feature_matching_loss
            self.feature_match_loss_weight = config.feature_matching.weight
        else:
            self.feature_match_loss = None

        if "resnet_pl" in config and config.resnet_pl.weight > 0:
            self.loss_resnet_pl = ResNetPL(**config.resnet_pl)
        else:
            self.loss_resnet_pl = None

    def forward_generator_loss(self, preds, targets, masks, generator, discriminator):
        total_loss = 0.
        metrics = dict()

        if self.l1_loss is not None:
            l1_value = self.l1_loss(preds, targets, masks, self.l1_weight_known, self.l1_weight_missing)
            total_loss = total_loss + l1_value
            metrics["l1"] = l1_value

        if self.mse_loss is not None:
            mse_value = self.mse_loss(preds, targets, masks, self.mse_weight_known, self.mse_weight_missing)
            total_loss = total_loss + mse_value
            metrics["mse"] = mse_value

        if self.loss_pl is not None:
            pl_value = self.loss_pl(preds, targets, mask=masks).sum() * self.loss_pl_weight
            total_loss = total_loss + pl_value
            metrics['gen_pl'] = pl_value

        if self.adversarial_loss is not None:
            self.adversarial_loss.pre_generator_step(
                real_batch=targets,
                fake_batch=preds,
                generator=generator,
                discriminator=discriminator
            )
            discr_real_pred, discr_real_features = discriminator(targets)
            discr_fake_pred, discr_fake_features = discriminator(preds)
            adv_gen_loss, adv_metrics = self.adversarial_loss.generator_loss(
                real_batch=targets,
                fake_batch=preds,
                discr_real_pred=discr_real_pred,
                discr_fake_pred=discr_fake_pred,
                mask=masks,
            )
            total_loss = total_loss + adv_gen_loss
            metrics['gen_adv'] = adv_gen_loss

        if self.feature_match_loss is not None and self.adversarial_loss is not None:
            fm_value = self.feature_match_loss(discr_fake_features, discr_real_features, mask=None)
            fm_value = fm_value * self.feature_match_loss_weight
            total_loss = total_loss + fm_value
            metrics['gen_fm'] = fm_value

        if self.loss_resnet_pl is not None:
            resnet_pl_value = self.loss_resnet_pl(preds, targets)
            total_loss = total_loss + resnet_pl_value
            metrics['gen_resnet_pl'] = resnet_pl_value

        return total_loss, metrics

    def forward_discriminator_loss(self, preds, targets, masks, generator, discriminator):
        total_loss = 0.
        metrics = dict()

        if self.adversarial_loss is not None:
            self.adversarial_loss.pre_discriminator_step(
                real_batch=targets,
                fake_batch=preds,
                generator=generator,
                discriminator=discriminator,
            )
            discr_real_pred, discr_real_features = discriminator(targets)
            discr_fake_pred, discr_fake_features = discriminator(preds)
            adv_discr_loss, adv_metrics = self.adversarial_loss.discriminator_loss(
                real_batch=targets,
                fake_batch=preds,
                discr_real_pred=discr_real_pred,
                discr_fake_pred=discr_fake_pred,
                mask=masks
            )
            total_loss = total_loss + adv_discr_loss
            metrics['discr_adv'] = adv_discr_loss

        return total_loss, metrics


class TrainingLossV2(nn.Module):
    def __init__(self, config_yaml):
        super(TrainingLossV2, self).__init__()

        config = OmegaConf.load(config_yaml)

        # 初始化 L1 损失
        if "l1" in config and config.l1.weight > 0:
            self.l1_loss = nn.L1Loss()
            self.l1_weight = config.l1.weight
        else:
            self.l1_loss = None

        # 初始化 Charbonnier 损失
        if "charbonnier" in config and config.charbonnier.weight > 0:
            self.charbonnier_loss = CharbonnierLoss(eps=1e-9)
            self.charbonnier_weight = config.charbonnier.weight
        else:
            self.charbonnier_loss = None

        # 初始化 MSE 损失
        if "mse" in config and config.mse.weight > 0:
            self.mse_loss = nn.MSELoss()
            self.mse_weight = config.mse.weight
        else:
            self.mse_loss = None

        # 初始化对抗损失
        if "adversarial" in config and config.adversarial.weight > 0:
            self.adversarial_loss = make_discrim_loss(**config.adversarial)
        else:
            self.adversarial_loss = None

        # 初始化特征损失
        if "feature_matching" in config and config.feature_matching.weight > 0:
            self.feature_match_loss = feature_matching_loss
            self.feature_match_loss_weight = config.feature_matching.weight
        else:
            self.feature_match_loss = None

        # 初始化VGG感知损失
        if "perceptual" in config and config.perceptual.weight > 0:
            self.loss_pl = PerceptualLoss()
            self.loss_pl_weight = config.perceptual.weight
        else:
            self.loss_pl = None

        # 初始化ResNet感知损失
        if "resnet_pl" in config and config.resnet_pl.weight > 0:
            self.loss_resnet_pl = ResNetPL(**config.resnet_pl)
        else:
            self.loss_resnet_pl = None

    def forward_generator_loss(self, preds, targets, generator, discriminator):
        total_loss = 0.
        metrics = dict()

        if self.charbonnier_loss is not None:
            charbonnier_value = self.charbonnier_loss(preds, targets) * self.charbonnier_weight
            total_loss = total_loss + charbonnier_value
            metrics["charbonnier"] = charbonnier_value

        if self.l1_loss is not None:
            l1_value = self.l1_loss(preds, targets) * self.l1_weight
            total_loss = total_loss + l1_value
            metrics["l1"] = l1_value

        if self.mse_loss is not None:
            mse_value = self.mse_loss(preds, targets) * self.mse_weight
            total_loss = total_loss + mse_value
            metrics["mse"] = mse_value

        if self.loss_pl is not None:
            pl_value = self.loss_pl(preds, targets) * self.loss_pl_weight
            total_loss = total_loss + pl_value
            metrics['gen_pl'] = pl_value

        if self.adversarial_loss is not None:
            self.adversarial_loss.pre_generator_step(
                real_batch=targets,
                fake_batch=preds,
                generator=generator,
                discriminator=discriminator
            )
            discr_real_pred, discr_real_features = discriminator(targets)
            discr_fake_pred, discr_fake_features = discriminator(preds)
            adv_gen_loss, adv_metrics = self.adversarial_loss.generator_loss(
                real_batch=targets,
                fake_batch=preds,
                discr_real_pred=discr_real_pred,
                discr_fake_pred=discr_fake_pred,
            )
            total_loss = total_loss + adv_gen_loss
            metrics['gen_adv'] = adv_gen_loss

        if self.feature_match_loss is not None and self.adversarial_loss is not None:
            fm_value = self.feature_match_loss(discr_fake_features, discr_real_features, mask=None)
            fm_value = fm_value * self.feature_match_loss_weight
            total_loss = total_loss + fm_value
            metrics['gen_fm'] = fm_value

        if self.loss_resnet_pl is not None:
            resnet_pl_value = self.loss_resnet_pl(preds, targets)
            total_loss = total_loss + resnet_pl_value
            metrics['gen_resnet_pl'] = resnet_pl_value

        return total_loss, metrics

    def forward_discriminator_loss(self, preds, targets, generator, discriminator):
        total_loss = 0.
        metrics = dict()

        if self.adversarial_loss is not None:
            self.adversarial_loss.pre_discriminator_step(
                real_batch=targets,
                fake_batch=preds,
                generator=generator,
                discriminator=discriminator,
            )
            discr_real_pred, discr_real_features = discriminator(targets)
            discr_fake_pred, discr_fake_features = discriminator(preds)
            adv_discr_loss, adv_metrics = self.adversarial_loss.discriminator_loss(
                real_batch=targets,
                fake_batch=preds,
                discr_real_pred=discr_real_pred,
                discr_fake_pred=discr_fake_pred,
            )
            total_loss = total_loss + adv_discr_loss
            metrics['discr_adv'] = adv_discr_loss

        return total_loss, metrics
