#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/12/9 14:17
# <AUTHOR> <EMAIL>
# @FileName: base

import torch
import torch.nn as nn


class CharbonnierLoss(nn.Module):
    """Charbonnier Loss (L1)"""

    def __init__(self, eps=1e-9):
        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()
        self.eps = eps

    def forward(self, x, y):
        diff = x - y
        loss = torch.mean(torch.sqrt((diff * diff) + self.eps))
        return loss