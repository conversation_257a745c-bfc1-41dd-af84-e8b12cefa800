<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS><TS version="2.1" language="zh_CN">
<context>
    <name>Canvas</name>
    <message>
        <location filename="../widgets/canvas.py" line="235"/>
        <source>Image</source>
        <translation>图像</translation>
    </message>
    <message>
        <location filename="../widgets/canvas.py" line="249"/>
        <source>Click &amp; drag to move point</source>
        <translation>点击并拖拽以移动控制点</translation>
    </message>
    <message>
        <location filename="../widgets/canvas.py" line="259"/>
        <source>Click &amp; drag to move shape &apos;%s&apos;</source>
        <translation>点击并拖拽以移动形状&apos;%s&apos;</translation>
    </message>
</context>
<context>
    <name>MainWindow</name>
    <message>
        <location filename="../app.py" line="113"/>
        <source>Flags</source>
        <translation>标记</translation>
    </message>
    <message>
        <location filename="../app.py" line="125"/>
        <source>Polygon Labels</source>
        <translation>多边形标签</translation>
    </message>
    <message>
        <location filename="../app.py" line="130"/>
        <source>Select label to start annotating for it. Press &apos;Esc&apos; to deselect.</source>
        <translation>选择标签类型并开始以其标注。按&apos;Esc&apos;取消选择。</translation>
    </message>
    <message>
        <location filename="../app.py" line="141"/>
        <source>Label List</source>
        <translation>标签列表</translation>
    </message>
    <message>
        <location filename="../app.py" line="146"/>
        <source>Search Filename</source>
        <translation>按文件名检索</translation>
    </message>
    <message>
        <location filename="../app.py" line="155"/>
        <source>File List</source>
        <translation>文件列表</translation>
    </message>
    <message>
        <location filename="../app.py" line="208"/>
        <source>&amp;Quit</source>
        <translation>退出(&amp;Q)</translation>
    </message>
    <message>
        <location filename="../app.py" line="208"/>
        <source>Quit application</source>
        <translation>退出应用</translation>
    </message>
    <message>
        <location filename="../app.py" line="215"/>
        <source>&amp;Open
</source>
        <translation>打开(&amp;O)</translation>
    </message>
    <message>
        <location filename="../app.py" line="215"/>
        <source>Open image or label file</source>
        <translation>打开图像或标签文件</translation>
    </message>
    <message>
        <location filename="../app.py" line="222"/>
        <source>Open Dir</source>
        <translation>打开目录</translation>
    </message>
    <message>
        <location filename="../app.py" line="229"/>
        <source>&amp;Next Image</source>
        <translation>下一幅(&amp;N)</translation>
    </message>
    <message>
        <location filename="../app.py" line="229"/>
        <source>Open next (hold Ctl+Shift to copy labels)</source>
        <translation>打开下一幅 (按Ctl+Shift拷贝标签)</translation>
    </message>
    <message>
        <location filename="../app.py" line="237"/>
        <source>&amp;Prev Image</source>
        <translation>上一幅(&amp;P)</translation>
    </message>
    <message>
        <location filename="../app.py" line="237"/>
        <source>Open prev (hold Ctl+Shift to copy labels)</source>
        <translation>打开上一幅 (按Ctl+Shift拷贝标签)</translation>
    </message>
    <message>
        <location filename="../app.py" line="245"/>
        <source>&amp;Save
</source>
        <translation>保存(&amp;S)</translation>
    </message>
    <message>
        <location filename="../app.py" line="245"/>
        <source>Save labels to file</source>
        <translation>保存标签到文件</translation>
    </message>
    <message>
        <location filename="../app.py" line="253"/>
        <source>&amp;Save As</source>
        <translation>另存为(&amp;S)</translation>
    </message>
    <message>
        <location filename="../app.py" line="253"/>
        <source>Save labels to a different file</source>
        <translation>保存标签到不同的文件</translation>
    </message>
    <message>
        <location filename="../app.py" line="262"/>
        <source>&amp;Delete File</source>
        <translation>删除(&amp;D)</translation>
    </message>
    <message>
        <location filename="../app.py" line="262"/>
        <source>Delete current label file</source>
        <translation>删除当前标签文件</translation>
    </message>
    <message>
        <location filename="../app.py" line="271"/>
        <source>&amp;Change Output Dir</source>
        <translation>更改输出路径(&amp;C)</translation>
    </message>
    <message>
        <location filename="../app.py" line="271"/>
        <source>Change where annotations are loaded/saved</source>
        <translation>更改载入、保存标注的路径</translation>
    </message>
    <message>
        <location filename="../app.py" line="279"/>
        <source>Save &amp;Automatically</source>
        <translation>自动保存(&amp;A)</translation>
    </message>
    <message>
        <location filename="../app.py" line="279"/>
        <source>Save automatically</source>
        <translation>自动保存</translation>
    </message>
    <message>
        <location filename="../app.py" line="290"/>
        <source>Save With Image Data</source>
        <translation>同时保存图像数据</translation>
    </message>
    <message>
        <location filename="../app.py" line="292"/>
        <source>Save image data in label file</source>
        <translation>将图像数据保存到标签文件中</translation>
    </message>
    <message>
        <location filename="../app.py" line="298"/>
        <source>&amp;Close</source>
        <translation>关闭(&amp;C)</translation>
    </message>
    <message>
        <location filename="../app.py" line="302"/>
        <source>Close current file</source>
        <translation>关闭当前文件</translation>
    </message>
    <message>
        <location filename="../app.py" line="305"/>
        <source>Keep Previous Annotation</source>
        <translation>保留最后的标注</translation>
    </message>
    <message>
        <location filename="../app.py" line="305"/>
        <source>Toggle &quot;keep pevious annotation&quot; mode</source>
        <translation>开关“保留最后的标注”模式</translation>
    </message>
    <message>
        <location filename="../app.py" line="315"/>
        <source>Create Polygons</source>
        <translation>创建多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="315"/>
        <source>Start drawing polygons</source>
        <translation>开始绘制多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="323"/>
        <source>Create Rectangle</source>
        <translation>创建矩形</translation>
    </message>
    <message>
        <location filename="../app.py" line="323"/>
        <source>Start drawing rectangles</source>
        <translation>开始绘制矩形</translation>
    </message>
    <message>
        <location filename="../app.py" line="331"/>
        <source>Create Circle</source>
        <translation>创建圆形</translation>
    </message>
    <message>
        <location filename="../app.py" line="331"/>
        <source>Start drawing circles</source>
        <translation>开始绘制圆形</translation>
    </message>
    <message>
        <location filename="../app.py" line="339"/>
        <source>Create Line</source>
        <translation>创建直线</translation>
    </message>
    <message>
        <location filename="../app.py" line="339"/>
        <source>Start drawing lines</source>
        <translation>开始创建直线</translation>
    </message>
    <message>
        <location filename="../app.py" line="347"/>
        <source>Create Point</source>
        <translation>创建控制点</translation>
    </message>
    <message>
        <location filename="../app.py" line="347"/>
        <source>Start drawing points</source>
        <translation>开始绘制控制点</translation>
    </message>
    <message>
        <location filename="../app.py" line="355"/>
        <source>Create LineStrip</source>
        <translation>创建折线</translation>
    </message>
    <message>
        <location filename="../app.py" line="355"/>
        <source>Start drawing linestrip. Ctrl+LeftClick ends creation.</source>
        <translation>开始绘制折线。Ctrl+单击左键结束绘制。</translation>
    </message>
    <message>
        <location filename="../app.py" line="363"/>
        <source>Create AI-Polygon</source>
        <translation>创建AI多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="363"/>
        <source>Start drawing ai_polygon. Ctrl+LeftClick ends creation.</source>
        <translation>开始绘制AI多边形。Ctrl+单击左键结束绘制。</translation>
    </message>
    <message>
        <location filename="../app.py" line="378"/>
        <source>Create AI-Mask</source>
        <translation>创建AI蒙版</translation>
    </message>
    <message>
        <location filename="../app.py" line="378"/>
        <source>Start drawing ai_mask. Ctrl+LeftClick ends creation.</source>
        <translation>开始绘制AI蒙版。Ctrl+单击左键结束绘制。</translation>
    </message>
    <message>
        <location filename="../app.py" line="393"/>
        <source>Edit Polygons</source>
        <translation>编辑多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="393"/>
        <source>Move and edit the selected polygons</source>
        <translation>移动、编辑选中的多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="402"/>
        <source>Delete Polygons</source>
        <translation>删除多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="402"/>
        <source>Delete the selected polygons</source>
        <translation>删除选中的多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="410"/>
        <source>Duplicate Polygons</source>
        <translation>复制多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="410"/>
        <source>Create a duplicate of the selected polygons</source>
        <translation>为选中的多边形创建副本</translation>
    </message>
    <message>
        <location filename="../app.py" line="418"/>
        <source>Copy Polygons</source>
        <translation>复制多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="418"/>
        <source>Copy selected polygons to clipboard</source>
        <translation>复制选中多边形到剪贴板</translation>
    </message>
    <message>
        <location filename="../app.py" line="426"/>
        <source>Paste Polygons</source>
        <translation>粘贴多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="426"/>
        <source>Paste copied polygons</source>
        <translation>粘贴已复制的多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="434"/>
        <source>Undo last point</source>
        <translation>撤销最后的控制点</translation>
    </message>
    <message>
        <location filename="../app.py" line="434"/>
        <source>Undo last drawn point</source>
        <translation>撤销最后一次绘制的控制点</translation>
    </message>
    <message>
        <location filename="../app.py" line="443"/>
        <source>Remove Selected Point</source>
        <translation>移除选中的控制点</translation>
    </message>
    <message>
        <location filename="../app.py" line="447"/>
        <source>Remove selected point from polygon</source>
        <translation>从多边形中移除选中的控制点</translation>
    </message>
    <message>
        <location filename="../app.py" line="451"/>
        <source>Undo
</source>
        <translation>撤销</translation>
    </message>
    <message>
        <location filename="../app.py" line="451"/>
        <source>Undo last add and edit of shape</source>
        <translation>撤销最近一次添加和编辑</translation>
    </message>
    <message>
        <location filename="../app.py" line="460"/>
        <source>&amp;Hide
Polygons</source>
        <translation>隐藏多边形(&amp;H)</translation>
    </message>
    <message>
        <location filename="../app.py" line="460"/>
        <source>Hide all polygons</source>
        <translation>隐藏所有多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="468"/>
        <source>&amp;Show
Polygons</source>
        <translation>显示多边形(&amp;S)</translation>
    </message>
    <message>
        <location filename="../app.py" line="468"/>
        <source>Show all polygons</source>
        <translation>显示所有多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="476"/>
        <source>&amp;Toggle
Polygons</source>
        <translation>开关多边形(&amp;S)</translation>
    </message>
    <message>
        <location filename="../app.py" line="476"/>
        <source>Toggle all polygons</source>
        <translation>开关所有多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="485"/>
        <source>&amp;Tutorial</source>
        <translation>教程[&amp;T]</translation>
    </message>
    <message>
        <location filename="../app.py" line="485"/>
        <source>Show tutorial page</source>
        <translation>显示教程网页</translation>
    </message>
    <message>
        <location filename="../app.py" line="494"/>
        <source>Zoom</source>
        <translation>缩放</translation>
    </message>
    <message>
        <location filename="../app.py" line="500"/>
        <source>Zoom in or out of the image. Also accessible with {} and {} from the canvas.</source>
        <translation>缩放图像。亦可从画布的{}和{}访问</translation>
    </message>
    <message>
        <location filename="../app.py" line="500"/>
        <source>Ctrl+Wheel</source>
        <translation>Ctrl+滚轮</translation>
    </message>
    <message>
        <location filename="../app.py" line="515"/>
        <source>Zoom &amp;In</source>
        <translation>放大(&amp;I)</translation>
    </message>
    <message>
        <location filename="../app.py" line="515"/>
        <source>Increase zoom level</source>
        <translation>增加缩放水平</translation>
    </message>
    <message>
        <location filename="../app.py" line="523"/>
        <source>&amp;Zoom Out</source>
        <translation>缩小(&amp;Z)</translation>
    </message>
    <message>
        <location filename="../app.py" line="523"/>
        <source>Decrease zoom level</source>
        <translation>减小缩放水平</translation>
    </message>
    <message>
        <location filename="../app.py" line="531"/>
        <source>&amp;Original size</source>
        <translation>原始大小(&amp;O)</translation>
    </message>
    <message>
        <location filename="../app.py" line="531"/>
        <source>Zoom to original size</source>
        <translation>缩放至原始大小</translation>
    </message>
    <message>
        <location filename="../app.py" line="539"/>
        <source>&amp;Keep Previous Scale</source>
        <translation>保留最后的比例(&amp;K)</translation>
    </message>
    <message>
        <location filename="../app.py" line="539"/>
        <source>Keep previous zoom scale</source>
        <translation>保留最后的缩放比例</translation>
    </message>
    <message>
        <location filename="../app.py" line="547"/>
        <source>&amp;Fit Window</source>
        <translation>适应窗口(&amp;F)</translation>
    </message>
    <message>
        <location filename="../app.py" line="547"/>
        <source>Zoom follows window size</source>
        <translation>跟随窗口大小缩放</translation>
    </message>
    <message>
        <location filename="../app.py" line="556"/>
        <source>Fit &amp;Width</source>
        <translation>适应宽度(&amp;W)</translation>
    </message>
    <message>
        <location filename="../app.py" line="556"/>
        <source>Zoom follows window width</source>
        <translation>跟随窗口宽度缩放</translation>
    </message>    
    <message>
        <location filename="../app.py" line="566"/>
        <source>&amp;Brightness Contrast</source>
        <translation>亮度 对比度(&amp;B)</translation>
    </message>
    <message>
        <location filename="../app.py" line="570"/>
        <source>Adjust brightness and contrast</source>
        <translation>调节亮度和对比度</translation>
    </message>
    <message>
        <location filename="../app.py" line="591"/>
        <source>&amp;Edit Label</source>
        <translation>编辑标签(&amp;E)</translation>
    </message>
    <message>
        <location filename="../app.py" line="591"/>
        <source>Modify the label of the selected polygon</source>
        <translation>修改选中多边形的标签</translation>
    </message>
    <message>
        <location filename="../app.py" line="600"/>
        <source>Fill Drawing Polygon</source>
        <translation>填充所绘多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="600"/>
        <source>Fill polygon while drawing</source>
        <translation>绘制时填充多边形</translation>
    </message>
    <message>
        <location filename="../app.py" line="712"/>
        <source>&amp;File</source>
        <translation>文件(&amp;F)</translation>
    </message>
    <message>
        <location filename="../app.py" line="712"/>
        <source>&amp;Edit</source>
        <translation>编辑(&amp;E)</translation>
    </message>
    <message>
        <location filename="../app.py" line="712"/>
        <source>&amp;View</source>
        <translation>视图(&amp;V)</translation>
    </message>
    <message>
        <location filename="../app.py" line="712"/>
        <source>&amp;Help</source>
        <translation>帮助(&amp;H)</translation>
    </message>
    <message>
        <location filename="../app.py" line="712"/>
        <source>Open &amp;Recent</source>
        <translation>最近打开(&amp;R)</translation>
    </message>
    <message>
        <location filename="../app.py" line="783"/>
        <source>AI Model</source>
        <translation>AI模型</translation>
    </message>
    <message>
        <location filename="../app.py" line="830"/>
        <source>%s started.</source>
        <translation>%s 启动完了</translation>
    </message>
    <message>
        <location filename="../app.py" line="1404"/>
        <source>Invalid label</source>
        <translation>无效的标签</translation>
    </message>
    <message>
        <location filename="../app.py" line="1404"/>
        <source>Invalid label &apos;{}&apos; with validation type &apos;{}&apos;</source>
        <translation>无效的标签&apos;{}&apos;，验证类型&apos;{}&apos;</translation>
    </message>
    <message>
        <location filename="../app.py" line="1343"/>
        <source>Error saving label data</source>
        <translation>保存标签发生错误</translation>
    </message>
    <message>
        <location filename="../app.py" line="1343"/>
        <source>&lt;b&gt;%s&lt;/b&gt;</source>
        <translation>&lt;b&gt;%s&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../app.py" line="1577"/>
        <source>Error opening file</source>
        <translation>打开文件发生错误</translation>
    </message>
    <message>
        <location filename="../app.py" line="1534"/>
        <source>No such file: &lt;b&gt;%s&lt;/b&gt;</source>
        <translation>文件不存在: &lt;b&gt;%s&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../app.py" line="1540"/>
        <source>Loading %s...</source>
        <translation>正在载入 %s...</translation>
    </message>
    <message>
        <location filename="../app.py" line="1549"/>
        <source>&lt;p&gt;&lt;b&gt;%s&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Make sure &lt;i&gt;%s&lt;/i&gt; is a valid label file.</source>
        <translation>&lt;p&gt;&lt;b&gt;%s&lt;/b&gt;&lt;/p&gt;&lt;p&gt;请确认&lt;i&gt;%s&lt;/i&gt;是一个合法的标签文件。</translation>
    </message>
    <message>
        <location filename="../app.py" line="1584"/>
        <source>Error reading %s</source>
        <translation>打开文件发生错误 %s</translation>
    </message>
    <message>
        <location filename="../app.py" line="1577"/>
        <source>&lt;p&gt;Make sure &lt;i&gt;{0}&lt;/i&gt; is a valid image file.&lt;br/&gt;Supported image formats: {1}&lt;/p&gt;</source>
        <translation>lt;p&gt;请确认&lt;i&gt;{0}&lt;/i&gt;是一个合法的图像文件。&lt;br/&gt;支持的格式包括: {1}&lt;/p&gt;</translation>
    </message>
    <message>
        <location filename="../app.py" line="1644"/>
        <source>Loaded %s</source>
        <translation>已加载 %s</translation>
    </message>
    <message>
        <location filename="../app.py" line="1786"/>
        <source>Image &amp; Label files (%s)</source>
        <translation>图像和标签文件(%s)</translation>
    </message>
    <message>
        <location filename="../app.py" line="1792"/>
        <source>%s - Choose Image or Label file</source>
        <translation>%s - 选择图像或标签文件</translation>
    </message>
    <message>
        <location filename="../app.py" line="1809"/>
        <source>%s - Save/Load Annotations in Directory</source>
        <translation>%s - 保存和加载批注的路径</translation>
    </message>
    <message>
        <location filename="../app.py" line="1823"/>
        <source>%s . Annotations will be saved/loaded in %s</source>
        <translation>%s . 批注会被加载和保存在 %s</translation>
    </message>
    <message>
        <location filename="../app.py" line="1853"/>
        <source>%s - Choose File</source>
        <translation>%s - 选择文件</translation>
    </message>
    <message>
        <location filename="../app.py" line="1872"/>
        <source>Label files (*%s)</source>
        <translation>标签文件(*%s)</translation>
    </message>
    <message>
        <location filename="../app.py" line="1872"/>
        <source>Choose File</source>
        <translation>选择文件</translation>
    </message>
    <message>
        <location filename="../app.py" line="1906"/>
        <source>You are about to permanently delete this label file, proceed anyway?</source>
        <translation>即将永久性删除此标签文件。还要继续吗?</translation>
    </message>
    <message>
        <location filename="../app.py" line="1987"/>
        <source>Attention</source>
        <translation>注意</translation>
    </message>
    <message>
        <location filename="../app.py" line="1944"/>
        <source>Save annotations to &quot;{}&quot; before closing?</source>
        <translation>关闭前保存批注到&quot;{}&quot;吗?</translation>
    </message>
    <message>
        <location filename="../app.py" line="1945"/>
        <source>Save annotations?</source>
        <translation>保存批注吗?</translation>
    </message>
    <message>
        <location filename="../app.py" line="1984"/>
        <source>You are about to permanently delete {} polygons, proceed anyway?</source>
        <translation>即将永久性删除多边形{}。还要继续吗?</translation>
    </message>
    <message>
        <location filename="../app.py" line="2017"/>
        <source>%s - Open Directory</source>
        <translation>%s - 打开目录</translation>
    </message>
</context>
</TS>
