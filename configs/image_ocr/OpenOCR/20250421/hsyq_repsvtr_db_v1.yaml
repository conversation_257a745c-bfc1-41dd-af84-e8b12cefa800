Global:
  device: gpu
  model_type: det
  distributed: true
  use_tensorboard: true
  log_smooth_window: 20
  image_res_width: &image_res_width 960
  image_res_height: &image_res_height 1280
  image_infer_res: &image_infer_res 1280

  checkpoints: null
  pretrained_model: null
  output_dir: /aipdf-mlp/xelawk/training_outputs/20250421/release/hsyq_repsvtr_db_v1

  print_batch_step: 100
  save_epoch_step: [0, 1]
  epoch_num: &epoch_num 500          # 训练轮数，用于动态调整数据增强参数
  eval_epoch_step: [0, 1]            # 优先级最高，表示每n个epoch开启验证，不用时请注释掉
  eval_pretrained_first: true        # 若有预训练模型时，训练次是否开启评估
  cal_metric_during_train: false     # 训练时可以评估指标

  infer_img: null
  save_res_path: null
  save_inference_dir: null

Architecture:
  algorithm: DB_mobile
  Backbone:
    name: RepSVTR_det
  Neck:
    name: RSEFPN
    out_channels: 96
    shortcut: True
  Head:
    name: DBHead
    k: 50

Loss:
  name: DBLoss
  balance_loss: true
  main_loss_type: DiceLoss
  alpha: 5
  beta: 10
  ohem_ratio: 3

Optimizer:
  name: Adam
  lr: 0.001
  weight_decay: 5.0e-05
  filter_bias_and_bn: False

LRScheduler:
  name: CosineAnnealingLR
  warmup_epoch: 2

PostProcess:
  name: DBPostProcess
  thresh: 0.3
  box_thresh: 0.6
  unclip_ratio: 1.5
  max_candidates: 1000

Metric:
  name: ParallelDetMetric
  main_indicator: hmean
  workers: 24

Train:
  dataset:
    name: SimpleDataSet
    data_dir: /aipdf-mlp/xelawk/train_dataset_release_v4/auto_labeled_adv_icdar_deg_v202503272141/det
    label_file_list:
      - /aipdf-mlp/xelawk/train_dataset_release_v4/auto_labeled_adv_icdar_deg_v202503272141/det/det_gt_train.txt
    ratio_list: [1.0]
    transforms:
      # 图像解码
      - DecodeImage:
          img_mode: BGR
          channel_first: false

      # 检测标签编码，将标注信息转换为模型训练所需的格式
      - DetLabelEncode: null

      # 随机复制粘贴文本
      - op_prob: 0.08
        CopyPaste:
          prob_color_transform: 0.

      # 印章合成（原数据中已经有部分印章增加，这里是额外添加，目的是提升比例，对印章颜色做更深的调整）
      - op_prob: 0.08
        SealPaste:
          seal_resources_dir: /aipdf-mlp/xelawk/datasets/private/seal  # 印章资源目录
          seal_paste_prob: 1.0           # 印章合成概率（触发后的概率）
          max_seals_per_image: 1         # 每张图片最多合成的印章数量
          allow_overlap: true            # 是否允许印章重叠
          seal_area_ratio: [0.02, 0.04]  # 印章面积占图片面积的1%~4%

      # 图像水平翻转
      - op_prob: 0.2
        IaaAugment:
          augmenter_args:
          - type: Fliplr
            args:
              p: 1.0

      # 任意角度旋转或者左右90度旋转
      - op_prob: 0.3
        IaaAugment:
          augmenter_args:
          - type: AffineOrRotate90
            args:
              p: 1.0
              affine_prob: 0.3
              clockwise: 'random'
              rotate_range: [-10, 10]
              interpolation: INTER_AREA

      # 随机缩放
      - op_prob: 0.5
        IaaAugment:
          augmenter_args:
          - type: Resize
            args:
              p: 1.0
              size: [1.3, 3.0]
              interpolation: INTER_AREA

      # 5. 智能随机裁剪，确保包含文本区域
      - AwesomeRandomCropData:
          size: [*image_res_width, *image_res_height]
          max_tries: 50
          p_no_crop: 0.3
          keep_ratio: true
          interpolation: INTER_AREA
          min_crop_side_ratio: 0.50

      # 6. 生成文本边界图，用于学习文本边界
      - MakeBorderMapAdvanced:
          thresh_min: 0.3
          thresh_max: 0.7
          min_shrink_ratio: 0.42
          max_shrink_ratio: 0.60
          total_epoch: *epoch_num

      # 7. 生成文本区域图，用于学习文本位置
      - MakeShrinkMapAdvanced:
          min_text_size: 2
          min_shrink_ratio: 0.42
          max_shrink_ratio: 0.60
          total_epoch: *epoch_num

      # 8. 图像标准化处理
      - NormalizeImage:
          scale: 1./255.
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          order: hwc

      # 9. 转换为CHW格式，用于模型输入
      - ToCHWImage: null

      # 10. 保留必要的键，过滤不必要的数据
      - KeepKeys:
          keep_keys:
          - image
          - threshold_map
          - threshold_mask
          - shrink_map
          - shrink_mask

    log_get_item_error: false  # 是否打印DataLoader获取数据时的错误信息

  loader:
    shuffle: true
    drop_last: false
    batch_size_per_card: 16
    num_workers: 32

Eval:
  dataset:
    name: SimpleDataSet
    data_dir: /aipdf-mlp/xelawk/train_dataset_release_v4/auto_labeled_adv_icdar_deg_v202503272141/det
    label_file_list:
      - /aipdf-mlp/xelawk/train_dataset_release_v4/auto_labeled_adv_icdar_deg_v202503272141/det/det_gt_eval.txt
    transforms:
      # 1. 图像解码
      - DecodeImage:
          img_mode: BGR
          channel_first: false

      # 2. 检测标签编码，将标注信息转换为模型训练所需的格式
      - DetLabelEncode: null

      # 3. 测试专用的图像缩放，限制最长边
      - DetResizeForTest:
          limit_type: max
          limit_side_len: *image_infer_res
          interpolation: INTER_AREA

      # 4. 图像标准化处理
      - NormalizeImage:
          scale: 1./255.
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          order: hwc

      # 5. 转换为CHW格式
      - ToCHWImage: null

      - KeepKeys:
          keep_keys:
            - image
            - shape
            - polys
            - ignore_tags

  loader:
    shuffle: false
    drop_last: false
    batch_size_per_card: 1
    num_workers: 8

Test:
  dataset:
    name: SimpleDataSet
    data_dir: /aipdf-mlp/xelawk/train_dataset_release_v4/auto_labeled_adv_icdar_deg_v202503272141/det
    label_file_list:
      - /aipdf-mlp/xelawk/train_dataset_release_v4/auto_labeled_adv_icdar_deg_v202503272141/det/det_gt_test.txt
    transforms:
      # 1. 图像解码
      - DecodeImage:
          img_mode: BGR
          channel_first: false

      # 2. 检测标签编码，将标注信息转换为模型训练所需的格式
      - DetLabelEncode: null

      # 3. 测试专用的图像缩放，限制最长边
      - DetResizeForTest:
          limit_type: max
          limit_side_len: *image_infer_res
          interpolation: INTER_AREA

      # 4. 图像标准化处理
      - NormalizeImage:
          scale: 1./255.
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          order: hwc

      # 5. 转换为CHW格式
      - ToCHWImage: null

      - KeepKeys:
          keep_keys:
            - image
            - shape
            - polys
            - ignore_tags

  loader:
    shuffle: false
    drop_last: false
    batch_size_per_card: 1
    num_workers: 8

profiler_options: null
