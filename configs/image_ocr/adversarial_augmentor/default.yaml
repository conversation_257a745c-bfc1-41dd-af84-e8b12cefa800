pipelines:
  # 水印对抗干扰样本合成配置
  - strategy: adv_watermark
    probability: 0.8193212706
    params:
      # 全局生效，一般意义上的，针对某些特殊情形的水印可能部分不会用到
      global_cfg:
        prob_text_wm: 0.3                         # 文字水印的概率
        prob_grey_wm: 0.6                         # 灰度水印的概率
        prob_tile_wm: 0.7                         # 水印平铺的概率
        prob_rotate_wm: 1.0                       # 角度旋转的概率
        is_position_fixed: 1.0                    # 随机水印中，固定位水印的占比
        is_force_center_fixed: True               # 当固定位水印生效时，强制水印居中
        prob_grid_line_wm: 0.1                    # 网格线水印的概率
        text_wm_len_range: [3, 12]                # 文字水印长度范围
        text_wm_lines_range: [1, 4]               # 文字水印行数范围
        wm_rotate_range: [-45, 45]                # logo旋转范围
        text_wm_rotate_range: [45, 45]            # 文字水印旋转范围
        text_font_size_range: [30, 100]           # 文字水印文字大小范围
        opacity_range: [0.10, 0.20, 0.01]         # 水印的透明度范围
        prob_small_logo: 0.7                      # 使用小LOGO的概率
        small_logo_percent: [0.01, 0.02, 0.001]   # 小LOGO类水印占比范围
        large_logo_percent: [0.02, 0.08, 0.001]   # 大LOGO类水印占比范围
        tile_margin_range: [256, 512]             # 平铺水印间的间距范围
        grid_spacing: [150, 350]                  # 网格线的范围
        grid_line_width: [1, 3]                   # 网格线的粗细范围
        pure_doc: True                            # 是否纯文档，旋转是混合策略(logo,text不同概率和范围)
        prob_text_pure_zh: 0.5                    # 纯中文的概率
        prob_text_pure_multi_lines: 0.5           # 多行纯中文的概率
        prob_text_pure_no_opacity: 0.00           # 文字水印没有透明度的概率
        watermark_template_opacity: [0.10, 0.20]  # 水印模板的透明度范围
        logo_with_text_percent: [0.01, 0.012]     # 文字LOGO的占比范围

        # 文字水印在文本上下左右等9个块中的概率
        prob_text_center_around: [0.025, 0.025, 0.025, 0.025, 0.5, 0.1, 0.1, 0.1, 0.1]

      # 纯文字型渲染资源
      text_based_cfg:
        ttf_dir: /aipdf-mlp/xelawk/datasets/private/source_watermarks/fonts

      # 纯logo型渲染资源
      logo_based_cfg:
        logo_dir: /aipdf-mlp/xelawk/datasets/private/source_watermarks/web_logo_dataset_for_doc
        assign:
          custom_common_logo:
            ratio: 0.0467
            allow_types: ["random", "tile"]
          custom_common_logo_binary:
            ratio: 0.0256
            allow_types: ["random", "tile"]
          custom_very_common_logo:
            ratio: 0.0357
            allow_types: ["random", "tile"]
          custom_very_common_logo_binary:
            ratio: 0.0215
            allow_types: ["random", "tile"]
          custom_watermark_templates:
            ratio: 0.1138
            allow_types: ["is_watermark_template"]
          pattern_based_common:
            ratio: 0.0808
            allow_types: ["random", "tile"]
          pattern_based_others:
            ratio: 0.0808
            allow_types: ["random", "tile"]
          pattern_text_based_common:
            ratio: 0.1258
            allow_types: ["random", "tile"]
          pattern_text_based_others:
            ratio: 0.1258
            allow_types: ["random", "tile"]
          text_based_common:
            ratio: 0.0866
            allow_types: [ "random", "tile" ]
          text_based_others:
            ratio: 0.1258
            allow_types: [ "random", "tile" ]
          very_common_logo:
            ratio: 0.0820
            allow_types: ["random", "tile"]
          very_common_logo_binary:
            ratio: 0.0491
            allow_types: ["random", "tile"]

  # 印章对抗干扰样本合成配置
  - strategy: adv_seal
    probability: 0.8193212706
    params:
      # 印章资源目录
      seal_dir: /aipdf-mlp/xelawk/datasets/private/seal
      # TODO: seal_cfg 支持参数配置化

  # 二维码对抗干扰样本合成配置
  - strategy: adv_qrcode
    probability: 0.8193212706
    params:
      # 二维码资源目录
      qrcode_dir: /aipdf-mlp/xelawk/datasets/private/qrcode
      # 二维码合成参数
      # TODO: qrcode_cfg 支持参数配置化
