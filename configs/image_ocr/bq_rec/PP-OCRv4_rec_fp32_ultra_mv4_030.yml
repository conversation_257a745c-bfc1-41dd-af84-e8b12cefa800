Global:
  debug: false
  use_gpu: true
  epoch_num: 100
  log_smooth_window: 20
  print_batch_step: 10
  save_model_dir: /aipdf-mlp/jiacheng/exp/pp_ocr_rec/bq_mv4_medium_v3_030_2
  save_epoch_step: 2
  eval_batch_step: [0, 5000]
  cal_metric_during_train: true
  pretrained_model: /aipdf-mlp/jiacheng/exp/pp_ocr_rec/bq_mv4_medium_v3_030/best_accuracy
  checkpoints:
  save_inference_dir:
  use_visualdl: false
  infer_img: /aipdf-mlp/ouhanqi/data/benchmark/rec
  infer_list: /aipdf-mlp/ouhanqi/data/benchmark/rec/rec_gt_bench.txt
  character_dict_path: /aipdf-mlp/jiacheng/code/text_render/example_data/char/bq_ocr_keys_v2.txt
  max_text_length: &max_text_length 120
  infer_mode: false
  use_space_char: true
  distributed: true
  save_res_path: /aipdf-mlp/jiacheng/exp/pp_ocr_rec/infer_res_temp/rec_gt_bench_bq_fp32_ultra_mv4m_7M.txt
  d2s_train_image_shape: [3, 48, 320]


Optimizer:
  name: Adam
  beta1: 0.9
  beta2: 0.999
  lr:
    name: Cosine
    learning_rate: 0.0005
    warmup_epoch: 1
  regularizer:
    name: L2
    factor: 3.0e-05


Architecture:
  model_type: rec
  algorithm: SVTR_LCNet
  Transform:
  Backbone:
    name: MobileNetV4
    scale: 0.3
    model_name: conv_medium
  Head:
    name: MultiHead
    head_list:
      - CTCHead:
          Neck:
            name: svtr
            dims: 120
            depth: 2
            hidden_dims: 120
            kernel_size: [1, 3]
            use_guide: True
          Head:
            fc_decay: 0.00001
      - NRTRHead:
          nrtr_dim: 384
          max_text_length: *max_text_length

Loss:
  name: MultiLoss
  loss_config_list:
    - CTCLoss:
    - NRTRLoss:

PostProcess:
  name: CTCLabelDecode

Metric:
  name: RecMetric
  main_indicator: acc

Train:
  dataset:
    name: MultiScaleDataSet
    ds_width: false
    data_dir: /aipdf-mlp/shared/ocr_rec_dataset
    ext_op_transform_idx: 1
    label_file_list:
    - /aipdf-mlp/shared/ocr_rec_dataset/synth_3M_train.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/pdf_250310_train_3M.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/math_formulas_30K_train.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/chemical_formulas_20K_train.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/chn_ocr_scene_train.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/synth_300K_chn_mini_train.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/synth_200K_en_mini_train.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/synth_100K_chn_mini_plus_train.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/synth_150K_single_chn_train.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/synth_150K_single_signal_n_train.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/synth_50K_single_signal_s_train.txt
    transforms:
    - DecodeImage:
        img_mode: BGR
        channel_first: false
    - RecConAug:
        prob: 0.5
        ext_data_num: 2
        image_shape: [ 48, 320, 3 ]
        max_text_length: *max_text_length
    - RecAug:
        tia_prob: 0.0
        degrade_prob: 0.8
        dilate_prob: 0.4
        italic_prob: 0.5
        crop_prob: 0.4
        reverse_prob: 0.4
        noise_prob: 0.4
        jitter_prob: 0.0
        blur_prob: 0.0
        hsv_aug_prob: 0.4
    - MultiLabelEncode:
        gtc_encode: NRTRLabelEncode
    - KeepKeys:
        keep_keys:
        - image
        - label_ctc
        - label_gtc
        - length
        - valid_ratio
  sampler:
    name: MultiScaleSampler
    scales: [[960, 32], [640, 32], [320, 32], [320, 48], [320, 64], [320, 128]]
    first_bs: &bs 60 # 160
    fix_bs: false
    divided_factor: [8, 16] # w, h
    is_training: True
  loader:
    shuffle: true
    batch_size_per_card: *bs
    drop_last: true
    num_workers: 8

Eval:
  dataset:
    name: SimpleDataSet
    data_dir: /aipdf-mlp/shared/ocr_rec_dataset
    label_file_list:
#    - /aipdf-mlp/shared/ocr_rec_dataset/synth_3M_val.txt
#    - /aipdf-mlp/shared/ocr_rec_dataset/pdf_250310_eval.txt
#    - /aipdf-mlp/shared/ocr_rec_dataset/math_formulas_30K_val.txt
#    - /aipdf-mlp/shared/ocr_rec_dataset/chemical_formulas_20K_val.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/pdf_degraded_2503_eval.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/pdf_degraded_2503_test.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/benchmark_rec.txt
    transforms:
    - DecodeImage:
        img_mode: BGR
        channel_first: false
    - MultiLabelEncode:
        gtc_encode: NRTRLabelEncode
    - RecResizeImg:
        image_shape: [3, 48, 320]
    - KeepKeys:
        keep_keys:
        - image
        - label_ctc
        - label_gtc
        - length
        - valid_ratio
  loader:
    shuffle: false
    drop_last: false
    batch_size_per_card: 128
    num_workers: 4

Test:
  dataset:
    name: SimpleDataSet
    data_dir: /aipdf-mlp/shared/ocr_rec_dataset
    label_file_list:
    - /aipdf-mlp/shared/ocr_rec_dataset/synth_3M_test.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/pdf_250310_test.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/math_formulas_30K_test.txt
    - /aipdf-mlp/shared/ocr_rec_dataset/chemical_formulas_20K_test.txt
    transforms:
    - DecodeImage:
        img_mode: BGR
        channel_first: false
    - MultiLabelEncode:
        gtc_encode: NRTRLabelEncode
    - RecResizeImg:
        image_shape: [3, 48, 320]
    - KeepKeys:
        keep_keys:
        - image
        - label_ctc
        - label_gtc
        - length
        - valid_ratio
  loader:
    shuffle: false
    drop_last: false
    batch_size_per_card: 128
    num_workers: 4