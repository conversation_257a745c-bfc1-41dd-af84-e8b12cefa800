# EfficientViT-TableBorderNet 表格边框分割训练配置文件
# Time: 2025-07-28
# Author: <EMAIL>
# Description: 基于OmegaConf的层级配置文件，专门用于TableBorderNet分割任务

# ============================================================================
# 基础配置
# ============================================================================
basic:
  # 调试模式
  debug: false
  # 随机种子
  seed: 42
  # 输出目录
  output_dir: /aipdf-mlp/xelawk/training_outputs/tsr_training/efficientvit-tablebordernet/release
  # 只执行可视化功能，不进行训练和验证
  only_vis_log: false

# ============================================================================
# 数据配置
# ============================================================================
data:
  # 数据路径配置 - 支持单个路径或多个路径列表
  paths:
    # 训练数据目录 - 可以是单个路径字符串或路径列表
    train_data_dir:
      # 有线表
      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/OurPrivateData/train
#      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/TabRecSetCh/train
#      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/TabRecSetEng/train
#      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/TALOCRTable/train
#      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/WTW/train

      # 无线表
      - /aipdf-mlp/shared/tsr_training/release/wireless_table/train/OurPrivateData/train
#      - [/aipdf-mlp/shared/tsr_training/release/wireless_table/train/synfintabs/train, 20000]  # 模式太单一，选2万，适当平衡
#      - /aipdf-mlp/shared/tsr_training/release/wireless_table/train/TabRecSetCh/train
#      - /aipdf-mlp/shared/tsr_training/release/wireless_table/train/TabRecSetCh2/train
#      - /aipdf-mlp/shared/tsr_training/release/wireless_table/train/TabRecSetEng/train
#      - /aipdf-mlp/shared/tsr_training/release/wireless_table/train/TabRecSetEng2/train
#      - /aipdf-mlp/shared/tsr_training/release/wireless_table/train/TALOCRTable/train
#      - /aipdf-mlp/shared/tsr_training/release/wireless_table/train/WTW/train

    # 验证数据目录 - 可以是单个路径字符串或路径列表
    val_data_dir:
      # 有线表
      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/OurPrivateData/val
#      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/TabRecSetCh/val
#      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/TabRecSetEng/val
#      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/TALOCRTable/val
#      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/WTW/val

      # 无线表
      - /aipdf-mlp/shared/tsr_training/release/wireless_table/train/OurPrivateData/val
#      - [/aipdf-mlp/shared/tsr_training/release/wireless_table/train/synfintabs/val, 150]  # 模式太单一，选150，适当平衡
#      - /aipdf-mlp/shared/tsr_training/release/wireless_table/train/TabRecSetCh/val
#      - /aipdf-mlp/shared/tsr_training/release/wireless_table/train/TabRecSetCh2/val
#      - /aipdf-mlp/shared/tsr_training/release/wireless_table/train/TabRecSetEng/val
#      - /aipdf-mlp/shared/tsr_training/release/wireless_table/train/TabRecSetEng2/val
#      - /aipdf-mlp/shared/tsr_training/release/wireless_table/train/TALOCRTable/val
#      - /aipdf-mlp/shared/tsr_training/release/wireless_table/train/WTW/val

  # 数据处理配置
  processing:
    # 最大样本数量，用于调试，null表示使用全部数据
    max_samples: 1000
    # 输出图像尺寸 [height, width] - 分割任务使用更高分辨率
    output_size: [1024, 1024]

  # 变换流水线配置 (基于流水线的变换系统)
  pipelines:
    # 训练流水线 (适配TableBorderNet分割任务)
    train:
      # 1. 加载图像
      - type: LoadImageFromFile
        to_float32: true
        color_type: color

      # 2. 加载标注 (包含边框mask)
      - type: LoadAnnotations
        with_bbox: true
        with_mask: false
        with_seg: false
        with_border_mask: true  # 加载表格边框mask

      # 3. 光度变换 (不影响mask)
      - type: PhotoMetricDistortion
        brightness_delta: 20
        contrast_range: [0.6, 1.4]
        saturation_range: [0.8, 1.2]  # 有风险，代码已经屏蔽
        hue_delta: 5                  # 有风险，代码已经屏蔽

      # 4. 透视变换增强 V2 (支持图像边界扩展 + 严格验证，mask同步变换)
      - type: PerspectiveTransformV2
        perspective_range: [ 0.0, 0.20 ]
        apply_prob: 0.5
        fill_mode: dominant_color
        strict_validation: true  # 启用严格验证，防止截断问题

      # 5. 随机旋转增强 V2 (支持图像边界扩展 + 严格验证，mask同步变换)
      - type: RandomRotationV2
        angle_range: [ -45.0, 45.0 ]
        apply_prob: 0.5
        center_mode: adaptive
        fill_mode: dominant_color
        fill_value: 0
        strict_validation: true  # 启用严格验证，防止截断问题

      # 6. 随机裁剪 (新的简洁高效实现，确保bbox完整保留，mask同步裁剪)
      - type: RandomCrop
        min_crop_ratio: 0.3
        max_crop_ratio: 1.2
        margin_ratio: 0.02
        apply_prob: 0.5

      # 7. 缩放到统一尺寸 (mask同步缩放)
      - type: Resize
        img_scale: [1024, 1024]
        keep_ratio: false
        random_keep_ratio: true  # 随机选择保持宽高比模式，增加数据多样性
        interpolation: bilinear

      # 8. 随机翻转 (mask同步翻转)
      - type: RandomFlip
        flip_ratio: 0.5
        direction: horizontal

      # 9. 归一化 (ImageNet标准，仅对图像)
      - type: Normalize
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]
        to_rgb: true

      # 10. 转换为张量 (图像和mask都转换)
      - type: ToTensor

    # 验证流水线
    val:
      # 1. 加载图像
      - type: LoadImageFromFile
        to_float32: true
        color_type: color

      # 2. 加载标注 (包含边框mask)
      - type: LoadAnnotations
        with_bbox: true
        with_mask: false
        with_seg: false
        with_border_mask: true  # 加载表格边框mask

      # 3. 缩放到目标尺寸 (mask同步缩放)
      - type: Resize
        img_scale: [1024, 1024]
        keep_ratio: true
        random_keep_ratio: false  # 验证时不使用随机模式，保持一致性
        interpolation: bilinear

      # 4. 归一化 (ImageNet标准，仅对图像)
      - type: Normalize
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]
        to_rgb: true

      # 5. 转换为张量 (图像和mask都转换)
      - type: ToTensor

    # 测试流水线
    test:
      # 1. 加载图像
      - type: LoadImageFromFile
        to_float32: true
        color_type: color

      # 2. 加载标注 (包含边框mask)
      - type: LoadAnnotations
        with_bbox: true
        with_mask: false
        with_seg: false
        with_border_mask: true  # 加载表格边框mask

      # 3. 缩放到目标尺寸 (mask同步缩放)
      - type: Resize
        img_scale: [1024, 1024]
        keep_ratio: true
        random_keep_ratio: false  # 验证时不使用随机模式，保持一致性
        interpolation: bilinear

      # 4. 归一化 (ImageNet标准，仅对图像)
      - type: Normalize
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]
        to_rgb: true

      # 5. 转换为张量 (图像和mask都转换)
      - type: ToTensor

  # 数据加载配置
  loader:
    # 数据加载器工作进程数
    num_workers: 16
    # 是否使用pin_memory
    pin_memory: true

# ============================================================================
# 模型配置 (TableBorderNet分割模型)
# ============================================================================
model:
  # 模型类型
  type: efficientvit_seg_l2
  # 分割类别数量 (双通道：0=实线，1=虚线)
  cls_num: 2
  # 预训练权重路径
  pretrained: null
  # 是否冻结骨干网络
  freeze_backbone: false

# ============================================================================
# 损失函数配置 (参考BiRefNet的损失函数设计策略)
# ============================================================================
loss:
  # 损失权重配置 (参考BiRefNet的lambdas_pix_last配置)
  weights:
    cross_entropy: 1.0      # 基础像素级分类损失
    dice: 1.0               # 区域重叠损失，处理类别不平衡
    focal: 1.0              # 困难样本关注损失
    structure: 1.0          # 结构损失，增强边界学习 (新增)
    iou: 0.0                # IoU损失，直接优化分割质量 (新增)

  # 交叉熵损失配置
  cross_entropy:
    ignore_index: -100

  # Dice损失配置
  dice:
    eps: 1e-6

  # Focal损失配置
  focal:
    alpha: 1.0
    gamma: 2.0

  # 结构损失配置 (参考BiRefNet的StructureLoss)
  structure:
    kernel_size: 31         # 平均池化核大小，用于计算边界权重
    weight_factor: 5.0      # 边界权重增强因子

  # IoU损失配置 (参考BiRefNet的IoULoss)
  iou:
    eps: 1e-6               # 数值稳定性参数

# ============================================================================
# 训练配置
# ============================================================================
training:
  # 基础训练参数
  epochs: 200
  batch_size: 16

  # 优化器配置
  optimizer:
    # 优化器类型 (SGD/Adam/AdamW/AdamW_8Bit)
    type: "SGD"
    learning_rate: 0.000125

    # SGD特定参数
    sgd:
      momentum: 0.9
      weight_decay: 0.0001

    # Adam系列的特定参数
    adamx:
      beta1: 0.9
      beta2: 0.999
      epsilon: 1e-08
      weight_decay: 0.01

  # 学习率调度器配置
  scheduler:
    # 调度器类型 (constant/constant_with_warmup/cosine/cosine_with_restarts/linear)
    type: "constant_with_warmup"
    # 余弦退火参数
    cosine:
      num_cycles: 1
    # 预热配置
    warmup:
      steps: 500
    # 其他参数
    power: 1.0

  # 梯度配置
  gradient:
    # 是否使用梯度裁剪
    clip_norm: false
    # 梯度裁剪阈值
    clip_value: 1.0
    # 梯度累积步数
    accumulation_steps: 1

# ============================================================================
# EMA配置
# ============================================================================
ema:
  # 是否启用EMA
  enabled: true
  # EMA衰减率
  decay: 0.9999
  # EMA开始步数
  start_step: 0
  # EMA更新周期
  update_period: 1

# ============================================================================
# 检查点和验证配置
# ============================================================================
checkpoint:
  # 保存配置
  save:
    # 保存检查点的步数间隔
    steps: 2000
    # 每N个epoch保存一次模型
    every_n_epoch: 1
    # 保留的检查点数量
    keep_num: 100

  # 恢复配置
  resume:
    # 从检查点恢复训练的路径
    from_checkpoint: null

  # 验证配置
  validation:
    # 验证时使用的批次数量
    num_batches: None

# ============================================================================
# 分布式训练配置
# ============================================================================
distributed:
  # 混合精度训练 (no/fp16/bf16)
  mixed_precision: "no"
  # 日志记录工具
  report_to: "tensorboard"
  # 跟踪器项目名称
  tracker_project_name: efficientvit-tablebordernet

# ============================================================================
# 可视化配置
# ============================================================================
visualization:
  # 是否启用可视化功能
  enabled: true
  # 可视化样本图片路径（用于纯图片可视化，不依赖验证集标注）
  sample_images_dir: "assets/vis4tsr"
  # 每次可视化的样本数量
  max_samples: 10
  # 可视化结果保存路径（null时默认为basic.output_dir/visualization_results）
  output_dir: null
  # 可视化频率：每N次验证进行一次可视化（1表示每次验证都可视化）
  frequency: 1

  # 样式配置
  style:
    # 实线颜色 [R, G, B] - 对应通道0
    solid_line_color: [255, 0, 0]
    # 虚线颜色 [R, G, B] - 对应通道1
    dashed_line_color: [0, 255, 0]
    # 透明度
    transparency: 0.6
    # 线条粗细
    line_thickness: 2

  # 分割配置
  segmentation:
    # 二值化阈值
    threshold: 0.5
    # 是否显示置信度
    show_confidence: true

# ============================================================================
# 调试可视化配置
# ============================================================================
debug_visualization:
  # 是否启用干运行模式（只加载数据并可视化，不训练模型）
  dry_run: false
  # 干运行模式下可视化的批次数量
  dry_run_batches: 2
  # 干运行模式下可视化结果保存路径（null时默认为basic.output_dir/dry_run_results）
  dry_run_output_dir: null

  # 样式配置
  style:
    # 实线颜色 [R, G, B] - 对应通道0
    solid_line_color: [255, 0, 0]
    # 虚线颜色 [R, G, B] - 对应通道1
    dashed_line_color: [0, 255, 0]
    # 背景颜色 [R, G, B]
    background_color: [128, 128, 128]
    # 线条粗细
    line_thickness: 2
    # 透明度
    transparency: 0.6

  # 分割配置
  segmentation:
    # 是否显示各个通道
    show_individual_channels: true
    # 是否显示组合mask
    show_combined_mask: true