# Cycle-CenterNet 表格结构识别训练配置文件 (ModelScope 版本)
# Time: 2025-07-13
# Author: <EMAIL>
# Description: 基于OmegaConf的层级配置文件，适配ModelScope版本的双通道热力图和推理流程

# ============================================================================
# 基础配置
# ============================================================================
basic:
  # 调试模式
  debug: false
  # 随机种子
  seed: 42
  # 输出目录 (ModelScope 版本)
  output_dir: /aipdf-mlp/xelawk/training_outputs/tsr_training/cycle-centernet-ms/debug
  # 只执行可视化功能，不进行训练和验证
  only_vis_log: false

# ============================================================================
# 数据配置
# ============================================================================
data:
  # 数据路径配置 - 支持单个路径或多个路径列表
  paths:
    # 训练数据目录 - 可以是单个路径字符串或路径列表
    train_data_dir:
      # 有线表
      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/OurPrivateData/train
      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/TabRecSetCh/train
      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/TabRecSetEng/train
      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/TALOCRTable/train
      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/WTW/train

    # 验证数据目录 - 可以是单个路径字符串或路径列表
    val_data_dir:
      # 有线表
      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/OurPrivateData/val
      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/TabRecSetCh/val
      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/TabRecSetEng/val
      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/TALOCRTable/val
      - /aipdf-mlp/shared/tsr_training/release/wired_table/train/WTW/val

  # 数据处理配置
  processing:
    # 最大样本数量，用于调试，null表示使用全部数据
    max_samples: 1000
    # 输出特征图尺寸 [height, width] - 优化性能：预先设定避免动态计算
    # 对应流水线中Resize的img_scale [1024, 1024] / down_ratio，即 [256, 256]
    output_size: [256, 256]
    # 下采样比例
    down_ratio: 4
    # 热图通道数配置：默认单通道, 1=单通道(仅中心点)，2=双通道(中心点+顶点)
    heatmap_channels: 2

  # 变换流水线配置 (基于流水线的变换系统)
  pipelines:
    # 训练流水线 (严格对齐Cycle-CenterNet)
    train:
      # 1. 加载图像
      - type: LoadImageFromFile
        to_float32: true
        color_type: color

      # 2. 光度变换 (严格对齐Cycle-CenterNet参数)
      - type: PhotoMetricDistortion
        brightness_delta: 20
        contrast_range: [0.6, 1.4]
        saturation_range: [0.8, 1.2]  # 有风险，代码已经屏蔽
        hue_delta: 5                  # 有风险，代码已经屏蔽

      # 3. 透视变换增强 V2 (支持图像边界扩展 + 严格验证)
      - type: PerspectiveTransformV2
        perspective_range: [ 0.0, 0.20 ]
        apply_prob: 0.5
        fill_mode: dominant_color
        strict_validation: true  # 启用严格验证，防止截断问题

      # 4. 随机旋转增强 V2 (支持图像边界扩展 + 严格验证)
      - type: RandomRotationV2
        angle_range: [ -45.0, 45.0 ]
        apply_prob: 0.5
        center_mode: adaptive
        fill_mode: dominant_color
        fill_value: 0
        strict_validation: true  # 启用严格验证，防止截断问题

      # 5. 随机裁剪 (新的简洁高效实现，确保bbox完整保留)
      - type: RandomCrop
        min_crop_ratio: 0.3
        max_crop_ratio: 1.2
        margin_ratio: 0.02
        apply_prob: 0.5

      # 6. 缩放到统一尺寸
      - type: Resize
        img_scale: [1024, 1024]
        keep_ratio: false
        random_keep_ratio: true  # 随机选择保持宽高比模式，增加数据多样性
        interpolation: bilinear

      # 7. 随机翻转
      - type: RandomFlip
        flip_ratio: 0.5
        direction: horizontal

      # 8. 归一化 (ImageNet标准)
      - type: Normalize
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]
        to_rgb: true

      # 9. 转换为张量
      - type: ToTensor

    # 验证流水线
    val:
      # 1. 加载图像
      - type: LoadImageFromFile
        to_float32: true
        color_type: color

      # 2. 缩放到目标尺寸
      - type: Resize
        img_scale: [1024, 1024]
        keep_ratio: true
        random_keep_ratio: false  # 验证时不使用随机模式，保持一致性
        interpolation: bilinear

      # 3. 归一化
      - type: Normalize
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]
        to_rgb: true

      # 4. 转换为张量
      - type: ToTensor

    # 测试流水线
    test:
      # 1. 加载图像
      - type: LoadImageFromFile
        to_float32: true
        color_type: color

      # 2. 缩放到目标尺寸
      - type: Resize
        img_scale: [1024, 1024]
        keep_ratio: true
        random_keep_ratio: false  # 测试时不使用随机模式，保持一致性
        interpolation: bilinear

      # 3. 归一化
      - type: Normalize
        mean: [0.485, 0.456, 0.406]
        std: [0.229, 0.224, 0.225]
        to_rgb: true

      # 4. 转换为张量
      - type: ToTensor

  # 数据加载配置
  loader:
    # 数据加载器工作进程数
    num_workers: 16
    # 是否使用pin_memory
    pin_memory: true

# ============================================================================
# 模型配置 (ModelScope 版本)
# ============================================================================
model:
  # 骨干网络名称
  base_name: "dla34"
  # 是否使用预训练权重
  pretrained: false
  # 下采样比例
  down_ratio: 4
  # 检测头中间层通道数
  head_conv: 256
  # ModelScope 预训练权重路径（可选）
  checkpoint_path: /aipdf-mlp/xelawk/models/tsr_models/cv_dla34_table-structure-recognition_cycle-centernet.pt

# ============================================================================
# 训练配置
# ============================================================================
training:
  # 基础训练参数
  epochs: 200
  batch_size: 16

  # 优化器配置
  optimizer:
    # 优化器类型 (SGD/Adam/AdamW/AdamW_8Bit)
    type: "SGD"
    learning_rate: 0.000125

    # SGD特定参数
    sgd:
      momentum: 0.9
      weight_decay: 0.0001

    # Adam系列的特定参数
    adamx:
      beta1: 0.9
      beta2: 0.999
      epsilon: 1e-08
      weight_decay: 0.01

  # 学习率调度器配置
  scheduler:
    # 调度器类型 (constant/constant_with_warmup/cosine/cosine_with_restarts/linear)
    type: "constant_with_warmup"
    # 余弦退火参数
    cosine:
      num_cycles: 1
    # 预热配置
    warmup:
      steps: 500
    # 其他参数
    power: 1.0

  # 梯度配置
  gradient:
    # 是否使用梯度裁剪
    clip_norm: false
    # 梯度裁剪阈值
    clip_value: 1.0
    # 梯度累积步数
    accumulation_steps: 1

# ============================================================================
# EMA配置
# ============================================================================
ema:
  # 是否启用EMA
  enabled: false
  # EMA衰减率
  decay: 0.999
  # EMA开始步数
  start_step: 0
  # EMA更新周期
  update_period: 1

# ============================================================================
# 损失函数配置
# ============================================================================
loss:
  # 各损失函数权重
  weights:
    heatmap: 1.0          # 中心点热图损失权重
    offset: 1.0           # 偏移损失权重
    center2vertex: 1.0    # 中心到顶点损失权重
    vertex2center: 0.5    # 顶点到中心损失权重

# ============================================================================
# 检查点和验证配置
# ============================================================================
checkpoint:
  # 保存配置
  save:
    # 保存检查点的步数间隔
    steps: 2000
    # 每N个epoch保存一次模型
    every_n_epoch: 1
    # 保留的检查点数量
    keep_num: 100

  # 恢复配置
  resume:
    # 从检查点恢复训练的路径
    from_checkpoint: null

  # 验证配置
  validation:
    # 验证时使用的批次数量
    num_batches: None

# ============================================================================
# 分布式训练配置
# ============================================================================
distributed:
  # 混合精度训练 (no/fp16/bf16)
  mixed_precision: "no"
  # 日志记录工具
  report_to: "tensorboard"
  # 跟踪器项目名称
  tracker_project_name: "cycle-centernet-training"

# ============================================================================
# 可视化配置 (ModelScope 版本)
# ============================================================================
visualization:
  # 是否启用可视化功能
  enabled: true
  # 可视化样本图片路径（用于纯图片可视化，不依赖验证集标注）
  sample_images_dir: "assets/vis4tsr"
  # 每次可视化的样本数量
  max_samples: 10
  # 可视化结果保存路径（null时默认为basic.output_dir/visualization_results）
  output_dir: null
  # 可视化频率：每N次验证进行一次可视化（1表示每次验证都可视化）
  frequency: 1

  # ModelScope 版本特有配置
  modelscope:
    # bbox 检测数量
    bbox_k: 1000
    # gbox 检测数量
    gbox_k: 4000
    # 置信度阈值
    confidence_threshold: 0.3
    # NMS 阈值
    nms_threshold: 0.3

  # 可视化样式配置
  style:
    bbox_color: [0, 255, 0]        # 边界框颜色 (绿色)
    keypoint_color: [255, 0, 0]    # 关键点颜色 (红色)
    center_color: [255, 0, 0]      # 中心点颜色 (红色)
    transparency: 0.8              # 透明度
    line_thickness: 2              # 线条粗细
    point_radius: 4                # 点的半径

  # 热图可视化配置（双通道热力图）
  heatmap:
    colormap: "jet"                # 颜色映射
    normalize: true                # 是否归一化
    threshold: 0.1                 # 显示阈值
    display_channel: [0, 1]        # 显示哪个通道的热图: 0(bbox中心点), 1(顶点), [0,1](并排显示双通道)

# ============================================================================
# 调试与可视化配置
# ============================================================================
debug_visualization:
  # 是否启用干运行模式（只加载数据并可视化，不训练模型）
  dry_run: false
  # 干运行模式下可视化的批次数量
  dry_run_batches: 2
  # 干运行模式下可视化结果保存路径（null时默认为basic.output_dir/dry_run_results）
  dry_run_output_dir: null

  # 干运行可视化样式配置
  style:
    center_point_color: [255, 0, 0]      # 中心点颜色 (红色)
    bbox_color: [0, 255, 0]              # 边界框颜色 (绿色)
    offset_color: [0, 0, 255]            # 偏移向量颜色 (蓝色)
    center2vertex_color: [255, 0, 0]     # 中心到顶点颜色 (红色)
    vertex2center_color: [255, 255, 0]   # 顶点到中心颜色 (黄色)
    line_thickness: 2                    # 线条粗细
    point_radius: 4                      # 点的半径
    vector_scale: 10.0                   # 向量显示放大倍数

  # 热图可视化配置（与visualization.heatmap保持一致）
  heatmap:
    colormap: "jet"                # 颜色映射
    normalize: true                # 是否归一化
    threshold: 0.1                 # 显示阈值
    display_channel: [0, 1]        # 显示哪个通道的热图: 0(bbox中心点), 1(顶点), [0,1](并排显示双通道)