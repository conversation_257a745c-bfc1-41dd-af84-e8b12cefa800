Global:
  device: gpu
  epoch_num: 20
  log_smooth_window: 20
  print_batch_step: 10
  output_dir: ./output/rec/u14m_filter/resnet50_fpn_srn
  eval_epoch_step: [0, 1]
  eval_batch_step: [0, 500]
  cal_metric_during_train: True
  pretrained_model:
  checkpoints:
  use_tensorboard: false
  infer_img:
  # for data or label process
  character_dict_path: ./tools/utils/EN_symbol_dict.txt
  max_text_length: 25
  use_space_char: False
  save_res_path: ./output/rec/u14m_filter/predicts_resnet50_fpn_srn.txt
  # find_unused_parameters: True
  use_amp: True
  grad_clip_val: 10

Optimizer:
  name: Adam
  lr: 0.002 # for 4gpus bs128/gpu
  weight_decay: 0.0
  filter_bias_and_bn: False

LRScheduler:
  name: OneCycleLR
  warmup_epoch: 1.5 # pct_start 0.075*20 = 1.5ep
  cycle_momentum: False

Architecture:
  model_type: rec
  algorithm: SRN
  in_channels: 3
  Transform:
  Encoder:
    name: ResNet_FPN
    layers: 50
  Decoder:
    name: SRNDecoder
    hidden_dims: 512

Loss:
  name: SRNLoss
  # smoothing: True

Metric:
  name: RecMetric
  main_indicator: acc
  is_filter: True

PostProcess:
  name: SRNLabelDecode

Train:
  dataset:
    name: LMDBDataSet
    data_dir: ../Union14M-L-LMDB-Filtered
    transforms:
      - DecodeImagePIL: # load image
          img_mode: RGB
          channel_first: False
      - PARSeqAugPIL:
      - SRNLabelEncode: # Class handling label
      - RecTVResize:
          image_shape: [64, 256] # h:48 w:[48,160]
          padding: False
      - KeepKeys:
          keep_keys: ['image', 'label'] # dataloader will return list in this order
  loader:
    shuffle: True
    batch_size_per_card: 128
    drop_last: True
    num_workers: 4

Eval:
  dataset:
    name: LMDBDataSet
    data_dir: ../evaluation
    transforms:
      - DecodeImagePIL: # load image
          img_mode: RGB
          channel_first: False
      - SRNLabelEncode: # Class handling label
      - RecTVResize:
          image_shape: [64, 256] # h:48 w:[48,160]
          padding: False
      - KeepKeys:
          keep_keys: ['image', 'label'] # dataloader will return list in this order
  loader:
    shuffle: False
    drop_last: False
    batch_size_per_card: 128
    num_workers: 2
