#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试推理脚本的导入是否正常
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

def test_imports():
    """测试所有必要的导入"""
    print("开始测试导入...")
    
    try:
        print("1. 测试基础模块导入...")
        import torch
        from accelerate import Accelerator
        print("   ✓ torch 和 accelerate 导入成功")
        
        print("2. 测试日志模块导入...")
        from modules.utils.log import LOGGER
        print("   ✓ LOGGER 导入成功")
        
        print("3. 测试可视化模块导入...")
        from modules.visualization.table_structure_visualizer_ms import TableStructureVisualizerMS
        print("   ✓ TableStructureVisualizerMS 导入成功")
        
        print("4. 测试模型模块导入...")
        from networks.cycle_centernet_ms import create_cycle_centernet_ms_model
        print("   ✓ create_cycle_centernet_ms_model 导入成功")
        
        print("5. 测试图像工具导入...")
        from modules.visualization.image_utils import draw_predictions_on_image
        print("   ✓ image_utils 导入成功")
        
        print("\n所有导入测试通过！✓")
        return True
        
    except ImportError as e:
        print(f"\n导入失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        print(f"详细错误信息:\n{traceback.format_exc()}")
        return False
    except Exception as e:
        print(f"\n其他错误: {e}")
        import traceback
        print(f"详细错误信息:\n{traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_imports()
    if success:
        print("\n推理脚本的依赖导入正常，可以继续使用推理功能。")
        sys.exit(0)
    else:
        print("\n推理脚本的依赖导入有问题，请检查上述错误信息。")
        sys.exit(1)
