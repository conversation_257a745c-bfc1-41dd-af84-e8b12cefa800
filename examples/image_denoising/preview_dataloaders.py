#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/12/4 11:34
# <AUTHOR> <EMAIL>
# @FileName: preview_dataloaders

import os
import shutil
import torch
import random
import argparse
from itertools import islice

from tqdm import tqdm
import torch.utils.data
import torchvision.transforms as transforms

from my_datasets.image_denoising.base_dataset_v1 import walk_dataloaders
from my_datasets.image_denoising.base_dataset_v1 import MyCustomDataset as MyClearDocDataset
from my_datasets.image_denoising.base_dataset_clean import MyCustomDataset as MyCleanDocDataset
from my_datasets.image_denoising.base_dataset_deblack import MyCustomDataset as MyDeblackDataset
from my_datasets.image_denoising.base_dataset_v1 import dynamic_collate_fn as clear_collate_fn
from my_datasets.image_denoising.base_dataset_clean import dynamic_collate_fn as clean_collate_fn
from my_datasets.image_denoising.base_dataset_deblack import dynamic_collate_fn as deblack_collate_fn


def prepare_dataloaders(args, mode, train_batch_size_per_device, seed=-1):
    debug = args.debug

    datasets, loaders = [], []
    deblack_doc_dataset_dir = args.deblack_doc_dataset_dir
    clear_doc_dataset_dir = args.clear_doc_dataset_dir
    clean_doc_dataset_dir = args.clean_doc_dataset_dir

    # 训练集才会用到去黑底的数据集
    deblack_doc_dataset = None
    if deblack_doc_dataset_dir is not None and mode == "train":
        deblack_doc_dataset = MyDeblackDataset(deblack_doc_dataset_dir, mode, seed, debug=debug)
        datasets.append(deblack_doc_dataset)

    clear_doc_dataset = None
    if clear_doc_dataset_dir is not None:
        clear_doc_dataset = MyClearDocDataset(clear_doc_dataset_dir, mode, seed, debug=debug)
        datasets.append(clear_doc_dataset)

    clean_doc_dataset = None
    if clean_doc_dataset_dir is not None:
        clean_doc_dataset = MyCleanDocDataset(clean_doc_dataset_dir, mode, seed, debug=debug)
        datasets.append(clean_doc_dataset)

    assert len(datasets) > 0, ("No datasets were loaded! Please ensure at least one dataset directory")

    def dynamic_resolution_for_deblack_doc():
        if mode == "train":
            if not args.res4deblackdoc:
                raise ValueError("args.res4deblackdoc list is empty.")
            return random.choice(args.res4deblackdoc)
        else:
            return 1024

    def dynamic_resolution_for_clear_doc():
        if mode == "train":
            if not args.res4cleardoc:
                raise ValueError("args.res4cleardoc list is empty.")
            return random.choice(args.res4cleardoc)
        else:
            return 1024

    def dynamic_resolution_for_clean_doc():
        if mode == "train":
            if not args.res4cleandoc:
                raise ValueError("args.res4cleandoc list is empty.")
            return random.choice(args.res4cleandoc)
        else:
            return 1024

    if deblack_doc_dataset is not None:
        deblack_doc_data_loader = torch.utils.data.DataLoader(
            deblack_doc_dataset,
            shuffle=True if mode == "train" else False,
            batch_size=train_batch_size_per_device,
            num_workers=32,
            collate_fn=lambda batch: deblack_collate_fn(batch, dynamic_resolution_for_deblack_doc)
        )
        loaders.append(("DeblackDoc", deblack_doc_data_loader))

    if clear_doc_dataset is not None:
        clear_doc_data_loader = torch.utils.data.DataLoader(
            clear_doc_dataset,
            shuffle=True if mode == "train" else False,
            batch_size=train_batch_size_per_device,
            num_workers=32,
            collate_fn=lambda batch: clear_collate_fn(batch, dynamic_resolution_for_clear_doc)
        )
        loaders.append(("ClearDoc", clear_doc_data_loader))

    if clean_doc_dataset is not None:
        clean_doc_data_loader = torch.utils.data.DataLoader(
            clean_doc_dataset,
            shuffle=True if mode == "train" else False,
            batch_size=train_batch_size_per_device,
            num_workers=32,
            collate_fn=lambda batch: clean_collate_fn(batch, dynamic_resolution_for_clean_doc)
        )
        loaders.append(("CleanDoc", clean_doc_data_loader))

    return datasets, loaders


def get_args():
    parser = argparse.ArgumentParser(description="Denoising with LaMa training loop")
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Whether or not to use debug for training with small version of dataset"
    )
    parser.add_argument(
        "--clear_doc_dataset_dir",
        type=str,
        default=None,
        help="Training data root path of making doc clear",
    )
    parser.add_argument(
        "--clean_doc_dataset_dir",
        type=str,
        default=None,
        help="Training data root path of making clean doc to be clear",
    )
    parser.add_argument(
        "--deblack_doc_dataset_dir",
        type=str,
        default=None,
        help="Training data root path of doc deblacking",
    )
    parser.add_argument(
        '--res4cleardoc',
        type=int,
        nargs='+',
        required=False,
        help="dynamic resolution for clear doc training"
    )
    parser.add_argument(
        '--res4cleandoc',
        type=int,
        nargs='+',
        required=False,
        help="dynamic resolution for clean doc training"
    )
    parser.add_argument(
        '--res4deblackdoc',
        type=int,
        nargs='+',
        required=False,
        help="dynamic resolution for deblack doc training"
    )

    args = parser.parse_args()

    return args


def save_tensor_as_image(tensor, save_path):
    """
    将 tensor 转换为 PIL 图像并保存
    """
    # 确保 tensor 在 [0, 1] 的范围内
    tensor = tensor.clamp(0, 1)
    # 转换为 numpy
    transform_to_pil = transforms.ToPILImage()
    image = transform_to_pil(tensor)
    image.save(save_path)


if __name__ == '__main__':
    args = get_args()
    all_train_datasets, all_train_loaders = prepare_dataloaders(args, 'train', 2, seed=42)

    preview_dir = "/aicamera-mlp/xelawk_train_space/tmp/数据预览/二期黑底阴影清晰样本对-V3"
    if os.path.exists(preview_dir):
        shutil.rmtree(preview_dir)
    os.makedirs(preview_dir, exist_ok=True)  # 确保保存目录存在

    num_batch_to_eval = 50
    dataloader_steps = islice(walk_dataloaders(all_train_loaders), 0, num_batch_to_eval)

    # 使用 tqdm 包装 walk_dataloaders 显示进度条
    for step, (flag, batch) in enumerate(tqdm(dataloader_steps, desc="Processing Dataloaders")):
        noised, target = batch['noised'], batch['target']

        # tqdm 子进度条，用于显示每个 batch 内的样本保存进度
        for i in tqdm(range(noised.size(0)), desc=f"Step {step} ({flag})", leave=False):
            # 计算分辨率（最长边）
            _, height, width = noised[i].size()  # C, H, W 格式
            max_resolution = max(height, width)

            # 保存 noised 图像，文件名包含分辨率信息
            noised_image_path = os.path.join(
                preview_dir, f"{flag}_{step}_{i}_noised_{max_resolution}px.png"
            )
            save_tensor_as_image(noised[i], noised_image_path)

            # 保存 target 图像，文件名包含分辨率信息
            target_image_path = os.path.join(
                preview_dir, f"{flag}_{step}_{i}_target_{max_resolution}px.png"
            )
            save_tensor_as_image(target[i], target_image_path)

    print(f"All samples have been saved to {preview_dir}.")