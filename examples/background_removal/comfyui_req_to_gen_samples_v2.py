#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/8 16:22
# <AUTHOR> <EMAIL>
# @FileName: comfyui_req_to_gen_samples

import os
import time
import uuid
import json
import random
import requests
from io import BytesIO

from PIL import Image
from tqdm import tqdm
from datasets import DatasetDict, concatenate_datasets
from concurrent.futures import ThreadPoolExecutor, as_completed

from modules.utils.log import LOGGER
from modules.utils.gpt4v_tagger import GPT4
from modules.utils.image_utils import get_all_image_path

CLI_ID = str(uuid.uuid4())


class GetFgBgWorkflow(object):
    def __init__(
        self,
        prompt_server,
        comfyui_temp_dir,
        template_config_path,
        prompt_time_out=10,
        query_prompt_time_out=600,
        enable_random_seed=True,
    ):
        self.title2index = {
            "加载底模": None,
            "前景提示词": None,
            "背景提示词": None,
            "负向提示词": None
        }
        self.output_node_titles = ['前景图', '背景图', '前背景合成图']

        self.seeds_node_index = []
        self.enable_random_seed = enable_random_seed
        self.prompt_server = prompt_server
        self.comfyui_temp_dir = comfyui_temp_dir
        self.template_config_path = template_config_path
        self._init_node_index()

        self.client_id = CLI_ID
        self.prompt_time_out = prompt_time_out
        self.query_prompt_time_out = query_prompt_time_out

    def _init_node_index(self):
        with open(self.template_config_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        self.output_nodes = dict()
        for index, val in data.items():
            if "_meta" in val and val["_meta"].get("title", None) in self.title2index:
                self.title2index[val["_meta"].get("title")] = index

            elif "_meta" in val and val["_meta"].get("title", None) in self.output_node_titles:
                self.output_nodes[index] = val["_meta"].get("title")

            elif "inputs" in val and "seed" in val["inputs"]:
                self.seeds_node_index.append(index)

    def _to_payload(self, base_model, fg_prompt, bg_prompt, neg_prompt):
        with open(self.template_config_path, 'r', encoding='utf-8') as f:
            prompt_wf = json.load(f)

        prompt_wf[self.title2index["加载底模"]]["inputs"]["ckpt_name"] = base_model
        prompt_wf[self.title2index["前景提示词"]]["inputs"]["text"] = fg_prompt
        prompt_wf[self.title2index["背景提示词"]]["inputs"]["text"] = bg_prompt
        prompt_wf[self.title2index["负向提示词"]]["inputs"]["text"] = neg_prompt

        if self.enable_random_seed:
            for index in self.seeds_node_index:
                prompt_wf[index]["inputs"]["seed"] = random.randint(1 << 32, 1 << 63)

        payload = {
            "client_id": self.client_id,
            "prompt": prompt_wf
        }

        return payload

    def _invoke_comfyui_prompt(self, payload):
        timeout = self.prompt_time_out

        try:
            response = requests.post(os.path.join(self.prompt_server, 'prompt'), json=payload, timeout=timeout)
        except requests.exceptions.Timeout:
            return False, f"prompt timeout"

        if response.status_code != 200:
            return False, f"Error response: {response.text}"

        response_json = response.json()
        prompt_id = response_json.get("prompt_id", None)
        if not prompt_id:
            return False, f"Error response: not found prompt_id"

        return True, prompt_id

    def _invok_comfyui_outputs(self, prompt_id):
        timeout = self.query_prompt_time_out

        tic = time.time()
        try:
            while True:
                toc = time.time()
                if toc - tic > timeout:
                    raise TimeoutError(f"timeout: {timeout}")

                try:
                    response = requests.get(os.path.join(self.prompt_server, f'history/{prompt_id}'), timeout=timeout)
                except requests.exceptions.Timeout:
                    raise TimeoutError(f"timeout: {timeout}")

                if response.status_code != 200:
                    return None, f"Error response: {response.text}"

                response_json = response.json()
                if not response_json:
                    time.sleep(1)
                    continue

                results = response_json[prompt_id]
                status = results["status"]
                if status["status_str"] != "success" or not status["completed"]:
                    time.sleep(1)
                    continue

                image_outputs = dict()
                outputs = results["outputs"]
                for node_index, data in outputs.items():
                    if node_index not in self.output_nodes:
                        continue
                    output_type = self.output_nodes[node_index]
                    image_resources = data.get("images", None)
                    for rsrc in image_resources:
                        filename = rsrc.get('filename')
                        subfolder = rsrc.get('subfolder')
                        image_result = Image.open(os.path.join(self.comfyui_temp_dir, subfolder, filename))
                        if output_type in image_outputs:
                            image_outputs[output_type].append(image_result)
                        else:
                            image_outputs[output_type] = [image_result]

                return image_outputs, "推理成功"

        except TimeoutError as e:
            return None, f"{e}"

    def request(self, base_model, fg_prompt, bg_prompt, neg_prompt):
        payload = self._to_payload(base_model, fg_prompt, bg_prompt, neg_prompt)

        is_ok, msg = self._invoke_comfyui_prompt(payload)
        if not is_ok:
            return None, msg

        prompt_id = msg
        outputs, msg = self._invok_comfyui_outputs(prompt_id)

        return outputs, msg


def main():
    # 配置路径
    select_num = 10000
    ref_image_dir = "/aicamera-mlp/xelawk_train_space/datasets/watermark_removal_ai/source_dataset/hres_nowm_samples_150w_hf/train"
    save_dir = '/mnt/aicamera-mlp/xelawk_train_space/datasets/background_removal/src_1w_gen_10w_v202408141700'
    os.makedirs(save_dir, exist_ok=True)

    # 初始化GPT和工作流
    gpt = GPT4(config_path="configs/background_removal/gpt4_api.yaml")
    base_model_name = 'RealVisXL_V4.0.safetensors'
    default_quality_prompt = "4k,HD,masterpiece,raw photo,dynamic angle,detailed,Photorealistic,High Detail"
    default_nege_prompt = '(worst quality, low quality, illustration, 3d, 2d, painting, cartoons, sketch), open mouth'

    # 初始化4个工作流
    workflows = [
        GetFgBgWorkflow(
            prompt_server=f'http://127.0.0.1:{port}',
            comfyui_temp_dir='/aicamera-mlp/comfyui_aicache/temp',
            template_config_path='configs/background_removal/gen_fg_bg_workflow.json'
        )
        for port in ['5001', '5002', '5003', '5004']
    ]

    # 获取所有图片路径
    dataset = get_src_dataset(ref_image_dir, select_num)
    source_images = [BytesIO(data['image_bytes']) for data in dataset]

    # 线程池参数配置
    batch_size = 4
    wf_thread_pool_size = 4      # 线程池大小，用于调用wf资源生成图片
    prompt_thread_pool_size = 4  # 线程池大小，用于处理gpt获取prompt

    # 进度条设置
    total_cnt = 0
    pbar = tqdm(total=len(source_images))
    def generate_single_image(wf, fg_prompt, bg_prompt, idx, part_dir):
        """调用工作流生成单张图像并保存"""
        fg_prompt_extended = f"{fg_prompt},{default_quality_prompt}"
        outputs, msg = wf.request(
            base_model=base_model_name,
            fg_prompt=fg_prompt_extended,
            bg_prompt=bg_prompt,
            neg_prompt=default_nege_prompt
        )

        for fg, bg, fg_bg in zip(outputs['前景图'], outputs['背景图'], outputs['前背景合成图']):
            fg.save(os.path.join(part_dir, f'{idx}_fg.png'))
            bg.save(os.path.join(part_dir, f'{idx}_bg.png'))
            fg_bg.save(os.path.join(part_dir, f'{idx}_fg_bg.png'))

            with open(os.path.join(part_dir, f'{idx}_prompts.txt'), 'w', encoding='utf-8') as f:
                f.write(f"fg_prompt: {fg_prompt_extended}\n")
                f.write(f"bg_prompt: {bg_prompt}\n")
                f.write(f"negative_prompt: {default_nege_prompt}\n")

    # 使用线程池获取prompts和生成图像
    with ThreadPoolExecutor(max_workers=prompt_thread_pool_size) as prompt_executor, \
            ThreadPoolExecutor(max_workers=wf_thread_pool_size) as wf_executor:

        for batch_start in range(0, len(source_images), batch_size):
            batch_images = source_images[batch_start:batch_start + batch_size]

            future_to_prompts = {
                prompt_executor.submit(gpt.get_image_fg_bg_prompts, image_path): image_path
                for image_path in batch_images
            }

            for future in as_completed(future_to_prompts):
                try:
                    future_result = future.result()
                except Exception as e:
                    LOGGER.error(e)
                    pbar.update(1)
                    continue

                if future_result is None:
                    continue
                fg_prompt, bg_prompt = future_result

                wf_futures = []
                for i in range(10):  # 每张图像生成10个变体
                    wf = workflows[i % len(workflows)]  # 选择一个工作流，轮流使用4个工作流

                    # 计算保存路径的子目录
                    part_num = (total_cnt// 500) + 1
                    part_dir = os.path.join(save_dir, f'part_{part_num:04d}')
                    os.makedirs(part_dir, exist_ok=True)

                    wf_futures.append(
                        wf_executor.submit(generate_single_image, wf, fg_prompt, bg_prompt, idx=total_cnt, part_dir=part_dir)
                    )
                    total_cnt += 1

                for wf_future in as_completed(wf_futures):
                    wf_future.result()

                pbar.update(1)

    pbar.close()


def get_src_dataset(data_dir, select_num=-1, seed=-1):
    dataset = []
    if seed == -1:
        seed = random.randint(1, 1000000)
    cur_dataset = DatasetDict.load_from_disk(data_dir)['train']
    if select_num > 0:
        cur_dataset = cur_dataset.shuffle(seed=seed)
        cur_dataset = cur_dataset.select(range(select_num))
    dataset.append(cur_dataset)
    dataset = concatenate_datasets(dataset)
    dataset = dataset.shuffle(seed=seed)
    return dataset


if __name__ == '__main__':
    main()
