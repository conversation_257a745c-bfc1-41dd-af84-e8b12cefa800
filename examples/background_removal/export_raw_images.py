#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/12 10:44
# <AUTHOR> <EMAIL>
# @FileName: export_raw_images

import io
import os
import random
from PIL import Image
from tqdm import tqdm
from datasets import DatasetDict, concatenate_datasets


def get_src_dataset(data_dir, select_num=-1, seed=-1):
    if seed == -1:
        seed = random.randint(1, 1000000)
    dataset = []
    cur_dataset = DatasetDict.load_from_disk(data_dir)['train']
    if select_num > 0:
        cur_dataset = cur_dataset.shuffle(seed=seed)
        cur_dataset = cur_dataset.select(range(select_num))
    dataset.append(cur_dataset)
    dataset = concatenate_datasets(dataset)
    dataset = dataset.shuffle(seed=seed)
    return dataset


def main():
    select_num = 300000
    data_dir = "/mnt/appdata2/dataset/xelawk/hres_nowm_samples_150w_hf/train"
    save_dir = '/mnt/appdata2/dataset/kantu_classifier/tmp/raw_image_30w_v202408202019'
    os.makedirs(save_dir, exist_ok=True)

    dataset = get_src_dataset(data_dir, select_num=select_num)

    part_counter = 1
    img_counter = 0
    part_dir = os.path.join(save_dir, f'part_{part_counter:05d}')
    os.makedirs(part_dir, exist_ok=True)

    # 使用tqdm包裹dataset，显示进度条
    for i, sample in enumerate(tqdm(dataset, desc="Processing images")):
        image_bytes = sample['image_bytes']
        image = Image.open(io.BytesIO(image_bytes))
        image_format = image.format.lower()  # 获取图像格式并转换为小写
        img_filename = os.path.join(part_dir, f'image_{i:05d}.{image_format}')
        image.save(img_filename)

        img_counter += 1
        if img_counter >= 500:
            part_counter += 1
            img_counter = 0
            part_dir = os.path.join(save_dir, f'part_{part_counter:05d}')
            os.makedirs(part_dir, exist_ok=True)


if __name__ == "__main__":
    main()

