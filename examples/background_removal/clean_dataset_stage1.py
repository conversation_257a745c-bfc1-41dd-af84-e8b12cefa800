import os
import shutil
from pathlib import Path

IMAGE_FMT = ['.jpg', '.jpeg', '.JPEG', '.JPG', '.png', '.PNG']


def get_all_image_path(file_dir, path_op=str, recursive=False, pattern=None):
    file_dir = Path(file_dir)
    image_path = []

    if recursive:
        glob_str = '**/*'
    else:
        glob_str = '*'

    if pattern is not None:
        glob_str = f"{glob_str}{pattern}*"

    for p in file_dir.glob(glob_str):
        if p.parent.name.startswith('.'):
            continue
        if p.is_dir():
            continue
        if p.stem.startswith('.'):
            continue
        if p.suffix in IMAGE_FMT:
            image_path.append(path_op(p))

    return image_path


source_dir = "/Volumes/BigDataSSD/工作数据/去背景相关/Datasets/clean_remove_bg_data"
origin_dataset_dir = os.path.join(source_dir, "origin")
new_target_dataset_dir = os.path.join(source_dir, "clean_stage_1st")

all_subset_dirs = list(Path(origin_dataset_dir).glob("*"))
test_dataset = []
train_dataset = []
for p in all_subset_dirs:
    if not p.is_dir():
        continue
    if p.stem.startswith('.'):
        continue

    if 'TE' in p.stem:
        test_dataset.append(p)
    elif 'TR' in p.stem:
        train_dataset.append(p)
    elif 'test' in p.stem:
        test_dataset.append(p)
    else:
        train_dataset.append(p)

print("***** 训练集有如下子集 *****")
total_train = 0
for p in train_dataset:
    image_path = get_all_image_path(p, pattern='*/images/*.jpg')
    total_train += len(image_path)
    new_subset_name = p.stem.replace('-train', '').replace('-test', '')
    new_subset_dir = os.path.join(new_target_dataset_dir, 'train', new_subset_name)
    os.makedirs(new_subset_dir, exist_ok=True)

    # 合并文件夹中的内容
    for item in p.glob('**/*'):
        dest = Path(new_subset_dir) / item.relative_to(p)
        if item.is_dir():
            os.makedirs(dest, exist_ok=True)
        else:
            shutil.copy2(item, dest)

    print(f"{p}: {Path(new_subset_dir).stem}, num: {len(image_path)}")
print(f"总数据量：{total_train}")
print("\n")

print("***** 测试集有如下子集 *****")
total_test = 0
for p in test_dataset:
    image_path = get_all_image_path(p, pattern='*/images/*.jpg')
    total_test += len(image_path)
    new_subset_name = p.stem.replace('-train', '').replace('-test', '')
    new_subset_dir = os.path.join(new_target_dataset_dir, 'test', new_subset_name)
    os.makedirs(new_subset_dir, exist_ok=True)

    # 合并文件夹中的内容
    for item in p.glob('**/*'):
        dest = Path(new_subset_dir) / item.relative_to(p)
        if item.is_dir():
            os.makedirs(dest, exist_ok=True)
        else:
            shutil.copy2(item, dest)

    print(f"{p}: {Path(new_subset_dir).stem}, num: {len(image_path)}")
print(f"总数据量：{total_test}")
