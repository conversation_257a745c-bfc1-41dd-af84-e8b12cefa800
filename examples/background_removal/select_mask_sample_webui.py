#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/12 15:16
# <AUTHOR> <EMAIL>
# @FileName: select_mask_sample_webui

import os
import logzero
import threading
from enum import Enum
from pathlib import Path
from functools import wraps

import gradio as gr
from PIL import Image

MAX_SIZE = 800
LOGGER = logzero.setup_logger()

with open('./style.css', 'r', encoding='utf-8') as f:
    CSS = f.read()

CSS = CSS + """
#btn_perfect {background: green; color: white;}
#btn_good {background: blue; color: white;}
#btn_normal {background: orange; color: white;}
#btn_bad {background: red; color: white;}
"""

DEFAULT_ROOT_DIR = '/aicamera-mlp/xelawk_train_space/datasets/background_removal/src_1w_gen_10w'

def synchronized(method):
    """装饰器，用于方法的线程安全性"""
    @wraps(method)
    def wrapper(self, *args, **kwargs):
        with self.lock:  # 使用实例的锁
            return method(self, *args, **kwargs)
    return wrapper


class Rating(Enum):
    PERFECT = "perfect"
    GOOD = "good"
    NORMAL = "normal"
    BAD = "bad"


class MaskSampleViewer(object):
    def __init__(self, root_dir, workspace):
        self.root_dir = root_dir
        self.workspace = workspace
        os.makedirs(os.path.join(root_dir, workspace), exist_ok=True)
        self.lock = threading.Lock()
        self.label_record_prefix = 'record'
        self.last_check_prefix = 'last_check'
        self.viewer_scheduler = self.init_viewer_scheduler()

    def init_viewer_scheduler(self):
        viewer_scheduler = dict()

        part_dirs = sorted(self.__glob_part_dirs())
        for part_dir in part_dirs:
            part_fg_files = sorted(self.__glob_fg_files(part_dir))
            part_last_check_txt = os.path.join(
                self.root_dir, self.workspace, f"{self.last_check_prefix}-{part_dir.stem}.txt"
            )
            if os.path.exists(part_last_check_txt):
                with open(part_last_check_txt, 'r', encoding='utf-8') as f:
                    part_index = int(f.read().strip())
            else:
                part_index = 0

            viewer_scheduler[part_dir.stem] = {
                "index": part_index,
                "files": part_fg_files,
                "num_files": len(part_fg_files)
            }

        return viewer_scheduler

    def show_image(self, part_dir):
        part_dir = Path(part_dir)
        data = self.viewer_scheduler[part_dir.stem]
        fg_file_idx = data['index']
        fg_file_list = data['files']
        fg_img_path = fg_file_list[fg_file_idx]
        visualized_img = self.show_masked_image(fg_img_path)
        return visualized_img, '/'.join(str(fg_img_path).split('/')[-2::])

    def show_masked_image(self, image_rel_path):
        # 加载图像
        fg_img_path = Path(image_rel_path)
        idx = int(fg_img_path.stem.split('_')[0])
        fg_bg_img_path = os.path.join(fg_img_path.parent, f"{idx}_fg_bg.png")
        if not os.path.exists(fg_bg_img_path):
            return None, f"{idx}_fg_bg.png not exists, plese skip"

        fg_bg_img = Image.open(fg_bg_img_path).convert('RGB')
        fg_img = Image.open(fg_img_path).convert('RGBA')

        # 计算图像缩放比例，使图像适应画布
        max_size = MAX_SIZE
        scale = min(max_size / fg_bg_img.width, max_size / fg_bg_img.height)
        new_size = (int(fg_bg_img.width * scale), int(fg_bg_img.height * scale))
        fg_bg_img = fg_bg_img.resize(new_size, Image.Resampling.LANCZOS)
        fg_img = fg_img.resize(new_size, Image.Resampling.LANCZOS)

        # 提取 alpha 通道作为 mask，并将其透明度降低一半
        mask = fg_img.getchannel('A').point(lambda p: p * 0.5)
        # 创建一个绿色图像来表示 mask 区域
        color_mask = Image.new('RGBA', fg_img.size, (0, 255, 0, 0))
        color_mask.putalpha(mask)
        # 将绿色 mask 叠加到 fg_bg_img 上
        visualized_img = Image.alpha_composite(fg_bg_img.convert('RGBA'), color_mask)

        return visualized_img

    def save_last_check(self, part_dir):
        part_dir = Path(part_dir)
        data = self.viewer_scheduler[part_dir.stem]
        lask_check_file = os.path.join(self.root_dir, self.workspace, f"{self.last_check_prefix}-{part_dir.stem}.txt")
        with open(lask_check_file, "w") as f:
            part_name = part_dir.stem
            f.write(f"{data['index']}")

    @synchronized
    def save_rating(self, img_rel_path, rating):
        if "plese skip" in img_rel_path:
            return f"invalid rating to save: {img_rel_path}"
        part_dir = Path(img_rel_path).parent.stem
        part_record_txt = os.path.join(self.root_dir, self.workspace, f"{self.label_record_prefix}-{part_dir}.txt")
        with open(part_record_txt, "a") as f:
            msg = f"{img_rel_path}, {rating}\n"
            f.write(f"{img_rel_path}, {rating}\n")
        return msg

    @synchronized
    def prev_image(self, part_dir):
        part_dir = Path(part_dir)
        last_check_file = os.path.join(self.root_dir, self.workspace, f"{self.last_check_prefix}-{part_dir.stem}.txt")
        if os.path.exists(last_check_file):
            index_shift = 1
        else:
            index_shift = 0
        data = self.viewer_scheduler[part_dir.stem]
        if data['index'] > 0:
            data['index'] -= index_shift
        msg = f"Progress: {part_dir.stem}, {data['index'] + 1}/{data['num_files']}, {100 * (data['index'] + 1) / data['num_files']:.2f}%"
        self.save_last_check(part_dir)
        img, img_rel_path = self.show_image(part_dir)
        if img is None and "plese skip" in img_rel_path:
            msg = f"warning: {img_rel_path}; {msg}"
        return img, img_rel_path, msg

    @synchronized
    def next_image(self, part_dir):
        part_dir = Path(part_dir)
        last_check_file = os.path.join(self.root_dir, self.workspace, f"{self.last_check_prefix}-{part_dir.stem}.txt")
        if os.path.exists(last_check_file):
            index_shift = 1
        else:
            index_shift = 0
        data = self.viewer_scheduler[part_dir.stem]
        if data['index'] < len(data['files']) - 1:
            data['index'] += index_shift
        msg = f"Progress: {part_dir.stem}, {data['index'] + 1}/{data['num_files']}, {100 * (data['index'] + 1) / data['num_files']:.2f}%"
        self.save_last_check(part_dir)
        img, img_rel_path = self.show_image(part_dir)
        if img is None and "plese skip" in img_rel_path:
            msg = f"warning: {img_rel_path}; {msg}"
        return img, img_rel_path, msg

    @synchronized
    def next_preview_image(self, part_dir, current_image_name=None, filter_label=None):
        part_dir = Path(part_dir)
        part_record_txt = os.path.join(self.root_dir, self.workspace, f"{self.label_record_prefix}-{part_dir}.txt")
        filtered_results = self.filter_record_results(part_record_txt, filter_label)
        if len(filtered_results) == 0:
            return None, None, "no saved record results found"

        next_preview_image = None
        if not current_image_name:
            next_preview_image = os.path.join(self.root_dir, filtered_results[0])
        else:
            for idx, img_rel_path in enumerate(filtered_results):
                if img_rel_path == current_image_name:
                    try:
                        next_preview_image = os.path.join(self.root_dir, filtered_results[idx+1])
                    except:
                        next_preview_image = os.path.join(self.root_dir, img_rel_path)
                    break
        visualized_img = self.show_masked_image(next_preview_image)
        next_preview_image = '/'.join(str(next_preview_image).split('/')[-2::])

        return visualized_img, next_preview_image, f"next image, label: {filter_label}"

    @synchronized
    def prev_preview_image(self, part_dir, current_image_name=None, filter_label=None):
        part_dir = Path(part_dir)
        part_record_txt = os.path.join(self.root_dir, self.workspace, f"{self.label_record_prefix}-{part_dir}.txt")
        filtered_results = self.filter_record_results(part_record_txt, filter_label)
        if len(filtered_results) == 0:
            return None, None, "not saved record results found"

        prev_preview_image = None
        if not current_image_name:
            prev_preview_image = os.path.join(self.root_dir, filtered_results[0])
        else:
            for idx, img_rel_path in enumerate(filtered_results):
                if img_rel_path == current_image_name:
                    try:
                        prev_preview_image = os.path.join(self.root_dir, filtered_results[idx-1])
                    except:
                        prev_preview_image = os.path.join(self.root_dir, img_rel_path)
                    break
        visualized_img = self.show_masked_image(prev_preview_image)
        prev_preview_image = '/'.join(str(prev_preview_image).split('/')[-2::])

        return visualized_img, prev_preview_image, f"previous image, label: {filter_label}"

    def filter_record_results(self, record_file, filter_label):
        img2label = dict()
        with open(record_file, 'r', encoding='utf-8') as f:
            data = [line for line in f.readlines() if line.strip()]
        for line in data:
            image_rel_path, label = line.split(', ')
            img2label[image_rel_path.strip()] = label.strip()

        results = []
        for image_rel_path, label in img2label.items():
            if filter_label is None:
                results.append(image_rel_path)
            elif label == filter_label:
                results.append(image_rel_path)

        return results

    def __glob_part_dirs(self):
        part_dirs = Path(self.root_dir).glob('part*')
        clean_part_dirs = []
        for _dir in part_dirs:
            if not _dir.is_dir():
                continue
            if _dir.stem.startswith('.'):
                continue
            clean_part_dirs.append(_dir)
        return clean_part_dirs

    def __glob_fg_files(self, _dir):
        fg_files = Path(_dir).glob('*_fg.png')
        clean_fg_files = []
        for file in fg_files:
            if not file.is_file():
                continue
            if file.stem.startswith('.'):
                continue
            clean_fg_files.append(file)
        return clean_fg_files


sample_viewer_manager = dict()

def init_sample_viewer_manager(root_dir, workspace):
    if not os.path.exists(root_dir) :
        return f"{root_dir} not found"
    if not Path(root_dir).is_dir():
        return f"please directory but not file path"
    if len(list(Path(root_dir).glob("part*"))) == 0:
        return f"invalid directory path which not contains data part directory"

    global sample_viewer_manager
    key = f"{root_dir}-{workspace}"
    if key not in sample_viewer_manager:
        sample_viewer_manager[key] = MaskSampleViewer(root_dir, workspace)
    return f"init sample viewer from `{root_dir}`, `{workspace}` successfully"


def get_part_list(root_dir, workspace):
    global sample_viewer_manager
    if f"{root_dir}-{workspace}" in sample_viewer_manager:
        return gr.update(choices=list(sample_viewer_manager[f"{root_dir}-{workspace}"].viewer_scheduler.keys()))
    return None


def next_image(root_dir, workspace, part_dir):
    if not root_dir:
        return None, None, "please specify a data root directory"
    if not workspace:
        return None, None, "please specify a workspace name"
    if not part_dir:
        return None, None, "please specify a part directory"

    global sample_viewer_manager
    if f"{root_dir}-{workspace}" in sample_viewer_manager:
        viewer = sample_viewer_manager[f"{root_dir}-{workspace}"]
        return viewer.next_image(part_dir)
    else:
        return None, None, "invalid data, please specify a data root directory and workspace name"


def next_preview_image(root_dir, workspace, part_dir, current_image_name, filter_label):
    if not root_dir:
        return None, None, "please specify a data root directory"
    if not workspace:
        return None, None, "please specify a workspace name"
    if not part_dir:
        return None, None, "please specify a part directory"
    if not filter_label:
        return None, None, "please specify a filter label"

    global sample_viewer_manager
    if f"{root_dir}-{workspace}" in sample_viewer_manager:
        viewer = sample_viewer_manager[f"{root_dir}-{workspace}"]
        return viewer.next_preview_image(part_dir, current_image_name, filter_label)
    else:
        return None, None, "invalid data, please specify a data root directory and workspace name"


def prev_preview_image(root_dir, workspace, part_dir, current_image_name, filter_label):
    if not root_dir:
        return None, None, "please specify a data root directory"
    if not workspace:
        return None, None, "please specify a workspace name"
    if not part_dir:
        return None, None, "please specify a part directory"
    if not filter_label:
        return None, None, "please specify a filter label"

    global sample_viewer_manager
    if f"{root_dir}-{workspace}" in sample_viewer_manager:
        viewer = sample_viewer_manager[f"{root_dir}-{workspace}"]
        return viewer.prev_preview_image(part_dir, current_image_name, filter_label)
    else:
        return None, None, "invalid data, please specify a data root directory and workspace name"


def prev_image(root_dir, workspace, part_dir):
    if not root_dir:
        return None, None, "please specify a data root directory"
    if not workspace:
        return None, None, "please specify a workspace name"
    if not part_dir:
        return None, None, "please specify a part directory"

    global sample_viewer_manager
    if f"{root_dir}-{workspace}" in sample_viewer_manager:
        viewer = sample_viewer_manager[f"{root_dir}-{workspace}"]
        return viewer.prev_image(part_dir)
    else:
        return None, None, "invalid data, please specify a data root directory and workspace name"


def save_rating(root_dir, workspace, img_rel_path, rating):
    if not root_dir:
        return "please specify a data root directory"
    if not workspace:
        return "please specify a workspace name"
    if not img_rel_path:
        return f"invalid image path to save rating"

    global sample_viewer_manager
    if f"{root_dir}-{workspace}" in sample_viewer_manager:
        viewer = sample_viewer_manager[f"{root_dir}-{workspace}"]
        return viewer.save_rating(img_rel_path, rating)
    else:
        return "invalid data, please specify a data root directory and workspace name"


def create_webui():
    with gr.Blocks(title="人在回路数据筛选", css=CSS, analytics_enabled=False) as webui:
        with gr.TabItem("LayerDiffusion合成抠图数据"):
            with gr.TabItem("人工数据筛选"):
                with gr.Row():
                    # 图片预览
                    with gr.Column():
                        image_output = gr.Image(label="当前图像", height=896)
                    with gr.Column():
                        # 指定数据源和工作空间
                        with gr.Row():
                            root_dir = gr.Textbox(
                                label="输入数据源路径",
                                value=DEFAULT_ROOT_DIR,
                            )
                            workspace = gr.Textbox(label="输入工作空间名称", value=None)

                        # 应用数据源
                        with gr.Row():
                            submit_src_btn = gr.Button(value="应用数据源", variant='primary')

                        # 下拉框选择不同的part、显示当前图像路径
                        with gr.Row():
                            part_selector = gr.Dropdown(
                                label="选择待筛选数据分组",
                                value=None,
                                choices=None,
                            )
                            path_output = gr.Textbox(label="当前图像路径", interactive=False)

                        # 按钮：评分；
                        with gr.Row():
                            rating_btns = []
                            for rating_type in Rating:
                                r_btn = gr.Button(
                                    value=rating_type.value, variant="primary", elem_id=f"btn_{rating_type.value}"
                                )
                                rating_btns.append(r_btn)

                        # 按钮：上一张, 下一张
                        with gr.Row():
                            prev_button = gr.Button(value="上一张", variant="secondary")
                            next_button = gr.Button(value="下一张", variant="secondary")

                        # 方便查看Debug信息
                        with gr.Column():
                            with gr.Row():
                                message_output = gr.Markdown(label="控制台输出", show_label=True)

                # 评分按钮
                for button in rating_btns:
                    button.click(
                        fn=save_rating,
                        inputs=[root_dir, workspace, path_output, button],
                        outputs=[message_output],
                    ).then(
                        fn=next_image,
                        inputs=[root_dir, workspace, part_selector],
                        outputs=[image_output, path_output, message_output],
                    )

                # 上一张按钮
                prev_button.click(
                    fn=prev_image,
                    inputs=[root_dir, workspace, part_selector],
                    outputs=[image_output, path_output, message_output],
                )
                # 下一张按钮
                next_button.click(
                    fn=next_image,
                    inputs=[root_dir, workspace, part_selector],
                    outputs=[image_output, path_output, message_output],
                )

                submit_src_btn.click(
                    fn=init_sample_viewer_manager,
                    inputs=[root_dir, workspace],
                    outputs=[message_output]
                ).then(
                    fn=get_part_list,
                    inputs=[root_dir, workspace],
                    outputs=[part_selector]
                )

                # 当选择不同的part时更新图像
                part_selector.change(
                    fn=next_image,
                    inputs=[root_dir, workspace, part_selector],
                    outputs=[image_output, path_output, message_output],
                )
            with gr.TabItem("样本筛选预览"):
                with gr.Row():
                    # 图片预览
                    with gr.Column():
                        image_preview = gr.Image(label="当前图像", height=896)
                    with gr.Column():
                        # 指定数据源和工作空间
                        with gr.Row():
                            root_dir_preview = gr.Textbox(
                                label="输入数据源路径",
                                interactive=True,
                                value=DEFAULT_ROOT_DIR
                            )
                            workspace_preview = gr.Textbox(
                                label="输入工作空间名称",
                                interactive=True,
                                value=None
                            )

                        # 应用数据源
                        with gr.Row():
                            submit_preview_btn = gr.Button(value="应用数据源", variant='primary')

                        # 下拉框选择不同的part、显示当前图像路径
                        with gr.Row():
                            part_selector_preview = gr.Dropdown(
                                label="选择待筛选数据分组",
                                value=None,
                                choices=None,
                            )
                            path_output_preview = gr.Textbox(label="当前图像路径", interactive=False)
                            filter_preview_label = gr.Dropdown(
                                label="选择待筛选数据分组",
                                value=None,
                                choices=[rating_type.value for rating_type in Rating],
                            )

                        # 按钮：上一张, 下一张
                        with gr.Row():
                            prev_button_preview = gr.Button(value="上一张", variant="secondary")
                            next_button_preview = gr.Button(value="下一张", variant="secondary")

                        # 方便查看Debug信息
                        with gr.Column():
                            with gr.Row():
                                message_output_preview = gr.Markdown(label="控制台输出", show_label=True)

                submit_preview_btn.click(
                    fn=init_sample_viewer_manager,
                    inputs=[root_dir_preview, workspace_preview],
                    outputs=[message_output_preview]
                ).then(
                    fn=get_part_list,
                    inputs=[root_dir_preview, workspace_preview],
                    outputs=[part_selector_preview]
                )

                # 当选择不同的part时更新图像
                next_button_preview.click(
                    fn=next_preview_image,
                    inputs=[root_dir_preview, workspace_preview, part_selector_preview, path_output_preview, filter_preview_label],
                    outputs=[image_preview, path_output_preview, message_output_preview],
                )
                prev_button_preview.click(
                    fn=prev_preview_image,
                    inputs=[root_dir_preview, workspace_preview, part_selector_preview, path_output_preview, filter_preview_label],
                    outputs=[image_preview, path_output_preview, message_output_preview],
                )
                filter_preview_label.change(
                    fn=lambda : gr.update(value=None),
                    inputs=[],
                    outputs=[path_output_preview]
                )


    webui.launch(server_name="0.0.0.0", server_port=5005)


if __name__ == "__main__":
    create_webui()
