import os
from pathlib import Path
from PIL import Image
import warnings

# 忽略特定的警告信息
warnings.filterwarnings("ignore", category=Image.DecompressionBombWarning)
warnings.filterwarnings("ignore", category=UserWarning)

IMAGE_FMT = ['.jpg', '.jpeg', '.JPEG', '.JPG', '.png', '.PNG']

def get_all_image_path(file_dir, path_op=str, recursive=False, pattern=None):
    file_dir = Path(file_dir)
    image_path = []

    if recursive:
        glob_str = '**/*'
    else:
        glob_str = '*'

    if pattern is not None:
        glob_str = f"{glob_str}{pattern}*"

    for p in file_dir.glob(glob_str):
        if p.parent.name.startswith('.'):
            continue
        if p.is_dir():
            continue
        if p.stem.startswith('.'):
            continue
        if p.suffix in IMAGE_FMT:
            image_path.append(path_op(p))

    return image_path

def get_size_distribution(image_paths):
    size_count = {
        '(512~1024, 512~1024)': 0,
        '(>1024, >1024)': 0,
        '其他': 0
    }
    total_images = len(image_paths)

    if total_images == 0:
        print("No images found in this subset directory.")
        return

    for image_path in image_paths:
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                if 512 <= width <= 1024 and 512 <= height <= 1024:
                    size_count['(512~1024, 512~1024)'] += 1
                elif width > 1024 and height > 1024:
                    size_count['(>1024, >1024)'] += 1
                else:
                    size_count['其他'] += 1
        except Exception as e:
            print(f"Error processing {image_path}: {e}")

    for size_range, count in size_count.items():
        percentage = (count / total_images) * 100
        print(f"Range: {size_range}, Count: {count}, Percentage: {percentage:.2f}%")

source_dir = "/Volumes/BigDataSSD/工作数据/去背景相关/Datasets/clean_remove_bg_data"
origin_dataset_dir = os.path.join(source_dir, "clean_stage_2nd")

all_subset_dirs = list(Path(origin_dataset_dir).glob("*"))
for subset_dir in all_subset_dirs:
    if subset_dir.is_dir():
        image_path = get_all_image_path(subset_dir, pattern='*/images/*.jpg')
        print(f"Subset Directory: {subset_dir}")
        get_size_distribution(image_path)
        print("\n")
    else:
        print(f"Skipping non-directory: {subset_dir}")
