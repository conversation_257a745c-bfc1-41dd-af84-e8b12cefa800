#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/1 15:52
# <AUTHOR> <EMAIL>
# @FileName: clean_image_size

import os
from pathlib import Path
from PIL import Image
import warnings

# 忽略特定的警告信息
warnings.filterwarnings("ignore", category=Image.DecompressionBombWarning)
warnings.filterwarnings("ignore", category=UserWarning)

IMAGE_FMT = ['.jpg', '.jpeg', '.JPEG', '.JPG', '.png', '.PNG']

def get_all_image_path(file_dir, path_op=str, recursive=False, pattern=None):
    file_dir = Path(file_dir)
    image_path = []

    if recursive:
        glob_str = '**/*'
    else:
        glob_str = '*'

    if pattern is not None:
        glob_str = f"{glob_str}{pattern}*"

    for p in file_dir.glob(glob_str):
        if p.parent.name.startswith('.'):
            continue
        if p.is_dir():
            continue
        if p.stem.startswith('.'):
            continue
        if p.suffix in IMAGE_FMT:
            image_path.append(path_op(p))

    return image_path

def remove_corresponding_masks(image_path, mask_dir):
    # 尝试删除jpg和png格式的mask文件
    mask_image_path = list(mask_dir.glob(f"{image_path.stem}*"))
    for mask_path in mask_image_path:
        try:
            print(f"Deleting corresponding mask: {mask_path}")
            os.remove(mask_path)
        except Exception as e:
            print(f"Error deleting mask {mask_path}: {e}")

def remove_small_images(image_path_list):
    for image_path in image_path_list:
        image_path = Path(image_path)
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                if 512 <= width <= 1024 and 512 <= height <= 1024:
                    continue
                elif width > 1024 and height > 1024:
                    continue
                else:
                    print(f"Deleting {image_path} (size: {width}x{height})")
                    os.remove(image_path)
                    mask_dir = image_path.parents[1] / 'masks'
                    remove_corresponding_masks(image_path, mask_dir)
        except Exception as e:
            print(f"Error processing {image_path}: {e}")

source_dir = "/Volumes/BigDataSSD/工作数据/去背景相关/Datasets/clean_remove_bg_data"
origin_dataset_dir = os.path.join(source_dir, "clean_stage_2nd")

all_subset_dirs = list(Path(origin_dataset_dir).glob("*"))
for subset_dir in all_subset_dirs:
    if subset_dir.is_dir():
        image_path = get_all_image_path(subset_dir, pattern='*/images/*.jpg')
        remove_small_images(image_path)
    else:
        print(f"Skipping non-directory: {subset_dir}")
