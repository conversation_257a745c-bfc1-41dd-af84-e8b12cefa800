#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/12 15:16
# <AUTHOR> <EMAIL>
# @FileName: select_mask_sample_gui

import os
import glob
import logzero
import tkinter as tk
from PIL import Image, ImageTk
from pathlib import Path

try:
    from tkmacosx import Button
except:
    from tkinter import Button

LOGGER = logzero.setup_logger()


class ImageViewerApp:
    def __init__(self, master, directory):
        self.master = master
        master.title("图像查看器")

        # 初始化变量
        self.directory = directory
        self.part_directories = []
        self.file_lists = {}
        self.current_part = None
        self.current_index = 0
        self.load_directory()

        # 创建画布
        self.canvas = tk.Canvas(master, width=800, height=800)
        self.canvas.pack()

        # 绑定键盘事件
        master.bind("<Left>", self.prev_image)
        master.bind("<Right>", self.next_image)
        master.bind("<space>", lambda e: self.save_and_next("good"))  # 按 Enter 键保存为 "good"

        # 创建按钮
        self.perfect_button = Button(master, text="Perfect", bg="green", command=lambda: self.save_and_next("perfect"))
        self.good_button = Button(master, text="Good", bg="lightgreen", command=lambda: self.save_and_next("good"))
        self.normal_button = Button(master, text="Normal", bg="orange", command=lambda: self.save_and_next("normal"))
        self.bad_button = Button(master, text="Bad", bg="red", command=lambda: self.save_and_next("bad"))
        self.perfect_button.pack(side=tk.LEFT)
        self.good_button.pack(side=tk.LEFT)
        self.normal_button.pack(side=tk.LEFT)
        self.bad_button.pack(side=tk.LEFT)

        # 创建一个 Label 组件来显示图片索引
        self.idx_label = tk.Label(master, text=f"当前图片: {self.current_index}", font=("Arial", 12))
        self.idx_label.pack()

        # 设置文件保存路径
        self.save_path = None
        self.set_save_path()

        # 恢复断点浏览
        self.restore_last_check()

        # 加载显示图片
        self.show_image()

    def load_directory(self):
        # 使用 glob 获取以 part 开头的文件夹，并按名称排序
        self.part_directories = sorted(glob.glob(os.path.join(self.directory, "part*")))

        # 加载每个 part 下的文件列表
        for part_dir in self.part_directories:
            fg_src = []
            for p in list(Path(part_dir).glob("*_fg.png")):
                if not p.stem.startswith('.'):
                    fg_src.append(str(p))
            self.file_lists[part_dir] = fg_src

        # 根据 last_check.txt 恢复当前 part 和 index
        if self.current_part is None:
            self.current_part = self.part_directories[0]
        self.file_list = self.file_lists[self.current_part]

    def update_idx_label(self):
        """更新图片索引标签。"""
        self.idx_label.config(text=f"当前图片: {self.current_index}")

    def show_image(self):
        # 清除之前的图像
        self.canvas.delete("all")

        # 加载图像
        fg_img_path = Path(self.file_list[self.current_index])  # 0_fg.png
        idx = int(fg_img_path.stem.split('_')[0])
        fg_bg_img_path = os.path.join(fg_img_path.parent, f"{idx}_fg_bg.png")

        fg_bg_img = Image.open(fg_bg_img_path).convert('RGB')
        fg_img = Image.open(fg_img_path).convert('RGBA')

        # 计算图像缩放比例，使图像适应画布
        max_size = 800
        scale = min(max_size / fg_bg_img.width, max_size / fg_bg_img.height)
        new_size = (int(fg_bg_img.width * scale), int(fg_bg_img.height * scale))
        fg_bg_img = fg_bg_img.resize(new_size, Image.Resampling.LANCZOS)
        fg_img = fg_img.resize(new_size, Image.Resampling.LANCZOS)

        # 提取 alpha 通道作为 mask，并将其透明度降低一半
        mask = fg_img.getchannel('A').point(lambda p: p * 0.5)
        # 创建一个绿色图像来表示 mask 区域
        color_mask = Image.new('RGBA', fg_img.size, (0, 255, 0, 0))
        color_mask.putalpha(mask)
        # 将绿色 mask 叠加到 fg_bg_img 上
        visualized_img = Image.alpha_composite(fg_bg_img.convert('RGBA'), color_mask)

        # 显示图像
        self.tk_image = ImageTk.PhotoImage(visualized_img)
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.tk_image)

        # 更新图片索引的标签
        self.update_idx_label()

    def prev_image(self, event):
        if self.current_index > 0:
            self.current_index -= 1
            self.show_image()
            self.save_last_check()

        # 更新图片索引的标签
        LOGGER.info(f"prev_image: {self.current_index}")
        self.update_idx_label()

    def next_image(self, event):
        if self.current_index < len(self.file_list) - 1:
            self.current_index += 1
            self.show_image()
            self.save_last_check()
        elif self.current_part is not None and self.current_part != self.part_directories[-1]:
            # 切换到下一个 part
            current_part_index = self.part_directories.index(self.current_part)
            self.current_part = self.part_directories[current_part_index + 1]
            self.file_list = self.file_lists[self.current_part]
            self.current_index = 0
            self.show_image()
            self.save_last_check()

        # 更新图片索引的标签
        LOGGER.info(f"next_image: {self.current_index}")
        self.update_idx_label()

    def save_rating(self, rating):
        filename = '/'.join(Path(self.file_list[self.current_index]).parts[-2::])
        with open(self.save_path, "a") as f:
            f.write(f"{filename} {rating}\n")

    def save_and_next(self, rating):
        self.save_rating(rating)
        self.next_image(None)

    def set_save_path(self):
        if not self.save_path:
            # 提供文件对话框让用户选择已有的记录文件
            # self.save_path = filedialog.askopenfilename(filetypes=[("Text files", "*.txt")])
            if not self.save_path:
                # 如果用户取消了文件对话框，使用默认的 record.txt
                self.save_path = os.path.join(self.directory, "record.txt")

    def restore_last_check(self):
        try:
            with open(os.path.join(self.directory, 'last_check.txt'), 'r') as f:
                part_name, index = f.read().split()
                part_index = [p for p in self.part_directories if p.endswith(part_name)][0]
                self.current_part = part_index
                self.current_index = int(index)
                self.file_list = self.file_lists[self.current_part]
        except FileNotFoundError:
            pass
        except IndexError:
            # 如果找不到对应的 part，则从第一个 part 开始
            self.current_part = self.part_directories[0]
            self.file_list = self.file_lists[self.current_part]
            self.current_index = 0

    def save_last_check(self):
        with open(os.path.join(self.directory, 'last_check.txt'), 'w') as f:
            part_name = os.path.basename(self.current_part)
            f.write(f"{part_name} {self.current_index}")


if __name__ == "__main__":
    root = tk.Tk()
    app = ImageViewerApp(root, "/Volumes/BigDataSSD/工作数据/去背景相关/Datasets/src_1w_gen_10w")
    root.mainloop()