#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/7/3 10:25
# <AUTHOR> <EMAIL>
# @FileName: remanage_dataset

"""
我会给定你一个源目录，src_dir，再给你一个点目标目录, dst_dir
src_dir 的数据组织形式如下：
src_dir
├── images
│   ├── part_0001
│   │   ├── xxx.jpg/png
│   │   ├── xxx.jpg/png
│   │   └── ...
│   ├── part_0002
│   │   ├── xxx.jpg/png
│   │   ├── xxx.jpg/png
│   │   └── ...
│   └── ...
├── labels
│   ├── part_0001
│   │   ├── xxx.json
│   │   ├── xxx.json
│   │   └── ...
│   ├── part_0002
│
├── manifest.json
...

我需要你将src_dir中的数据重新组织到dst_dir中，具体要求如下：
1. 将images目录下的所有图片移动到dst_dir/part_000x目录下
2. 将labels目录下的所有json文件移动到dst_dir/part_000x目录下，但json需要重命名为 xxx_table_annotation.json
3. 不要复制图片以及json，而是用硬链接的方式，节省磁盘空间
"""

import os
import argparse
from pathlib import Path
from typing import List, Tuple, Dict
import random


def get_part_dirs(base_dir: Path) -> List[str]:
    """获取所有part_xxx目录名"""
    if not base_dir.exists():
        return []
    
    part_dirs = []
    for item in base_dir.iterdir():
        if item.is_dir() and item.name.startswith('part_'):
            part_dirs.append(item.name)
    
    return sorted(part_dirs)


def create_hard_link(src_file: Path, dst_file: Path, dry_run: bool = False) -> bool:
    """创建硬链接"""
    try:
        if dry_run:
            # 在dry-run模式下，只打印信息，不实际创建链接
            return True
        
        # 确保目标目录存在
        dst_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 如果目标文件已存在，先删除
        if dst_file.exists():
            dst_file.unlink()
        
        # 创建硬链接
        os.link(src_file, dst_file)
        return True
    except Exception as e:
        print(f"创建硬链接失败: {src_file} -> {dst_file}, 错误: {e}")
        return False


def process_images(src_dir: Path, dst_dir: Path, part_name: str, dry_run: bool = False) -> Tuple[int, int, Dict]:
    """处理图片文件"""
    src_images_dir = src_dir / "images" / part_name
    dst_part_dir = dst_dir / part_name
    
    if not src_images_dir.exists():
        print(f"警告: 源图片目录不存在: {src_images_dir}")
        return 0, 0, {}
    
    success_count = 0
    total_count = 0
    file_mapping = {}
    
    # 支持的图片格式
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    for image_file in src_images_dir.iterdir():
        if image_file.is_file() and image_file.suffix.lower() in image_extensions:
            total_count += 1
            dst_image_file = dst_part_dir / image_file.name
            
            file_mapping[str(image_file)] = str(dst_image_file)
            
            if create_hard_link(image_file, dst_image_file, dry_run):
                success_count += 1
                if not dry_run:
                    print(f"图片链接成功: {image_file.name}")
            else:
                print(f"图片链接失败: {image_file.name}")
    
    return success_count, total_count, file_mapping


def process_labels(src_dir: Path, dst_dir: Path, part_name: str, dry_run: bool = False) -> Tuple[int, int, Dict]:
    """处理标注文件"""
    src_labels_dir = src_dir / "labels" / part_name
    dst_part_dir = dst_dir / part_name
    
    if not src_labels_dir.exists():
        print(f"警告: 源标注目录不存在: {src_labels_dir}")
        return 0, 0, {}
    
    success_count = 0
    total_count = 0
    file_mapping = {}
    
    for label_file in src_labels_dir.iterdir():
        if label_file.is_file() and label_file.suffix.lower() == '.json':
            total_count += 1
            
            # 重命名为 xxx_table_annotation.json
            base_name = label_file.stem
            new_name = f"{base_name}_table_annotation.json"
            dst_label_file = dst_part_dir / new_name
            
            file_mapping[str(label_file)] = str(dst_label_file)
            
            if create_hard_link(label_file, dst_label_file, dry_run):
                success_count += 1
                if not dry_run:
                    print(f"标注文件链接成功: {label_file.name} -> {new_name}")
            else:
                print(f"标注文件链接失败: {label_file.name}")
    
    return success_count, total_count, file_mapping


def remanage_dataset(src_dir: str, dst_dir: str, dry_run: bool = False):
    """重新组织数据集"""
    src_path = Path(src_dir)
    dst_path = Path(dst_dir)
    
    # 检查源目录是否存在
    if not src_path.exists():
        raise FileNotFoundError(f"源目录不存在: {src_dir}")
    
    # 创建目标目录
    if not dry_run:
        dst_path.mkdir(parents=True, exist_ok=True)
    
    # 获取所有part目录
    images_dir = src_path / "images"
    labels_dir = src_path / "labels"
    
    image_parts = get_part_dirs(images_dir)
    label_parts = get_part_dirs(labels_dir)
    
    # 获取所有唯一的part目录
    all_parts = sorted(set(image_parts + label_parts))
    
    if not all_parts:
        print("警告: 没有找到任何part_xxx目录")
        return
    
    print(f"找到 {len(all_parts)} 个part目录: {all_parts}")
    
    total_images_success = 0
    total_images_count = 0
    total_labels_success = 0
    total_labels_count = 0
    
    all_image_mappings = {}
    all_label_mappings = {}
    
    # 处理每个part目录
    for part_name in all_parts:
        print(f"\n处理 {part_name}...")
        
        # 处理图片
        img_success, img_total, img_mappings = process_images(src_path, dst_path, part_name, dry_run)
        total_images_success += img_success
        total_images_count += img_total
        all_image_mappings.update(img_mappings)
        
        # 处理标注
        label_success, label_total, label_mappings = process_labels(src_path, dst_path, part_name, dry_run)
        total_labels_success += label_success
        total_labels_count += label_total
        all_label_mappings.update(label_mappings)
        
        print(f"{part_name} 处理完成: 图片 {img_success}/{img_total}, 标注 {label_success}/{label_total}")
    
    # 在dry-run模式下，显示部分文件的映射情况
    if dry_run and (all_image_mappings or all_label_mappings):
        print("\n" + "="*50)
        print("DRY RUN 模式 - 数据重组预览:")
        print("="*50)
        
        # 显示图片映射示例
        if all_image_mappings:
            print("\n图片文件映射示例 (最多10个):")
            print("-" * 100)
            sample_images = list(all_image_mappings.items())
            random.shuffle(sample_images)
            for i, (src, dst) in enumerate(sample_images[:10]):
                print(f"{i+1}. {src} -> {dst}")
        
        # 显示标注映射示例
        if all_label_mappings:
            print("\n标注文件映射示例 (最多10个):")
            print("-" * 100)
            sample_labels = list(all_label_mappings.items())
            random.shuffle(sample_labels)
            for i, (src, dst) in enumerate(sample_labels[:10]):
                print(f"{i+1}. {src} -> {dst}")
        
        # 显示目标目录结构预览
        print("\n目标目录结构预览:")
        print("-" * 100)
        print(f"{dst_dir}/")
        for part in all_parts[:5]:  # 最多显示5个part目录
            print(f"├── {part}/")
            
            # 随机选择几个图片文件显示
            part_images = [dst for src, dst in all_image_mappings.items() 
                          if f"/{part}/" in dst]
            for img in part_images[:3]:  # 最多显示3个图片
                img_name = Path(img).name
                print(f"│   ├── {img_name}")
            
            if len(part_images) > 3:
                print(f"│   ├── ... (还有 {len(part_images)-3} 个图片文件)")
            
            # 随机选择几个标注文件显示
            part_labels = [dst for src, dst in all_label_mappings.items() 
                          if f"/{part}/" in dst]
            for lbl in part_labels[:3]:  # 最多显示3个标注
                lbl_name = Path(lbl).name
                print(f"│   ├── {lbl_name}")
            
            if len(part_labels) > 3:
                print(f"│   └── ... (还有 {len(part_labels)-3} 个标注文件)")
        
        if len(all_parts) > 5:
            print(f"└── ... (还有 {len(all_parts)-5} 个part目录)")
    
    # 打印总结
    print(f"\n" + "="*50)
    if dry_run:
        print(f"DRY RUN 模式 - 数据重组预览完成!")
    else:
        print(f"数据重组完成!")
    print(f"图片文件: {total_images_success}/{total_images_count} 成功")
    print(f"标注文件: {total_labels_success}/{total_labels_count} 成功")
    print(f"目标目录: {dst_dir}")
    print(f"="*50)


def main():
    parser = argparse.ArgumentParser(description="重新组织数据集目录结构")
    parser.add_argument("src_dir", help="源数据目录路径")
    parser.add_argument("dst_dir", help="目标数据目录路径")
    parser.add_argument("--dry-run", action="store_true", help="仅显示将要执行的操作，不实际执行")
    parser.add_argument("--sample", type=int, default=10, help="在dry-run模式下显示的样本数量")
    
    args = parser.parse_args()
    
    try:
        if args.dry_run:
            print("DRY RUN MODE - 仅显示操作，不实际执行")
            print(f"将从 {args.src_dir} 重组数据到 {args.dst_dir}")
        
        # 设置随机种子，确保结果可重现
        random.seed(42)
        
        remanage_dataset(args.src_dir, args.dst_dir, args.dry_run)
    except Exception as e:
        print(f"错误: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())