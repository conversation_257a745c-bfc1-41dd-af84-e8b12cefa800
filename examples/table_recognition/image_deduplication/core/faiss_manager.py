# -*- coding: utf-8 -*-
# @Time    : 2025/1/17 
# <AUTHOR> <EMAIL>
# @FileName: faiss_manager.py

"""
FAISS索引管理器模块

负责特征索引的构建、保存、加载和相似性搜索。
使用FAISS库实现高效的近似最近邻搜索。
"""

import faiss
import numpy as np
import os
from pathlib import Path
from typing import List, Tuple, Optional, Union
import pickle

from ..config.default_config import FAISS_CONFIG, SIMILARITY_CONFIG
from ..utils.log_manager import LogManager


class FAISSManager:
    """
    FAISS索引管理器类
    
    管理特征向量的索引构建和相似性搜索。
    """
    
    def __init__(self, feature_dim: int, log_manager: Optional[LogManager] = None):
        """
        初始化FAISS管理器
        
        Args:
            feature_dim: 特征向量维度
            log_manager: 日志管理器实例
        """
        self.feature_dim = feature_dim
        self.log_manager = log_manager
        self.index = None
        self.index_type = FAISS_CONFIG['index_type']
        self.is_trained = False
        self.total_vectors = 0
        
        # 存储图片路径，与索引位置对应
        self.image_paths = []
        
        if self.log_manager:
            self.log_manager.info(f"FAISS管理器初始化完成, 特征维度: {feature_dim}, 索引类型: {self.index_type}")
    
    def _create_index(self) -> faiss.Index:
        """
        创建FAISS索引
        
        Returns:
            FAISS索引对象
        """
        if self.index_type == 'IndexFlatIP':
            # 内积索引，适用于归一化特征的余弦相似度
            index = faiss.IndexFlatIP(self.feature_dim)
            
        elif self.index_type == 'IndexFlatL2':
            # L2距离索引
            index = faiss.IndexFlatL2(self.feature_dim)
            
        elif self.index_type == 'IndexIVFFlat':
            # IVF索引，需要训练
            quantizer = faiss.IndexFlatIP(self.feature_dim)
            index = faiss.IndexIVFFlat(quantizer, self.feature_dim, FAISS_CONFIG['nlist'])
            
        elif self.index_type == 'IndexHNSW':
            # HNSW索引
            index = faiss.IndexHNSWFlat(self.feature_dim, FAISS_CONFIG['hnsw_m'])
            index.hnsw.efConstruction = FAISS_CONFIG['ef_construction']
            index.hnsw.efSearch = FAISS_CONFIG['ef_search']
            
        else:
            raise ValueError(f"不支持的索引类型: {self.index_type}")
        
        if self.log_manager:
            self.log_manager.debug(f"创建{self.index_type}索引完成")
        
        return index
    
    def build_index(self, features: np.ndarray, image_paths: List[str]):
        """
        构建特征索引
        
        Args:
            features: 特征向量数组 [n_samples, feature_dim]
            image_paths: 对应的图片路径列表
        """
        if features.shape[0] != len(image_paths):
            raise ValueError("特征数量与图片路径数量不匹配")
        
        if features.shape[1] != self.feature_dim:
            raise ValueError(f"特征维度不匹配: 期望{self.feature_dim}, 实际{features.shape[1]}")
        
        if self.log_manager:
            self.log_manager.start_timer("index_building")
            self.log_manager.debug(f"开始构建索引: {features.shape[0]} 个特征向量")
        
        # 创建索引
        self.index = self._create_index()
        
        # 训练索引（如果需要）
        if hasattr(self.index, 'train'):
            if not self.index.is_trained:
                if self.log_manager:
                    self.log_manager.info("开始训练索引...")
                self.index.train(features)
                self.is_trained = True
                if self.log_manager:
                    self.log_manager.info("索引训练完成")
        
        # 添加特征向量到索引
        self.index.add(features)
        self.total_vectors = features.shape[0]
        self.image_paths = image_paths.copy()
        
        if self.log_manager:
            build_time = self.log_manager.stop_timer("index_building")
            self.log_manager.debug(f"索引构建完成: {self.total_vectors} 个向量, 耗时 {build_time:.2f} 秒")
    
    def add_features(self, features: np.ndarray, image_paths: List[str]):
        """
        增量添加特征到现有索引
        
        Args:
            features: 特征向量数组
            image_paths: 对应的图片路径列表
        """
        if self.index is None:
            self.build_index(features, image_paths)
            return
        
        if features.shape[0] != len(image_paths):
            raise ValueError("特征数量与图片路径数量不匹配")
        
        if features.shape[1] != self.feature_dim:
            raise ValueError(f"特征维度不匹配: 期望{self.feature_dim}, 实际{features.shape[1]}")
        
        # 添加特征向量
        self.index.add(features)
        self.total_vectors += features.shape[0]
        self.image_paths.extend(image_paths)
        
        if self.log_manager:
            self.log_manager.debug(f"增量添加 {features.shape[0]} 个特征向量, 总计 {self.total_vectors} 个")
    
    def search_similar(self, query_features: np.ndarray, k: int = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        搜索相似特征
        
        Args:
            query_features: 查询特征向量 [n_queries, feature_dim]
            k: 返回的最近邻数量，默认使用配置值
            
        Returns:
            (similarities, indices) - 相似度分数和索引
        """
        if self.index is None:
            raise RuntimeError("索引未构建，请先调用build_index")
        
        if k is None:
            k = FAISS_CONFIG['search_k']
        
        # 确保k不超过索引中的向量数量
        k = min(k, self.total_vectors)
        
        if self.log_manager:
            self.log_manager.debug(f"搜索相似向量: {query_features.shape[0]} 个查询, k={k}")
        
        # 执行搜索
        similarities, indices = self.index.search(query_features, k)
        
        return similarities, indices
    
    def find_duplicates(self, similarity_threshold: float = None, progress_callback=None, save_callback=None) -> List[List[int]]:
        """
        查找重复组

        Args:
            similarity_threshold: 相似度阈值，默认使用配置值
            progress_callback: 进度回调函数，接收 (current, total) 参数
            save_callback: 保存回调函数，接收 duplicate_groups 参数

        Returns:
            重复组列表，每个组包含相似图片的索引
        """
        if self.index is None:
            raise RuntimeError("索引未构建，请先调用build_index")

        if similarity_threshold is None:
            similarity_threshold = SIMILARITY_CONFIG['threshold']

        if self.log_manager:
            self.log_manager.start_timer("duplicate_detection")
            self.log_manager.debug(f"开始查找重复图片, 阈值: {similarity_threshold}")
            self.log_manager.debug(f"总共需要检查 {self.total_vectors} 个特征向量")

        duplicate_groups = []
        processed = set()

        # 进度相关变量
        last_progress_log = 0
        save_interval = max(1000, self.total_vectors // 100)  # 每1%或每1000个向量保存一次

        # 逐一检查每个特征向量
        for i in range(self.total_vectors):
            if i in processed:
                continue

            # 搜索与当前向量相似的所有向量
            try:
                query_vector = self.index.reconstruct(int(i)).reshape(1, -1)
                similarities, indices = self.search_similar(query_vector, k=self.total_vectors)
            except Exception as e:
                if self.log_manager:
                    self.log_manager.warning(f"无法重构特征向量 {i}: {e}")
                continue

            # 找到相似度超过阈值的向量
            similar_indices = []
            for sim, idx in zip(similarities[0], indices[0]):
                if sim >= similarity_threshold and idx not in processed:
                    similar_indices.append(idx)
                    processed.add(idx)

            # 如果找到多个相似向量，形成重复组
            if len(similar_indices) > 1:
                duplicate_groups.append(similar_indices)
                if self.log_manager:
                    self.log_manager.debug(f"发现重复组 {len(duplicate_groups)}: {len(similar_indices)} 个相似图片")

            # 进度回调
            if progress_callback:
                progress_callback(i + 1, self.total_vectors)

            # 进度日志（每5%输出一次）
            progress_percent = ((i + 1) / self.total_vectors) * 100
            if progress_percent - last_progress_log >= 5.0:
                if self.log_manager:
                    self.log_manager.info(f"重复检测进度: {i + 1}/{self.total_vectors} ({progress_percent:.1f}%), 已发现 {len(duplicate_groups)} 个重复组")
                last_progress_log = progress_percent

            # 中间结果保存
            if save_callback and (i + 1) % save_interval == 0:
                save_callback(duplicate_groups)
                if self.log_manager:
                    self.log_manager.debug(f"中间结果已保存: {len(duplicate_groups)} 个重复组")

        # 最终保存
        if save_callback:
            save_callback(duplicate_groups)

        if self.log_manager:
            detection_time = self.log_manager.stop_timer("duplicate_detection")
            self.log_manager.info(f"重复检测完成: 发现 {len(duplicate_groups)} 个重复组, 耗时 {detection_time:.2f} 秒")

        return duplicate_groups
    
    def get_image_paths_by_indices(self, indices: List[int]) -> List[str]:
        """
        根据索引获取图片路径
        
        Args:
            indices: 索引列表
            
        Returns:
            图片路径列表
        """
        return [self.image_paths[i] for i in indices if i < len(self.image_paths)]
    
    def save_index(self, save_dir: Union[str, Path]):
        """
        保存索引到文件
        
        Args:
            save_dir: 保存目录
        """
        if self.index is None:
            raise RuntimeError("索引未构建，无法保存")
        
        save_dir = Path(save_dir)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存FAISS索引
        index_file = save_dir / FAISS_CONFIG['index_file']
        faiss.write_index(self.index, str(index_file))
        
        # 保存图片路径映射
        paths_file = save_dir / FAISS_CONFIG['paths_file']
        with open(paths_file, 'wb') as f:
            pickle.dump(self.image_paths, f)
        
        # 保存元数据
        metadata = {
            'feature_dim': self.feature_dim,
            'index_type': self.index_type,
            'total_vectors': self.total_vectors,
            'is_trained': self.is_trained
        }
        
        metadata_file = save_dir / FAISS_CONFIG['metadata_file']
        with open(metadata_file, 'wb') as f:
            pickle.dump(metadata, f)
        
        if self.log_manager:
            self.log_manager.info(f"索引已保存到: {save_dir}")
    
    def load_index(self, load_dir: Union[str, Path]) -> bool:
        """
        从文件加载索引
        
        Args:
            load_dir: 加载目录
            
        Returns:
            是否成功加载
        """
        load_dir = Path(load_dir)
        
        index_file = load_dir / FAISS_CONFIG['index_file']
        paths_file = load_dir / FAISS_CONFIG['paths_file']
        metadata_file = load_dir / FAISS_CONFIG['metadata_file']
        
        # 检查文件是否存在
        if not all(f.exists() for f in [index_file, paths_file, metadata_file]):
            if self.log_manager:
                self.log_manager.warning("索引文件不完整，无法加载")
            return False
        
        try:
            # 加载元数据
            with open(metadata_file, 'rb') as f:
                metadata = pickle.load(f)
            
            # 验证兼容性
            if metadata['feature_dim'] != self.feature_dim:
                if self.log_manager:
                    self.log_manager.error(f"特征维度不匹配: 期望{self.feature_dim}, 文件中为{metadata['feature_dim']}")
                return False
            
            # 加载FAISS索引
            self.index = faiss.read_index(str(index_file))
            
            # 加载图片路径
            with open(paths_file, 'rb') as f:
                self.image_paths = pickle.load(f)
            
            # 恢复状态
            self.index_type = metadata['index_type']
            self.total_vectors = metadata['total_vectors']
            self.is_trained = metadata['is_trained']
            
            if self.log_manager:
                self.log_manager.info(f"成功加载索引: {self.total_vectors} 个向量")
            
            return True
            
        except Exception as e:
            if self.log_manager:
                self.log_manager.error("加载索引失败", e)
            return False
    
    def get_index_info(self) -> dict:
        """
        获取索引信息
        
        Returns:
            索引信息字典
        """
        if self.index is None:
            return {'status': 'not_built'}
        
        return {
            'status': 'built',
            'index_type': self.index_type,
            'feature_dim': int(self.feature_dim),
            'total_vectors': int(self.total_vectors),
            'is_trained': bool(self.is_trained),
            'memory_usage_mb': float(self.index.ntotal * self.feature_dim * 4 / (1024 * 1024))  # 假设float32
        }