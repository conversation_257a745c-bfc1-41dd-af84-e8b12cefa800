#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/17 
# <AUTHOR> <EMAIL>
# @FileName: detect_duplicates.py

"""
独立重复检测脚本

基于已有的FAISS索引进行重复图片检测的独立脚本。
"""

import argparse
import sys
import os
from pathlib import Path

# 添加项目路径到系统路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent.parent
sys.path.append(str(project_root))

from modules.utils.image_utils import get_all_image_path, IMAGE_FMT
from examples.table_recognition.image_deduplication.config.default_config import *
from examples.table_recognition.image_deduplication.utils.log_manager import LogManager
from examples.table_recognition.image_deduplication.utils.file_handler import FileHandler
from examples.table_recognition.image_deduplication.core.duplicate_detector import DuplicateDetector


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='检测重复图片')
    
    parser.add_argument('index_dir', type=str, help='FAISS索引目录')
    parser.add_argument('output_dir', type=str, help='输出目录')
    
    parser.add_argument('--threshold', type=float, default=SIMILARITY_CONFIG['threshold'],
                        help=f'相似度阈值，默认 {SIMILARITY_CONFIG["threshold"]}')
    parser.add_argument('--move-files', action='store_true',
                        help='移动重复文件到输出目录')
    parser.add_argument('--copy-files', action='store_true',
                        help='复制重复文件到输出目录（保留原文件）')
    parser.add_argument('--dry-run', action='store_true',
                        help='模拟运行（不实际移动/复制文件）')
    parser.add_argument('--create-script', action='store_true',
                        help='创建删除脚本')
    
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_args()
    
    # 路径验证
    index_dir = Path(args.index_dir)
    output_dir = Path(args.output_dir)
    
    if not index_dir.exists():
        print(f"错误: 索引目录不存在: {index_dir}")
        sys.exit(1)
    
    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 初始化日志管理器
    log_manager = LogManager(
        log_dir=output_dir / "logs",
        log_level=LOG_CONFIG['level']
    )
    
    log_manager.info("=" * 60)
    log_manager.info("开始重复检测任务")
    log_manager.info(f"索引目录: {index_dir}")
    log_manager.info(f"输出目录: {output_dir}")
    log_manager.info(f"相似度阈值: {args.threshold}")
    log_manager.info(f"模拟运行: {args.dry_run}")
    log_manager.info("=" * 60)
    
    try:
        # 初始化重复检测器
        detector = DuplicateDetector(log_manager=log_manager)
        
        # 加载索引
        log_manager.info("加载FAISS索引...")
        if not detector.load_index(str(index_dir)):
            log_manager.error("索引加载失败")
            return
        
        log_manager.info("索引加载成功")
        
        # 检测重复
        log_manager.info("开始重复检测...")
        duplicate_groups = detector.detect_duplicates(args.threshold)
        
        if not duplicate_groups:
            log_manager.info("未发现重复图片")
            return
        
        # 获取详细信息
        duplicate_groups_with_paths = detector.get_duplicate_groups_with_paths()
        detailed_info = detector.get_detailed_duplicate_info()
        
        # 生成完整报告
        log_manager.info("生成检测报告...")
        report = detector.generate_report()
        
        # 初始化文件处理器
        file_handler = FileHandler(output_dir, log_manager=log_manager)
        
        # 保存报告
        report_file = file_handler.save_report(report)
        log_manager.info(f"检测报告已保存: {report_file}")
        
        # 创建清单文件
        file_handler.create_group_manifest(duplicate_groups_with_paths, detailed_info)
        
        # 打印检测摘要
        detector.print_summary()
        
        # 文件操作
        operation_stats = None
        
        if args.move_files or args.copy_files:
            if args.move_files:
                log_manager.info("开始移动重复文件...")
                operation_stats = file_handler.move_duplicate_groups(
                    duplicate_groups_with_paths, 
                    dry_run=args.dry_run
                )
            else:
                log_manager.info("开始复制重复文件...")
                operation_stats = file_handler.copy_duplicate_groups(
                    duplicate_groups_with_paths, 
                    dry_run=args.dry_run
                )
            
            # 保存操作日志
            if not args.dry_run:
                file_handler.save_operation_log()
        
        # 创建删除脚本
        if args.create_script:
            log_manager.info("创建删除脚本...")
            removal_recommendations = detector.get_recommendations_for_removal()
            script_file = file_handler.create_removal_script(removal_recommendations)
            log_manager.info(f"删除脚本已创建: {script_file}")
        
        # 输出最终统计
        log_manager.info("=" * 60)
        log_manager.info("重复检测任务完成")
        
        stats = detector.duplicate_stats
        log_manager.info(f"总图片数: {stats['total_images']:,}")
        log_manager.info(f"重复组数: {stats['duplicate_groups']:,}")
        log_manager.info(f"重复图片数: {stats['total_duplicates']:,}")
        log_manager.info(f"重复率: {stats['duplicate_rate']:.2%}")
        
        if operation_stats:
            if args.move_files:
                log_manager.info(f"移动文件数: {operation_stats['total_files_moved']:,}")
            else:
                log_manager.info(f"复制文件数: {operation_stats['total_files_copied']:,}")
            log_manager.info(f"成功处理组数: {operation_stats['successful_groups']:,}")
            log_manager.info(f"失败处理组数: {operation_stats['failed_groups']:,}")
        
        log_manager.info(f"输出目录: {output_dir}")
        log_manager.info("=" * 60)
        
        # 清理空目录
        file_handler.cleanup_empty_directories()
        
        # 输出文件摘要
        output_summary = file_handler.get_output_summary()
        log_manager.info("输出文件摘要:")
        for key, value in output_summary.items():
            log_manager.info(f"  {key}: {value}")
        
    except KeyboardInterrupt:
        log_manager.warning("用户中断任务")
        sys.exit(1)
    except Exception as e:
        log_manager.error("重复检测任务失败", e)
        sys.exit(1)


if __name__ == "__main__":
    main() 