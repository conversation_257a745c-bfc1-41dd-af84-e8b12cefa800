#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/6/10 10:02
# <AUTHOR> <EMAIL>
# @FileName: clean_user_table_samples.py

import os
import shutil
import random

from tqdm import tqdm
from pathlib import Path

IMAGE_FMT = ['.jpg', '.jpeg', '.JPEG', '.JPG', '.png', '.PNG']

def get_all_image_path(file_dir, path_op=str, recursive=False, pattern=None):
    file_dir = Path(file_dir)
    image_path = []

    if recursive:
        glob_str = '**/*'
    else:
        glob_str = '*'

    if pattern is not None:
        glob_str = f"{glob_str}{pattern}*"

    for p in file_dir.glob(glob_str):
        if p.parent.name.startswith('.'):
            continue
        if p.is_dir():
            continue
        if p.stem.startswith('.'):
            continue
        if p.suffix in IMAGE_FMT:
            image_path.append(path_op(p))

    return image_path


def clean_user_table_samples():
    # 定义源目录和目标目录
    source_dir = "/aipdf-mlp/xelawk/datasets/public/tmp/table_ocr_to_label"
    target_dir = "/aipdf-mlp/xelawk/datasets/public/tmp/table_ocr_to_label_clean"
    
    # 确保目标目录存在
    os.makedirs(target_dir, exist_ok=True)
    
    # 获取源目录下的所有图片路径
    print(f"正在获取源目录 {source_dir} 下的所有图片...")
    image_paths = get_all_image_path(source_dir, recursive=True)
    random.shuffle(image_paths)
    print(f"共找到 {len(image_paths)} 张图片")
    
    # 每500张图片为一组
    batch_size = 100
    
    # 复制图片到目标目录
    for i, img_path in enumerate(tqdm(image_paths, desc="复制图片")):
        # 计算当前图片应该放在哪个子目录
        batch_index = i // batch_size + 1
        sub_dir_name = f"part_{batch_index:04d}"
        sub_dir_path = os.path.join(target_dir, sub_dir_name)
        
        # 确保子目录存在
        os.makedirs(sub_dir_path, exist_ok=True)
        
        # 获取原图片文件名
        img_filename = os.path.basename(img_path)
        
        # 目标路径
        target_path = os.path.join(sub_dir_path, img_filename)
        
        # 复制图片
        shutil.copy2(img_path, target_path)
    
    print(f"图片复制完成，共处理 {len(image_paths)} 张图片")
    print(f"目标目录: {target_dir}")


if __name__ == "__main__":
    clean_user_table_samples()