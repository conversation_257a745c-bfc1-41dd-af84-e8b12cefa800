#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# @Time    : 2025-07-22 18:40
# <AUTHOR> <EMAIL>
# @FileName: batch_photometric_augmentation.py

"""
批量图像PhotoMetricDistortion增强脚本

功能：
1. 批量处理指定目录中的图片
2. 应用PhotoMetricDistortion变换生成多个增强版本
3. 生成可视化对比图和统计报告
4. 支持命令行参数和进度显示

使用示例：
python batch_photometric_augmentation.py --input_dir ./images --output_dir ./augmented --num_variants 5
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path
from typing import List, Tuple, Dict
import json

import cv2
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm

from my_datasets.table_structure_recognition.table_transforms import PhotoMetricDistortion


class BatchPhotoMetricAugmenter:
    """批量PhotoMetric增强器"""
    
    def __init__(
        self,
        brightness_delta: int = 20,
        contrast_range: Tuple[float, float] = (0.7, 1.3),
        saturation_range: Tuple[float, float] = (0.9, 1.1),
        hue_delta: int = 5
    ):
        """
        初始化增强器
        
        Args:
            brightness_delta: 亮度变化范围
            contrast_range: 对比度变化范围
            saturation_range: 饱和度变化范围
            hue_delta: 色调变化范围
        """
        # 保存初始化参数
        self.brightness_delta = brightness_delta
        self.contrast_range = contrast_range
        self.saturation_range = saturation_range
        self.hue_delta = hue_delta

        self.transform = PhotoMetricDistortion(
            brightness_delta=20,
            contrast_range=[0.7, 1.3],
            saturation_range=saturation_range,
            hue_delta=hue_delta
        )
        
        # 支持的图片格式
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        # 统计信息
        self.stats = {
            'total_images': 0,
            'processed_images': 0,
            'generated_variants': 0,
            'failed_images': 0,
            'start_time': None,
            'end_time': None
        }

        # 记录每个增强图片的参数
        self.augmentation_records = []
        
        # 设置日志
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('batch_augmentation.log')
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _is_image_file(self, file_path: Path) -> bool:
        """检查是否为支持的图片文件"""
        return file_path.suffix.lower() in self.supported_formats
    
    def _load_image(self, image_path: Path) -> np.ndarray:
        """加载图片"""
        try:
            # 使用cv2加载图片
            image = cv2.imread(str(image_path))
            if image is None:
                raise ValueError(f"无法加载图片: {image_path}")
            
            # 转换为RGB格式（PhotoMetricDistortion期望RGB）
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 转换为float32
            image = image.astype(np.float32)
            
            return image
            
        except Exception as e:
            self.logger.error(f"加载图片失败 {image_path}: {e}")
            raise
    
    def _save_image(self, image: np.ndarray, output_path: Path):
        """保存图片"""
        try:
            # 确保图片值在有效范围内
            image = np.clip(image, 0, 255).astype(np.uint8)
            
            # 转换回BGR格式用于保存
            image_bgr = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            
            # 创建输出目录
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存图片
            success = cv2.imwrite(str(output_path), image_bgr)
            if not success:
                raise ValueError(f"保存图片失败: {output_path}")
                
        except Exception as e:
            self.logger.error(f"保存图片失败 {output_path}: {e}")
            raise
    
    def _generate_variants(
        self,
        image: np.ndarray,
        num_variants: int,
        original_filename: str = ""
    ) -> List[Tuple[np.ndarray, Dict]]:
        """生成图片的多个增强版本，并记录使用的参数"""
        variants = []

        for i in range(num_variants):
            try:
                # 创建样本字典（PhotoMetricDistortion期望的格式）
                sample = {'image': image.copy()}

                # 应用变换
                augmented_sample = self.transform(sample)
                augmented_image = augmented_sample['image']

                # 记录使用的参数
                params_used = {
                    'variant_id': i + 1,
                    'original_file': original_filename,
                    'parameter_ranges': {
                        'brightness_delta_range': f"±{self.brightness_delta}",
                        'contrast_range': f"{self.contrast_range[0]}-{self.contrast_range[1]}",
                        'saturation_range': f"{self.saturation_range[0]}-{self.saturation_range[1]}",
                        'hue_delta_range': f"±{self.hue_delta}"
                    }
                }

                # 获取实际使用的参数
                if 'photometric_params' in augmented_sample:
                    actual_params = augmented_sample['photometric_params']
                    params_used['actual_parameters'] = {
                        'brightness_applied': actual_params['brightness_applied'],
                        'brightness_delta': round(actual_params['brightness_delta'], 3) if actual_params['brightness_applied'] else None,
                        'contrast_applied': actual_params['contrast_applied'],
                        'contrast_alpha': round(actual_params['contrast_alpha'], 3) if actual_params['contrast_applied'] else None,
                        'contrast_mode': actual_params['contrast_mode'],
                        'saturation_applied': actual_params['saturation_applied'],
                        'saturation_alpha': round(actual_params['saturation_alpha'], 3) if actual_params['saturation_applied'] else None,
                        'hue_applied': actual_params['hue_applied'],
                        'hue_delta': round(actual_params['hue_delta'], 3) if actual_params['hue_applied'] else None,
                        'channel_swap_applied': actual_params['channel_swap_applied'],
                        'channel_order': actual_params['channel_order'] if actual_params['channel_swap_applied'] else [0, 1, 2]
                    }
                else:
                    params_used['actual_parameters'] = 'Not available (PhotoMetricDistortion did not return parameters)'

                variants.append((augmented_image, params_used))

            except Exception as e:
                self.logger.warning(f"生成第{i+1}个变体失败: {e}")
                continue

        return variants
    
    def process_single_image(
        self, 
        input_path: Path, 
        output_dir: Path, 
        num_variants: int = 5
    ) -> bool:
        """处理单张图片"""
        try:
            self.logger.info(f"处理图片: {input_path.name}")
            
            # 加载图片
            original_image = self._load_image(input_path)
            
            # 生成增强版本
            variants = self._generate_variants(original_image, num_variants, input_path.name)

            if not variants:
                self.logger.warning(f"未能生成任何增强版本: {input_path.name}")
                return False

            # 保存增强版本
            base_name = input_path.stem
            extension = input_path.suffix

            for i, (variant_image, variant_params) in enumerate(variants, 1):
                output_name = f"{base_name}_aug{i}{extension}"
                output_path = output_dir / output_name
                self._save_image(variant_image, output_path)

                # 记录增强参数
                record = {
                    'original_file': input_path.name,
                    'output_file': output_name,
                    'output_path': str(output_path),
                    'parameters': variant_params
                }
                self.augmentation_records.append(record)
            
            # 更新统计
            self.stats['processed_images'] += 1
            self.stats['generated_variants'] += len(variants)
            
            return True
            
        except Exception as e:
            self.logger.error(f"处理图片失败 {input_path}: {e}")
            self.stats['failed_images'] += 1
            return False
    
    def process_directory(
        self, 
        input_dir: Path, 
        output_dir: Path, 
        num_variants: int = 5
    ):
        """批量处理目录中的图片"""
        self.logger.info(f"开始批量处理: {input_dir} -> {output_dir}")
        self.stats['start_time'] = time.time()
        
        # 获取所有图片文件
        image_files = [
            f for f in input_dir.rglob('*') 
            if f.is_file() and self._is_image_file(f)
        ]
        
        self.stats['total_images'] = len(image_files)
        self.logger.info(f"找到 {len(image_files)} 张图片")
        
        if not image_files:
            self.logger.warning("未找到任何图片文件")
            return
        
        # 创建输出目录
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 处理每张图片
        for image_file in tqdm(image_files, desc="处理图片"):
            # 保持相对路径结构
            relative_path = image_file.relative_to(input_dir)
            output_subdir = output_dir / relative_path.parent
            
            self.process_single_image(image_file, output_subdir, num_variants)
        
        self.stats['end_time'] = time.time()
        self.logger.info("批量处理完成")
    
    def generate_comparison_visualization(
        self, 
        input_dir: Path, 
        output_dir: Path, 
        num_samples: int = 6
    ):
        """生成对比可视化图"""
        self.logger.info("生成可视化对比图")
        
        # 获取原始图片
        image_files = [
            f for f in input_dir.rglob('*') 
            if f.is_file() and self._is_image_file(f)
        ]
        
        if not image_files:
            self.logger.warning("未找到原始图片用于可视化")
            return
        
        # 随机选择几张图片进行可视化
        import random
        selected_files = random.sample(
            image_files, 
            min(num_samples, len(image_files))
        )
        
        # 创建可视化
        fig, axes = plt.subplots(len(selected_files), 3, figsize=(15, 5 * len(selected_files)))
        if len(selected_files) == 1:
            axes = axes.reshape(1, -1)
        
        for i, image_file in enumerate(selected_files):
            try:
                # 加载原始图片
                original = self._load_image(image_file)
                
                # 生成一个增强版本用于对比
                variants = self._generate_variants(original, 1, image_file.name)
                if not variants:
                    continue

                augmented, _ = variants[0]  # 解包图像和参数
                
                # 计算差异图
                diff = np.abs(original - augmented)
                
                # 显示图片
                axes[i, 0].imshow(original.astype(np.uint8))
                axes[i, 0].set_title(f"Original: {image_file.name}")
                axes[i, 0].axis('off')

                axes[i, 1].imshow(augmented.astype(np.uint8))
                axes[i, 1].set_title("Augmented")
                axes[i, 1].axis('off')

                axes[i, 2].imshow(diff.astype(np.uint8))
                axes[i, 2].set_title("Difference")
                axes[i, 2].axis('off')
                
            except Exception as e:
                self.logger.warning(f"可视化失败 {image_file}: {e}")
                continue
        
        plt.tight_layout()
        
        # 保存可视化图
        viz_path = output_dir / "augmentation_comparison.png"
        plt.savefig(viz_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"可视化图已保存: {viz_path}")
    
    def generate_report(self, output_dir: Path):
        """生成统计报告"""
        self.logger.info("生成统计报告")
        
        # 计算处理时间
        processing_time = self.stats['end_time'] - self.stats['start_time']
        
        # 创建报告
        report = {
            "处理统计": {
                "总图片数": self.stats['total_images'],
                "成功处理": self.stats['processed_images'],
                "失败数量": self.stats['failed_images'],
                "生成增强版本": self.stats['generated_variants'],
                "成功率": f"{self.stats['processed_images'] / max(1, self.stats['total_images']) * 100:.1f}%"
            },
            "性能统计": {
                "总处理时间": f"{processing_time:.2f}秒",
                "平均每张图片": f"{processing_time / max(1, self.stats['processed_images']):.2f}秒",
                "处理速度": f"{self.stats['processed_images'] / max(1, processing_time):.2f}张/秒"
            },
            "增强参数配置": {
                "brightness_delta": self.brightness_delta,
                "contrast_range": self.contrast_range,
                "saturation_range": self.saturation_range,
                "hue_delta": self.hue_delta
            },
            "详细增强记录": self.augmentation_records
        }
        
        # 保存JSON报告
        report_path = output_dir / "augmentation_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 打印报告
        print("\n" + "="*50)
        print("📊 批量增强统计报告")
        print("="*50)
        for category, stats in report.items():
            print(f"\n{category}:")
            if isinstance(stats, dict):
                for key, value in stats.items():
                    print(f"  {key}: {value}")
            elif isinstance(stats, list):
                print(f"  共记录 {len(stats)} 个增强文件的详细参数")
                print(f"  详细信息请查看JSON报告文件")
            else:
                print(f"  {stats}")
        
        self.logger.info(f"详细报告已保存: {report_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="批量图像PhotoMetricDistortion增强工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python batch_photometric_augmentation.py --input_dir ./images --output_dir ./augmented
  python batch_photometric_augmentation.py -i ./data -o ./output -n 3 --brightness 20
        """
    )
    
    parser.add_argument(
        '--input_dir', '-i',
        type=str,
        required=True,
        help='输入图片目录路径'
    )
    
    parser.add_argument(
        '--output_dir', '-o',
        type=str,
        required=True,
        help='输出目录路径'
    )
    
    parser.add_argument(
        '--num_variants', '-n',
        type=int,
        default=5,
        help='每张图片生成的增强版本数量 (默认: 5)'
    )
    
    parser.add_argument(
        '--brightness',
        type=int,
        default=16,
        help='亮度变化范围 (默认: 16)'
    )
    
    parser.add_argument(
        '--contrast_min',
        type=float,
        default=0.8,
        help='对比度最小值 (默认: 0.8)'
    )
    
    parser.add_argument(
        '--contrast_max',
        type=float,
        default=1.2,
        help='对比度最大值 (默认: 1.2)'
    )
    
    parser.add_argument(
        '--saturation_min',
        type=float,
        default=0.9,
        help='饱和度最小值 (默认: 0.9)'
    )
    
    parser.add_argument(
        '--saturation_max',
        type=float,
        default=1.1,
        help='饱和度最大值 (默认: 1.1)'
    )
    
    parser.add_argument(
        '--hue_delta',
        type=int,
        default=5,
        help='色调变化范围 (默认: 5)'
    )
    
    parser.add_argument(
        '--no_visualization',
        action='store_true',
        help='跳过生成可视化对比图'
    )
    
    args = parser.parse_args()
    
    # 验证路径
    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)
    
    if not input_dir.exists():
        print(f"❌ 输入目录不存在: {input_dir}")
        sys.exit(1)
    
    if not input_dir.is_dir():
        print(f"❌ 输入路径不是目录: {input_dir}")
        sys.exit(1)
    
    # 创建增强器
    augmenter = BatchPhotoMetricAugmenter(
        brightness_delta=args.brightness,
        contrast_range=(args.contrast_min, args.contrast_max),
        saturation_range=(args.saturation_min, args.saturation_max),
        hue_delta=args.hue_delta
    )
    
    print("🚀 开始批量图像增强")
    print(f"📁 输入目录: {input_dir}")
    print(f"📁 输出目录: {output_dir}")
    print(f"🔢 增强版本数: {args.num_variants}")
    print(f"⚙️  增强参数: brightness={args.brightness}, contrast=[{args.contrast_min}, {args.contrast_max}], saturation=[{args.saturation_min}, {args.saturation_max}], hue={args.hue_delta}")
    
    try:
        # 执行批量处理
        augmenter.process_directory(input_dir, output_dir, args.num_variants)
        
        # 生成可视化（如果需要）
        if not args.no_visualization:
            augmenter.generate_comparison_visualization(input_dir, output_dir)
        
        # 生成报告
        augmenter.generate_report(output_dir)
        
        print("\n🎉 批量增强完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断处理")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 处理过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
