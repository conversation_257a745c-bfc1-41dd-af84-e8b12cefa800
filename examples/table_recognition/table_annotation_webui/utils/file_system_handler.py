"""
File System Handler for Table Annotation WebUI System
Provides file system operations and temporary directory management
"""
import os
import shutil
import tempfile
import tarfile
import json
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from pathlib import Path

from .config_manager import config_manager


class FileInfo:
    """File information data class"""
    
    def __init__(self, filename: str, file_path: str, file_size: int = 0, 
                 created_time: Optional[datetime] = None, upload_time: Optional[datetime] = None):
        self.filename = filename
        self.file_path = file_path
        self.file_size = file_size
        self.created_time = created_time or datetime.fromtimestamp(os.path.getctime(file_path)) if os.path.exists(file_path) else None
        self.upload_time = upload_time
    
    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        return {
            'filename': self.filename,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'created_time': self.created_time.isoformat() if self.created_time else None,
            'upload_time': self.upload_time.isoformat() if self.upload_time else None
        }


class FileSystemHandler:
    """File system operations handler"""
    
    def __init__(self):
        """Initialize file system handler"""
        self.config = config_manager
    
    def list_directory_files(self, directory: str, extension: str = ".tar.gz") -> List[FileInfo]:
        """
        List files in directory with specific extension
        
        Args:
            directory: Directory path to scan
            extension: File extension to filter (default: .tar.gz)
            
        Returns:
            List of FileInfo objects
        """
        files = []
        
        if not os.path.exists(directory):
            return files
        
        try:
            for filename in os.listdir(directory):
                if not filename.endswith(extension):
                    continue
                
                if self.config.should_exclude_hidden_files() and filename.startswith('.'):
                    continue
                
                file_path = os.path.join(directory, filename)
                if os.path.isfile(file_path):
                    file_size = os.path.getsize(file_path)
                    created_time = datetime.fromtimestamp(os.path.getctime(file_path))
                    
                    files.append(FileInfo(
                        filename=filename,
                        file_path=file_path,
                        file_size=file_size,
                        created_time=created_time
                    ))
        
        except Exception as e:
            print(f"Error listing directory {directory}: {e}")
        
        # Sort by filename (natural order for part_0001, part_0002, etc.)
        import re
        def natural_sort_key(file_info):
            # Extract numbers from filename for natural sorting
            parts = re.split(r'(\d+)', file_info.filename.lower())
            return [int(part) if part.isdigit() else part for part in parts]

        files.sort(key=natural_sort_key)
        return files
    
    def get_file_size_mb(self, file_path: str) -> float:
        """Get file size in MB"""
        if not os.path.exists(file_path):
            return 0.0
        return os.path.getsize(file_path) / (1024 * 1024)
    
    def format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    def create_temp_directory(self) -> str:
        """
        Create a temporary directory
        
        Returns:
            Path to temporary directory
        """
        temp_base = self.config.get_temp_dir()
        os.makedirs(temp_base, exist_ok=True)
        
        temp_dir = tempfile.mkdtemp(dir=temp_base)
        return temp_dir
    
    def cleanup_temp_directory(self, temp_dir: str) -> bool:
        """
        Clean up temporary directory
        
        Args:
            temp_dir: Path to temporary directory
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            return True
        except Exception as e:
            print(f"Error cleaning up temp directory {temp_dir}: {e}")
            return False
    
    def extract_tar_gz(self, tar_path: str, extract_to: str) -> bool:
        """
        Extract tar.gz file to specified directory
        
        Args:
            tar_path: Path to tar.gz file
            extract_to: Directory to extract to
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with tarfile.open(tar_path, 'r:gz') as tar:
                tar.extractall(path=extract_to)
            return True
        except Exception as e:
            print(f"Error extracting {tar_path}: {e}")
            return False
    
    def move_file(self, src_path: str, dst_path: str) -> bool:
        """
        Move file from source to destination
        
        Args:
            src_path: Source file path
            dst_path: Destination file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure destination directory exists
            dst_dir = os.path.dirname(dst_path)
            os.makedirs(dst_dir, exist_ok=True)
            
            shutil.move(src_path, dst_path)
            return True
        except Exception as e:
            print(f"Error moving file from {src_path} to {dst_path}: {e}")
            return False
    
    def copy_file(self, src_path: str, dst_path: str) -> bool:
        """
        Copy file from source to destination
        
        Args:
            src_path: Source file path
            dst_path: Destination file path
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure destination directory exists
            dst_dir = os.path.dirname(dst_path)
            os.makedirs(dst_dir, exist_ok=True)
            
            shutil.copy2(src_path, dst_path)
            return True
        except Exception as e:
            print(f"Error copying file from {src_path} to {dst_path}: {e}")
            return False
    
    def read_json_file(self, file_path: str) -> Optional[Dict]:
        """
        Read JSON file
        
        Args:
            file_path: Path to JSON file
            
        Returns:
            Dictionary if successful, None otherwise
        """
        try:
            if not os.path.exists(file_path):
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error reading JSON file {file_path}: {e}")
            return None
    
    def write_json_file(self, file_path: str, data: Dict) -> bool:
        """
        Write JSON file
        
        Args:
            file_path: Path to JSON file
            data: Data to write
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure directory exists
            directory = os.path.dirname(file_path)
            os.makedirs(directory, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Error writing JSON file {file_path}: {e}")
            return False
    
    def scan_images_in_directory(self, directory: str) -> List[str]:
        """
        Scan for image files in directory recursively
        
        Args:
            directory: Directory to scan
            
        Returns:
            List of image file paths
        """
        image_files = []
        
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if not self.config.should_process_file(file):
                        continue
                    
                    if self.config.is_image_file(file):
                        image_files.append(os.path.join(root, file))
        
        except Exception as e:
            print(f"Error scanning images in {directory}: {e}")
        
        return image_files
    
    def file_exists(self, file_path: str) -> bool:
        """Check if file exists"""
        return os.path.exists(file_path) and os.path.isfile(file_path)
    
    def directory_exists(self, directory: str) -> bool:
        """Check if directory exists"""
        return os.path.exists(directory) and os.path.isdir(directory)


# Global file system handler instance
file_system_handler = FileSystemHandler()
