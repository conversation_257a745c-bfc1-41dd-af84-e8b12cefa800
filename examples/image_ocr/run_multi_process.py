#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/02/12 15:40
# <AUTHOR> <EMAIL>
# @FileName: run_multi_process.py

import os
import sys
import argparse
import multiprocessing as mp

from pathlib import Path


def process_pdf_dir(args):
    """处理单个PDF目录
    Args:
        args: (source_dir, output_dir, enable_render_images, seed, limit_num, draw_bbox) 元组
    """
    source_dir, output_dir, enable_render_images, seed, limit_num, draw_bbox = args
    
    # 获取脚本所在目录的上级目录（train-anything）
    root_dir = str(Path(__file__).parent.parent.parent)
    script_path = os.path.join("examples", "image_ocr", "pdf_to_ppocrlabel_pdfium.py")
    
    # 设置环境变量
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir
    
    # 构建命令行参数
    cmd = [
        sys.executable,  # 当前Python解释器路径
        script_path,
        "--pdf_dir", source_dir,
        "--output_dir", os.path.join(output_dir, Path(source_dir).name),  # 使用源目录名作为子目录
        "--seed", str(seed),
        "--limit_num", str(limit_num),
    ]
    
    # 如果启用了渲染图片，添加该参数
    if enable_render_images:
        cmd.append("--enable_render_images")
    
    # 如果启用了绘制边界框，添加该参数
    if draw_bbox:
        cmd.append("--draw_bbox")
    
    # 切换到根目录并执行命令
    current_dir = os.getcwd()
    try:
        os.chdir(root_dir)
        os.system(" ".join(cmd))
    finally:
        os.chdir(current_dir)


def main():
    # 直接设置默认参数（方便直接修改）
    seed = 42 + 42
    limit_num = 50
    num_processes = 4
    draw_bbox = False
    enable_render_images = True

    target_dir = "/aipdf-mlp/xelawk/datasets_release_v2/pdf_dataset_v20250213_cvt_v2"
    output_dir = f"/aipdf-mlp/xelawk/datasets_release_v3/auto_labeled_with_image/{Path(target_dir).name}"

    # 解析命令行参数（如果提供）
    parser = argparse.ArgumentParser(description="多进程处理PDF文件")
    parser.add_argument(
        "--target_dir",
        default=None,
        help="输入数据目录"
    )
    parser.add_argument(
        "--output_dir",
        default=None,
        help="输出目录"
    )
    parser.add_argument(
        "--num_processes",
        type=int,
        default=None,
        help="并行处理的进程数"
    )
    parser.add_argument(
        "--enable_render_images",
        action="store_true",
        help="是否渲染图片"
    )
    parser.add_argument(
        "--seed",
        type=int,
        default=None,
        help="随机种子"
    )
    parser.add_argument(
        "--limit_num",
        type=int,
        default=None,
        help="每个PDF文件处理的最大页数"
    )
    parser.add_argument(
        "--draw_bbox",
        action="store_true",
        help="是否绘制边界框"
    )
    
    args = parser.parse_args()
    
    # 如果提供了命令行参数，覆盖默认值
    if args.target_dir is not None:
        target_dir = args.target_dir
    if args.output_dir is not None:
        output_dir = args.output_dir
    if args.num_processes is not None:
        num_processes = args.num_processes
    if args.seed is not None:
        seed = args.seed
    if args.limit_num is not None:
        limit_num = args.limit_num
    # 对于布尔标志，如果在命令行中使用了该标志，则设为True
    if args.enable_render_images:
        enable_render_images = True
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 打印使用的参数
    print(f"处理参数：")
    print(f"  输入目录: {target_dir}")
    print(f"  输出目录: {output_dir}")
    print(f"  进程数量: {num_processes}")
    print(f"  随机种子: {seed}")
    print(f"  渲染图片: {enable_render_images}")
    print(f"  最大页数: {limit_num}")
    print(f"  绘制边界框: {draw_bbox}")
    
    # 准备任务参数
    source_dirs = []
    for p in Path(target_dir).glob('**/part_*'):
        if p.is_dir() and not p.name.startswith('.'):
            source_dirs.append(str(p))
            print(f"Find source_dir: {p}")

    task_args = []
    for source_dir in source_dirs:
        sub_args = (
            source_dir, os.path.join(output_dir, Path(source_dir).parent.name),
            enable_render_images, seed, limit_num, draw_bbox
        )
        task_args.append(sub_args)
        print(f"Task args: {sub_args}")

    # 使用进程池处理
    with mp.Pool(num_processes) as pool:
        pool.map(process_pdf_dir, task_args)


if __name__ == "__main__":
    main()
