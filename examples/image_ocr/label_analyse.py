#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time     : 2024/12/24 14:36
# <AUTHOR> lia<PERSON><PERSON><PERSON><EMAIL>
# @Modified : <EMAIL>
# @FileName : label_analyse.py

import os
import re
import cv2
import json
import numpy as np
from PIL import Image
from Levenshtein import distance as levenshtein_distance


def compare_det_rec_results():
    """比较det_gt_bench.txt和rec_gt_bench.txt中的文本结果"""
    # 读取det_gt_bench.txt
    det_results = {}
    with open('labels/det_gt_bench_revised.txt', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split('\t')
            if len(parts) != 2:
                continue
            image_name = parts[0].split('/')[-1]
            try:
                json_data = json.loads(parts[1])
                texts = [item['transcription'] for item in json_data]
                det_results[image_name] = texts
            except:
                continue

    # 读取rec_gt_bench.txt
    rec_results = {}
    with open('/aipdf-mlp/ouhanqi/data/benchmark/rec/rec_gt_bench.txt', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split('\t')
            if len(parts) != 2:
                continue

            image_path = parts[0].split('/')[-1]
            text = parts[1]

            # 提取原始图片名和裁剪索引
            if '_crop_' not in image_path:
                continue

            base_name = image_path[:image_path.find('_crop_')]
            try:
                crop_idx = int(image_path[image_path.find('_crop_') + 6:image_path.rfind('.')])
                if base_name not in rec_results:
                    rec_results[base_name] = {}
                rec_results[base_name][crop_idx] = text
            except:
                continue

    # 比较结果并输出
    with open('labels/comparison_results_v250318.txt', 'w', encoding='utf-8') as f:
        for img_name in det_results:
            f.write(f"{img_name}\n")  # 写入原始图片名
            det_texts = det_results[img_name]
            rec_texts = []

            img_key = img_name.split('.')[0]
            if img_key in rec_results:
                # 对每个检测文本，查找对应的识别文本
                for i in range(len(det_texts)):
                    if i in rec_results[img_key]:
                        rec_texts.append(rec_results[img_key][i])
                    else:
                        rec_texts.append("None")
            else:
                # 如果在识别结果中找不到这张图片，所有识别文本标记为None
                rec_texts = ["None"] * len(det_texts)

            # 写入对比结果
            idx = 0
            for det_text, rec_text in zip(det_texts, rec_texts):
                if det_text == rec_text:
                    match_type = "match"
                elif rec_text == "None":
                    match_type = "error!"
                else:
                    match_type = "mismatch"
                f.write(f"{str(idx)}\t{det_text}\t{rec_text}\t{match_type}\n")
                idx += 1
            f.write("\n")


def fix_det_gt_bench():
    """根据comparison_results.txt修复det_gt_bench.txt中的标注"""
    # 读取comparison_results.txt，获取需要修改的内容
    fixes = {}  # 格式: {image_name: {idx: correct_text}}
    current_image = None

    with open('labels/comparison_results_v250318.txt', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                current_image = None
                continue

            if '.' in line and '\t' not in line:  # 图片名行
                current_image = line
                fixes[current_image] = {}
            elif current_image and '\t' in line:  # 对比结果行
                idx, det_text, rec_text, status = line.split('\t')
                if status == "mismatch":  # 需要修改的情况
                    fixes[current_image][int(idx)] = rec_text

    # 如果没有需要修改的内容，直接返回
    if not any(fixes.values()):
        print("没有需要修改的内容")
        return

    # 读取并修改det_gt_bench.txt
    fixed_lines = []
    modified_count = 0
    with open('labels/det_gt_bench_revised.txt', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue

            parts = line.split('\t')
            if len(parts) != 2:
                fixed_lines.append(line)
                continue

            image_path = parts[0].split('/')[-1]
            try:
                # 解析JSON并修改需要更正的文本
                json_data = json.loads(parts[1])
                modified = False

                # 检查所有项的difficult属性
                for item in json_data:
                    if item.get('difficult', False) is True:
                        item['difficult'] = False
                        modified = True
                        modified_count += 1

                # 修改需要更正的文本
                if image_path in fixes:
                    for idx, correct_text in fixes[image_path].items():
                        if idx < len(json_data):
                            if json_data[idx]['transcription'] != correct_text:
                                json_data[idx]['transcription'] = correct_text
                                modified = True

                # 重新组合行
                fixed_line = f"{parts[0]}\t{json.dumps(json_data, ensure_ascii=False)}"
                fixed_lines.append(fixed_line)
                if modified:
                    print(f"已修复图片 {image_path} 的标注")
            except json.JSONDecodeError:
                print(f"处理 {image_path} 时出现JSON解析错误")
                fixed_lines.append(line)
            except Exception as e:
                print(f"处理 {image_path} 时出现错误: {str(e)}")
                fixed_lines.append(line)

    # 写入修改后的内容
    with open('labels/det_gt_bench_v250318.txt', 'w', encoding='utf-8') as f:
        for line in fixed_lines:
            f.write(line + '\n')

    print(f"\n总共修改了 {modified_count} 处 difficult=True 的标注")


def concatenate_text_regions(det_gt_path, source_dir, output_dir, rotation_dict=None):
    """
    根据检测标注文件，将每张图像中的文本区域裁剪并按行拼接成新图像
    当文本区域超过20行时，会自动创建新的图片继续拼接

    Args:
        det_gt_path: 检测标注文件路径
        source_dir: 源图像目录路径
        output_dir: 输出目录路径，用于保存拼接图像和标签文件
        rotation_dict: 旋转字典，key是图片文件名，value是旋转角度(90,180,270)
    """
    MAX_REGIONS_PER_IMAGE = 20  # 每张图片最多容纳的文本行数
    rotation_dict = rotation_dict or {}  # 如果没有提供旋转字典，使用空字典

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 读取检测标注文件
    with open(det_gt_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue

            # 解析每一行
            parts = line.split('\t')
            if len(parts) != 2:
                continue

            image_rel_path = parts[0]
            # if 'hard' not in image_rel_path:
            #     break
            # 移除路径中的_final后缀
            image_rel_path = image_rel_path.replace('_final', '')
            image_path = os.path.join(source_dir, image_rel_path)
            if not os.path.exists(image_path):
                print(f"Warning: Image not found: {image_path}")
                continue

            try:
                # 读取图像
                image = cv2.imread(image_path)
                if image is None:
                    print(f"Warning: Failed to read image: {image_path}")
                    continue

                # 解析JSON数据获取文本框和文本
                json_data = json.loads(parts[1])

                # 按y坐标排序文本框，实现自上而下的顺序
                text_regions = []
                for item in json_data:
                    points = np.array(item['points'])
                    y_center = np.mean(points[:, 1])
                    text_regions.append({
                        'points': points,
                        'text': item['transcription'],
                        'y_center': y_center
                    })
                text_regions.sort(key=lambda x: x['y_center'])

                # 将文本区域分组，每组最多MAX_REGIONS_PER_IMAGE个区域
                region_groups = []
                for i in range(0, len(text_regions), MAX_REGIONS_PER_IMAGE):
                    group = text_regions[i:i + MAX_REGIONS_PER_IMAGE]
                    region_groups.append(group)

                # 检查是否需要旋转
                image_name = os.path.basename(image_rel_path)
                need_rotation = image_name in rotation_dict
                rotation_angle = rotation_dict.get(image_name)

                # 处理每一组文本区域
                for group_idx, group in enumerate(region_groups, 1):
                    # 裁剪并拼接文本区域
                    crops = []
                    texts = []
                    padding = 10  # 设置上下间距
                    max_width = 0
                    total_height = 0

                    for region in group:
                        points = region['points']
                        # 获取文本框的边界
                        x_min, y_min = np.min(points, axis=0)
                        x_max, y_max = np.max(points, axis=0)
                        # 确保坐标为整数
                        x_min, y_min = int(x_min), int(y_min)
                        x_max, y_max = int(x_max), int(y_max)
                        # 裁剪文本区域
                        crop = image[y_min:y_max, x_min:x_max]
                        if crop.size == 0:
                            continue

                        # 对裁剪出的文本区域进行旋转
                        if need_rotation:
                            if rotation_angle == 90:
                                crop = cv2.rotate(crop, cv2.ROTATE_90_CLOCKWISE)
                            elif rotation_angle == 180:
                                crop = cv2.rotate(crop, cv2.ROTATE_180)
                            elif rotation_angle == 270:
                                crop = cv2.rotate(crop, cv2.ROTATE_90_COUNTERCLOCKWISE)

                        crops.append(crop)
                        texts.append(region['text'])
                        # 更新最大宽度和总高度
                        max_width = max(max_width, crop.shape[1])
                        total_height += crop.shape[0] + padding

                    if not crops:
                        continue

                    # 创建拼接图像
                    concat_image = np.zeros((total_height, max_width, 3), dtype=np.uint8)
                    concat_image.fill(255)  # 设置白色背景

                    # 拼接图像
                    y_offset = 0
                    for crop in crops:
                        h, w = crop.shape[:2]
                        # 将文本区域居中放置
                        x_offset = (max_width - w) // 2
                        concat_image[y_offset:y_offset + h, x_offset:x_offset + w] = crop
                        y_offset += h + padding

                    # 保持原始的相对路径结构
                    rel_dir = os.path.dirname(image_rel_path)  # 获取相对目录路径
                    if rel_dir:
                        # 在输出目录中创建相应的子目录
                        out_subdir = os.path.join(output_dir, rel_dir)
                        os.makedirs(out_subdir, exist_ok=True)

                    # 保存拼接图像和标签，保持相对路径结构
                    base_name = os.path.splitext(image_rel_path)[0]
                    # 如果有多个图片，添加序号后缀
                    if len(region_groups) > 1:
                        image_output_path = os.path.join(output_dir, f"{base_name}_concat_{group_idx}.jpg")
                        label_output_path = os.path.join(output_dir, f"{base_name}_concat_{group_idx}.txt")
                    else:
                        image_output_path = os.path.join(output_dir, f"{base_name}_concat.jpg")
                        label_output_path = os.path.join(output_dir, f"{base_name}_concat.txt")

                    cv2.imwrite(image_output_path, concat_image)

                    # 保存文本标签
                    with open(label_output_path, 'w', encoding='utf-8') as f_label:
                        for text in texts:
                            f_label.write(f"{text}\n")

                    print(f"Processed: {image_rel_path} - Part {group_idx}/{len(region_groups)}")

            except Exception as e:
                print(f"Error processing {image_rel_path}: {str(e)}")
                continue


def convert_full_to_half(text):
    """将全角字符转换为半角字符，特殊处理冒号：
    1. 先将所有全角字符转换为半角
    2. 然后检查中文后面的冒号，将其改为全角
    """
    # 第一步：全部转换为半角
    result = []
    for char in text:
        code = ord(char)
        # 全角空格
        if code == 0x3000:
            result.append(" ")
        # 全角字符转换为半角字符
        elif 0xFF01 <= code <= 0xFF5E:
            result.append(chr(code - 0xFEE0))
        else:
            result.append(char)

    # 第二步：检查中文后面的冒号，改为全角
    text = ''.join(result)
    result = []
    for i, char in enumerate(text):
        if char == ':':  # 找到半角冒号
            # 检查前一个字符是否是中文
            if i > 0 and 0x4E00 <= ord(text[i - 1]) <= 0x9FFF:
                result.append('：')  # 如果前面是中文，改为全角冒号
            else:
                result.append(char)  # 保持半角冒号
        else:
            result.append(char)

    return ''.join(result)


def convert_train_list_to_txt():
    """将train.list文件转换为txt格式，去除前两个数字，只保留图像名和文本"""
    # 读取train.list文件
    image_text_pairs = []
    with open('labels/train.list', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                # 每行格式：数字1 数字2 图片名 文本
                parts = line.split('\t')
                if len(parts) >= 4:  # 确保有足够的部分
                    image_name = parts[2]
                    # 将文本中的全角字符转换为半角字符
                    text = convert_full_to_half(parts[3])
                    image_text_pairs.append((image_name, text))

    # 写入txt文件
    with open('/aipdf-mlp/shared/ocr_rec_dataset/chn_ocr_scene/train.txt', 'w', encoding='utf-8') as f:
        for image_name, text in image_text_pairs:
            f.write(f"{image_name}\t{text}\n")

    print(f"已将train.list转换为train.txt，共处理{len(image_text_pairs)}个图片文本对")


def normalize_text_for_comparison(text):
    """
    标准化文本用于宽松比较：
    1. 保留英文字母间的空格
    2. 去除其他空格
    3. 将全角括号、冒号、问号、感叹号转换为半角

    Args:
        text: 输入文本

    Returns:
        str: 标准化后的文本
    """
    if not text:
        return ""

    # 将连续的英文字母及其间的空格提取出来
    eng_words = re.findall(r'[a-zA-Z]+(?:\s+[a-zA-Z]+)*', text)
    eng_dict = {}
    for i, word in enumerate(eng_words):
        placeholder = f"__ENG_{i}__"
        text = text.replace(word, placeholder)
        eng_dict[placeholder] = word

    # 去除所有空格
    text = text.replace(" ", "")

    # 全角转半角字符映射
    full_to_half = {
        "（": "(",
        "）": ")",
        "：": ":",
        "？": "?",
        "！": "!"
    }

    # 转换全角为半角
    for full, half in full_to_half.items():
        text = text.replace(full, half)

    # 还原英文单词
    for placeholder, word in eng_dict.items():
        text = text.replace(placeholder, word)

    return text


def calculate_accuracy_and_distance(txt_path):
    """
    计算文本对比的准确率和平均编辑距离，包括严格模式和宽松模式

    Args:
        txt_path: 包含对比结果的txt文件路径，每行格式为"距离\t文本1\t文本2"

    Returns:
        tuple: (严格准确率, 严格平均编辑距离, 宽松准确率, 宽松平均编辑距离)
    """
    total_valid_lines = 0
    strict_exact_matches = 0
    strict_total_distance_ratio = 0.0
    relaxed_exact_matches = 0
    relaxed_total_distance_ratio = 0.0

    with open(txt_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue

            parts = line.split('\t')
            # if len(parts) < 3:
            #     continue

            try:
                # distance = float(parts[0])
                # text1 = parts[1]
                # text2 = parts[2]

                distance = float(parts[1])
                text1 = parts[2]
                text2 = parts[3]

                if not text1:  # 跳过空文本
                    continue

                total_valid_lines += 1

                # 严格模式计算
                if distance == 0:
                    strict_exact_matches += 1
                strict_total_distance_ratio += distance / len(text1) if text1 else 1

                # 宽松模式计算
                if distance != 0:  # 只有当严格距离不为0时才进行宽松比较
                    norm_text1 = normalize_text_for_comparison(text1)
                    norm_text2 = normalize_text_for_comparison(text2)

                    if norm_text1 == norm_text2:
                        relaxed_exact_matches += 1
                        relaxed_total_distance_ratio += 0
                    else:
                        relaxed_distance = levenshtein_distance(norm_text1, norm_text2)
                        relaxed_total_distance_ratio += relaxed_distance / len(norm_text1) if norm_text1 else 1
                else:
                    # 如果严格距离为0，宽松模式也记为完全匹配
                    relaxed_exact_matches += 1
                    relaxed_total_distance_ratio += 0

            except ValueError:
                continue

    if total_valid_lines == 0:
        return 0, 0, 0, 0

    strict_accuracy = strict_exact_matches / total_valid_lines
    strict_avg_distance = strict_total_distance_ratio / total_valid_lines
    relaxed_accuracy = relaxed_exact_matches / total_valid_lines
    relaxed_avg_distance = relaxed_total_distance_ratio / total_valid_lines
    print(strict_accuracy, 1 - strict_avg_distance, relaxed_accuracy, 1 - relaxed_avg_distance)

    return strict_accuracy, strict_avg_distance, relaxed_accuracy, relaxed_avg_distance


def compare_det_rec_txts(gt_txt_path, pred_txt_path, output_path="labels/compare_details.txt",
                         metrics_path="labels/metrics.txt",
                         gt_img_root="", pred_img_root="", threshold=50):
    """
    对比两个检测识别结果txt文件

    Args:
        gt_txt_path: 真实结果txt路径，每行格式为"图像相对路径\t检测识别结果"
        pred_txt_path: 预测结果txt路径，格式同上
        output_path: 保存对比详细信息的输出路径
        metrics_path: 保存评估指标的输出路径
        gt_img_root: 真实结果图片根路径
        pred_img_root: 预测结果图片根路径
        threshold: 行分组的垂直距离阈值，默认50像素

    Returns:
        tuple: (严格准确率, 严格平均编辑距离, 宽松准确率, 宽松平均编辑距离)
    """
    import cv2
    import os

    def get_image_size(img_path):
        """获取图像尺寸"""
        if not os.path.exists(img_path):
            return None, None
        img = cv2.imread(img_path)
        if img is None:
            return None, None
        return img.shape[1], img.shape[0]  # width, height

    def get_box_dimensions(box, img_width=None, img_height=None):
        """获取文本框的宽高，并根据图像尺寸计算相对阈值"""
        points = box['points']
        min_x = min(p[0] for p in points)
        max_x = max(p[0] for p in points)
        min_y = min(p[1] for p in points)
        max_y = max(p[1] for p in points)
        width = max_x - min_x
        height = max_y - min_y
        center_x = (min_x + max_x) / 2
        center_y = (min_y + max_y) / 2

        # 如果有图像尺寸，计算相对阈值
        if img_width and img_height:
            x_threshold = img_width * 0.015
            y_threshold = img_height * 0.015
        else:
            x_threshold = threshold
            y_threshold = threshold

        return width, height, center_x, center_y, x_threshold, y_threshold

    def group_boxes_by_line(boxes, img_width=None, img_height=None):
        """按y坐标对框进行分组（同一行）并返回每行的文本"""
        if not boxes:
            return [], []

        # 分离宽高比小于0.5的文本框
        normal_boxes = []
        narrow_boxes = []
        for box in boxes:
            width, height, _, _, _, _ = get_box_dimensions(box, img_width, img_height)
            if width / height < 0.5:
                narrow_boxes.append(box)
            else:
                normal_boxes.append(box)

        # 处理正常文本框
        normal_texts = []
        if normal_boxes:
            # 按y坐标中心点排序得到行
            sorted_boxes = sorted(normal_boxes, key=lambda x: get_box_dimensions(x, img_width, img_height)[3])

            lines = [[sorted_boxes[0]]]
            _, _, _, last_y, _, y_threshold = get_box_dimensions(sorted_boxes[0], img_width, img_height)

            for box in sorted_boxes[1:]:
                _, _, _, current_y, _, y_threshold = get_box_dimensions(box, img_width, img_height)
                if abs(current_y - last_y) <= y_threshold:
                    lines[-1].append(box)
                else:
                    lines.append([box])
                    last_y = current_y

            # 对每一行内的文本框按最小x和最小y的和从小到大排序，并合并文本
            for line in lines:
                # 使用最小x和最小y的和排序
                sorted_line = sorted(line,
                                     key=lambda x: min(p[0] for p in x['points']) + 2 * min(p[1] for p in x['points']))
                text = "".join(box['transcription'] for box in sorted_line)
                normal_texts.append(text)

        # 处理窄文本框（按列合并）
        column_texts = []
        if narrow_boxes:
            # 按x坐标分组
            sorted_boxes = sorted(narrow_boxes, key=lambda x: get_box_dimensions(x, img_width, img_height)[2])

            # 处理每个窄文本框
            for box in sorted_boxes:
                _, _, current_x, _, x_threshold, _ = get_box_dimensions(box, img_width, img_height)

                # 查找是否有可以合并的组
                merged = False
                for i, group in enumerate(column_texts):
                    _, _, group_x, _, x_threshold, _ = get_box_dimensions(group[0], img_width, img_height)
                    if abs(current_x - group_x) <= x_threshold:
                        column_texts[i].append(box)
                        merged = True
                        break

                # 如果没有可以合并的组，创建新组
                if not merged:
                    column_texts.append([box])

            # 对每组内的文本框按y坐标排序并合并
            texts = []
            for group in column_texts:
                sorted_group = sorted(group, key=lambda x: get_box_dimensions(x, img_width, img_height)[3])
                text = "".join(box['transcription'] for box in sorted_group)
                texts.append(text)
            column_texts = texts

        return normal_texts, column_texts

    def longest_common_substring(s1, s2):
        """计算两个字符串的最长公共子串长度，先进行宽松转换"""
        # 先进行宽松转换
        s1 = normalize_text_for_comparison(s1)
        s2 = normalize_text_for_comparison(s2)

        if not s1 or not s2:
            return 0

        m = len(s1)
        n = len(s2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        max_len = 0

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if s1[i - 1] == s2[j - 1]:
                    dp[i][j] = dp[i - 1][j - 1] + 1
                    max_len = max(max_len, dp[i][j])

        return max_len

    def find_best_match(text, texts, start_idx, used_indices, min_common_len=2):
        """在文本列表中找到最佳匹配的文本"""
        best_idx = -1
        best_common_len = 0
        best_distance = float('inf')

        # 向后搜索
        for i in range(start_idx, len(texts)):
            if i in used_indices:  # 跳过已使用的文本
                continue
            # 先计算编辑距离
            distance = levenshtein_distance(text, texts[i])
            if distance <= 2:  # 编辑距离小于等于2直接匹配
                best_idx = i
                best_distance = distance
                best_common_len = min(len(text), len(texts[i]))  # 使用较短文本长度作为公共长度
                if distance == 0:  # 找到完全匹配就立即返回
                    break
            else:  # 编辑距离大于2时计算最长公共子串
                common_len = longest_common_substring(text, texts[i])
                if common_len >= min_common_len:
                    # 如果有多个满足条件的匹配，选择最长公共子串更长的，或者编辑距离更小的
                    if common_len > best_common_len or (common_len == best_common_len and distance < best_distance):
                        best_common_len = common_len
                        best_distance = distance
                        best_idx = i

        # 如果向后没找到，向前搜索
        if best_idx == -1:
            for i in range(start_idx - 1, -1, -1):
                if i in used_indices:  # 跳过已使用的文本
                    continue
                # 先计算编辑距离
                distance = levenshtein_distance(text, texts[i])
                if distance <= 2:  # 编辑距离小于等于2直接匹配
                    best_idx = i
                    best_distance = distance
                    best_common_len = min(len(text), len(texts[i]))  # 使用较短文本长度作为公共长度
                    if distance == 0:  # 找到完全匹配就立即返回
                        break
                else:  # 编辑距离大于2时计算最长公共子串
                    common_len = longest_common_substring(text, texts[i])
                    if common_len >= min_common_len:
                        # 如果有多个满足条件的匹配，选择最长公共子串更长的，或者编辑距离更小的
                        if common_len > best_common_len or (common_len == best_common_len and distance < best_distance):
                            best_common_len = common_len
                            best_distance = distance
                            best_idx = i

        return best_idx, best_distance, best_common_len

    # 读取真实结果
    gt_results = {}
    with open(gt_txt_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split('\t')
            if len(parts) != 2:
                continue
            image_path = parts[0]
            image_path = "bench/" + image_path.replace("_final", "")
            try:
                result = json.loads(parts[1])
                gt_results[image_path] = result
            except:
                continue

    # 读取预测结果
    pred_results = {}
    with open(pred_txt_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split('\t')
            if len(parts) != 2:
                continue
            image_path, result_str = parts
            try:
                result = json.loads(result_str)
                pred_results[image_path] = result
            except:
                continue

    total_valid_lines = 0
    strict_exact_matches = 0
    strict_total_distance_ratio = 0.0
    relaxed_exact_matches = 0
    relaxed_total_distance_ratio = 0.0

    # 对比每张图片的结果
    with open(output_path, 'w', encoding='utf-8') as out_f:
        for image_path in gt_results:
            if image_path not in pred_results:
                continue

            # 分别获取真实图像和预测图像的尺寸
            gt_img_path = os.path.join(gt_img_root, image_path)
            pred_img_path = os.path.join(pred_img_root, image_path)

            gt_width, gt_height = get_image_size(gt_img_path)
            pred_width, pred_height = get_image_size(pred_img_path)

            # 计算缩放比例
            w_ratio = gt_width / pred_width
            h_ratio = gt_height / pred_height

            gt_boxes = gt_results[image_path]
            pred_boxes = pred_results[image_path]

            # 对预测框进行缩放
            for pred_box in pred_boxes:
                points = pred_box["points"]
                points = [[pts[0] * w_ratio, pts[1] * h_ratio] for pts in points]
                pred_box["points"] = points

            # 写入图片名和分隔线
            out_f.write(f"{image_path}\n")
            out_f.write("-" * 20 + "\n")

            # 分别对真实框和预测框按行分组，使用各自图像的尺寸
            gt_normal_texts, gt_column_texts = group_boxes_by_line(gt_boxes, gt_width, gt_height)
            pred_normal_texts, pred_column_texts = group_boxes_by_line(pred_boxes, gt_width,
                                                                       gt_height)  # 使用gt的尺寸，因为已经缩放了

            # 先对比正常文本
            out_f.write("正常文本对比：\n")
            gt_idx = 0
            pred_idx = 0
            used_pred_indices = set()

            while gt_idx < len(gt_normal_texts):
                gt_text = gt_normal_texts[gt_idx]

                if pred_idx >= len(pred_normal_texts):
                    # 预测文本已用完，剩余真实文本标记为未匹配
                    out_f.write(f"-1\t{gt_text}\t\n")
                    gt_idx += 1
                    continue

                # 搜索最佳匹配
                best_pred_idx, best_distance, best_common_len = find_best_match(gt_text, pred_normal_texts, pred_idx,
                                                                                used_pred_indices)

                if best_pred_idx != -1:  # 不需要检查是否在used_pred_indices中，因为find_best_match已经处理了
                    # 找到满足条件的匹配
                    total_valid_lines += 1
                    best_pred_text = pred_normal_texts[best_pred_idx]
                    out_f.write(f"{best_distance}\t{gt_text}\t{best_pred_text}\n")
                    used_pred_indices.add(best_pred_idx)

                    # 严格模式计算
                    if best_distance == 0:
                        strict_exact_matches += 1
                    strict_total_distance_ratio += best_distance / len(gt_text) if gt_text else 1

                    # 宽松模式计算
                    if best_distance != 0:
                        norm_gt = normalize_text_for_comparison(gt_text)
                        norm_pred = normalize_text_for_comparison(best_pred_text)

                        if norm_gt == norm_pred:
                            relaxed_exact_matches += 1
                            relaxed_total_distance_ratio += 0
                        else:
                            relaxed_distance = levenshtein_distance(norm_gt, norm_pred)
                            relaxed_total_distance_ratio += relaxed_distance / len(norm_gt) if norm_gt else 1
                    else:
                        relaxed_exact_matches += 1
                        relaxed_total_distance_ratio += 0

                    gt_idx += 1
                    if best_pred_idx == pred_idx:
                        pred_idx += 1
                else:
                    # 没找到满足条件的匹配，当前真实文本标记为未匹配
                    out_f.write(f"-1\t{gt_text}\t\n")
                    gt_idx += 1

            # 处理剩余的预测文本
            while pred_idx < len(pred_normal_texts):
                if pred_idx not in used_pred_indices:
                    pred_text = pred_normal_texts[pred_idx]
                    out_f.write(f"-1\t\t{pred_text}\n")
                pred_idx += 1

            # 再对比列文本，使用相同的逻辑
            if gt_column_texts or pred_column_texts:
                out_f.write("\n窄文本对比（按列合并）：\n")
                gt_idx = 0
                pred_idx = 0
                used_pred_indices = set()

                while gt_idx < len(gt_column_texts):
                    gt_text = gt_column_texts[gt_idx]

                    if pred_idx >= len(pred_column_texts):
                        # 预测文本已用完，剩余真实文本标记为未匹配
                        out_f.write(f"-1\t{gt_text}\t\n")
                        gt_idx += 1
                        continue

                    # 搜索最佳匹配
                    best_pred_idx, best_distance, best_common_len = find_best_match(gt_text, pred_column_texts,
                                                                                    pred_idx, used_pred_indices)

                    if best_pred_idx != -1:  # 不需要检查是否在used_pred_indices中，因为find_best_match已经处理了
                        # 找到满足条件的匹配
                        total_valid_lines += 1
                        best_pred_text = pred_column_texts[best_pred_idx]
                        out_f.write(f"{best_distance}\t{gt_text}\t{best_pred_text}\n")
                        used_pred_indices.add(best_pred_idx)

                        # 严格模式计算
                        if best_distance == 0:
                            strict_exact_matches += 1
                        strict_total_distance_ratio += best_distance / len(gt_text) if gt_text else 1

                        # 宽松模式计算
                        if best_distance != 0:
                            norm_gt = normalize_text_for_comparison(gt_text)
                            norm_pred = normalize_text_for_comparison(best_pred_text)

                            if norm_gt == norm_pred:
                                relaxed_exact_matches += 1
                                relaxed_total_distance_ratio += 0
                            else:
                                relaxed_distance = levenshtein_distance(norm_gt, norm_pred)
                                relaxed_total_distance_ratio += relaxed_distance / len(norm_gt) if norm_gt else 1
                        else:
                            relaxed_exact_matches += 1
                            relaxed_total_distance_ratio += 0

                        gt_idx += 1
                        if best_pred_idx == pred_idx:
                            pred_idx += 1
                    else:
                        # 没找到满足条件的匹配，当前真实文本标记为未匹配
                        out_f.write(f"-1\t{gt_text}\t\n")
                        gt_idx += 1

                # 处理剩余的预测文本
                while pred_idx < len(pred_column_texts):
                    if pred_idx not in used_pred_indices:
                        pred_text = pred_column_texts[pred_idx]
                        out_f.write(f"-1\t\t{pred_text}\n")
                    pred_idx += 1

            # 每张图片处理完后添加空行
            out_f.write("\n")

            # break

    if total_valid_lines == 0:
        return 0, 0, 0, 0

    strict_accuracy = strict_exact_matches / total_valid_lines
    strict_avg_distance = strict_total_distance_ratio / total_valid_lines
    relaxed_accuracy = relaxed_exact_matches / total_valid_lines
    relaxed_avg_distance = relaxed_total_distance_ratio / total_valid_lines

    # 保存评估指标
    with open(metrics_path, 'w', encoding='utf-8') as f:
        f.write(f"总行数: {total_valid_lines}\n")
        f.write(f"严格准确率: {strict_accuracy:.4f}\n")
        f.write(f"严格距离相似度: {1 - strict_avg_distance:.4f}\n")
        f.write(f"宽松准确率: {relaxed_accuracy:.4f}\n")
        f.write(f"宽松距离相似度: {1 - relaxed_avg_distance:.4f}\n")

    print(f"总行数: {total_valid_lines}")
    print(f"严格准确率: {strict_accuracy:.4f}, 严格距离相似度: {1 - strict_avg_distance:.4f}")
    print(f"宽松准确率: {relaxed_accuracy:.4f}, 宽松距离相似度: {1 - relaxed_avg_distance:.4f}")

    return strict_accuracy, strict_avg_distance, relaxed_accuracy, relaxed_avg_distance


def compare_det_rec_txts_v2(gt_txt_path, pred_txt_path, output_path="labels/compare_details.txt",
                            metrics_path="labels/metrics.txt",
                            gt_img_root="", pred_img_root="", threshold=50):
    """
    对比两个检测识别结果txt文件，按行分组后再按列排序，得到完整的阅读顺序后计算编辑距离

    Args:
        gt_txt_path: 真实结果txt路径，每行格式为"图像相对路径\t检测识别结果"
        pred_txt_path: 预测结果txt路径，格式同上
        output_path: 保存对比详细信息的输出路径
        metrics_path: 保存评估指标的输出路径
        gt_img_root: 真实结果图片根路径
        pred_img_root: 预测结果图片根路径
        threshold: 行分组的垂直距离阈值，默认50像素

    Returns:
        tuple: (严格平均编辑距离, 宽松平均编辑距离)
    """

    def get_reading_order_text(boxes, img_width=None, img_height=None):
        """获取按阅读顺序排序后的文本"""
        if not boxes:
            return ""

        # 计算相对阈值
        if img_width and img_height:
            x_threshold = img_width * 0.015
            y_threshold = img_height * 0.015
        else:
            x_threshold = threshold
            y_threshold = threshold

        # 按y坐标中心点排序得到行
        sorted_boxes = sorted(boxes, key=lambda x: get_box_dimensions(x, img_width, img_height)[3])

        lines = [[sorted_boxes[0]]]
        _, _, _, last_y, _, y_threshold = get_box_dimensions(sorted_boxes[0], img_width, img_height)

        # 按y坐标分组
        for box in sorted_boxes[1:]:
            _, _, _, current_y, _, y_threshold = get_box_dimensions(box, img_width, img_height)
            if abs(current_y - last_y) <= y_threshold:
                lines[-1].append(box)
            else:
                lines.append([box])
                last_y = current_y

        # 对每一行内的文本框按最小x和最小y的和排序
        result_text = ""
        for line in lines:
            sorted_line = sorted(line,
                                 key=lambda x: min(p[0] for p in x['points']) + 2 * min(p[1] for p in x['points']))
            line_text = "".join(box['transcription'] for box in sorted_line)
            result_text += line_text + "\n"

        return result_text.strip()

    def get_image_size(img_path):
        """获取图像尺寸"""
        if not os.path.exists(img_path):
            return None, None
        img = cv2.imread(img_path)
        if img is None:
            return None, None
        return img.shape[1], img.shape[0]  # width, height

    def get_box_dimensions(box, img_width=None, img_height=None):
        """获取文本框的宽高，并根据图像尺寸计算相对阈值"""
        points = box['points']
        min_x = min(p[0] for p in points)
        max_x = max(p[0] for p in points)
        min_y = min(p[1] for p in points)
        max_y = max(p[1] for p in points)
        width = max_x - min_x
        height = max_y - min_y
        center_x = (min_x + max_x) / 2
        center_y = (min_y + max_y) / 2

        # 如果有图像尺寸，计算相对阈值
        if img_width and img_height:
            x_threshold = img_width * 0.015
            y_threshold = img_height * 0.015
        else:
            x_threshold = threshold
            y_threshold = threshold

        return width, height, center_x, center_y, x_threshold, y_threshold

    # 读取真实结果
    gt_results = {}
    with open(gt_txt_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split('\t')
            if len(parts) != 2:
                continue
            image_path = parts[0]
            image_path = "bench/" + image_path.replace("_final", "")
            boxes = json.loads(parts[1])
            gt_results[image_path] = boxes

    # 读取预测结果
    pred_results = {}
    # invalid_cases = [
    #     '16d2106419f96df35642644006ba7cb1_page003',
    #     '16d2106419f96df35642644006ba7cb1_page004',
    #     'e7e03d3b97cb8406028d964a1928d1ae_page001',
    #     '64aae53ece466f6a6825f677b8c6ff68_page001',
    #     'a1126c1b66b459ba629445d3cbad510c_page001',
    #     'code_page002.png',
    #     'dee0ddded1c64a9a51a490b98ddeec74_page003',
    #     'dee0ddded1c64a9a51a490b98ddeec74_page004',
    #     'dee0ddded1c64a9a51a490b98ddeec74_page005',
    #     'fb4a2246114f74b5b126324edeb5c056_page002',
    #     '1a0c17849a5519054529cd4302ba24e7_page001',
    #     '472ddabc189615fb92b3a7171aeac8fc_page001',
    #     '859321af712b875a9f21ded24455ea9c_page016'
    # ]
    with open(pred_txt_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split('\t')
            if len(parts) != 2:
                continue
            image_path, boxes_str = parts

            # invalid = False
            # for invalid_case in invalid_cases:
            #     if invalid_case in image_path:
            #         invalid = True
            #         break
            # if invalid:
            #     continue

            boxes = json.loads(boxes_str)
            pred_results[image_path] = boxes

    # 统计指标
    total_images = 0
    strict_total_distance_ratio = 0
    relaxed_total_distance_ratio = 0

    # 对比每张图片的结果
    with open(output_path, 'w', encoding='utf-8') as out_f:
        for image_path in gt_results:
            if image_path not in pred_results:
                continue

            total_images += 1

            # 分别获取真实图像和预测图像的尺寸
            gt_img_path = os.path.join(gt_img_root, image_path)
            pred_img_path = os.path.join(pred_img_root, image_path)

            gt_width, gt_height = get_image_size(gt_img_path)
            pred_width, pred_height = get_image_size(pred_img_path)

            # 计算缩放比例
            w_ratio = gt_width / pred_width
            h_ratio = gt_height / pred_height

            gt_boxes = gt_results[image_path]
            pred_boxes = pred_results[image_path]

            # 对预测框进行缩放
            for pred_box in pred_boxes:
                points = pred_box["points"]
                points = [[pts[0] * w_ratio, pts[1] * h_ratio] for pts in points]
                pred_box["points"] = points

            # 获取按阅读顺序排序的文本
            gt_text = get_reading_order_text(gt_boxes, gt_width, gt_height)
            pred_text = get_reading_order_text(pred_boxes, gt_width, gt_height)

            # 计算严格编辑距离
            strict_distance = levenshtein_distance(gt_text, pred_text)
            # 使用真实文本长度归一化
            strict_distance_ratio = strict_distance / len(gt_text) if gt_text else 1
            strict_total_distance_ratio += strict_distance_ratio

            # 计算宽松编辑距离
            norm_gt = normalize_text_for_comparison(gt_text)
            norm_pred = normalize_text_for_comparison(pred_text)

            if norm_gt == norm_pred:
                relaxed_distance = 0
            else:
                relaxed_distance = levenshtein_distance(norm_gt, norm_pred)
            # 使用归一化后的真实文本长度归一化
            relaxed_distance_ratio = relaxed_distance / len(norm_gt) if norm_gt else 1
            relaxed_total_distance_ratio += relaxed_distance_ratio

            # 写入对比结果
            out_f.write(f"{image_path}\n")
            out_f.write("-" * 20 + "\n")
            out_f.write("真实文本：\n")
            out_f.write(gt_text + "\n\n")
            out_f.write("预测文本：\n")
            out_f.write(pred_text + "\n\n")
            out_f.write(f"严格编辑距离：{strict_distance} (归一化: {strict_distance_ratio:.4f})\n")
            out_f.write(f"宽松编辑距离：{relaxed_distance} (归一化: {relaxed_distance_ratio:.4f})\n")
            out_f.write("\n")

    if total_images == 0:
        return 0, 0

    # 计算评估指标
    strict_avg_distance_ratio = strict_total_distance_ratio / total_images
    relaxed_avg_distance_ratio = relaxed_total_distance_ratio / total_images

    # 保存评估指标
    with open(metrics_path, 'w', encoding='utf-8') as f:
        f.write(f"总图片数：{total_images}\n")
        f.write(f"严格平均编辑距离比：{1 - strict_avg_distance_ratio:.4f}\n")
        f.write(f"宽松平均编辑距离比：{1 - relaxed_avg_distance_ratio:.4f}\n")

    return strict_avg_distance_ratio, relaxed_avg_distance_ratio


if __name__ == "__main__":
    # 先生成对比结果
    # compare_det_rec_results()
    # 根据对比结果修复det_gt_bench.txt
    # fix_det_gt_bench()

    # convert_train_list_to_txt()

    # 定义旋转字典
    # rotation_dict = {
    #     "dee0ddded1c64a9a51a490b98ddeec74_page003.png": 180,
    #     "dee0ddded1c64a9a51a490b98ddeec74_page004.png": 180,
    #     "dee0ddded1c64a9a51a490b98ddeec74_page005.png": 180,
    #     "fb4a2246114f74b5b126324edeb5c056_page002.png": 270,
    #     "1a0c17849a5519054529cd4302ba24e7_page001.png": 90,
    #     "472ddabc189615fb92b3a7171aeac8fc_page001.png": 90,
    # }

    # concatenate_text_regions(
    #     "labels/det_gt_bench_v250318.txt",
    #     "/aipdf-mlp/ouhanqi/data/benchmark/det/bench",
    #     "results",
    #     rotation_dict
    # )

    # calculate_accuracy_and_distance("/aipdf-mlp/jiacheng/exp/ocr_benchmark_res/kuake_compare_details_revised.txt")
    # calculate_accuracy_and_distance("/aipdf-mlp/jiacheng/exp/pp_ocr_rec/infer_res_temp/rec_gt_bench_bq_doc_6M_plus.txt")

    # compare_det_rec_txts(
    #     gt_txt_path = "/aipdf-mlp/jiacheng/code/ocr_corpus/labels/det_gt_bench_v250318.txt",
    #     pred_txt_path = "/aipdf-mlp/xelawk/debug/ocr_benchmark_results/baidu_bs_r2048/original/Label.txt",
    #     output_path="/aipdf-mlp/jiacheng/exp/ocr_benchmark_res/baidu_bs_r2048_compare_details.txt",
    #     metrics_path="/aipdf-mlp/jiacheng/exp/ocr_benchmark_res/baidu_bs_r2048_metrics.txt",
    #     gt_img_root="/aipdf-mlp/ouhanqi/data/benchmark/det",
    #     pred_img_root="/aipdf-mlp/xelawk/debug/ocr_benchmark_results/baidu_bs_r2048/original"
    # )
    # compare_det_rec_txts(
    #     gt_txt_path="/aipdf-mlp/jiacheng/code/ocr_corpus/labels/det_gt_bench_v250318.txt",
    #     pred_txt_path="/aipdf-mlp/jiacheng/exp/pp_ocr_rec/vis_rec_base_0318_det_bd_doc_rec/predict_res.txt",
    #     output_path="/aipdf-mlp/jiacheng/exp/ocr_benchmark_res/vis_rec_base_0318_det_bd_doc_rec_compare_details.txt",
    #     metrics_path="/aipdf-mlp/jiacheng/exp/ocr_benchmark_res/vis_rec_base_0318_det_bd_doc_rec_metrics.txt",
    #     gt_img_root="/aipdf-mlp/ouhanqi/data/benchmark/det",
    #     pred_img_root="/aipdf-mlp/ouhanqi/data/benchmark/det"
    # )

    compare_det_rec_txts_v2(
        gt_txt_path = "/aipdf-mlp/jiacheng/code/ocr_corpus/labels/det_gt_bench_v250318.txt",
        pred_txt_path = "/aipdf-mlp/xelawk/debug/ocr_benchmark_results/kuake/original/Label.txt",
        output_path="/aipdf-mlp/jiacheng/exp/ocr_benchmark_res_v2/kuake_compare_details.txt",
        metrics_path="/aipdf-mlp/jiacheng/exp/ocr_benchmark_res_v2/kuake_metrics.txt",
        gt_img_root="/aipdf-mlp/ouhanqi/data/benchmark/det",
        pred_img_root="/aipdf-mlp/xelawk/debug/ocr_benchmark_results/kuake/visualized"
    )
    # compare_det_rec_txts_v2(
    #     gt_txt_path="/aipdf-mlp/jiacheng/code/ocr_corpus/labels/det_gt_bench_v250318.txt",
    #     pred_txt_path="/aipdf-mlp/jiacheng/exp/pp_ocr_rec/vis_rec_base_0318_det_bd_doc_rec/predict_res.txt",
    #     output_path="/aipdf-mlp/jiacheng/exp/ocr_benchmark_res_v2/vis_rec_base_0318_det_bd_doc_rec_compare_details.txt",
    #     metrics_path="/aipdf-mlp/jiacheng/exp/ocr_benchmark_res_v2/vis_rec_base_0318_det_bd_doc_rec_metrics.txt",
    #     gt_img_root="/aipdf-mlp/ouhanqi/data/benchmark/det",
    #     pred_img_root="/aipdf-mlp/ouhanqi/data/benchmark/det"
    # )