import os
import argparse
import yaml
import cv2
import json
import paddle
import torch
import onnxruntime as ort
import numpy as np
from typing import Optional, Dict, List, Union, Tuple
from pathlib import Path
import copy
from PIL import Image, ImageDraw, ImageFont
import time
from tqdm import tqdm

from third_parties import init_third_parties_env
init_third_parties_env()  # 初始化第三方库环境

from opendet.modeling import build_model as build_det_model
from opendet.preprocess import create_operators as openocr_create_operators
from opendet.preprocess import transform as openocr_transform
from opendet.postprocess import build_post_process as openocr_build_post_process

import tools.program as program
from ppocr.utils.save_load import load_model
from ppocr.postprocess import build_post_process
from ppocr.data import create_operators, transform
from ppocr.modeling.architectures import build_model

from paddlex import create_model


class BaseOCR:
    """OCR基类，提供通用功能"""
    
    def __init__(self, config_path: str = None, device_type: str = 'cpu', device_id: int = 0):
        """
        初始化OCR基类
        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
            device_type: 设备类型，'cpu'或'gpu'
            device_id: GPU设备ID
        """
        self.config = self._load_config(config_path) if config_path else None
        self.device = self._set_device(device_type, device_id)
        self.model = None
        self.post_process = None
        self.ops = None
    
    def _load_config(self, config_path: str) -> Dict:
        """加载yaml格式的配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    
    def _set_device(self, device_type: str, device_id: int) -> torch.device:
        """设置运行设备"""
        if device_type.lower() == 'gpu':
            if not torch.cuda.is_available():
                print("警告：GPU不可用，将使用CPU")
                device = torch.device('cpu')
                paddle.set_device('cpu')
            else:
                device = torch.device(f'cuda:{device_id}')
                paddle.set_device(f'gpu:{device_id}')
        else:
            device = torch.device('cpu')
            paddle.set_device('cpu')
        
        print(f"已设置运行设备为: {device}")
        return device
    
    def _load_torch_checkpoint(self, checkpoint_path: str) -> Dict:
        """加载PyTorch模型检查点"""
        return torch.load(checkpoint_path, map_location=torch.device('cpu'))
    
    def set_checkpoint_path(self, model_path: str):
        """设置预训练模型路径"""
        if self.config is not None:
            self.config["Global"]["checkpoints"] = model_path


class OCRDetector(BaseOCR):
    """文本检测器类"""
    
    def __init__(self, config_path: str, framework: str = 'ppocr', device_type: str = 'cpu', device_id: int = 0):
        """
        初始化检测器
        Args:
            config_path: 配置文件路径
            framework: 使用的框架，'ppocr'、'openocr'或'onnx'
            device_type: 设备类型
            device_id: GPU设备ID
        """
        super().__init__(config_path, device_type, device_id)
        self.framework = framework
        self.ort_session = None  # ONNX运行时会话
    
    def init_model(self):
        """初始化检测模型"""
        if self.framework == 'onnx':
            # 根据设备类型设置ONNX运行时的执行提供程序
            providers = ['CPUExecutionProvider'] if self.device == 'cpu' else \
                       [(f'CUDAExecutionProvider', {'device_id': 0})]
            
            # 从配置中获取ONNX模型路径
            model_path = self.config["Global"]["checkpoints"]
            if not model_path or not os.path.exists(model_path):
                raise ValueError("ONNX模型路径未指定或不存在")
                
            # 创建ONNX运行时会话
            self.ort_session = ort.InferenceSession(model_path, providers=providers)
            
            # 创建后处理和预处理
            self.post_process = openocr_build_post_process(self.config["PostProcess"])
            transforms = []
            for op in self.config["Test"]["dataset"]["transforms"]:
                op_name = list(op)[0]
                if "Label" in op_name:
                    continue
                elif op_name == "KeepKeys":
                    op[op_name]["keep_keys"] = ["image", "shape"]
                transforms.append(op)
            self.ops = openocr_create_operators(transforms, self.config["Global"])
            
        elif self.framework == 'openocr':
            self.model = build_det_model(self.config['Architecture'])
            self._load_checkpoint(self.config)
            self.model.eval()
            self.model.to(device=self.device)
            
            # 创建后处理和预处理
            self.post_process = openocr_build_post_process(self.config['PostProcess'], self.config["Global"])
            transforms = []
            for op in self.config['Test']['dataset']['transforms']:
                op_name = list(op)[0]
                if 'Label' in op_name:
                    continue
                elif op_name == 'KeepKeys':
                    op[op_name]['keep_keys'] = ['image', 'shape']
                transforms.append(op)
            self.ops = openocr_create_operators(transforms, self.config["Global"])
        else:
            self.model = build_model(self.config["Architecture"])
            load_model(self.config, self.model)
            self.model.eval()
            
            # 创建后处理和预处理
            self.post_process = build_post_process(self.config["PostProcess"])
            transforms = []
            for op in self.config["Test"]["dataset"]["transforms"]:
                op_name = list(op)[0]
                if "Label" in op_name:
                    continue
                elif op_name == "KeepKeys":
                    op[op_name]["keep_keys"] = ["image", "shape"]
                transforms.append(op)
            self.ops = create_operators(transforms, self.config["Global"])
    
    def _load_checkpoint(self, config: Dict):
        """加载模型检查点"""
        checkpoints = config["Global"].get("checkpoints")
        pretrained_model = config["Global"].get("pretrained_model")
        
        if checkpoints and os.path.exists(checkpoints):
            checkpoint = self._load_torch_checkpoint(checkpoints)
            self.model.load_state_dict(checkpoint["state_dict"], strict=True)
        elif pretrained_model and os.path.exists(pretrained_model):
            checkpoint = self._load_torch_checkpoint(pretrained_model)
            self.model.load_state_dict(checkpoint["state_dict"], strict=False)
    
    def load_det_res(self, det_res_path: str = None) -> Dict:

        det_res_dict = {}

        if det_res_path is not None:
            with open(det_res_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    parts = line.split('\t')
                    if len(parts) != 2:
                        continue
                    image_path, boxes_str = parts

                    boxes = json.loads(boxes_str)
                    boxes_list = []
                    for box in boxes:
                        boxes_list.append(box["points"])
                    det_res_dict[image_path] = boxes_list

        return det_res_dict

    def detect(self, image_path: str) -> np.ndarray:
        """
        对图像进行文本检测
        Args:
            image_path: 图像路径
        Returns:
            检测到的文本框坐标
        """
        # 读取并预处理图像
        if self.framework == 'onnx':
            with open(image_path, 'rb') as f:
                img = f.read()
                data = {'image': img}
            data = openocr_transform(data, self.ops[:1])
            batch = openocr_transform(data, self.ops[1:])
            
            # 准备输入数据
            images = np.expand_dims(batch[0], axis=0)
            shape_list = np.expand_dims(batch[1], axis=0)
            
            # 获取模型输入名称
            input_name = self.ort_session.get_inputs()[0].name
            
            # ONNX模型推理
            preds = self.ort_session.run(None, {input_name: images.astype(np.float32)})[0]
            preds_dict = {
                'maps': torch.from_numpy(preds).to(device=self.device),
            }
            post_result = self.post_process(preds_dict, [None, shape_list])
            
        elif self.framework == 'openocr':
            with open(image_path, 'rb') as f:
                img = f.read()
                data = {'image': img}
            data = openocr_transform(data, self.ops[:1])
            batch = openocr_transform(data, self.ops[1:])
            
            # 模型推理
            images = np.expand_dims(batch[0], axis=0)
            shape_list = np.expand_dims(batch[1], axis=0)
            images = torch.from_numpy(images).to(device=self.device)

            with torch.no_grad():
                preds = self.model(images)
            post_result = self.post_process(preds, [None, shape_list])
        else:
            with open(image_path, "rb") as f:
                img = f.read()
                data = {"image": img}
            batch = transform(data, self.ops)
            
            # 模型推理
            images = np.expand_dims(batch[0], axis=0)
            shape_list = np.expand_dims(batch[1], axis=0)
            
            images = paddle.to_tensor(images, dtype='float32')
            with paddle.no_grad():
                preds = self.model(images)
                post_result = self.post_process(preds, shape_list)
        
        return post_result[0]["points"]


class OCRClassifier(BaseOCR):
    """文本方向分类器类"""
    
    def __init__(self, config_path: str = None, device_type: str = 'cpu', device_id: int = 0):
        """
        初始化分类器
        Args:
            config_path: 配置文件路径，如果为None则使用预训练模型
            device_type: 设备类型
            device_id: GPU设备ID
        """
        super().__init__(config_path, device_type, device_id)
    
    def init_model(self):
        """初始化分类模型"""
        self.model = create_model(
            model_name=self.config["Global"]["model_name"],
            model_dir="/aipdf-mlp/shared/ocr_pretrained/PP-LCNet_x0_25_textline_ori_pretrained",
            device='gpu:0',
        )
    
    def classify(self, img_list: List[np.ndarray], save_path: str = None) -> Tuple[List[str], List[float]]:
        """
        对图像列表进行方向分类
        Args:
            img_list: 图像列表
            save_path: 中间结果保存路径
        Returns:
            angles: 旋转角度列表
            scores: 置信度列表
        """

        if save_path:
            for idx, img in enumerate(img_list):
                os.makedirs(save_path, exist_ok=True)
                cv2.imwrite(os.path.join(save_path, f"{idx}.jpg"), img)


        angles = []
        rotate_scores = []

        output = self.model.predict(img_list, batch_size=1)
        for res in output:
            rotate_score = res["scores"][0]
            pred_cls = res['label_names'][0]

            angle = '180' if '180' in pred_cls else '0'

            angles.append(angle)
            rotate_scores.append(rotate_score)

        
        return angles, rotate_scores


class OCRRecognizer(BaseOCR):
    """文本识别器类"""
    
    def __init__(self, config_path: str, device_type: str = 'cpu', device_id: int = 0):
        """
        初始化识别器
        Args:
            config_path: 配置文件路径
            device_type: 设备类型
            device_id: GPU设备ID
        """
        super().__init__(config_path, device_type, device_id)
    
    def init_model(self):
        """初始化识别模型"""
        # 构建后处理器
        self.post_process = build_post_process(self.config["PostProcess"], self.config["Global"])
        
        # 设置字符数
        if hasattr(self.post_process, "character"):
            char_num = len(getattr(self.post_process, "character"))
            if self.config["Architecture"]["algorithm"] in [
                "Distillation",
            ]:  # distillation model
                for key in self.config["Architecture"]["Models"]:
                    if (self.config["Architecture"]["Models"][key]["Head"]["name"] == "MultiHead"):  # multi head
                        out_channels_list = {}
                        if self.config["PostProcess"]["name"] == "DistillationSARLabelDecode":
                            char_num = char_num - 2
                        if self.config["PostProcess"]["name"] == "DistillationNRTRLabelDecode":
                            char_num = char_num - 3
                        out_channels_list["CTCLabelDecode"] = char_num
                        out_channels_list["SARLabelDecode"] = char_num + 2
                        out_channels_list["NRTRLabelDecode"] = char_num + 3
                        self.config["Architecture"]["Models"][key]["Head"][
                            "out_channels_list"
                        ] = out_channels_list
                    else:
                        self.config["Architecture"]["Models"][key]["Head"][
                            "out_channels"
                        ] = char_num
            elif self.config["Architecture"]["Head"]["name"] == "MultiHead":  # multi head
                out_channels_list = {}
                char_num = len(getattr(self.post_process, "character"))
                if self.config["PostProcess"]["name"] == "SARLabelDecode":
                    char_num = char_num - 2
                if self.config["PostProcess"]["name"] == "NRTRLabelDecode":
                    char_num = char_num - 3
                out_channels_list["CTCLabelDecode"] = char_num
                out_channels_list["SARLabelDecode"] = char_num + 2
                out_channels_list["NRTRLabelDecode"] = char_num + 3
                self.config["Architecture"]["Head"]["out_channels_list"] = out_channels_list
            else:  # base rec model
                self.config["Architecture"]["Head"]["out_channels"] = char_num
        
        # 构建模型
        self.model = build_model(self.config["Architecture"])
        load_model(self.config, self.model)
        self.model.eval()
        
        # 创建预处理操作
        transforms = []
        for op in self.config["Test"]["dataset"]["transforms"]:
            op_name = list(op)[0]
            if "Label" in op_name:
                continue
            elif op_name in ["RecResizeImg"]:
                op[op_name]["infer_mode"] = True
            elif op_name == "KeepKeys":
                if self.config["Architecture"]["algorithm"] == "SRN":
                    op[op_name]["keep_keys"] = [
                        "image",
                        "encoder_word_pos",
                        "gsrm_word_pos",
                        "gsrm_slf_attn_bias1",
                        "gsrm_slf_attn_bias2",
                    ]
                elif self.config["Architecture"]["algorithm"] == "SAR":
                    op[op_name]["keep_keys"] = ["image", "valid_ratio"]
                elif self.config["Architecture"]["algorithm"] == "RobustScanner":
                    op[op_name]["keep_keys"] = ["image", "valid_ratio", "word_positons"]
                else:
                    op[op_name]["keep_keys"] = ["image"]
            transforms.append(op)
        
        self.config["Global"]["infer_mode"] = True
        self.config["Global"]["infer_auto"] = False
        if "Train" in self.config and "sampler" in self.config["Train"] and "scales" in self.config["Train"]["sampler"]:
            self.config["Global"]["scales"] = self.config["Train"]["sampler"]["scales"]
        self.ops = create_operators(transforms, self.config["Global"])
    
    def recognize(self, 
                 img_list: List[np.ndarray],
                 rotate_angles: List[str],
                 rotate_scores: List[float],
                 repeat_rotate: bool = True,
                 rotate_conf_threshold: float = 0.9,
                 rec_threshold: float = 0.75
                ) -> List[Dict]:
        """
        识别图像列表中的文本
        Args:
            img_list: 图像列表
            rotate_angles: 旋转角度列表
            rotate_scores: 旋转置信度列表
            repeat_rotate: 是否进行多次旋转尝试
            rotate_conf_threshold: 旋转置信度阈值
            rec_threshold: 识别置信度阈值
        Returns:
            识别结果列表，每项包含文本, 置信度以及是否旋转的标志
        """
        rec_res = []
        for img, rotate_angle, rotate_score in zip(img_list, rotate_angles, rotate_scores):
            origin_image_backup = img.copy()
            
            try_time = 0
            rotate_tag = False
            best_text = ""
            best_score = 0.
            
            while True:
                if try_time == 0 and rotate_angle == '180':
                    img = cv2.rotate(origin_image_backup, cv2.ROTATE_180)
                    rotate_tag = True
                if try_time == 1:
                    if rotate_tag:
                        img = origin_image_backup
                    else:
                        img = cv2.rotate(origin_image_backup, cv2.ROTATE_180)
                        rotate_tag = True
                if try_time == 2:
                    img = cv2.rotate(origin_image_backup, cv2.ROTATE_90_CLOCKWISE)
                    h, w, c = img.shape
                    if h/w > 1.5:
                        break
                if try_time == 3:
                    img = cv2.rotate(origin_image_backup, cv2.ROTATE_90_COUNTERCLOCKWISE)
                
                # 将图像转换为字节流
                success, encoded_img = cv2.imencode('.jpg', img)
                if not success:
                    print(f"警告：图像编码失败，跳过处理")
                    continue
                img_bytes = encoded_img.tobytes()
                
                data = {"image": img_bytes}
                batch = transform(data, self.ops)
                images = np.expand_dims(batch[0], axis=0)
                images = paddle.to_tensor(images)
                
                with paddle.no_grad():
                    preds = self.model(images)
                    rec_result = self.post_process(preds)
                
                text = rec_result[0][0]
                rec_score = float(rec_result[0][1])
                
                if rec_score > best_score:
                    best_text = text
                    best_score = rec_score
                
                if repeat_rotate:
                    if try_time == 0:
                        if rotate_score < rotate_conf_threshold or rec_score < rec_threshold:
                            try_time += 1
                            continue
                        else:
                            break
                    else:
                        try_time += 1
                        if try_time > 3:
                            break
                else:
                    break
            
            rec_res.append({
                "text": best_text,
                "confidence": best_score,
                "rotate_try": rotate_tag
            })
        
        return rec_res


def parse_args():
    """命令行参数解析"""
    parser = argparse.ArgumentParser(description='OCR Pipeline Inference')
    parser.add_argument('--det_config', type=str, required=True,
                      help='检测模型配置文件路径')
    parser.add_argument('--det_model_path', type=str, required=True,
                      help='检测模型权重文件路径')
    parser.add_argument('--cls_config', type=str, required=True,
                      help='方向分类模型配置文件路径')
    parser.add_argument('--cls_model_path', type=str, required=True,
                      help='方向分类模型权重文件路径')
    parser.add_argument('--rec_config', type=str, required=True,
                      help='识别模型配置文件路径')
    parser.add_argument('--rec_model_path', type=str, required=True,
                      help='识别模型权重文件路径')
    parser.add_argument('--input_dir', type=str, required=True,
                      help='输入图像路径或图像目录')
    parser.add_argument('--output_dir', type=str, default='./output',
                      help='结果保存目录')
    parser.add_argument('--repeat_rotate', type=bool, default=True,
                        help='是否启用多次旋转的后处理')
    parser.add_argument('--rotate_conf_threshold', type=float, default=0.9,
                        help='旋转置信度阈值')
    parser.add_argument('--rec_conf_threshold', type=float, default=0.75,
                        help='识别置信度阈值')
    parser.add_argument('--device', type=str, default='gpu',
                        help='运行设备类型，可选 cpu 或 gpu')
    parser.add_argument('--device_id', type=int, default=0,
                        help='GPU设备ID（当device为gpu时有效）')
    parser.add_argument('--det_framework', type=str, default='ppocr',
                        help='检测框架，可选 ppocr、openocr 或 onnx')
    parser.add_argument('--det_res_path', type=str, default=None,
                        help='指定检测结果，如果有则跳过检测直接使用已有结果')
    return parser.parse_args()


def get_image_paths(input_path: str) -> List[str]:
    """获取输入路径下的所有图像文件路径"""
    image_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    image_paths = []
    
    if os.path.isfile(input_path):
        ext = os.path.splitext(input_path)[1].lower()
        if ext in image_formats:
            image_paths.append(input_path)
    else:
        for root, _, files in os.walk(input_path):
            for file in files:
                ext = os.path.splitext(file)[1].lower()
                if ext in image_formats:
                    image_paths.append(os.path.join(root, file))
    
    return sorted(image_paths)


def draw_chinese_text(img, text, position, text_color=(0, 0, 255), text_size=20):
    """使用PIL绘制中文文本"""
    img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(img_pil)
    fontStyle = ImageFont.truetype(
        "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common/sys_Deng.ttf",
        text_size,
        encoding="utf-8"
    )
    draw.text(position, text, text_color, font=fontStyle)
    return cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)


def sorted_boxes(dt_boxes):
    """将文本框按照从上到下、从左到右的顺序排序"""
    num_boxes = dt_boxes.shape[0]
    sorted_boxes = sorted(dt_boxes, key=lambda x: (x[0][1], x[0][0]))
    _boxes = list(sorted_boxes)
    
    for i in range(num_boxes - 1):
        for j in range(i, -1, -1):
            if abs(_boxes[j + 1][0][1] - _boxes[j][0][1]) < 10 and (
                _boxes[j + 1][0][0] < _boxes[j][0][0]
            ):
                tmp = _boxes[j]
                _boxes[j] = _boxes[j + 1]
                _boxes[j + 1] = tmp
            else:
                break
    return _boxes


def get_minarea_rect_crop(img, points):
    """获取最小外接矩形的裁剪图像"""
    bounding_box = cv2.minAreaRect(np.array(points).astype(np.int32))
    points = sorted(list(cv2.boxPoints(bounding_box)), key=lambda x: x[0])
    
    # 重新排列四个顶点的顺序
    index_a, index_b, index_c, index_d = 0, 1, 2, 3
    if points[1][1] > points[0][1]:
        index_a = 0
        index_d = 1
    else:
        index_a = 1
        index_d = 0
    if points[3][1] > points[2][1]:
        index_b = 2
        index_c = 3
    else:
        index_b = 3
        index_c = 2
    
    box = [points[index_a], points[index_b], points[index_c], points[index_d]]
    crop_img = get_rotate_crop_image(img, np.array(box))
    return crop_img


def get_rotate_crop_image(img, points):
    """获取旋转后的裁剪图像"""
    assert len(points) == 4, "文本框顶点数量必须为4"
    img_crop_width = int(
        max(
            np.linalg.norm(points[0] - points[1]),
            np.linalg.norm(points[2] - points[3])
        )
    )
    img_crop_height = int(
        max(
            np.linalg.norm(points[0] - points[3]),
            np.linalg.norm(points[1] - points[2])
        )
    )
    pts_std = np.float32([
        [0, 0],
        [img_crop_width, 0],
        [img_crop_width, img_crop_height],
        [0, img_crop_height],
    ])
    M = cv2.getPerspectiveTransform(points, pts_std)
    dst_img = cv2.warpPerspective(
        img,
        M,
        (img_crop_width, img_crop_height),
        borderMode=cv2.BORDER_REPLICATE,
        flags=cv2.INTER_CUBIC,
    )
    dst_img_height, dst_img_width = dst_img.shape[0:2]
    if dst_img_height * 1.0 / dst_img_width >= 1.5:
        dst_img = np.rot90(dst_img)
    return dst_img


def main():
    """主函数入口"""
    args = parse_args()
    
    # 获取所有需要处理的图像路径
    image_paths = get_image_paths(args.input_dir)
    if not image_paths:
        print(f"错误：在 {args.input_dir} 中没有找到支持的图像文件")
        print(f"支持的图像格式：jpg, jpeg, png, bmp, tiff, webp")
        return
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化三个模型
    detector = OCRDetector(args.det_config, args.det_framework, args.device, args.device_id)
    detector.set_checkpoint_path(args.det_model_path)
    detector.init_model()
    det_res_dict = detector.load_det_res(args.det_res_path)
    
    classifier = OCRClassifier(args.cls_config, args.device, args.device_id)
    classifier.set_checkpoint_path(args.cls_model_path)
    classifier.init_model()
    
    recognizer = OCRRecognizer(args.rec_config, args.device, args.device_id)
    recognizer.set_checkpoint_path(args.rec_model_path)
    recognizer.init_model()
    
    # 统计总运行时间
    total_det_time = 0.0
    total_cls_time = 0.0
    total_rec_time = 0.0
    total_time = 0.0
    
    # 处理每张图像
    with open(os.path.join(args.output_dir, 'predict_res.txt'), "w") as f:
        for image_path in tqdm(image_paths):
            print(f"正在处理图像: {image_path}")
            rel_path = os.path.relpath(image_path, args.input_dir)
            
            # 读取原始图像
            ori_im = cv2.imread(image_path)
            if ori_im is None:
                print(f"警告：无法读取图像 {image_path}，跳过处理")
                continue
            
            start_time = time.time()
            
            # 文本检测
            det_start = time.time()
            if len(det_res_dict) > 0:
                dt_boxes = det_res_dict[rel_path]
            else:
                dt_boxes = detector.detect(image_path)

            det_time = time.time() - det_start
            total_det_time += det_time

            
            if dt_boxes is None or len(dt_boxes) == 0:
                print(f"警告：在图像 {image_path} 中未检测到文本")
                continue

            # 对检测框进行排序
            dt_boxes = sorted_boxes(np.array(dt_boxes))
            
            # 裁剪检测框
            img_crop_list = []
            for bno in range(len(dt_boxes)):
                tmp_box = copy.deepcopy(dt_boxes[bno])
                img_crop = get_minarea_rect_crop(ori_im, tmp_box)
                img_crop_list.append(img_crop)
            
            # 方向分类
            cls_start = time.time()
            crop_save_path = os.path.join(args.output_dir, 'crop_vis', os.path.dirname(rel_path))
            crop_save_path = os.path.join(crop_save_path, os.path.basename(image_path).split('.')[0])
            cls_angles, rotate_scores = classifier.classify(
                img_crop_list,
                crop_save_path
            )
            cls_time = time.time() - cls_start
            total_cls_time += cls_time
            
            # 文本识别
            rec_start = time.time()
            rec_results = recognizer.recognize(
                img_crop_list,
                cls_angles,
                rotate_scores,
                args.repeat_rotate,
                args.rotate_conf_threshold,
                args.rec_conf_threshold
            )
            assert len(img_crop_list) == len(rec_results)
            single_image_res = []
            for idx in range(len(rec_results)):
                res_dict = {
                    "transcription": rec_results[idx]['text'],
                    "points": dt_boxes[idx].tolist(),
                }
                single_image_res.append(res_dict)

            rec_time = time.time() - rec_start
            total_rec_time += rec_time
            
            total_time_per_image = time.time() - start_time
            total_time += total_time_per_image
            
            # 输出每张图片的处理时间
            print(f"\n图像 {os.path.basename(image_path)} 处理时间统计：")
            print(f"  检测耗时: {det_time:.3f}s")
            print(f"  方向分类耗时: {cls_time:.3f}s")
            print(f"  文字识别耗时: {rec_time:.3f}s")
            print(f"  总耗时: {total_time_per_image:.3f}s")
            
            # 可视化结果
            ori_im_with_results = ori_im.copy()
            for box, rec_result in zip(dt_boxes, rec_results):
                box = np.array(box).astype(np.int32).reshape(-1, 2)
                cv2.polylines(ori_im_with_results, [box], True, (0, 255, 0), 2)
                
                text_x = np.min(box[:, 0])
                text_y = np.min(box[:, 1]) - 20
                color = (255, 0, 0) if not rec_result['rotate_try'] else (0, 0, 255)
                
                ori_im_with_results = draw_chinese_text(
                    ori_im_with_results,
                    rec_result["text"],
                    (text_x, text_y),
                    color
                )
            
            # 保存结果
            vis_save_path = os.path.join(args.output_dir, 'vis', os.path.dirname(rel_path))
            os.makedirs(vis_save_path, exist_ok=True)
            cv2.imwrite(os.path.join(vis_save_path, os.path.basename(image_path)), ori_im_with_results)
            
            # 写入识别结果
            f.write(image_path.split('det/')[1] + "\t")
            json.dump(single_image_res, f, ensure_ascii=False)
            f.write('\n')
    
    # 输出总运行时间统计
    print("\n总运行时间统计：")
    print(f"总检测耗时: {total_det_time:.3f}s, 平均每张: {total_det_time/len(image_paths):.3f}s")
    print(f"总方向分类耗时: {total_cls_time:.3f}s, 平均每张: {total_cls_time/len(image_paths):.3f}s")
    print(f"总文字识别耗时: {total_rec_time:.3f}s, 平均每张: {total_rec_time/len(image_paths):.3f}s")
    print(f"总耗时: {total_time:.3f}s, 平均每张: {total_time/len(image_paths):.3f}s")
        
    # 将总运行时间写入txt文件
    time_save_path = os.path.join(args.output_dir, 'total_time.txt')
    with open(time_save_path, 'w', encoding='utf-8') as f:
        f.write("\n总运行时间统计：")
        f.write(f"总检测耗时: {total_det_time:.3f}s, 平均每张: {total_det_time/len(image_paths):.3f}s")
        f.write(f"总方向分类耗时: {total_cls_time:.3f}s, 平均每张: {total_cls_time/len(image_paths):.3f}s")
        f.write(f"总文字识别耗时: {total_rec_time:.3f}s, 平均每张: {total_rec_time/len(image_paths):.3f}s")
        f.write(f"总耗时: {total_time:.3f}s, 平均每张: {total_time/len(image_paths):.3f}s")
            
if __name__ == '__main__':
    main()