# !/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/3/6 15:03
# <AUTHOR> <EMAIL>
# @FileName: generate_qrcode

from typing import Optional, Tuple, List, Union, Dict

import os
import random
import shutil

import qrcode
import numpy as np
from tqdm import tqdm
from PIL import Image, ImageDraw, ImageFont, ImageColor, ImageFilter, ImageChops


def create_basic_qrcode(
    data: str,
    output_path: str,
    version: int = 1,
    error_correction: int = qrcode.constants.ERROR_CORRECT_H,
    box_size: int = 10,
    border: int = 4
) -> str:
    """
    创建基本的二维码

    Args:
        data: 要编码的数据
        output_path: 输出文件路径
        version: 二维码版本，范围1-40
        error_correction: 错误纠正级别
        box_size: 每个模块的像素大小
        border: 边框宽度(模块数)

    Returns:
        生成的二维码文件路径
    """
    qr = qrcode.QRCode(
        version=version,
        error_correction=error_correction,
        box_size=box_size,
        border=border,
    )
    qr.add_data(data)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    img.save(output_path)
    return output_path


def create_colored_qrcode(
    data: str,
    output_path: str,
    fill_color: str = "#000000",
    back_color: str = "#FFFFFF",
    version: int = 1,
    error_correction: int = qrcode.constants.ERROR_CORRECT_H,
    box_size: int = 10,
    border: int = 4
) -> str:
    """
    创建彩色二维码

    Args:
        data: 要编码的数据
        output_path: 输出文件路径
        fill_color: 填充颜色 (十六进制格式)
        back_color: 背景颜色 (十六进制格式)
        version: 二维码版本，范围1-40
        error_correction: 错误纠正级别
        box_size: 每个模块的像素大小
        border: 边框宽度(模块数)

    Returns:
        生成的二维码文件路径
    """
    qr = qrcode.QRCode(
        version=version,
        error_correction=error_correction,
        box_size=box_size,
        border=border,
    )
    qr.add_data(data)
    qr.make(fit=True)

    img = qr.make_image(fill_color=fill_color, back_color=back_color)

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    img.save(output_path)
    return output_path


def create_gradient_qrcode(
    data: str,
    output_path: str,
    start_color: str = "#0000FF",
    end_color: str = "#FF0000",
    gradient_type: str = "linear",  # "linear" or "radial"
    version: int = 1,
    error_correction: int = qrcode.constants.ERROR_CORRECT_H,
    box_size: int = 10,
    border: int = 4
) -> str:
    """
    创建渐变色二维码

    Args:
        data: 要编码的数据
        output_path: 输出文件路径
        start_color: 起始颜色 (十六进制格式)
        end_color: 结束颜色 (十六进制格式)
        gradient_type: 渐变类型，可以是"linear"或"radial"
        version: 二维码版本，范围1-40
        error_correction: 错误纠正级别
        box_size: 每个模块的像素大小
        border: 边框宽度(模块数)

    Returns:
        生成的二维码文件路径
    """
    qr = qrcode.QRCode(
        version=version,
        error_correction=error_correction,
        box_size=box_size,
        border=border,
    )
    qr.add_data(data)
    qr.make(fit=True)

    # 创建基本二维码
    qr_img = qr.make_image(fill_color="black", back_color="white")
    qr_img = qr_img.convert("RGBA")

    # 创建渐变图像
    width, height = qr_img.size
    gradient = Image.new("RGBA", (width, height), color=0)
    draw = ImageDraw.Draw(gradient)

    # 解析颜色
    r1, g1, b1 = ImageColor.getcolor(start_color, "RGB")
    r2, g2, b2 = ImageColor.getcolor(end_color, "RGB")

    # 创建渐变
    if gradient_type == "linear":
        for y in range(height):
            # 计算当前位置的渐变比例
            ratio = y / height
            r = r1 * (1 - ratio) + r2 * ratio
            g = g1 * (1 - ratio) + g2 * ratio
            b = b1 * (1 - ratio) + b2 * ratio
            draw.line([(0, y), (width, y)], fill=(int(r), int(g), int(b), 255))
    else:  # radial
        for y in range(height):
            for x in range(width):
                # 计算到中心的距离
                distance = ((x - width / 2) ** 2 + (y - height / 2) ** 2) ** 0.5
                # 归一化距离
                max_distance = ((width / 2) ** 2 + (height / 2) ** 2) ** 0.5
                ratio = min(distance / max_distance, 1.0)
                r = r1 * (1 - ratio) + r2 * ratio
                g = g1 * (1 - ratio) + g2 * ratio
                b = b1 * (1 - ratio) + b2 * ratio
                draw.point((x, y), fill=(int(r), int(g), int(b), 255))

    # 创建最终图像
    final_img = Image.new("RGBA", qr_img.size, (255, 255, 255, 0))
    qr_data = np.array(qr_img)
    gradient_data = np.array(gradient)

    # 将黑色区域替换为渐变色
    mask = qr_data[:, :, 0] == 0  # 黑色区域的掩码
    final_data = np.array(final_img)
    final_data[mask] = gradient_data[mask]

    # 转回PIL图像
    final_img = Image.fromarray(final_data)
    final_img = final_img.convert("RGB")

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    # 保存时使用PNG格式以保留透明通道
    output_path_with_ext = output_path if output_path.lower().endswith('.png') else output_path.rsplit('.', 1)[0] + '.png'
    final_img.save(output_path_with_ext, 'PNG')
    return output_path


def create_qrcode_with_params(
    data: str,
    output_path: str,
    logo_path: Optional[str] = None,
    logo_size_ratio: float = 0.2,
    logo_shape: str = "rectangle",
    version: int = 1,
    error_correction: int = qrcode.constants.ERROR_CORRECT_H,
    box_size: int = 10,
    border: int = 4,
    fill_color: str = "black",
    back_color: str = "white",
    apply_rounded_corners: bool = False,
    rounded_radius: int = 30,
    apply_shadow: bool = False,
    shadow_blur: int = 5,
    shadow_opacity: int = 50,
    shadow_padding: int = 10,
    **kwargs
) -> str:
    """
    创建自定义参数的二维码

    Args:
        data: 要编码的数据，可以是URL、文本或任何字符串内容
        
        output_path: 输出文件路径，建议以.png为扩展名以保留透明度
        
        logo_path: logo图片路径，设为None则不添加logo
            推荐使用透明背景的PNG格式logo以获得最佳效果
        
        logo_size_ratio: logo占整体二维码的比例
            合理范围: 0.1-0.3，过大可能影响扫描成功率
            推荐值: 0.15-0.25
            
        logo_shape: logo的形状
            可选值:
            - "rectangle": 矩形logo(默认)
            - "circle": 圆形logo
            - "rounded": 圆角矩形logo
        
        version: 二维码版本号，范围1-40
            版本越高，二维码容量越大，但也更密集更复杂
            version=1: 最多存储约17个字符
            version=10: 最多存储约271个字符
            version=40: 最多存储约4296个字符
            建议范围: 1-10，超过10可能在打印或低分辨率显示时难以识别
        
        error_correction: 错误纠正级别，控制二维码的容错能力
            L (ERROR_CORRECT_L): 约7%的容错率，适合环境良好的场景
            M (ERROR_CORRECT_M): 约15%的容错率，默认级别
            Q (ERROR_CORRECT_Q): 约25%的容错率，适合印刷质量不高的场景
            H (ERROR_CORRECT_H): 约30%的容错率，最高级别，适合添加logo场景
            如果添加logo，建议使用H级别以提高容错性
        
        box_size: 每个二维码模块(小方格)的像素大小
            合理范围: 5-20像素
            值越大生成的二维码图片尺寸越大
            小于5可能不利于扫描，大于20文件尺寸过大
        
        border: 二维码四周留白宽度，以模块(小方格)为单位
            合理范围: 1-10，标准规范推荐至少4个单位的留白
            过小可能影响扫描，一般推荐4-6
        
        fill_color: 二维码前景色(方块颜色)
            可使用颜色名称(如"black")或十六进制颜色代码(如"#000000")
            深色对比度高更容易被识别，浅色可能降低识别率
            建议使用深色如黑色("#000000")、深蓝("#1677FF")、深绿("#07C160")等
        
        back_color: 二维码背景色
            可使用颜色名称(如"white")或十六进制颜色代码(如"#FFFFFF")
            浅色背景对比度高更易识别，深色背景可能降低识别率
            建议使用白色("#FFFFFF")或非常浅的颜色
        
        apply_rounded_corners: 是否应用圆角效果
            True: 应用圆角，使二维码看起来更现代美观
            False: 保持传统方形边角
            圆角可能略微影响边缘识别，但现代扫描器一般都能正常处理
        
        rounded_radius: 圆角半径(像素)，仅当apply_rounded_corners=True时有效
            合理范围: 10-50像素
            较小的值(10-20)效果微妙，较大的值(30-50)效果明显
            不建议超过50，可能影响边缘识别
        
        apply_shadow: 是否应用阴影效果
            True: 添加阴影效果，使二维码看起来有立体感
            False: 不添加阴影
            适度的阴影可增加美观度，但过重的阴影可能影响识别
        
        shadow_blur: 阴影模糊程度(像素)，仅当apply_shadow=True时有效
            合理范围: 1-15像素
            3-8为适中效果，超过10会出现明显的模糊扩散
        
        shadow_opacity: 阴影不透明度，仅当apply_shadow=True时有效
            合理范围: 20-100 (实际对应透明度值0-255的8%-40%)
            30-60为适中效果，低于20几乎看不见，高于80太重影响美观
        
        shadow_padding: 阴影与二维码的间距(像素)，仅当apply_shadow=True时有效
            合理范围: 5-20像素
            值越大，阴影与二维码的距离越远，投影效果越明显
        
        **kwargs: 其他可选参数，会直接传递给底层的二维码生成函数

    Returns:
        生成的二维码文件路径
    """
    # 所有参数直接传入create_styled_qrcode
    return create_styled_qrcode(
        data=data,
        output_path=output_path,
        logo_path=logo_path,
        logo_size_ratio=logo_size_ratio,
        logo_shape=logo_shape,
        version=version,
        error_correction=error_correction,
        box_size=box_size,
        border=border,
        fill_color=fill_color,
        back_color=back_color,
        apply_rounded_corners=apply_rounded_corners,
        rounded_radius=rounded_radius,
        apply_shadow=apply_shadow,
        shadow_blur=shadow_blur,
        shadow_opacity=shadow_opacity,
        shadow_padding=shadow_padding,
        **kwargs
    )


def get_random_qrcode_params():
    """
    生成随机的二维码参数，覆盖各种可能的样式组合
    各参数的随机范围均基于实际应用经验，确保生成的二维码兼具美观性和实用性
    
    Returns:
        随机生成的参数字典，包含以下键值:
        - fill_color: 二维码前景颜色，从预定义的颜色列表中随机选择
        - apply_rounded_corners: 是否应用圆角效果，有40%的概率为True
        - rounded_radius: 圆角半径，在10-50像素之间随机选择
        - apply_shadow: 是否应用阴影效果，有30%的概率为True
        - shadow_blur: 阴影模糊程度，在3-10像素之间随机选择
        - shadow_opacity: 阴影不透明度，在30-80之间随机选择
        - shadow_padding: 阴影与二维码的间距，在5-15像素之间随机选择
        - logo_size_ratio: logo大小比例，在0.15-0.25之间随机选择
        - use_logo: 是否使用logo，有50%的概率为True
        - logo_shape: logo形状，随机从"rectangle"(矩形)、"circle"(圆形)、"rounded"(圆角矩形)中选择
    """
    # 颜色列表 - 各类深色调，这些颜色与白色背景都有良好的对比度
    colors = [
        "#000000",  # 黑色 - 经典二维码颜色，最高识别率
        "#1677FF",  # 蓝色 - 类似支付宝蓝，高识别率
        "#07C160",  # 绿色 - 类似微信绿，高识别率
        "#FF5733",  # 红色 - 高对比度红色，适合特殊场景
        "#9C27B0",  # 紫色 - 深紫色，有足够对比度
        "#FF9800",  # 橙色 - 亮橙色，独特而有足够识别度
        "#795548",  # 棕色 - 深棕色，带复古感
        "#607D8B",  # 蓝灰色 - 商务风格，适合正式场合
        "#E91E63"   # 粉色 - 高识别度粉色，女性化产品常用
    ]
    
    # 随机生成参数 - 概率控制确保生成的数据集中各类样式均衡分布
    use_rounded = random.random() < 0.4  # 40% 概率有圆角 - 不过高以确保识别率
    use_shadow = random.random() < 0.3   # 30% 概率有阴影 - 阴影可能降低识别率，概率较低
    use_logo = random.random() < 0.5     # 50% 概率有logo - 平衡分布
    
    # logo形状选项 - 矩形、圆形、圆角矩形
    # 增加圆形logo的出现比例，将比例调整为 2:3:1 (矩形:圆形:圆角矩形)
    logo_shapes = ["rectangle", "rectangle", "circle", "circle", "circle", "rounded"] 
    
    # 参数字典 - 包含所有可定制的二维码参数
    params = {
        # 前景颜色 - 从colors列表中随机选择一种颜色
        "fill_color": random.choice(colors),
        
        # 圆角相关参数
        "apply_rounded_corners": use_rounded,  # 是否应用圆角
        # 圆角半径: 10-50像素之间随机（如果应用圆角）
        "rounded_radius": random.randint(10, 50) if use_rounded else 30,
        
        # 阴影相关参数
        "apply_shadow": use_shadow,  # 是否应用阴影
        # 阴影模糊度: 3-10像素范围，适中的模糊效果
        "shadow_blur": random.randint(3, 10) if use_shadow else 5,
        # 阴影不透明度: 30-80范围，低于30几乎看不见，高于80太重
        "shadow_opacity": random.randint(30, 80) if use_shadow else 50,
        # 阴影与二维码间距: 5-15像素，控制阴影与二维码的间距
        "shadow_padding": random.randint(5, 15) if use_shadow else 10,
        
        # Logo相关参数
        # Logo大小比例: 0.15-0.25范围，保证不会过大影响扫描
        "logo_size_ratio": random.uniform(0.15, 0.25) if use_logo else 0.2,
        # Logo形状: 从矩形、圆形、圆角矩形随机选择
        "logo_shape": random.choice(logo_shapes) if use_logo else "rectangle"
        # 注意: 不再返回use_logo参数，因为create_styled_qrcode函数不接受该参数
        # 在调用随机参数的地方通过logo_path是否为None来判断是否使用logo
    }
    
    return params


# 测试兼容性应用，将撤销下面的函数定义，统一使用create_qrcode_with_params
# 这些函数仅用于向后兼容性，并不建议在新代码中使用


def create_styled_qrcode(
    data: str,
    output_path: str,
    logo_path: Optional[str] = None,
    logo_size_ratio: float = 0.2,
    logo_shape: str = "rectangle",  # 新增参数，控制logo形状
    version: int = 1,
    error_correction: int = qrcode.constants.ERROR_CORRECT_H,
    box_size: int = 10,
    border: int = 4,
    fill_color: str = "black",
    back_color: str = "white",
    apply_gradient: bool = False,      # 是否使用渐变色
    gradient_direction: str = "horizontal",  # 渐变方向: horizontal, vertical, diagonal
    gradient_start_color: str = "#000000",  # 渐变起始颜色
    gradient_end_color: str = "#1677FF",    # 渐变结束颜色
    apply_rounded_corners: bool = False,
    rounded_radius: int = 30,
    apply_shadow: bool = False,
    shadow_blur: int = 5,
    shadow_opacity: int = 50,
    shadow_padding: int = 10
) -> str:
    """
    创建自定义风格的二维码，各种属性可自由组合
    
    Args:
        data: 要编码的数据
        output_path: 输出文件路径
        logo_path: logo图片路径，可选
        logo_size_ratio: logo占二维码的比例
        logo_shape: logo形状，可选值为"rectangle"(矩形)、"circle"(圆形)、"rounded"(圆角矩形)
        version: 二维码版本，范围1-40
        error_correction: 错误纠正级别
        box_size: 每个模块的像素大小
        border: 边框宽度(模块数)
        fill_color: 填充颜色，十六进制颜色代码
        back_color: 背景颜色
        apply_rounded_corners: 是否应用圆角效果
        rounded_radius: 圆角半径
        apply_shadow: 是否应用阴影效果
        shadow_blur: 阴影模糊程度
        shadow_opacity: 阴影不透明度(0-255)
        shadow_padding: 阴影与二维码的间距和边距
    
    Returns:
        生成的二维码文件路径
    """
    
    # 创建QR码对象
    qr = qrcode.QRCode(
        version=version,
        error_correction=error_correction,
        box_size=box_size,
        border=border,
    )
    qr.add_data(data)
    qr.make(fit=True)
    
    # 生成基础二维码图像
    if not apply_gradient:
        qr_img = qr.make_image(fill_color=fill_color, back_color=back_color).convert("RGBA")
    else:
        # 渐变色效果处理
        # 首先创建一个黑白二维码作为模板
        qr_img = qr.make_image(fill_color="black", back_color="white").convert("RGBA")
        
        # 获取图片尺寸
        width, height = qr_img.size
        
        # 创建渐变填充
        gradient = Image.new("RGBA", (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(gradient)
        
        # 从16进制颜色代码中提取RGB值
        start_r, start_g, start_b = tuple(int(gradient_start_color[i:i+2], 16) for i in (1, 3, 5))
        end_r, end_g, end_b = tuple(int(gradient_end_color[i:i+2], 16) for i in (1, 3, 5))
        
        # 创建渐变色
        for y in range(height):
            for x in range(width):
                # 计算渐变因子 (0 到 1)
                if gradient_direction == "horizontal":
                    factor = x / width
                elif gradient_direction == "vertical":
                    factor = y / height
                elif gradient_direction == "diagonal":
                    factor = (x + y) / (width + height)
                else:  # 默认水平渐变
                    factor = x / width
                    
                # 插值颜色
                r = int(start_r + factor * (end_r - start_r))
                g = int(start_g + factor * (end_g - start_g))
                b = int(start_b + factor * (end_b - start_b))
                
                # 设置像素颜色
                draw.point((x, y), fill=(r, g, b, 255))
        
        # 使用二维码作为模板创建渐变色二维码
        qr_data = np.array(qr_img)
        gradient_data = np.array(gradient)
        
        # 在原二维码的黑色区域(前景)应用渐变色
        mask = (qr_data[:, :, 0] == 0) & (qr_data[:, :, 1] == 0) & (qr_data[:, :, 2] == 0)
        qr_data[mask] = gradient_data[mask]
        
        # 如果背景是透明的，还需要保持透明度
        if back_color == "transparent":
            # 对于白色背景区域设置为透明
            white_mask = (qr_data[:, :, 0] == 255) & (qr_data[:, :, 1] == 255) & (qr_data[:, :, 2] == 255)
            qr_data[white_mask, 3] = 0
        else:
            # 对于其他颜色背景，保持原来设置
            back_r, back_g, back_b = tuple(int(back_color[i:i+2], 16) for i in (1, 3, 5)) if back_color.startswith('#') else (255, 255, 255)
            white_mask = (qr_data[:, :, 0] == 255) & (qr_data[:, :, 1] == 255) & (qr_data[:, :, 2] == 255)
            qr_data[white_mask] = [back_r, back_g, back_b, 255]
        
        # 转回 PIL 图像
        qr_img = Image.fromarray(qr_data)
    
    qr_width, qr_height = qr_img.size
    
    # 处理圆角
    if apply_rounded_corners:
        mask = Image.new("L", (qr_width, qr_height), 0)
        draw = ImageDraw.Draw(mask)
        draw.rounded_rectangle((0, 0, qr_width, qr_height), rounded_radius, fill=255)
        
        # 应用圆角蒙版
        rounded_img = Image.new("RGBA", (qr_width, qr_height), (255, 255, 255, 0))
        rounded_img.paste(qr_img, (0, 0), mask)
        qr_img = rounded_img
    
    # 添加阴影效果
    if apply_shadow:
        # 阴影尺寸可调
        shadow_size = (qr_width + shadow_padding*2, qr_height + shadow_padding*2)
        shadow = Image.new("RGBA", shadow_size, (0, 0, 0, 0))
        shadow_draw = ImageDraw.Draw(shadow)
        shadow_rect = (shadow_padding, shadow_padding, 
                      shadow_size[0]-shadow_padding, shadow_size[1]-shadow_padding)
        shadow_draw.rectangle(shadow_rect, fill=(0, 0, 0, shadow_opacity))
        shadow = shadow.filter(ImageFilter.GaussianBlur(shadow_blur))
        
        # 创建背景与边距
        bg_padding = 5  # 额外边距
        bg_size = (shadow_size[0] + bg_padding*2, shadow_size[1] + bg_padding*2)
        background = Image.new("RGBA", bg_size, (255, 255, 255, 255))
        
        # 粘贴阴影和二维码
        background.paste(shadow, (bg_padding, bg_padding), shadow)
        background.paste(qr_img, (bg_padding + shadow_padding, bg_padding + shadow_padding), qr_img)
        final_img = background
        # 更新尺寸以正确定位logo
        qr_width, qr_height = final_img.size
    else:
        final_img = qr_img
    
    # 处理logo
    if logo_path:
        # 1. 打开logo图像
        logo = Image.open(logo_path).convert("RGBA")
        
        # 2. 计算logo大小
        logo_max_size = int(min(qr_width, qr_height) * logo_size_ratio)
        
        # 3. 调整logo大小
        logo_width, logo_height = logo.size
        if logo_width > logo_max_size or logo_height > logo_max_size:
            if logo_width > logo_height:
                logo_height = int(logo_height * logo_max_size / logo_width)
                logo_width = logo_max_size
            else:
                logo_width = int(logo_width * logo_max_size / logo_height)
                logo_height = logo_max_size
            logo = logo.resize((logo_width, logo_height), Image.LANCZOS)
        
        # 4. 形状裁剪 - 根据logo_shape参数进行不同形状的裁剪
        if logo_shape != "rectangle":
            # 创建一个透明遮罩
            mask = Image.new("L", (logo_width, logo_height), 0)
            draw = ImageDraw.Draw(mask)
            
            if logo_shape == "circle":
                # 圆形logo - 画一个正圆
                radius = min(logo_width, logo_height) // 2
                center = (logo_width // 2, logo_height // 2)
                # 画一个正圆作为遮罩
                draw.ellipse(
                    (center[0] - radius, center[1] - radius, 
                     center[0] + radius, center[1] + radius),
                    fill=255
                )
            elif logo_shape == "rounded":
                # 圆角矩形logo - 使用rounded_rectangle
                # 圆角半径 - 选择一个适当的值，这里选择logo最小边长的5%
                corner_radius = min(logo_width, logo_height) // 10
                draw.rounded_rectangle(
                    (0, 0, logo_width, logo_height),
                    radius=corner_radius,
                    fill=255
                )
            
            # 应用形状遮罩到logo
            logo_with_mask = Image.new("RGBA", (logo_width, logo_height), (0, 0, 0, 0))
            logo_with_mask.paste(logo, (0, 0), mask)
            logo = logo_with_mask
        
        # 5. 计算白色背景大小和内边距
        padding = 4  # 每边的填充大小
        bg_size = (logo_width + padding*2, logo_height + padding*2)
        
        # 6. 创建一个新图像来作为背景
        combined = Image.new("RGBA", bg_size, (255, 255, 255, 255))
        
        # 7. 如果是圆形或圆角矩形logo，创建对应形状的背景
        if logo_shape == "circle":
            # 为圆形logo创建圆形白色背景
            bg_mask = Image.new("L", bg_size, 0)
            bg_draw = ImageDraw.Draw(bg_mask)
            bg_radius = min(bg_size) // 2
            bg_center = (bg_size[0] // 2, bg_size[1] // 2)
            bg_draw.ellipse(
                (bg_center[0] - bg_radius, bg_center[1] - bg_radius, 
                 bg_center[0] + bg_radius, bg_center[1] + bg_radius),
                fill=255
            )
            # 创建圆形白色背景
            circle_bg = Image.new("RGBA", bg_size, (255, 255, 255, 255))
            combined = Image.new("RGBA", bg_size, (0, 0, 0, 0))
            combined.paste(circle_bg, (0, 0), bg_mask)
        elif logo_shape == "rounded":
            # 为圆角矩形logo创建圆角矩形白色背景
            bg_mask = Image.new("L", bg_size, 0)
            bg_draw = ImageDraw.Draw(bg_mask)
            bg_radius = min(bg_size) // 8  # 背景的圆角半径
            bg_draw.rounded_rectangle(
                (0, 0, bg_size[0], bg_size[1]),
                radius=bg_radius,
                fill=255
            )
            # 创建圆角矩形白色背景
            rounded_bg = Image.new("RGBA", bg_size, (255, 255, 255, 255))
            combined = Image.new("RGBA", bg_size, (0, 0, 0, 0))
            combined.paste(rounded_bg, (0, 0), bg_mask)
        
        # 8. 将logo粘贴到背景上
        logo_position = (padding, padding)
        combined.paste(logo, logo_position, logo)
        
        # 9. 计算logo在二维码上的位置
        position = ((qr_width - bg_size[0]) // 2, (qr_height - bg_size[1]) // 2)
        
        # 10. 将处理好的logo贴到二维码上
        final_img.paste(combined, position, combined)

    
    # 保存最终图像（保留透明通道）
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    # 保存时使用PNG格式以保留透明通道
    output_path_with_ext = output_path if output_path.lower().endswith('.png') else output_path.rsplit('.', 1)[0] + '.png'
    final_img.save(output_path_with_ext, 'PNG')
    return output_path


def create_random_style_qrcode(
    data: str,
    output_path: str,
    logo_path: Optional[str] = None
) -> str:
    """
    创建完全随机风格的二维码，使用随机生成的参数组合
    随机生成所有参数，包括颜色、圆角、阴影、方块大小等，保证生成数据集的多样性

    Args:
        data: 要编码的数据内容，如URL或文本
        output_path: 输出文件路径，建议使用.png格式以保留透明度
        logo_path: logo图片路径，可选参数
            如果提供logo_path，也会根据get_random_qrcode_params生成的use_logo
            参数决定是否实际使用logo，实现随机性

    Returns:
        生成的二维码文件路径
    """
    # 获取随机样式参数（颜色、圆角、阴影、logo大小等）
    params = get_random_qrcode_params()
    
    # 随机版本号和错误修正级别
    # 版本号控制二维码容量和复杂度，范围1-10，避开过大版本导致扰点过小
    version = random.randint(1, 10)  # 较大版本可能会显示不友好
    
    # 随机选择错误修正级别，影响二维码扩散性和容错能力
    # L(7%), M(15%), Q(25%), H(30%) - 越高级别容错能力越强，但二维码点越多
    error_correction_levels = [
        qrcode.constants.ERROR_CORRECT_L,  # 约7%的容错率 
        qrcode.constants.ERROR_CORRECT_M,  # 约15%的容错率
        qrcode.constants.ERROR_CORRECT_Q,  # 约25%的容错率
        qrcode.constants.ERROR_CORRECT_H   # 约30%的容错率
    ]
    # 随机选择一种错误修正级别，与logo情况相关（使用logo建议选H级别）
    error_correction = random.choice(error_correction_levels)
    
    # 每个模块(小方格)的像素大小，范围5-15像素
    # 尺寸越大越容易扫描，但图片文件也越大
    box_size = random.randint(5, 15)
    
    # 二维码四周留白宽度，以模块(小方格)为单位，范围1-5单位
    # 标准规范建议至少4单位，但随机生成时可使用更广范围
    border = random.randint(1, 5)
    
    # 处理参数字典，移除use_logo键，防止它被传递到不接受它的函数
    use_logo = params.pop("use_logo", False)
    
    # 创建随机参数的二维码
    return create_qrcode_with_params(
        data=data,
        output_path=output_path,
        logo_path=logo_path if use_logo else None,
        version=version,
        error_correction=error_correction,
        box_size=box_size,
        border=border,
        **params
    )


def generate_dataset(
    output_dir: str,
    count: int = 100,
    logo_dir: Optional[str] = None
) -> List[str]:
    """
    生成多样化的二维码数据集，使用完全参数化设计
    特点：
    1. 随机生成多种内容类型：URL、文本、联系人信息等
    2. 随机生成多种样式组合：基础、自定义、完全随机
    3. 生成的文件名包含特征信息，方便后续分析

    Args:
        output_dir: 输出目录路径
            建议使用绝对路径，确保具有写入权限
            如果目录不存在将自动创建
        
        count: 需要生成的二维码数量
            合理范围: 1-1000
            过少不足以覆盖各种参数组合，过多则生成时间过长
            推荐范围: 100-300个样本，可提供充分多样性
        
        logo_dir: logo图片目录路径，可选参数
            该目录应包含多个PNG格式的透明背景图标
            如不提供此参数，生成的二维码将不使用logo

    Returns:
        生成的二维码文件路径列表，可用于进一步处理
    """
    # 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)
    generated_files = []

    # 如果提供了logo目录，获取所有支持的图像文件
    # 支持PNG、JPG、JPEG、GIF格式，但建议使用PNG格式以保留透明通道
    logos = []
    if logo_dir and os.path.exists(logo_dir):
        for file in os.listdir(logo_dir):
            if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif')):
                logos.append(os.path.join(logo_dir, file))

    # 所有二维码均通过create_qrcode_with_params生成
    # 通过随机参数生成不同风格的二维码，确保数据集多样性

    # 添加tqdm进度条显示生成进度
    for i in tqdm(range(count), desc="生成二维码", unit="个"):
        #========== 随机内容生成部分 ==========
        # 生成三种不同类型的内容，使用加权随机选择：
        # - url: 60%概率，生成随机网址
        # - text: 30%概率，生成随机文本内容
        # - contact: 10%概率，生成随机联系人信息
        content_type = random.choices(["url", "text", "contact"], weights=[0.6, 0.3, 0.1])[0]

        if content_type == "url":  # 生成随机URL地址
            # 随机组合域名、路径和参数生成多样化的URL
            domains = ["example.com", "test.org", "sample.net", "demo.io"]  # 域名列表
            paths = ["", "page", "product", "about", "contact", "service"]  # 路径列表
            params = ["", "?id=123", "?user=test", "?q=search"]  # URL参数列表

            # 组合生成完整URL
            content = f"https://www.{random.choice(domains)}/{random.choice(paths)}{random.choice(params)}"
            
        elif content_type == "text":  # 生成随机文本内容
            # 从预定义的文本列表中随机选择一条
            texts = [
                "Hello World",
                "QR Code Test",
                "Scan me for information",
                "This is a test QR code",
                "Example text content"
            ]
            content = random.choice(texts)
            
        else:  # 生成随机联系人信息(vCard格式)
            # 随机组合姓名、电话和邮箱生成联系人信息
            names = ["John Doe", "Jane Smith", "Alex Johnson", "Sam Wilson"]  # 姓名列表
            phones = ["1234567890", "0987654321", "1122334455", "5566778899"]  # 电话列表
            emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]  # 邮箱列表

            # 使用简化的格式组合联系人信息，用换行符分隔不同字段
            # 实际vCard格式更复杂，这里为了简化使用自定义格式
            content = f"NAME:{random.choice(names)}\nTEL:{random.choice(phones)}\nEMAIL:{random.choice(emails)}"

        #========== 二维码类型选择 ==========
        # 使用标准的随机参数生成二维码

        # 随机参数特征描述（用于文件名）
        features = []

        # 随机选择logo
        logo_path = None
        if logos and random.random() < 0.5:  # 50% 概率使用logo
            logo_path = random.choice(logos)
            features.append("logo")
            
        # 随机参数
        use_rounded = random.random() < 0.4  # 40% 概率使用圆角
        if use_rounded:
            features.append("rounded")
            
        use_shadow = random.random() < 0.3  # 30% 概率使用阴影
        if use_shadow:
            features.append("shadow")
            
        # 颜色映射表 - 确保文件名与实际颜色一致
        color_mapping = {
            "black": "#000000",  # 黑色
            "blue": "#1677FF",  # 蓝色
            "green": "#07C160",  # 绿色
            "red": "#FF5733",  # 红色
            "purple": "#9C27B0",  # 紫色
            "orange": "#FF9800",  # 橙色
            "brown": "#795548",  # 棕色
            "gray": "#607D8B",  # 蓝灰色
            "pink": "#E91E63"   # 粉色
        }
        
        # 前景颜色选择 - 黑色占比 50%，其他颜色平均分配
        if random.random() < 0.5:  # 50% 概率选择黑色
            color_name = "black"
        else:
            # 从其他颜色中随机选择
            color_name = random.choice([c for c in color_mapping.keys() if c != "black"])
        features.append(color_name)
            
        # 统一使用随机参数生成二维码
        # 生成随机参数
        params = {}
        
        # 渐变色设置 - 20% 概率使用渐变色
        use_gradient = random.random() < 0.2  # 20% 概率使用渐变色
        
        if use_gradient:
            # 渐变色参数设置
            params["apply_gradient"] = True
            
            # 随机选择渐变方向
            gradient_directions = ["horizontal", "vertical", "diagonal"]
            gradient_direction = random.choice(gradient_directions)
            params["gradient_direction"] = gradient_direction
            
            # 随机生成渐变起始颜色和结束颜色
            gradient_colors = [
                "#1677FF",  # 蓝色
                "#07C160",  # 绿色
                "#FF5733",  # 红色
                "#9C27B0",  # 紫色
                "#FF9800",  # 橙色
                "#E91E63"   # 粉色
            ]
            
            # 确保起始颜色和结束颜色不同
            start_color = random.choice(gradient_colors)
            end_colors = [c for c in gradient_colors if c != start_color]
            end_color = random.choice(end_colors)
            
            params["gradient_start_color"] = start_color
            params["gradient_end_color"] = end_color
            
            # 添加渐变色特性到文件名
            features.append(f"gradient_{gradient_direction}")
        else:
            # 如果不使用渐变色，则使用普通的填充颜色
            params["fill_color"] = color_mapping[color_name]
            params["apply_gradient"] = False
        
        # 背景色设置 - 50% 白色, 30% 透明, 20% 随机彩色
        bg_rand = random.random()
        use_transparent_bg = False  # 标记是否使用透明背景
        
        if bg_rand < 0.5:  # 50% 概率为白色背景（标准）
            params["back_color"] = "white"
            features.append("white_bg")
        elif bg_rand < 0.8:  # 30% 概率为透明背景
            params["back_color"] = "transparent"
            features.append("transparent_bg")
            use_transparent_bg = True  # 标记使用透明背景
        else:  # 20% 概率为随机彩色背景
            # 随机生成浅色调的背景色
            light_colors = [
                "#F0F8FF",  # Alice Blue
                "#F5F5DC",  # Beige
                "#FFE4C4",  # Bisque
                "#FFEBCD",  # Blanched Almond
                "#8FBC8F",  # Dark Sea Green
                "#FAEBD7",  # Antique White
                "#D8BFD8",  # Thistle
                "#FFE4E1"   # Misty Rose
            ]
            params["back_color"] = random.choice(light_colors)
            features.append("color_bg")
            
        # 特征字符串用于文件名
        feature_str = "_".join(features) if features else "basic"
        
        # 输出文件路径
        file_path = os.path.join(output_dir, f"qrcode_{feature_str}_{i:04d}.png")
        
        # 设置圆角
        params["apply_rounded_corners"] = use_rounded
        if use_rounded:
            params["rounded_radius"] = random.randint(10, 50)
            
        # 设置阴影 - 如果是透明背景，不使用阴影效果
        if use_transparent_bg:
            # 透明背景时禁用阴影效果
            params["apply_shadow"] = False
            # 移除可能已添加的shadow特性
            if "shadow" in features:
                features.remove("shadow")
        else:
            # 非透明背景时正常设置阴影
            params["apply_shadow"] = use_shadow
            if use_shadow:
                params["shadow_blur"] = random.randint(3, 10)
                params["shadow_opacity"] = random.randint(30, 80)
                params["shadow_padding"] = random.randint(5, 15)
            
        # 设置Logo参数
        if logo_path:
            params["logo_size_ratio"] = random.uniform(0.15, 0.25)
            # logo形状选项 - 矩形、圆形、圆角矩形
            # 增加圆形logo的出现比例，将比例调整为 2:3:1 (矩形:圆形:圆角矩形)
            logo_shapes = ["rectangle", "rectangle", "circle", "circle", "circle", "rounded"]
            # 从 logo_shapes 随机选择形状，圆形的出现概率更高
            params["logo_shape"] = random.choice(logo_shapes)
            
        # 生成二维码
        create_qrcode_with_params(
            data=content,
            output_path=file_path,
            logo_path=logo_path,
            **params
        )

        generated_files.append(file_path)

    return generated_files


def main():
    # 合理范围: 1-1000。生成量越大，数据集越多样化，但计算时间也更长
    count = 1000
    # 建议使用绝对路径，确保有写入权限。目录不存在将自动创建
    output = "/aipdf-mlp/xelawk/datasets/202503/aipdf/generated_qrcode"
    if os.path.exists(output):
        shutil.rmtree(output)
    # 可以包含多种类型的logo，生成数据集时会随机选择
    logo_dir = "/aipdf-mlp/xelawk/datasets/202503/aipdf/source_watermarks/web_logo_dataset_release/custom_common_logo"

    files = generate_dataset(output, count, logo_dir)
    print(f"已生成 {len(files)} 个二维码到 {output} 目录")


if __name__ == "__main__":
    main()