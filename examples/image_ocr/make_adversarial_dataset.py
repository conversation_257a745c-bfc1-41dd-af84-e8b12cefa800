#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/3/7 15:01
# <AUTHOR> <EMAIL>
# @FileName: make_adversarial_dataset

import os
import copy
import random
import shutil
from typing import Iterator, Dict, List, Any, Tuple, Set, Optional
os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'

import json
from pathlib import Path
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed

import numpy as np
import matplotlib.pyplot as plt

from PIL import Image, ImageDraw
from shapely.geometry import Polygon, box
from tqdm import tqdm

from modules.utils.log import LOGGER
from modules.image_ocr.augmentor.adversarial_augmentor import AdversarialAugmentor

# 文件名全局定义
TARGET_LABEL_TXT_NAME = 'Label.txt'

# 标识名全局定义
SEAL_REGION_FLAG = 'seal_region_flag'
QRCODE_REGION_FLAG = 'qrcode_region_flag'
NATURAL_REGION_FLAG = 'natural_region_flag'

# 区域宽高比的全局配置
MAX_ASPECT_RATIO = 2.0  # 最大宽高比限制 - 更严格控制，避免细长的框
MIN_AREA_RATIO = 0.005     # 最小面积比例限制
MAX_REGIONS_PER_TYPE = 3  # 每种类型最多选择的区域数


def get_md5_from_filename(filename: Path) -> str:
    """从文件名中提取MD5值"""
    basename = Path(filename).stem
    parts = basename.split('-')
    if len(parts) > 1:
        return parts[0]
    return basename


def find_label_files(root_dir: Path) -> Iterator[Path]:
    """递归遍历目录找到所有的Label.txt文件

    Args:
        root_dir: 根目录路径

    Yields:
        找到的Label.txt文件的Path对象
    """
    if isinstance(root_dir, str):
        root_dir = Path(root_dir)

    # 使用pathlib的rglob方法递归查找所有Label.txt文件
    for path in root_dir.rglob(TARGET_LABEL_TXT_NAME):
        # 跳过隐藏目录中的文件
        if any(part.startswith('.') for part in path.parts):
            continue
        yield path


def parse_label_file(file_path: Path) -> Dict[str, List[Dict[str, Any]]]:
    """解析标注文件，支持Label.txt和ch_PP-OCRv4_server格式

    文件格式示例：
    part_0001/image1.jpg    [{"transcription": "text1", "points": [[x1,y1],...], "difficult": false}, ...]
    part_0001/image2.jpg    [{"transcription": "text2", "points": [[x1,y1],...], "difficult": false}, ...]

    Args:
        file_path: 标注文件路径

    Returns:
        Dict[str, List[Dict[str, Any]]]: 图片名到标注列表的映射
    """
    annotations = {}

    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue

            try:
                # 分割图片名和JSON数组
                parts = line.split('\t')
                if len(parts) != 2:
                    LOGGER.debug(f"无效的行格式: {line}")
                    continue

                image_name = parts[0].strip()
                json_str = parts[1].strip()

                # 解析JSON数组
                boxes = json.loads(json_str)
                if not isinstance(boxes, list):
                    print('\n')
                    LOGGER.warning(f"标注数据不是列表格式: {json_str}")
                    continue

                annotations[image_name] = boxes

            except (ValueError, json.JSONDecodeError) as e:
                print('\n')
                LOGGER.warning(f"解析行'{line}'时发生错误: {e}")
                continue

    return annotations


def extract_removed_image_regions(
    annotations: Dict[str, List[Dict[str, Any]]],
    base_dir: Path
) -> Dict[str, List[Dict[str, Any]]]:
    """提取标注数据中被移除的图片区域

    Args:
        annotations: 图片名到标注列表的映射
        base_dir: 图片文件所在的基础目录

    Returns:
        Dict[str, List[Dict[str, Any]]]: 包含被移除区域信息的字典
    """
    removed_regions = {}

    for image_name, boxes in annotations.items():
        img_path = base_dir / image_name

        # 尝试获取图片尺寸
        try:
            if not img_path.exists():
                print('\n')
                LOGGER.warning(f"图片不存在: {img_path}")
                continue

            with Image.open(img_path) as img:
                img_width, img_height = img.size
        except Exception as e:
            print('\n')
            LOGGER.warning(f"无法打开图片 {img_path}: {e}")
            continue

        # 查找包含 "image_removed" 标记的标注
        removed_areas = []
        for annotation_box in boxes:
            transcription = annotation_box.get('transcription', '')

            # 检查是否是被移除的图片区域标记
            if '-$img_rm:' in transcription or 'image_removed' in transcription.lower():
                points = annotation_box.get('points', [])
                if len(points) == 4:  # 确保有4个点（矩形）
                    area = {
                        'points': points,
                        'polygon': Polygon(points),
                        'area': Polygon(points).area,
                        'text': transcription
                    }
                    removed_areas.append(area)

        # 如果找到了被移除的区域，计算与文本框的交集
        if removed_areas:
            text_boxes = []
            for annotation_box in boxes:
                transcription = annotation_box.get('transcription', '')
                # 只处理非图片移除标记的文本框
                if '-$img_rm:' not in transcription and 'image_removed' not in transcription.lower():
                    points = annotation_box.get('points', [])
                    if len(points) == 4:  # 确保有4个点
                        text_boxes.append(Polygon(points))

            # 计算各区域的信息
            for area in removed_areas:
                # 计算与文本框相交的部分
                intersects_with_text = False
                intersection_area = 0

                for text_box in text_boxes:
                    if area['polygon'].intersects(text_box):
                        intersects_with_text = True
                        intersection_area += area['polygon'].intersection(text_box).area

                # 计算占比
                total_img_area = img_width * img_height
                area_ratio = area['area'] / total_img_area

                # 判断类型
                if intersects_with_text:
                    intersection_ratio = intersection_area / area['area']
                    area_type = 'intersect_with_text'
                else:
                    intersection_ratio = 0
                    area_type = 'no_intersect_with_text'

                # 添加额外信息
                area['intersects_with_text'] = intersects_with_text
                area['intersection_ratio'] = intersection_ratio
                area['area_ratio_to_image'] = area_ratio
                area['type'] = area_type

            removed_regions[image_name] = removed_areas

    return removed_regions


def extract_single_image_removed_regions(
    img_size: Tuple[int, int],
    annotations: List[Dict[str, Any]]
) -> Dict[str, List[Dict[str, Any]]]:
    """提取单张图片中被移除的图片区域

    Args:
        img_size: 图片尺寸
        annotations: 该图片的标注列表

    Returns:
        Dict[str, List[Dict[str, Any]]]: 包含被移除区域信息的字典，
        区分'intersect_with_text'和'no_intersect_with_text'两种类型
    """
    result = {
        'intersect_with_text': [],
        'no_intersect_with_text': []
    }

    # 尝试获取图片尺寸
    img_width, img_height = img_size

    # 查找包含 "image_removed" 标记的标注
    removed_areas = []
    for annotation_box in annotations:
        transcription = annotation_box.get('transcription', '')

        # 检查是否是被移除的图片区域标记
        if '-$img_rm:' in transcription or 'image_removed' in transcription.lower():
            points = annotation_box.get('points', [])
            if len(points) == 4:
                area = {
                    'points': points,
                    'polygon': Polygon(points),
                    'area': Polygon(points).area,
                    'text': transcription
                }
                removed_areas.append(area)

    # 如果找到了被移除的区域，计算与文本框的交集
    if removed_areas:
        text_boxes = []
        for annotation_box in annotations:
            transcription = annotation_box.get('transcription', '')
            # 只处理非图片移除标记的文本框
            if '-$img_rm:' not in transcription and 'image_removed' not in transcription.lower():
                points = annotation_box.get('points', [])
                if len(points) == 4:  # 确保有4个点
                    text_boxes.append(Polygon(points))

        # 计算各区域的信息
        for area in removed_areas:
            # 计算与文本框相交的部分
            intersects_with_text = False
            intersection_area = 0

            for text_box in text_boxes:
                if area['polygon'].intersects(text_box):
                    intersects_with_text = True
                    intersection_area += area['polygon'].intersection(text_box).area

            # 计算占比
            total_img_area = img_width * img_height
            area_ratio = area['area'] / total_img_area

            # 判断类型
            if intersects_with_text:
                intersection_ratio = intersection_area / area['area']
                area_type = 'intersect_with_text'
                area['intersection_ratio'] = intersection_ratio
            else:
                area_type = 'no_intersect_with_text'
                area['intersection_ratio'] = 0

            # 添加额外信息
            area['intersects_with_text'] = intersects_with_text
            area['area_ratio_to_image'] = area_ratio
            area['source'] = 'removed'  # 标记来源为标注中移除的区域

            # 将区域添加到对应类型
            result[area_type].append(area)

    return result


def generate_candidate_regions(
    img_size: Tuple[int, int],
    annotations: List[Dict[str, Any]],
    min_area_ratio: float = 0.02,
    max_area_ratio: float = 0.10,
    aspect_ratios: List[float] = [1.0, 4/3, 3/4, 16/9, 9/16, 1.5, 1/1.5],
    max_retries: int = 50
) -> Dict[str, List[Dict[str, Any]]]:
    """生成候选的图像插入区域

    Args:
        img_size: 图片尺寸
        annotations: 该图片的标注列表
        min_area_ratio: 插入区域占原图最小面积比例
        max_area_ratio: 插入区域占原图最大面积比例
        aspect_ratios: 候选区域的宽高比列表
        max_retries: 每个宽高比的最大尝试次数

    Returns:
        Dict[str, List[Dict[str, Any]]]: 包含候选区域信息的字典
    """
    result = {
        NATURAL_REGION_FLAG: [],  # 自然图像候选区域
        SEAL_REGION_FLAG: [],           # 印章候选区域
        QRCODE_REGION_FLAG: []          # 二维码候选区域
    }

    img_width, img_height = img_size

    # 构建文本框列表
    text_boxes = []
    for annotation_box in annotations:
        transcription = annotation_box.get('transcription', '')
        # 只处理非图片移除标记的文本框
        if '-$img_rm:' not in transcription and 'image_removed' not in transcription.lower():
            points = annotation_box.get('points', [])
            if len(points) == 4:
                text_boxes.append(Polygon(points))

    # 图片总面积
    total_img_area = img_width * img_height

    # 已选区域列表（用于确保不同区域不重叠）
    selected_regions = []

    # 为每种类型生成候选区域
    for region_type in ['natural_image', 'seal', 'qrcode']:
        # 根据区域类型确定是否可与文本框相交
        allow_intersect_with_text = (region_type != 'qrcode')  # 二维码不允许与文本框相交

        # 对每个宽高比尝试生成区域
        for aspect_ratio in aspect_ratios:
            retries = 0
            while retries < max_retries:
                # 随机生成面积比例
                area_ratio = np.random.uniform(min_area_ratio, max_area_ratio)

                # 计算区域面积
                area = total_img_area * area_ratio

                # 根据面积和宽高比计算宽高
                width = int(np.sqrt(area * aspect_ratio))
                height = int(np.sqrt(area / aspect_ratio))

                # 确保在图片范围内
                if width > img_width * 0.9 or height > img_height * 0.9:
                    retries += 1
                    continue

                # 随机生成左上角坐标
                x = np.random.randint(0, img_width - width)
                y = np.random.randint(0, img_height - height)

                # 创建矩形区域
                rect = box(x, y, x + width, y + height)

                # 检查是否与文本框相交
                intersects_with_text = False
                for text_box in text_boxes:
                    if rect.intersects(text_box):
                        intersects_with_text = True
                        break

                # 检查是否与已选区域相交
                intersects_with_selected = False
                for region in selected_regions:
                    if rect.intersects(region['polygon']):
                        intersects_with_selected = True
                        break

                # 根据区域类型和文本相交情况决定是否接受该区域
                if (not intersects_with_selected) and (allow_intersect_with_text or not intersects_with_text):
                    # 符合条件，添加到对应类型
                    points = [
                        [x, y],
                        [x + width, y],
                        [x + width, y + height],
                        [x, y + height]
                    ]

                    area_dict = {
                        'points': points,
                        'polygon': rect,
                        'area': rect.area,
                        'area_ratio_to_image': area_ratio,
                        'aspect_ratio': aspect_ratio,
                        'intersects_with_text': intersects_with_text,
                        'source': 'generated'  # 标记为自动生成的区域
                    }

                    # 添加到对应类型
                    if region_type == 'natural_image':
                        result[NATURAL_REGION_FLAG].append(area_dict)
                    elif region_type == 'seal':
                        result[SEAL_REGION_FLAG].append(area_dict)
                    elif region_type == 'qrcode':
                        result[QRCODE_REGION_FLAG].append(area_dict)

                    # 添加到已选区域
                    selected_regions.append(area_dict)

                    # 每种宽高比只取一个候选区域
                    break

                retries += 1

    return result


def get_candidate_insertion_regions(
    img_size: Tuple[int, int],
    annotations: List[Dict[str, Any]]
) -> Dict[str, List[Dict[str, Any]]]:
    """
    获取图像插入候选区域，包括自然图像、印章和二维码的区域

    Args:
        annotations: 图片标注数据

    Returns:
        Dict: 包含不同类型候选区域的字典
    """
    result = {
        NATURAL_REGION_FLAG: [],  # 自然图像候选区域
        SEAL_REGION_FLAG: [],           # 印章候选区域
        QRCODE_REGION_FLAG: []          # 二维码候选区域
    }

    # 1. 从标注中提取被移除的区域
    removed_regions = extract_single_image_removed_regions(img_size, annotations)

    # 2. 生成额外的候选区域
    generated_regions = generate_candidate_regions(img_size, annotations)

    # 3. 分配移除的区域到合适的类型
    # 与文本相交的区域可以用于自然图像和印章
    for area in removed_regions['intersect_with_text']:
        # 复制区域信息，添加类型标记
        natural_image_area = area.copy()
        natural_image_area['type'] = 'natural_image'
        result[NATURAL_REGION_FLAG].append(natural_image_area)

        # 相同区域也可用于印章
        seal_area = area.copy()
        seal_area['type'] = 'seal'
        result[SEAL_REGION_FLAG].append(seal_area)

    # 不与文本相交的区域可以用于所有类型
    for area in removed_regions['no_intersect_with_text']:
        # 复制区域信息，添加类型标记
        natural_image_area = area.copy()
        natural_image_area['type'] = 'natural_image'
        result[NATURAL_REGION_FLAG].append(natural_image_area)

        seal_area = area.copy()
        seal_area['type'] = 'seal'
        result[SEAL_REGION_FLAG].append(seal_area)

        qrcode_area = area.copy()
        qrcode_area['type'] = 'qrcode'
        result[QRCODE_REGION_FLAG].append(qrcode_area)

    # 4. 添加生成的候选区域
    result[NATURAL_REGION_FLAG].extend(generated_regions[NATURAL_REGION_FLAG])
    result[SEAL_REGION_FLAG].extend(generated_regions[SEAL_REGION_FLAG])
    result[QRCODE_REGION_FLAG].extend(generated_regions[QRCODE_REGION_FLAG])

    return result


def debug_visualize_regions(image_obj, output_path, regions_to_display, max_dimension=1280) -> None:
    """
    可视化候选区域，方便调试

    在图像上用不同颜色显示各类型区域。
    - 绿色半透明区域: 自然图像区域
    - 红色半透明区域: 印章区域
    - 黑色半透明区域: 二维码区域

    Args:
        image_obj: 原始图像，或者路径
        output_path: 输出图像保存路径，如果提供则保存图像，否则在线显示
        regions_to_display: 要显示的区域字典，格式为{'region_type': [{'points': [...]}]}
        max_dimension: 图像最长边的最大分辨率限制，超过则缩放，默认1280
    """
    # 打开原始图像
    if isinstance(image_obj, Image.Image):
        img = image_obj
    elif isinstance(image_obj, str) and os.path.exists(image_obj):
        img = Image.open(image_obj)
    else:
        raise ValueError(f"invalid image type: {type(image_obj)}")

    # 计算缩放比例
    original_width, original_height = img.size
    scale_factor = 1.0
    max_side = max(original_width, original_height)

    # 如果图像尺寸超过限制，计算缩放比例并调整图像大小
    if max_side > max_dimension:
        scale_factor = max_dimension / max_side
        new_width = int(original_width * scale_factor)
        new_height = int(original_height * scale_factor)
        img = img.resize((new_width, new_height), Image.LANCZOS)
        LOGGER.debug(f"原始尺寸: {original_width}x{original_height}, 缩放后: {new_width}x{new_height}, 比例: {scale_factor:.2f}")

    img_draw = ImageDraw.Draw(img, 'RGBA')

    # 区域颜色定义 (R, G, B, A) - A为透明度
    colors = {
        NATURAL_REGION_FLAG: (0, 255, 0, 128),    # 绿色半透明
        SEAL_REGION_FLAG: (255, 0, 0, 128),             # 红色半透明
        QRCODE_REGION_FLAG: (0, 0, 0, 128)              # 黑色半透明
    }

    # 遍历并绘制每种类型的区域
    if regions_to_display:
        for region_type, regions in regions_to_display.items():
            if region_type in colors and regions:
                for region in regions:
                    if 'points' in region:
                        # 转换为PIL可接受的多边形坐标格式，并应用缩放系数
                        polygon_points = [(int(p[0] * scale_factor), int(p[1] * scale_factor)) for p in region['points']]
                        # 绘制多边形
                        img_draw.polygon(polygon_points, fill=colors[region_type])

    # 判断是保存图像还是显示图像
    if output_path is not None:
        # 直接保存处理后的图像
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        img.save(output_path)
        LOGGER.debug(f"已保存可视化图像到: {output_path}")
    else:
        # 转换为numpy数组供matplotlib显示
        img_array = np.array(img)

        # 创建新图形，不指定固定大小，让matplotlib根据图像自动调整
        dpi = 100  # 设置分辨率以确保清晰度
        height, width, _ = img_array.shape
        figsize = width / dpi, height / dpi  # 根据图像原始尺寸计算figsize

        fig = plt.figure(figsize=figsize, dpi=dpi)
        ax = plt.Axes(fig, [0, 0, 1, 1])  # 创建一个完全填充的坐标轴系，无边距
        ax.set_axis_off()
        fig.add_axes(ax)

        # 显示图像
        ax.imshow(img_array)
        plt.show()


def check_regions_validity(img_size: Tuple[int, int], regions: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[str]]:
    """
    检查非重叠区域的有效性，主要检查：
    1. 是否有框框超出原图边界
    2. 每个框是否满足最长最短边比例限制

    Args:
        img_size: 图像尺寸 (width, height)
        regions: 非重叠区域字典，格式为{'region_type': [{'points': [...]}]}

    Returns:
        Dict[str, List[str]]: 包含各种问题的字典，按区域类型分类
    """
    img_width, img_height = img_size
    # 使用全局定义的宽高比限制

    # 初始化问题记录字典
    issues = {
        NATURAL_REGION_FLAG: [],
        SEAL_REGION_FLAG: [],
        QRCODE_REGION_FLAG: []
    }

    # 检查每种类型的区域
    for region_type, region_list in regions.items():
        if region_type not in issues:
            continue

        for i, region in enumerate(region_list):
            if 'points' not in region:
                issues[region_type].append(f"区域 #{i+1} 缺少points属性")
                continue

            points = region['points']

            # 检查是否有点超出图像边界
            out_of_bounds = False
            for x, y in points:
                if x < 0 or x >= img_width or y < 0 or y >= img_height:
                    out_of_bounds = True
                    issues[region_type].append(f"区域 #{i+1} 有点超出图像边界: ({x}, {y})")
                    break

            # 检查宽高比
            x_coords = [p[0] for p in points]
            y_coords = [p[1] for p in points]
            width = max(x_coords) - min(x_coords)
            height = max(y_coords) - min(y_coords)

            if width == 0 or height == 0:
                issues[region_type].append(f"区域 #{i+1} 宽度或高度为0")
            else:
                aspect_ratio = max(width, height) / min(width, height)
                if aspect_ratio > MAX_ASPECT_RATIO:
                    issues[region_type].append(
                        f"区域 #{i+1} 宽高比不合理: {aspect_ratio:.2f} > {MAX_ASPECT_RATIO} "
                        f"(宽: {width:.1f}, 高: {height:.1f})"
                    )

    # 移除没有问题的空列表
    return {k: v for k, v in issues.items() if v}


def adjust_qrcode_region(qr_region, overlapping_region, img_width, img_height, min_area_ratio, total_img_area):
    """调整二维码区域，裁剪掉与其他区域重叠的部分

    尝试通过裁剪方式调整二维码区域，确保处理后的区域是原区域的子集：
    1. 计算二维码区域与重叠区域的交叉情况
    2. 找出不重叠的最大子区域（保持矩形形状）
    3. 验证子区域是否满足最小面积要求
    4. 验证子区域是否满足合理的宽高比要求

    Args:
        qr_region: 要调整的二维码区域
        overlapping_region: 与二维码重叠的区域
        img_width: 图像宽度
        img_height: 图像高度
        min_area_ratio: 最小区域面积比例
        total_img_area: 图像总面积

    Returns:
        裁剪后的二维码区域，如果无法裁剪则返回None
    """
    # 定义宽高比限制
    max_aspect_ratio = 2.0  # 与 get_non_overlapping_interesting_regions 函数中的值保持一致

    # 检查宽高比是否合理
    def is_good_aspect_ratio(rect):
        minx, miny, maxx, maxy = rect.bounds
        width = maxx - minx
        height = maxy - miny

        if width == 0 or height == 0:
            return False

        # 计算宽高比
        aspect_ratio = max(width / height, height / width)

        # 返回是否合理
        return aspect_ratio <= max_aspect_ratio
    # 获取二维码区域的边界
    qr_points = qr_region['points']
    qr_x_min = min(p[0] for p in qr_points)
    qr_y_min = min(p[1] for p in qr_points)
    qr_x_max = max(p[0] for p in qr_points)
    qr_y_max = max(p[1] for p in qr_points)

    # 创建原始二维码的矩形区域
    qr_rect = box(qr_x_min, qr_y_min, qr_x_max, qr_y_max)

    # 获取重叠区域
    overlap_poly = overlapping_region['polygon']

    # 如果没有重叠，直接返回原始区域
    if not qr_rect.intersects(overlap_poly):
        return qr_region

    # 计算重叠部分
    intersection = qr_rect.intersection(overlap_poly)

    # 获取重叠区域的边界框
    overlap_bounds = intersection.bounds  # (minx, miny, maxx, maxy)
    if not overlap_bounds or len(overlap_bounds) != 4:
        # 如果无法确定边界，返回None
        return None

    overlap_x_min, overlap_y_min, overlap_x_max, overlap_y_max = overlap_bounds

    # 尝试几种裁剪策略（保持矩形形状）
    candidate_rects = []

    # 策略1：裁剪右侧部分（如果重叠在左侧）
    if overlap_x_max < qr_x_max and (overlap_x_max - qr_x_min) < (qr_x_max - overlap_x_max):
        rect = box(overlap_x_max, qr_y_min, qr_x_max, qr_y_max)
        area_ratio = rect.area / total_img_area
        if area_ratio >= min_area_ratio and is_good_aspect_ratio(rect):
            candidate_rects.append(('right', rect, area_ratio))

    # 策略2：裁剪左侧部分（如果重叠在右侧）
    if overlap_x_min > qr_x_min and (qr_x_max - overlap_x_min) < (overlap_x_min - qr_x_min):
        rect = box(qr_x_min, qr_y_min, overlap_x_min, qr_y_max)
        area_ratio = rect.area / total_img_area
        if area_ratio >= min_area_ratio and is_good_aspect_ratio(rect):
            candidate_rects.append(('left', rect, area_ratio))

    # 策略3：裁剪下方部分（如果重叠在上方）
    if overlap_y_max < qr_y_max and (overlap_y_max - qr_y_min) < (qr_y_max - overlap_y_max):
        rect = box(qr_x_min, overlap_y_max, qr_x_max, qr_y_max)
        area_ratio = rect.area / total_img_area
        if area_ratio >= min_area_ratio and is_good_aspect_ratio(rect):
            candidate_rects.append(('bottom', rect, area_ratio))

    # 策略4：裁剪上方部分（如果重叠在下方）
    if overlap_y_min > qr_y_min and (qr_y_max - overlap_y_min) < (overlap_y_min - qr_y_min):
        rect = box(qr_x_min, qr_y_min, qr_x_max, overlap_y_min)
        area_ratio = rect.area / total_img_area
        if area_ratio >= min_area_ratio and is_good_aspect_ratio(rect):
            candidate_rects.append(('top', rect, area_ratio))

    # 如果有可行的裁剪方案，选择面积最大的
    if candidate_rects:
        # 按面积比例排序，选择最大的
        candidate_rects.sort(key=lambda x: x[2], reverse=True)
        best_position, best_rect, best_area_ratio = candidate_rects[0]

        # 提取矩形的角点坐标
        minx, miny, maxx, maxy = best_rect.bounds
        new_points = [
            [minx, miny],
            [maxx, miny],
            [maxx, maxy],
            [minx, maxy]
        ]

        # 返回裁剪后的区域
        return {
            'points': new_points,
            'polygon': best_rect,
            'area': best_rect.area,
            'area_ratio_to_image': best_area_ratio,
            'source': f'cropped_{best_position}'  # 标记为裁剪后的区域
        }

    # 如果没有找到合适的裁剪方案，尝试更复杂的子区域提取
    # 找出二维码区域中不与重叠区域相交的最大矩形

    # 如果所有策略都失败，返回None
    return None


def clip_points_to_image_bounds_global(points, img_size) -> List[Tuple[float, float]]:
    """
    将点坐标截断到图像边界内

    Args:
        points: 坐标点列表
        img_size: 图像尺寸 (width, height)

    Returns:
        截断后的坐标点列表
    """
    img_width, img_height = img_size

    # 检查是否有点超出边界
    has_out_of_bounds = False
    for x, y in points:
        if x < 0 or x >= img_width or y < 0 or y >= img_height:
            has_out_of_bounds = True
            break

    # 如果没有超出边界的点，直接返回原始坐标
    if not has_out_of_bounds:
        return points

    # 将超出边界的点截断到图像边界内
    clipped_points = []
    for x, y in points:
        clipped_x = max(0, min(img_width - 1, x))
        clipped_y = max(0, min(img_height - 1, y))
        clipped_points.append((clipped_x, clipped_y))

    return clipped_points


def clip_original_removed_regions(annotation, img_size) -> bool:
    if 'points' not in annotation or annotation['transcription'] != "-$img_rm: don't training$-":
        return

    # 使用截断函数处理坐标
    original_points = annotation['points']
    clipped_points = clip_points_to_image_bounds_global(original_points, img_size)

    # 更新标注数据中的坐标
    annotation['points'] = clipped_points

    # 如果有面积属性，也需要更新
    if 'area' in annotation:
        original_area = annotation['area']
        new_area = Polygon(clipped_points).area
        annotation['area'] = new_area
        LOGGER.debug(f"面积修改: {original_area:.2f} -> {new_area:.2f}")


def clip_all_original_removed_regions(annotations, img_size) -> int:
    """
    截断所有图像移除区域的坐标点

    Args:
        annotations: 标注对象列表
        img_size: 图像尺寸 (width, height)
    """
    for annotation in annotations:
        clip_original_removed_regions(annotation, img_size)


def filter_regions_by_text_overlap(regions_dict: Dict[str, List[Dict[str, Any]]], annotations: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """
    在获取到non_overlap_interesting_regions后，对QRCODE_REGION_FLAG和NATURAL_REGION_FLAG再进行一次过滤，
    确保它们不与任何文本区域重叠（而不是image_removed=True的区域）。
    
    Args:
        regions_dict: 包含不同类型区域的字典，通常是get_non_overlapping_interesting_regions的输出
        annotations: 图像标注数据列表
    
    Returns:
        Dict[str, List[Dict[str, Any]]]: 过滤后的区域字典
    """
    # 如果regions_dict为空或不是字典类型，直接返回
    if not regions_dict or not isinstance(regions_dict, dict):
        return regions_dict
    
    # 拷贝一份，避免修改原始数据
    filtered_regions = {k: v.copy() if isinstance(v, list) else v for k, v in regions_dict.items()}
    
    # 仅处理需要过滤的两种类型
    filter_types = [QRCODE_REGION_FLAG, NATURAL_REGION_FLAG]
    for filter_type in filter_types:
        if filter_type not in filtered_regions:
            continue
    
    # 提取所有文本区域多边形（排除image_removed=True的区域）
    text_polygons = []
    for annot in annotations:
        # 跳过image_removed类型的区域
        if 'transcription' in annot:
            transcription = annot.get('transcription', '')
            if '-$img_rm:' in transcription or 'image_removed' in transcription.lower():
                continue
        
        # 只收集有效的文本区域
        if 'points' in annot and len(annot['points']) >= 4:
            text_polygons.append(Polygon(annot['points']))
    
    # 如果没有文本区域，直接返回原始区域
    if not text_polygons:
        return filtered_regions
    
    # 为每种需要过滤的区域类型执行过滤
    for region_type in filter_types:
        if region_type not in filtered_regions:
            continue
            
        filtered_type_regions = []
        
        for region in filtered_regions[region_type]:
            region_poly = Polygon(region['points'])
            
            # 检查是否与任何文本区域重叠
            overlap_with_text = False
            for text_poly in text_polygons:
                if region_poly.intersects(text_poly):
                    overlap_with_text = True
                    break
            
            # 只保留不与文本重叠的区域
            if not overlap_with_text:
                filtered_type_regions.append(region)
        
        # 更新过滤后的区域
        filtered_regions[region_type] = filtered_type_regions
        LOGGER.debug(f"过滤后的{region_type}区域数: {len(filtered_type_regions)}")
    
    return filtered_regions


def get_non_overlapping_interesting_regions(
    img_size: Tuple[int, int],
    annotations: List[Dict[str, Any]]
) -> Dict[str, List[Dict[str, Any]]]:
    """
    获取三种不互相重叠的区域：自然图像区域、印章区域、二维码区域
    要求：
    1. 自然图像区域和二维码区域需确保不与文字框重叠
    2. 三种区域互相之间不重叠
    3. 印章区域可以与文字框重叠
    4. 区域不能过于细长，有合理的宽高比
    5. 每张图至少有一个自然图像区域、一个印章区域和一个二维码区域

    Args:
        img_size: 图像尺寸 (width, height)
        annotations: 图像标注数据列表

    Returns:
        Dict[str, List[Dict[str, Any]]]: 包含三种类型不重叠区域的字典，若无法找到满足条件的区域，则相应类型的列表为空
    """
    img_width, img_height = img_size
    total_img_area = img_height * img_width
    # 使用全局定义的参数(MIN_AREA_RATIO, MAX_ASPECT_RATIO, MAX_REGIONS_PER_TYPE)

    def get_region_area(region) -> float:
        """获取区域面积"""
        if 'area' in region:
            return region['area']
        return Polygon(region['points']).area

    def get_aspect_ratio(points) -> float:
        """计算区域的宽高比"""
        x_coords = [p[0] for p in points]
        y_coords = [p[1] for p in points]
        width = max(x_coords) - min(x_coords)
        height = max(y_coords) - min(y_coords)
        if width == 0 or height == 0:
            return 1e10
        return max(width, height) / min(width, height)

    def is_good_aspect_ratio(points) -> bool:
        """检查区域的宽高比是否合理"""
        return get_aspect_ratio(points) <= MAX_ASPECT_RATIO

    def sort_regions_by_aspect_ratio(regions) -> List[Dict[str, Any]]:
        """
        按宽高比排序区域，只返回宽高比合理的区域
        先过滤，再排序，确保没有细长的框
        """
        if not regions:
            return []

        # 按宽高比从好到差排序 (越接近正方形越好)
        return sorted(regions, key=lambda r: get_aspect_ratio(r['points']))

    def check_text_overlap(region_poly, text_polys) -> bool:
        """检查区域是否与文字框重叠"""
        for text_poly in text_polys:
            if region_poly.intersects(text_poly):
                return True
        return False

    def check_selected_overlap(region_poly, selected_polys) -> bool:
        """检查区域是否与已选区域重叠"""
        for poly in selected_polys:
            if region_poly.intersects(poly):
                return True
        return False

    def filter_by_area_and_ratio(regions, min_ratio) -> List[Dict[str, Any]]:
        """按面积和宽高比过滤区域"""
        cleaned_regions = []
        for r in regions:
            if get_region_area(r) / total_img_area < min_ratio:
                continue
            if not is_good_aspect_ratio(r['points']):
                continue
            cleaned_regions.append(r)

        return cleaned_regions

    def select_regions(candidates, selected_polys, check_text=True, text_polys=None, max_count=2) -> List[Dict[str, Any]]:
        """
        选择区域的通用函数

        Args:
            candidates: 候选区域列表
            selected_polys: 已选区域多边形列表
            check_text: 是否检查与文字框的重叠
            text_polys: 文字框多边形列表
            max_count: 最多选择的区域数量

        Returns:
            符合条件的区域列表
        """
        valid_regions = []

        for region in candidates:
            region_poly = Polygon(region['points'])

            # 检查与已选区域的重叠
            if check_selected_overlap(region_poly, selected_polys):
                continue

            # 检查与文字框的重叠（如果需要）
            if check_text and check_text_overlap(region_poly, text_polys):
                continue

            valid_regions.append(region)

        # 按宽高比排序并选择
        sorted_regions = sort_regions_by_aspect_ratio(valid_regions)
        return sorted_regions[:min(max_count, len(sorted_regions))]

    # 获取所有文本框区域多边形
    text_polygons = []
    for annot in annotations:
        if 'points' in annot and annot['transcription'] != "-$img_rm: don't training$-" and len(annot['points']) >= 4:
            text_polygons.append(Polygon(annot['points']))

    # 获取候选区域
    candidate_regions = get_candidate_insertion_regions(img_size, annotations)

    # 存储最终选定的不重叠区域
    final_regions = {}
    selected_polygons = []  # 用于跟踪已选区域的多边形

    # 1. 首先处理自然图像区域 - 确保不与文字框重叠
    natural_image_regions = []
    if NATURAL_REGION_FLAG in candidate_regions and candidate_regions[NATURAL_REGION_FLAG]:
        # 过滤候选区域
        filtered_candidates = filter_by_area_and_ratio(candidate_regions[NATURAL_REGION_FLAG], MIN_AREA_RATIO)

        # 选择区域
        selected_regions = select_regions(
            filtered_candidates,
            selected_polygons,
            check_text=True,
            text_polys=text_polygons,
            max_count=MAX_REGIONS_PER_TYPE
        )

        # 选择符合条件的区域
        if selected_regions:
            for region in selected_regions:
                region_poly = Polygon(region['points'])
                natural_image_regions.append(region)
                selected_polygons.append(region_poly)

    final_regions[NATURAL_REGION_FLAG] = natural_image_regions

    # 2. 处理印章区域 - 可以与文字框重叠，但不能与已选区域重叠
    seal_regions = []
    if SEAL_REGION_FLAG in candidate_regions and candidate_regions[SEAL_REGION_FLAG]:
        # 过滤候选区域
        filtered_candidates = filter_by_area_and_ratio(candidate_regions[SEAL_REGION_FLAG], MIN_AREA_RATIO)

        # 选择区域 - 印章区域不需要检查与文本框的重叠
        selected_regions = select_regions(
            filtered_candidates,
            selected_polygons,
            check_text=False,
            max_count=MAX_REGIONS_PER_TYPE
        )

        # 如果找到了合适的区域
        if selected_regions:
            for region in selected_regions:
                region_poly = Polygon(region['points'])
                seal_regions.append(region)
                selected_polygons.append(region_poly)

    final_regions[SEAL_REGION_FLAG] = seal_regions

    # 3. 处理二维码区域 - 不与文字框和已选区域重叠
    qrcode_regions = []
    if QRCODE_REGION_FLAG in candidate_regions and candidate_regions[QRCODE_REGION_FLAG]:
        # 过滤候选区域
        filtered_candidates = filter_by_area_and_ratio(
            candidate_regions[QRCODE_REGION_FLAG],
            MIN_AREA_RATIO
        )

        # 选择不与文字框重叠的区域
        valid_regions = []
        for region in filtered_candidates:
            region_poly = Polygon(region['points'])
            if not check_text_overlap(region_poly, text_polygons):
                valid_regions.append(region)

        # 按宽高比排序并选择候选二维码
        candidate_qrcodes = []
        if valid_regions:
            sorted_regions = sort_regions_by_aspect_ratio(valid_regions)
            num_to_select = min(MAX_REGIONS_PER_TYPE, len(sorted_regions))
            candidate_qrcodes = sorted_regions[:num_to_select]

        # 选择二维码区域，需要处理重叠裁剪
        qr_count = 0
        max_qr_count = MAX_REGIONS_PER_TYPE

        for qr_region in candidate_qrcodes:
            if qr_count >= max_qr_count:
                break

            qr_polygon = Polygon(qr_region['points'])
            overlaps = False

            # 检查与已选区域的重叠
            for idx, existing_poly in enumerate(selected_polygons):
                if qr_polygon.intersects(existing_poly):
                    overlaps = True
                    # 如果已达到最大限制，不再裁剪
                    if qr_count >= max_qr_count:
                        break

                    # 尝试裁剪
                    existing_region = {'polygon': existing_poly}
                    adjusted_qr = adjust_qrcode_region(
                        qr_region,
                        existing_region,
                        img_width,
                        img_height,
                        MIN_AREA_RATIO,
                        total_img_area
                    )

                    if adjusted_qr:
                        # 检查调整后的区域是否与其他已选区域重叠
                        adjusted_poly = Polygon(adjusted_qr['points'])
                        if not check_selected_overlap(adjusted_poly, selected_polygons):
                            # 调整成功且不与其他区域重叠
                            qrcode_regions.append(adjusted_qr)
                            selected_polygons.append(adjusted_poly)
                            qr_count += 1
                            break

            if not overlaps and qr_count < max_qr_count:
                # 如果原始区域不重叠，直接添加
                qrcode_regions.append(qr_region)
                selected_polygons.append(qr_polygon)
                qr_count += 1
        
    final_regions[QRCODE_REGION_FLAG] = qrcode_regions

    # 输出不重叠区域信息
    LOGGER.debug(f"自然图像不重叠区域数: {len(final_regions[NATURAL_REGION_FLAG])}")
    LOGGER.debug(f"印章不重叠区域数: {len(final_regions[SEAL_REGION_FLAG])}")
    LOGGER.debug(f"二维码不重叠区域数: {len(final_regions[QRCODE_REGION_FLAG])}")
    
    return final_regions


def select_median_area_region(regions, region_type="general", img_size=None):
    """
    从给定的区域列表中选择中位大小的区域

    Args:
        regions: 区域列表，每个区域包含 'points' 字段
        region_type: 区域类型描述，用于日志输出
        img_size: 原图尺寸，用于计算面积占比 (width, height)

    Returns:
        选中的区域点坐标列表 [(x1,y1), (x2,y2), (x3,y3), (x4,y4)] 或 None
    """
    area_regions = []
    
    # 计算原图总面积
    img_total_area = img_size[0] * img_size[1] if img_size else None
    
    # 遍历区域，计算面积
    for region in regions:
        if 'points' in region:
            points = region['points']
            # 计算多边形面积
            poly = Polygon(points)
            area = poly.area
            area_regions.append({
                'points': points,
                'area': area,
                'area_ratio': area / img_total_area if img_total_area else None,
                'type': region.get('type', region_type)
            })
    
    # 按面积排序
    area_regions.sort(key=lambda x: x['area'])
    
    # 选择中位数大小的区域
    if area_regions:
        median_index = len(area_regions) // 2
        selected_region = area_regions[median_index]
        target_region = [(int(p[0]), int(p[1])) for p in selected_region['points']]
        
        # 根据是否提供图像尺寸来切换日志输出格式
        if img_total_area and 'area_ratio' in selected_region:
            # 显示面积占比
            LOGGER.debug(f"选择了面积占比为 {selected_region['area_ratio']*100:.2f}% 的{region_type}区域")
        else:
            # 显示原始面积
            LOGGER.debug(f"选择了面积为 {selected_region['area']:.2f} 的{region_type}区域")
            
        # 如果区域有类型信息且与当前指定类型不同，输出类型信息
        if 'type' in selected_region and selected_region['type'] != region_type:
            LOGGER.debug(f"区域类型为: {selected_region['type']}")
        return target_region
    else:
        LOGGER.debug(f"未找到合适的{region_type}区域")
        return None


def merge_regions(regions_dict, key1, key2):
    """
    合并两类区域
    
    Args:
        regions_dict: 包含多种类型区域的字典
        key1: 第一类区域的键
        key2: 第二类区域的键
        
    Returns:
        合并后的区域列表
    """
    merged_regions = []
    
    # 添加第一类区域
    if key1 in regions_dict and regions_dict[key1]:
        for region in regions_dict[key1]:
            region_copy = region.copy()
            region_copy['type'] = key1
            merged_regions.append(region_copy)
    
    # 添加第二类区域
    if key2 in regions_dict and regions_dict[key2]:
        for region in regions_dict[key2]:
            region_copy = region.copy()
            region_copy['type'] = key2
            merged_regions.append(region_copy)
            
    return merged_regions


def should_process_file(
    filename: str,
    target_types: Optional[Set[str]] = None,
    target_qualities: Optional[Set[str]] = None
) -> bool:
    """判断文件是否需要处理

    Args:
        filename: 文件名
        target_types: 目标类型集合，如 {'t01', 't11'}
        target_qualities: 目标质量等级集合，如 {'qL0', 'qL1'}

    Returns:
        是否需要处理该文件
    """
    # 检查文件是否为jpg
    if not filename.endswith('.jpg'):
        return False

    # 解析文件名中的类型和质量标记
    parts = filename.split('-')
    if len(parts) < 3:
        return False

    type_mark = parts[1]     # t00, t01等
    quality_mark = parts[2]  # qL0, qL1等

    # 如果指定了目标类型，检查类型是否匹配
    if target_types and type_mark not in target_types:
        return False

    # 如果指定了目标质量等级，检查质量是否匹配
    if target_qualities and quality_mark not in target_qualities:
        return False

    return True


def process_label_task(label_file, src_dir, dst_dir, all_annotations, adv_aug_config_path, target_types, target_qualities, task_id):
    """
    处理单个label文件的任务函数，作为单独的进程运行
    
    Args:
        label_file: 标注文件路径
        src_dir: 源目录
        dst_dir: 目标目录
        all_annotations: 所有标注数据
        adv_aug_config_path: 对抗性增强器配置文件路径
        target_types: 目标类型
        target_qualities: 目标质量等级
        task_id: 任务ID，用于生成idx
    """
    LOGGER.debug(f"进程 {task_id} 处理文件: {label_file}")
    # 在进程内部初始化增强器实例
    adv_augmentor = AdversarialAugmentor(config_path=adv_aug_config_path)
    base_dir = label_file.parent
    cnt = task_id * 10000
    
    # 准备目标Label.txt文件路径，但不直接复制
    label_src_file = label_file
    label_dst_file = os.path.join(dst_dir, base_dir.relative_to(src_dir), label_file.name)
    Path(label_dst_file).parent.mkdir(parents=True, exist_ok=True)
    
    # 确保目标Label.txt文件是空的，用于追加写入
    if os.path.exists(label_dst_file):
        open(label_dst_file, 'w', encoding='utf-8').close()

    # 获取已解析的标注文件
    processed_count = 0
    annotations = all_annotations[label_file]
    total_images = len(annotations)
    local_progress = tqdm(total=total_images, desc=f"进程 {task_id}", position=task_id, leave=False)

    # 遍历每张图片
    md5_cnt = dict()
    image_name_keys = list(annotations.keys())
    random.shuffle(image_name_keys)
    for image_name in image_name_keys:
        local_progress.set_description(f"进程 {task_id} - {Path(image_name).name}")

        file_md5 = get_md5_from_filename(image_name)
        if file_md5 in md5_cnt:
            md5_cnt[file_md5] += 1
        else:
            md5_cnt[file_md5] = 1

        if md5_cnt[file_md5] > 5:
            local_progress.update(1)
            continue

        image_annotations = annotations[image_name]
        image_path = base_dir.parent / image_name
        image_annotations_copy = copy.deepcopy(image_annotations)

        # 尝试判断是否为目标资源，并且尝试获取图片以及尺寸
        if not should_process_file(image_name, target_types, target_qualities):
            local_progress.update(1)
            continue

        try:
            if not image_path.exists():
                print('\n')
                LOGGER.warning(f"图片不存在: {image_path}")
                local_progress.update(1)
                continue
            image_pil = Image.open(image_path)
            img_width, img_height = image_pil.size

        except Exception as e:
            print('\n')
            LOGGER.warning(f"无法打开图片 {image_path}: {e}")
            local_progress.update(1)
            continue

        # 可视化候选区域和不重叠区域，方便调试
        output_image_path = os.path.join(dst_dir, Path(image_path).relative_to(src_dir))
        os.makedirs(os.path.dirname(output_image_path), exist_ok=True)

        # 预处理标注数据，确保所有坐标都在图像边界内
        clip_all_original_removed_regions(image_annotations, (img_width, img_height))

        # 获取不重叠的区域
        non_overlap_interesting_regions = get_non_overlapping_interesting_regions(
            (img_width, img_height),
            image_annotations
        )

        # 额外过滤，确保QRCODE_REGION_FLAG和NATURAL_REGION_FLAG不与文本区域重叠
        non_overlap_interesting_regions = filter_regions_by_text_overlap(
            non_overlap_interesting_regions,
            image_annotations
        )

        check_regions_validity((img_width, img_height), non_overlap_interesting_regions)

        # 选择印章区域：从 SEAL_REGION_FLAG 中选择中位大小的区域
        target_seal_region = None
        if SEAL_REGION_FLAG in non_overlap_interesting_regions:
            target_seal_region = select_median_area_region(
                non_overlap_interesting_regions[SEAL_REGION_FLAG],
                '印章',
                (img_width, img_height)  # 传入图像尺寸参数
            )

        # 合并自然区域和二维码区域，选择中位大小区域用于二维码
        merged_regions = merge_regions(
            non_overlap_interesting_regions,
            NATURAL_REGION_FLAG,
            QRCODE_REGION_FLAG
        )
        target_natural_region = select_median_area_region(
            merged_regions,
            '二维码放置',
            (img_width, img_height)  # 传入图像尺寸参数
        )

        # 调用增强器并获取状态信息
        image_augmented, augmentation_status = adv_augmentor(
            image=image_pil,
            seal_rect_pts=target_seal_region,
            qrcode_rect_pts=target_natural_region,
            idx=cnt,
        )

        # 输出各种增强的应用状态
        if not augmentation_status.get('has_been_augmented', False):
            cnt += 1
            local_progress.update(1)
            continue

        # 保存增强后的图片
        image_augmented.save(output_image_path)

        if os.path.exists(output_image_path):
            # 图片保存成功后，立即写入对应的标注信息
            rel_path = Path(output_image_path).relative_to(Path(label_dst_file).parent.parent)
            rel_path_str = str(rel_path)

            with open(label_dst_file, 'a', encoding='utf-8') as f:
                f.write(f"{rel_path_str}\t{json.dumps(image_annotations_copy)}\n")

            LOGGER.debug(f"已保存图片: {output_image_path}")
            LOGGER.debug(f"已保存标注信息: {rel_path_str}")

        else:
            print(f"\n")
            LOGGER.error(f"图片可能保存失败: {output_image_path}，文件不存在或大小为0")
        
        # 更新计数器和进度条
        cnt += 1
        processed_count += 1
        local_progress.update(1)
    
    # 处理完所有图片后，关闭进度条
    local_progress.close()
    return processed_count


def main():
    # 设置多进程支持
    try:
        multiprocessing.set_start_method('spawn')
    except RuntimeError:
        pass

    # 进程数量
    num_workers = 2
        
    # 资料路径
    src_dir = "/aipdf-mlp/xelawk/datasets_release_v4/release_v2/auto_labeled_filtered_clean"
    dst_dir = "/aipdf-mlp/xelawk/datasets_release_v4/release_v2/auto_labeled_filtered_clean_degrade"
    adv_aug_config_path = "configs/image_ocr/adversarial_augmentor/default_only_degrade.yaml"

    # 要提取的目标类型和质量等级，如果不需要，可设为设为None
    target_types = ['t00', 't10']
    target_qualities = ['qL0', 'qL1']

    # 清理目标目录
    if os.path.exists(dst_dir):
        shutil.rmtree(dst_dir)
    target_types = set(target_types)
    target_qualities = set(target_qualities)
    
    # 查找所有标注文件
    all_label_files = list(find_label_files(src_dir))
    random.shuffle(all_label_files)
    all_label_files = all_label_files[:20]

    dataset_by_src = dict()
    for label_file in all_label_files:
        label_src = label_file.parts[-4]
        if label_src not in dataset_by_src:
            dataset_by_src[label_src] = [label_file]
        else:
            dataset_by_src[label_src].append(label_file)

    all_label_files = []
    src_keys = list(dataset_by_src.keys())
    while any(len(dataset_by_src[src]) > 0 for src in src_keys):
        for src in src_keys:
            if len(dataset_by_src[src]) > 0:
                all_label_files.append(dataset_by_src[src].pop(0))

    LOGGER.debug(f"找到 {len(all_label_files)} 个标注文件")
    
    # 预先计算总的annotations数量
    total_annotations_count = 0
    all_annotations = {}  # 存储所有文件的annotations，避免重复解析
    
    for label_file in all_label_files:
        annotations = parse_label_file(label_file)
        all_annotations[label_file] = annotations
        total_annotations_count += len(annotations)
    
    LOGGER.debug(f"总共找到 {total_annotations_count} 个图片标注")
    
    # 多进程处理 - 每个进程负责一个标注文件
    # 计算可用的CPU核心数
    max_workers = min(multiprocessing.cpu_count(), num_workers)
    LOGGER.debug(f"使用 {max_workers} 个进程处理 {len(all_label_files)} 个标注文件")
    
    # 创建进程池用于处理
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 准备任务
        futures = []
        for i, label_file in enumerate(all_label_files):
            future = executor.submit(
                process_label_task, 
                label_file, 
                src_dir, 
                dst_dir, 
                all_annotations, 
                adv_aug_config_path,
                target_types, 
                target_qualities, 
                i
            )
            futures.append(future)
        
        # 显示总体进度
        total_processed = 0
        with tqdm(total=total_annotations_count, desc="总进度", position=0) as progress_bar:
            for future in as_completed(futures):
                try:
                    processed_count = future.result()
                    total_processed += processed_count
                    progress_bar.update(processed_count)
                except Exception as e:
                    LOGGER.error(f"处理标注文件时发生错误: {e}")
                    continue
        
        LOGGER.debug(f"总共成功处理 {total_processed} 个图片")


if __name__ == '__main__':
    main()
