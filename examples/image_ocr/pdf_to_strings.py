#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/02/12 10:31
# <AUTHOR> <EMAIL>
# @FileName: pdf_to_strings.py

import fitz

def extract_pdf_text(pdf_path: str) -> list:
    """
    使用fitz(PyMuPDF)提取PDF文本
    Args:
        pdf_path: PDF文件路径
    Returns:
        list: 每页文本内容的列表
    """
    doc = fitz.open(pdf_path)
    pages_text = []
    
    for page in doc:
        text = page.get_text()
        pages_text.append(text)
    
    doc.close()
    return pages_text

def main():
    pdf_path = "/Users/<USER>/Documents/临时数据/tmp/8c95276147d894813e301617ce682850.pdf"
    pages_text = extract_pdf_text(pdf_path)
    
    for i, text in enumerate(pages_text, 1):
        print(f"\n=== 第 {i} 页 ===")
        print(text)

if __name__ == "__main__":
    main()
