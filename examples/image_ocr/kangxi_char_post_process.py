#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/2/18 16:14
# <AUTHOR> <EMAIL>
# @FileName: kangxi_char_post_process

import json
from tqdm import tqdm
from pathlib import Path
from typing import Iterator
from modules.image_ocr.utils.text_proc import replace_kangxi_chars
from modules.image_ocr.dto.spec_mapping_dto import KANGXI_STRICT_MAPPING


def find_label_files(root_dir: Path) -> Iterator[Path]:
    """
    递归遍历目录找到所有的Label.txt文件
    
    Args:
        root_dir: 根目录路径
    
    Yields:
        找到的Label.txt文件的Path对象
    """
    # 使用pathlib的rglob方法递归查找所有Label.txt文件
    for path in root_dir.rglob('Label.txt'):
        # 跳过隐藏目录中的文件
        if any(part.startswith('.') for part in path.parts):
            continue
        yield path


def process_label_file(label_file: Path) -> None:
    """
    处理单个Label.txt文件，替换其中的康熙部首字符
    
    Args:
        label_file: Label.txt文件路径
    """
    if not label_file.exists():
        raise FileNotFoundError(f"找不到输入文件: {label_file}")
    
    # 构造输出文件路径
    output_file = label_file.parent / 'Label_fixed.txt'
    
    # 读取并处理每一行
    processed_lines = []
    file_has_replacements = False
    
    with label_file.open('r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
                
            # 分割文件名和JSON数据
            try:
                filename, json_str = line.split('\t', 1)
                annotations = json.loads(json_str)
                
                # 处理每个标注
                line_has_replacements = False
                for anno in annotations:
                    if 'transcription' in anno:
                        new_text, replacements = replace_kangxi_chars(
                            anno['transcription'], 
                            KANGXI_STRICT_MAPPING
                        )
                        
                        if replacements:
                            line_has_replacements = True
                            file_has_replacements = True
                            print(f"\n在文件 {label_file} 中发现需要替换的内容：")
                            print(f"- 图片文件: {filename}")
                            print(f"- 行号: {line_num}")
                            print(f"- 原文本: {anno['transcription']}")
                            print(f"- 替换后: {new_text}")
                            print("- 替换详情:")
                            for kangxi, modern in replacements:
                                print(f"  康熙字符 '{kangxi}' -> 现代字符 '{modern}'")
                        
                        anno['transcription'] = new_text
                
                # 重新组合行
                processed_line = f"{filename}\t{json.dumps(annotations, ensure_ascii=False)}"
                processed_lines.append(processed_line)
                
            except (ValueError, json.JSONDecodeError) as e:
                print(f"处理行时出错: {line[:100]}...")
                print(f"错误: {str(e)}")
                continue
    
    # 写入处理后的文件
    output_file.write_text('\n'.join(processed_lines) + '\n', encoding='utf-8')
    
    if file_has_replacements:
        print(f"\n已完成文件处理，输出至: {output_file}")
    else:
        print(f"\n文件 {label_file} 中未发现需要替换的内容")


def process_directory(root_dir: str | Path) -> None:
    """
    处理目录下的所有Label.txt文件
    
    Args:
        root_dir: 根目录路径，可以是字符串或Path对象
    """
    # 转换为Path对象
    root_path = Path(root_dir)
    
    # 确保目录存在
    if not root_path.is_dir():
        raise NotADirectoryError(f"目录不存在: {root_path}")
    
    # 先收集所有需要处理的文件
    label_files = list(find_label_files(root_path))
    
    if not label_files:
        print(f"在目录 {root_path} 下没有找到Label.txt文件")
        return
        
    print(f"\n共找到 {len(label_files)} 个Label.txt文件需要处理")
    
    # 使用tqdm显示处理进度
    for label_file in tqdm(label_files, desc="处理进度"):
        try:
            process_label_file(label_file)
        except Exception as e:
            print(f"\n处理文件 {label_file} 时出错: {str(e)}")
            continue


if __name__ == '__main__':
    root_dir = "/aipdf-mlp/xelawk/datasets_release/auto_labeled_with_image_filtered"  # 请替换为实际的根目录路径
    process_directory(root_dir)
