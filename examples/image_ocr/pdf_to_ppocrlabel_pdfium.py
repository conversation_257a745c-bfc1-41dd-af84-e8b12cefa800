#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/17 14:35
# <AUTHOR> <EMAIL>
# @FileName: pdf_to_ppocrlabel_pdfium.py

from third_parties import init_third_parties_env
init_third_parties_env()

import os
import math
import shutil
import traceback

import tqdm
import json
import random
import multiprocessing as mp

from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Tuple

import fitz
import pypdfium2 as pdfium
from PIL import Image, ImageDraw
from pypdfium2.internal import pdfium_c

import logging
from modules.utils.log import LOGGER
from modules.utils.fs import calculate_file_md5
from modules.image_ocr.dto.spec_mapping_dto import (
    INVISIBLE_CHARS,
    KANGXI_STRICT_MAPPING,
    SPECIAL_CONTINEOUS_CHARS
)
from modules.image_ocr.dto.sample_quality import PageType
from modules.image_ocr.utils.eval import evaluate_quality_by_ocr
from modules.image_ocr.utils.text_proc import compare_text_extraction, replace_chars_with_mapping

USE_GPU = True
LIMIT_PAGE_PER_PDF = 2000  # 每个PDF文件最大只处理页数

OCR_MODELS_ROOT = "/aipdf-mlp/xelawk/models/PaddleOCRModels"
# OCR_MODELS_ROOT = "/Users/<USER>/Documents/模型相关下载/PaddleOCRModels"
OCR_MODEL_USED = "ch_PP-OCRv4_server"
ALL_OCR_MODELS = {
    "ch_PP-OCRv4": {
        "det": os.path.join(OCR_MODELS_ROOT, "ch_PP-OCRv4_det_infer"),
        "rec": os.path.join(OCR_MODELS_ROOT, "ch_PP-OCRv4_rec_infer"),
    },
    "ch_PP-OCRv4_server": {
        "det": os.path.join(OCR_MODELS_ROOT, "ch_PP-OCRv4_det_server_infer"),
        "rec": os.path.join(OCR_MODELS_ROOT, "ch_PP-OCRv4_rec_server_infer"),
    },
    "ch_repsvtr": {
        "det": os.path.join(OCR_MODELS_ROOT, "openatom_det_repsvtr_ch_infer"),
        "rec": os.path.join(OCR_MODELS_ROOT, "openatom_rec_repsvtr_ch_infer")
    }
}

# 设置PaddleOCR日志级别为WARNING，过滤DEBUG信息
logging.getLogger("ppocr").setLevel(logging.WARNING)

REMOVED_IMAGE_ANNO_FLAG = '-$img_rm: don\'t training$-'

@dataclass
class TextBox:
    """文本框数据类"""
    bbox: List[float]  # [x0, y0, x1, y1]
    text: str
    confidence: float = 1.0
    _char_font_sizes: List[float] = field(default_factory=list)  # 存储每个字符的字号大小
    _char_widths: List[float] = field(default_factory=list)      # 存储每个字符的宽度
    _char_heights: List[float] = field(default_factory=list)     # 存储每个字符的高度
    cell_id: Optional[int] = None                                # 用于标记所属单元格（若表格存在）
    is_vertical: bool = False                                    # 标记是否为垂直文本

    def add_char_size(self, width: float, height: float, font_size: float) -> None:
        """添加字符尺寸"""
        self._char_widths.append(width)
        self._char_heights.append(height)
        self._char_font_sizes.append(font_size)

    @property
    def median_width(self) -> float:
        """获取字符宽度的中位数"""
        if not self._char_widths:
            return 0.0
        sorted_widths = sorted(self._char_widths)
        mid = len(sorted_widths) // 2
        return sorted_widths[mid] if len(sorted_widths) % 2 == 1 else \
            (sorted_widths[mid - 1] + sorted_widths[mid]) / 2

    @property
    def median_height(self) -> float:
        """获取字符高度的中位数"""
        if not self._char_heights:
            return 0.0
        sorted_heights = sorted(self._char_heights)
        mid = len(sorted_heights) // 2
        return sorted_heights[mid] if len(sorted_heights) % 2 == 1 else \
            (sorted_heights[mid - 1] + sorted_heights[mid]) / 2

    @property
    def median_font_size(self) -> float:
        """获取字符的中位数字号"""
        if not self._char_font_sizes:
            return 0.0
        sorted_sizes = sorted(self._char_font_sizes)
        mid = len(sorted_sizes) // 2
        return sorted_sizes[mid] if len(sorted_sizes) % 2 == 1 else \
            (sorted_sizes[mid - 1] + sorted_sizes[mid]) / 2

class GarbledTextError(Exception):
    """乱码风险异常"""
    def __init__(self, message, diff_ratio):
        self.message = message
        self.diff_ratio = diff_ratio
        super().__init__(self.message)


def draw_bboxes_on_image(
    image: Image.Image,
    annotations: List[Dict[str, Any]],
    table_cells: Optional[List[Dict[str, Any]]] = None,
    enable_draw_table_cells: bool = True,
    ocr_boxes = None
) -> Image.Image:
    """在图片上绘制检测框
    Args:
        image: PIL Image对象
        annotations: 标注信息列表
        table_cells: 表格单元格列表，每个单元格包含x0,y0,x1,y1的相对坐标
        enable_draw_table_cells: 是否绘制表格单元格
        ocr_boxes: OCR检测到的边界框列表 [[x1,y1,x2,y2], ...]
    Returns:
        PIL Image对象（带有检测框的新图片）
    """
    def random_color():
        """生成随机RGB颜色"""
        return (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255))
    
    img_draw = image.copy()
    draw = ImageDraw.Draw(img_draw, 'RGBA')  # 使用RGBA模式以支持半透明填充

    # 绘制OCR检测框
    if len(ocr_boxes) > 0:
        for box in ocr_boxes:
            x1, y1, x2, y2 = box['points']
            points = [(x1, y1), (x2, y1), (x2, y2), (x1, y2)]
            draw.polygon(points, outline=(255, 0, 0), width=2)  # 红色

    # 绘制文本框和被删除的图像区域
    for ann in annotations:
        points = ann.get('points', [])
        if len(points) != 4:
            continue

        if ann.get('image_removed', False):
            # 使用半透明灰色填充被删除的图像区域
            draw.polygon([(p[0], p[1]) for p in points], fill=(128, 128, 128, 64), outline=(128, 128, 128), width=2)
        else:
            # 绘制普通文本框（绿色）
            draw.polygon([(p[0], p[1]) for p in points], outline=(0, 255, 0), width=2)

    # 绘制表格单元格
    if table_cells and enable_draw_table_cells:
        for cell in table_cells:
            # 获取单元格坐标并转换为图像坐标
            x0 = cell['x0'] * image.width
            y0 = cell['y0'] * image.height
            x1 = cell['x1'] * image.width
            y1 = cell['y1'] * image.height

            # 使用随机颜色绘制表格单元格
            draw.rectangle([x0, y0, x1, y1], outline='blue', width=2)

    return img_draw


def color_to_rgba(color):
    """将颜色值转换为RGBA格式
    Args:
        color: 整数颜色值
    Returns:
        tuple: (RGB元组, alpha值)
    """
    if color is None:
        return None, None

    # 将负数转换为无符号整数
    if color < 0:
        color = color & 0xFFFFFFFF

    # 提取RGBA分量
    blue = color & 0xFF
    green = (color >> 8) & 0xFF
    red = (color >> 16) & 0xFF
    alpha = (color >> 24) & 0xFF  # 提取alpha通道

    return (red, green, blue), alpha  # 分别返回RGB和alpha


def extract_font_infos(page_by_fitz):
    # 获取页面的文本块
    blocks = page_by_fitz.get_text("dict")["blocks"]

    # 遍历所有文本块
    char_font_infos = []
    for block in blocks:
        if "lines" not in block:
            continue

        for line in block["lines"]:
            # 直接从line的dir属性计算角度
            dx, dy = line.get("dir", (1, 0))
            rotation = math.degrees(math.atan2(dy, dx))

            for span in line["spans"]:
                text = span["text"]
                font_size = span["size"]
                font_name = span["font"]
                bbox = span["bbox"]
                color, alpha = color_to_rgba(span.get("color", None))  # 获取字体颜色和透明度（如果有）

                # 计算每个字符的近似位置
                text_length = len(text)
                if text_length > 0:
                    char_width = (bbox[2] - bbox[0]) / text_length

                    for i, char in enumerate(text):
                        # 估算字符的边界框
                        char_x0 = bbox[0] + i * char_width
                        char_x1 = char_x0 + char_width
                        char_bbox = (char_x0, bbox[1], char_x1, bbox[3])

                        if char not in INVISIBLE_CHARS:
                            char_info = {
                                "char": char,
                                "font_size": round(font_size, 2),
                                "font_name": font_name,
                                "bbox": [round(x, 2) for x in char_bbox],
                                "color": color,
                                "alpha": alpha,
                                "rotation": round(rotation, 2)
                            }
                            char_font_infos.append(char_info)

    return char_font_infos


def extract_table_from_page(page_by_fitz):
    """使用PyMuPDF的表格检测功能
    Args:
        page_by_fitz: PyMuPDF页面对象
    Returns:
        单元格列表，每个单元格包含位置和文本内容
    """
    cells = []
    page = page_by_fitz

    for table in page.find_tables():
        # 遍历表格的所有行
        for cell in table.cells:
            cells.append({
                'x0': cell[0] / page.rect.width,
                'y0': cell[1] / page.rect.height,
                'x1': cell[2] / page.rect.width,
                'y1': cell[3] / page.rect.height,
            })
    
    return cells


def find_containing_cell(char_bounds, table_cells, width, height):
    """查找包含字符的单元格
    Args:
        char_bounds: 字符边界 [x0, y0, x1, y1]
        table_cells: 表格单元格列表
        width: 页面宽度
        height: 页面高度
    Returns:
        单元格索引，如果不在任何单元格内则返回None
    """
    if not table_cells:
        return None
        
    # PDF坐标系（左下角原点）转换为图片坐标系（左上角原点）
    x0 = char_bounds[0] / width
    y0 = 1 - (char_bounds[3] / height)  # 使用bottom计算top
    x1 = char_bounds[2] / width
    y1 = 1 - (char_bounds[1] / height)  # 使用top计算bottom
    
    for idx, cell in enumerate(table_cells):
        if (x0 >= cell['x0'] and x1 <= cell['x1'] and 
            y0 >= cell['y0'] and y1 <= cell['y1']):
            return idx
    return None


def check_continuous_special_chars(text_page, start_idx, count_chars, count=4):
    """检查从start_idx开始是否存在连续的特殊字符
    Args:
        text_page: PDFium的TextPage对象
        start_idx: 起始字符索引
        count: 连续特殊字符的阈值，有默认值
    Returns:
        (是否连续特殊字符, 连续字符的结束索引)
    """
    if start_idx + 1 >= count_chars:
        return False, start_idx

    # 获取起始字符
    first_char = text_page.get_text_range(start_idx, 1)
    if first_char not in SPECIAL_CONTINEOUS_CHARS:
        return False, start_idx

    # 向后检查连续相同字符
    continuous_count = 1
    for curr_idx in range(start_idx + 1, count_chars):
        curr_char = text_page.get_text_range(curr_idx, 1)
        if curr_char != first_char:
            break
        continuous_count += 1

    return continuous_count >= count, curr_idx


def normalize_loc(char_bounds, width, height):
    # PDF坐标系（左下角原点）转换为图片坐标系（左上角原点）
    x0 = char_bounds[0] / width
    y0 = 1 - (char_bounds[3] / height)  # 使用bottom计算top
    x1 = char_bounds[2] / width
    y1 = 1 - (char_bounds[1] / height)  # 使用top计算bottom

    # 标准化坐标
    x0 = max(0, min(x0, 1))
    y0 = max(0, min(y0, 1))
    x1 = max(0, min(x1, 1))
    y1 = max(0, min(y1, 1))

    return x0, y0, x1, y1


def extract_text_from_page(
    page_by_pdfium,
    page_by_fitz,
    x_tolerance: float = 3.0,
    y_tolerance: float = 1.0,
) -> List[TextBox]:
    """从PDF页面提取文本框信息
    Args:
        page_by_pdfium: PDFPage对象
        page_by_fitz: PDFPage对象
        x_tolerance: x方向的容差值（相对于当前文本框字符宽度中位数的倍数，默认2.0）
        y_tolerance: y方向的容差值（相对于当前文本框字符高度中位数的倍数，默认1.0）
    Returns:
        文本框列表
    """
    # 添加乱码风险检测
    diff_ratio_thres = 0.02
    diff_ratio, diff_text = compare_text_extraction(page_by_pdfium, page_by_fitz, diff_threshold=0.1)
    if diff_ratio > diff_ratio_thres:  # 如果差异率超过阈值
        raise GarbledTextError(
            f"检测到潜在的乱码风险！PyMuPDF和PDFium提取的文本差异率为{diff_ratio:.2%}", 
            diff_ratio
        )

    # pdfium对字体字号的解析不好，这里用fitz(PyMuPDF)代替解析工作
    font_infos = extract_font_infos(page_by_fitz)
    table_cells = extract_table_from_page(page_by_fitz)

    # 返回关键标识信息
    is_include_special_words = False
    for f in font_infos:
        if f['color'] in [(255, 255, 255)]:
            is_include_special_words = True
            break
        if f['alpha'] != 255 and f['rotation'] != 0:
            is_include_special_words = True
            break
    flags = {"is_include_special_words": is_include_special_words}

    # pdfium 获取 text page 对象
    text_page = page_by_pdfium.get_textpage()
    rotation = page_by_pdfium.get_rotation() % 360

    if rotation:
        raise Exception(f"旋转PDF文档，{rotation}度，跳过处理")

    # 获取页面尺寸
    width = page_by_pdfium.get_width()
    height = page_by_pdfium.get_height()

    # 获取总字符数
    char_count = text_page.count_chars()
    if char_count == 0:
        return [], flags

    # 临时存储每行的字符框
    line_cnt = 0
    last_x0 = None
    last_x1 = None
    last_y0 = None
    last_y1 = None
    all_line_boxes = {line_cnt: None}

    real_char_idx = 0
    font_size = None
    prev_font_size = None
    raw_strings = ''
    is_catch_first_word = False
    LOGGER.debug(f"\n=== 开始逐字符解析 ===")
    reach_idx = 0
    for char_idx in range(char_count):
        # 检测并跳过特殊连续连接字符，如`----, _____, .......`等
        if char_idx != reach_idx:
            continue
        is_continuous, next_no_continuous_idx = check_continuous_special_chars(text_page, char_idx, char_count)
        if is_continuous:
            reach_idx = next_no_continuous_idx
            continue
        else:
            reach_idx += 1

        # 获取单个字符
        char = text_page.get_text_range(char_idx, 1)

        # 在遇到第一个可见字符之前跳过所有不可见字符
        if not is_catch_first_word:
            is_catch_first_word = char not in INVISIBLE_CHARS
            if not is_catch_first_word:
                continue

        char_bounds = text_page.get_charbox(char_idx)
        if not char_bounds:
            continue
        raw_strings += char

        # 获取字体大小
        if char not in INVISIBLE_CHARS:
            if real_char_idx == 0:
                font_size = font_infos[real_char_idx]['font_size']
                prev_font_size = font_size
            else:
                font_size = font_infos[real_char_idx]['font_size']
                prev_font_size = font_infos[real_char_idx - 1]['font_size']
            real_char_idx += 1

        if char in ['\n', '\r', '\t']:
            continue

        x0, y0, x1, y1 = normalize_loc(char_bounds, width, height)

        # 对于存在表格的页面需要关注单元格分割问题
        cur_cell_idx = None
        if table_cells:
            cur_cell_idx = find_containing_cell(char_bounds, table_cells, width, height)

        # 检查是否发现换行操作
        is_new_line = False
        if char_idx > 0:
            # 基础条件：确保有当前行的文本框和前一个字符的y坐标
            if not is_new_line and last_y0 is not None and all_line_boxes[line_cnt]:
                curr_box = all_line_boxes[line_cnt]

                # 基本换行判断逻辑
                prev_char = text_page.get_text_range(char_idx - 1, 1)
                is_new_line = prev_char in ['\n', '\r', '\t']
                if is_new_line:
                    LOGGER.debug(f"line changed when {prev_char!r} -> '{char}', 遇到换行符号")

                # 检查是否有非占宽不可见字符
                if prev_char in INVISIBLE_CHARS and not is_new_line and last_x1 is not None and curr_box.median_width > 0.:
                    is_new_line = ((x0 - last_x1) / curr_box.median_width <= 0.75)
                    if is_new_line:
                        LOGGER.debug(f"line changed when '{prev_char}' -> '{char}'：非占宽不可见字符")

                # 检查不可见字符超宽占位
                if prev_char in INVISIBLE_CHARS and not is_new_line and last_x1 is not None and curr_box.median_width > 0.:
                    is_new_line = ((x0 - last_x1) / curr_box.median_width >= 1.5)
                    if is_new_line:
                        LOGGER.debug(f"line changed when '{prev_char}' -> '{char}'：修正不可见字符带来的偏移")

                # 检查相邻字符的字号差异
                if not is_new_line:
                    if char not in INVISIBLE_CHARS and real_char_idx - 1 > 0:
                        curr_font_size = font_size
                        if curr_font_size > 0 and prev_font_size > 0:
                            font_size_ratio = max(curr_font_size, prev_font_size) / min(curr_font_size, prev_font_size)
                            if font_size_ratio >= 2.0:
                                LOGGER.debug(f"字号差异触发换行: 前一字符({prev_font_size:.1f}) -> 当前字符'{char}'({curr_font_size:.1f}), 比例: {font_size_ratio:.2f}")
                                is_new_line = True

                # 如果没有因字号差异换行，则检查位置差异
                if not is_new_line:
                    # 通过字符间距和行距判断是否需要换行
                    if curr_box.median_width > 0 and curr_box.median_height > 0 and curr_box.median_font_size > 0:  # 确保有有效的中位数
                        x_diff = abs(x0 - curr_box.bbox[2])           # 计算x方向差异
                        x_tol = x_tolerance * curr_box.median_width   # 计算x方向容忍值
                        y_diff = abs(y1 - curr_box.bbox[3])           # 计算y方向差异
                        y_tol = y_tolerance * curr_box.median_height  # 计算y方向容忍值
                        
                        # x方向距离过大或y方向差异过大时换行
                        is_new_line = (x_diff > x_tol or y_diff > y_tol)
                        if is_new_line:
                            LOGGER.debug(f"line changed when '{prev_char}' -> '{char}'：x_diff: {x_diff:.8f}, x_tol: {x_tol:.8f}, y_diff: {y_diff:.8f}, y_tol: {y_tol:.8f}")

                # 检查是否跨越不同单元格
                if not is_new_line and table_cells:
                    if cur_cell_idx != curr_box.cell_id:
                        is_new_line = True
                        LOGGER.debug(f"line changed when '{prev_char}' -> '{char}'：跨越不同单元格")

        # 输出调试信息，包括字体大小
        LOGGER.debug(f"{char}[{font_size:.1f}pt], c_w: {x1 - x0: .8f}, c_h: {y1 - y0: .8f}, loc: [{x0:.8f}, {y0:.8f}, {x1:.8f}, {y1:.8f}]")

        if is_new_line:
            line_cnt += 1
            last_x0 = None
            last_x1 = None
            last_y0 = None
            last_y1 = None
            all_line_boxes[line_cnt] = None

        if all_line_boxes[line_cnt]:
            # 合并到上一个框
            last_box = all_line_boxes[line_cnt]
            last_box.bbox[2] = max(last_box.bbox[2], x1)
            last_box.text += char
            if char not in INVISIBLE_CHARS:
                last_box.cell_id = cur_cell_idx
                last_box.add_char_size(x1 - x0, y1 - y0, font_size)
        else:
            # 如果是新行的第一个字符，创建新的字符框
            if char not in INVISIBLE_CHARS:
                all_line_boxes[line_cnt] = TextBox(bbox=[x0, y0, x1, y1], text=char, cell_id=cur_cell_idx)
                all_line_boxes[line_cnt].add_char_size(x1 - x0, y1 - y0, font_size)
                all_line_boxes[line_cnt].is_vertical = False  # 确保设置is_vertical标记
            else:
                continue

        # 更新当前行的高度
        if all_line_boxes[line_cnt] and not is_new_line:
            if last_y0 is not None:
                # 更新当前行所有字符框的高度
                box = all_line_boxes[line_cnt]
                box.bbox[1] = min(box.bbox[1], y0)  # 更新top
                box.bbox[3] = max(box.bbox[3], y1)  # 更新bottom

        last_x0 = x0
        last_x1 = x1
        last_y0 = y0
        last_y1 = y1

    LOGGER.debug(f"\n=== 原始内容 ===")
    LOGGER.debug("raw strings: %r", raw_strings)

    text_boxes = merge_all_boxes(all_line_boxes)

    return text_boxes, flags


def merge_all_boxes(all_line_boxes, max_iterations=5):
    """智能合并相邻的文本框
    Args:
        all_line_boxes: 按行号索引的文本框字典
        max_iterations: 最大合并迭代次数
    Returns:
        合并后的文本框列表
    """
    iteration = 0
    current_boxes = all_line_boxes
    # 记录已经被确认为垂直文本行的TextBox
    vertical_text_boxes = set()
    
    while iteration < max_iterations:
        # 记录合并前的文本框数量
        prev_box_count = len(current_boxes) if isinstance(current_boxes, list) else len(current_boxes.keys())

        # 按行号排序，并过滤掉None的box
        text_boxes = []
        sorted_lines = [(k, v) for k, v in sorted(current_boxes.items(), key=lambda x: x[0]) if v is not None]
        for _, box in sorted_lines:
            for s in INVISIBLE_CHARS:
                box.text = box.text.strip(s)
        
        # 如果没有文本框，直接返回
        if not sorted_lines:
            return text_boxes
            
        # 初始化第一个文本框
        curr_line_num, curr_box = sorted_lines[0]
        merged_box = TextBox(
            bbox=curr_box.bbox.copy(),
            text=curr_box.text
        )
        merged_box._char_widths = curr_box._char_widths.copy()
        merged_box._char_heights = curr_box._char_heights.copy()
        merged_box._char_font_sizes = curr_box._char_font_sizes.copy()
        merged_box.cell_id = curr_box.cell_id
        merged_box.is_vertical = curr_box.is_vertical  # 确保设置is_vertical标记
        
        # 是否已经标记为垂直文本行
        curr_is_vertical = curr_line_num in vertical_text_boxes
        
        # 遍历后续的文本框
        for next_line_num, next_box in sorted_lines[1:]:
            # 下一个文本框是否已被标记为垂直文本行
            next_is_vertical = next_line_num in vertical_text_boxes
            
            # 计算y方向的差异
            y_overlap = (min(curr_box.bbox[3], next_box.bbox[3]) - max(curr_box.bbox[1], next_box.bbox[1]))
            y_height = min(curr_box.bbox[3] - curr_box.bbox[1], next_box.bbox[3] - next_box.bbox[1])
            
            # 计算x方向的差异
            x_gap = next_box.bbox[0] - curr_box.bbox[2]
            
            # 计算x方向重叠度（用于判断垂直文本）
            x_overlap = min(curr_box.bbox[2], next_box.bbox[2]) - max(curr_box.bbox[0], next_box.bbox[0])
            x_width = min(curr_box.bbox[2] - curr_box.bbox[0], next_box.bbox[2] - next_box.bbox[0])
            x_overlap_ratio = x_overlap / x_width if x_width > 0 else 0
            
            # 判断是否在同一行
            is_same_line = False
            is_vertical_text = False
            
            # 情况1：y轴重叠度高，可能是同一行
            if y_overlap >= 0.5 * y_height:
                is_same_line = True
                
            # 情况2：一个bbox包含另一个bbox（考虑轻微误差）
            if y_overlap >= 0.8 * y_height:  # 字号差异不能过大
                is_same_line = True
                
            # 情况3：垂直排列的文本（x方向重叠度高且为单字符）
            # 检查是否为真正的垂直排列文本 - 两个条件：
            # 1. x方向重叠度高
            # 2. 单字符或者已经被标记为垂直文本
            # 3. 非表格单元格内的文本，或者同一单元格内的垂直文本
            is_in_table = curr_box.cell_id is not None or next_box.cell_id is not None
            is_same_cell = curr_box.cell_id == next_box.cell_id
            
            # 只有非表格内的文本，或者同一单元格内的单字符文本才可能被识别为垂直文本
            if x_overlap_ratio >= 0.5 and ((not is_in_table) or (is_in_table and is_same_cell and len(curr_box.text.strip()) == 1 and len(next_box.text.strip()) == 1)):
                if len(curr_box.text.strip()) == 1 and len(next_box.text.strip()) == 1 or curr_is_vertical or next_is_vertical:
                    # 检查是否为垂直排列的文本
                    is_vertical_text = True
                    # 仅当明确不是同一行时才标记为垂直文本
                    if not is_same_line:
                        LOGGER.debug(f"检测到垂直文本: 当前[{curr_box.text}], 下一个[{next_box.text}]")
            
            # 初步确认为同一行，但需要检查字号差异和单元格
            if is_same_line or is_vertical_text:
                curr_font_size = curr_box.median_font_size
                next_font_size = next_box.median_font_size
                if curr_font_size > 0 and next_font_size > 0:
                    font_size_ratio = max(curr_font_size, next_font_size) / min(curr_font_size, next_font_size)
                    if font_size_ratio > 2.0:  # 字号差异过大，重置为不同行
                        is_same_line = False
                        is_vertical_text = False  # 也不考虑为垂直文本
                        LOGGER.debug(f"字号差异过大，不合并: curr_size={curr_font_size:.1f}, next_size={next_font_size:.1f}, ratio={font_size_ratio:.2f}")
                
                # 检查是否属于不同单元格
                if curr_box.cell_id is not None and next_box.cell_id is not None and curr_box.cell_id != next_box.cell_id:
                    is_same_line = False
                    is_vertical_text = False  # 也不考虑为垂直文本
                    LOGGER.debug(f"不同单元格，不合并: curr_cell[{curr_box.text}]={curr_box.cell_id}, next_cell[{next_box.text}]={next_box.cell_id}")
            
            # 禁止水平合并垂直文本行
            if is_same_line and (curr_is_vertical or next_is_vertical or 
                                 curr_box.is_vertical or next_box.is_vertical or 
                                 merged_box.is_vertical or 
                                 is_vertical_text or
                                 (len(merged_box.text) >= 2 and x_overlap_ratio >= 0.4)):  # 增强防止垂直文本被水平合并的条件
                is_same_line = False
                LOGGER.debug(f"禁止水平合并垂直文本行: '{merged_box.text}'")
                
            # 特殊处理表格内的文本：
            # 在表格单元格内，倾向于按行分割文本而不是垂直合并
            if curr_box.cell_id is not None and next_box.cell_id is not None and curr_box.cell_id == next_box.cell_id:
                # 如果没有足够的证据证明是垂直排列文本，则禁止垂直合并
                if (len(curr_box.text) > 1 or len(next_box.text) > 1) and is_vertical_text:
                    # 对于多字符文本，禁止垂直合并
                    is_vertical_text = False
                    LOGGER.debug(f"表格内多字符文本，禁止垂直合并: curr[{curr_box.text}], next[{next_box.text}]")
            
            # 如果在同一行或垂直文本，判断是否需要合并
            if is_same_line or is_vertical_text:
                # 使用两个框中较大的中位宽度作为判断标准
                curr_median_width = curr_box.median_width
                next_median_width = next_box.median_width
                median_width = max(curr_median_width, next_median_width)
                
                # 对于垂直文本，使用高度作为判断标准
                curr_median_height = curr_box.median_height
                next_median_height = next_box.median_height
                median_height = max(curr_median_height, next_median_height)
                
                LOGGER.debug(f"=== 开始合并判断 ===")
                LOGGER.debug(f"当前文本: '{curr_box.text}' (line {curr_line_num})")
                LOGGER.debug(f"下一文本: '{next_box.text}' (line {next_line_num})")
                
                if is_same_line:
                    # 处理水平方向合并
                    LOGGER.debug(f"中位宽度 - 当前: {curr_median_width:.6f}, 下一个: {next_median_width:.6f}, 使用: {median_width:.6f}")
                    LOGGER.debug(f"x轴间距: {x_gap:.6f}")
                    
                    lower_bound = 0.50
                    upper_bound = 1.50
                    
                    # 间距小于 下界系数*中位宽度，直接合并
                    if x_gap <= lower_bound * median_width:
                        merged_box.text += next_box.text
                        LOGGER.debug(f"直接合并 -> 结果: '{merged_box.text}'")
                        
                    # 间距在 下界系数*中位宽度 到 上界系数*中位宽度 之间，添加空格后合并
                    elif x_gap <= upper_bound * median_width:
                        # 计算间距倍数，并乘以2得到空格数量
                        width_ratio = x_gap / median_width
                        space_count = math.ceil(width_ratio * 1)
                        space_count = min(space_count, 1)
                        LOGGER.debug(f"间距是中位宽度的{width_ratio:.6f}倍，添加{space_count}个空格")
                        merged_box.text += ' ' * space_count + next_box.text
                        LOGGER.debug(f"添加{space_count}个空格合并 -> 结果: '{merged_box.text}'")
                        
                    # 间距过大，不合并
                    else:
                        LOGGER.debug(f"间距过大({x_gap:.6f} > {upper_bound * median_width:.6f})，不合并")
                        text_boxes.append(merged_box)
                        merged_box = TextBox(
                            bbox=next_box.bbox.copy(),
                            text=next_box.text
                        )
                        merged_box._char_widths = next_box._char_widths.copy()
                        merged_box._char_heights = next_box._char_heights.copy()
                        merged_box._char_font_sizes = next_box._char_font_sizes.copy()
                        merged_box.cell_id = next_box.cell_id  # 复制cell_id
                        # 继承垂直文本状态
                        curr_is_vertical = next_is_vertical
                        merged_box.is_vertical = next_is_vertical
                        
                elif is_vertical_text:
                    # 处理垂直方向合并
                    LOGGER.debug(f"中位高度 - 当前: {curr_median_height:.6f}, 下一个: {next_median_height:.6f}, 使用: {median_height:.6f}")
                    LOGGER.debug(f"垂直间距: {next_box.bbox[1] - curr_box.bbox[3]:.6f}")
                    
                    # 垂直合并需要考虑垂直间距
                    y_gap = next_box.bbox[1] - curr_box.bbox[3]
                    
                    # 设置垂直合并的阈值
                    v_lower_bound = 0.1  # 降低紧凑垂直文本的阈值，使其更容易直接合并
                    v_upper_bound = 1.2  # 降低较宽松的垂直文本阈值，减少分散合并
                    
                    # 针对表格内文本的特殊处理
                    if curr_box.cell_id is not None and next_box.cell_id is not None and curr_box.cell_id == next_box.cell_id:
                        # 表格内部的垂直文本处理，只有在确定是单字符垂直文本时才合并
                        if len(curr_box.text.strip()) > 1 or len(next_box.text.strip()) > 1:
                            # 对于多字符文本，倾向于不做垂直合并
                            LOGGER.debug(f"表格内多字符文本，不做垂直合并: curr[{curr_box.text}], next[{next_box.text}]")
                            text_boxes.append(merged_box)
                            merged_box = TextBox(
                                bbox=next_box.bbox.copy(),
                                text=next_box.text
                            )
                            merged_box._char_widths = next_box._char_widths.copy()
                            merged_box._char_heights = next_box._char_heights.copy()
                            merged_box._char_font_sizes = next_box._char_font_sizes.copy()
                            merged_box.cell_id = next_box.cell_id  # 复制cell_id
                            # 继承垂直文本状态
                            curr_is_vertical = next_is_vertical
                            merged_box.is_vertical = next_is_vertical
                            continue
                    
                    # 根据不同间距决定是否合并及如何合并
                    if y_gap <= v_lower_bound * median_height:
                        # 间距很小，直接合并
                        merged_box.text += next_box.text
                        # 标记为垂直文本
                        curr_is_vertical = True
                        vertical_text_boxes.add(curr_line_num)
                        merged_box.is_vertical = True
                        LOGGER.debug(f"垂直方向直接合并 -> 结果: '{merged_box.text}'")
                    elif y_gap <= v_upper_bound * median_height:
                        # 间距适中，添加空格而非换行符
                        height_ratio = y_gap / median_height
                        space_count = math.ceil(height_ratio * 2)
                        space_count = min(space_count, 1)  # 限制最大空格数为1
                        LOGGER.debug(f"垂直间距是中位高度的{height_ratio:.6f}倍，添加{space_count}个空格")
                        merged_box.text += ' ' * space_count + next_box.text
                        # 标记为垂直文本
                        curr_is_vertical = True
                        vertical_text_boxes.add(curr_line_num)
                        merged_box.is_vertical = True
                        LOGGER.debug(f"垂直方向添加空格合并 -> 结果: '{merged_box.text}'")
                    else:
                        # 间距过大，认为不是同一个垂直文本，不合并
                        LOGGER.debug(f"垂直间距过大({y_gap:.6f} > {v_upper_bound * median_height:.6f})，不合并")
                        text_boxes.append(merged_box)
                        merged_box = TextBox(
                            bbox=next_box.bbox.copy(),
                            text=next_box.text
                        )
                        merged_box._char_widths = next_box._char_widths.copy()
                        merged_box._char_heights = next_box._char_heights.copy()
                        merged_box._char_font_sizes = next_box._char_font_sizes.copy()
                        merged_box.cell_id = next_box.cell_id  # 复制cell_id
                        # 继承垂直文本状态
                        curr_is_vertical = next_is_vertical
                        merged_box.is_vertical = next_is_vertical
                
                LOGGER.debug("=== 合并判断结束 ===\n")
                
                # 无论是水平合并还是垂直合并，只要确实进行了合并，就更新bbox
                if merged_box.text != next_box.text and (is_same_line or is_vertical_text):
                    # 更新合并后的bbox
                    merged_box.bbox[0] = min(merged_box.bbox[0], next_box.bbox[0])
                    merged_box.bbox[1] = min(merged_box.bbox[1], next_box.bbox[1])
                    merged_box.bbox[2] = max(merged_box.bbox[2], next_box.bbox[2])
                    merged_box.bbox[3] = max(merged_box.bbox[3], next_box.bbox[3])
                    merged_box._char_widths.extend(next_box._char_widths)
                    merged_box._char_heights.extend(next_box._char_heights)
                    merged_box._char_font_sizes.extend(next_box._char_font_sizes)
                    merged_box.is_vertical = is_vertical_text  # 标记是否为垂直文本
            
            else:  # 不在同一行，也不是垂直文本，保存当前合并结果并开始新的合并
                text_boxes.append(merged_box)
                merged_box = TextBox(
                    bbox=next_box.bbox.copy(),
                    text=next_box.text
                )
                merged_box._char_widths = next_box._char_widths.copy()
                merged_box._char_heights = next_box._char_heights.copy()
                merged_box._char_font_sizes = next_box._char_font_sizes.copy()
                merged_box.cell_id = next_box.cell_id  # 复制cell_id
                # 继承垂直文本状态
                curr_is_vertical = next_is_vertical
                merged_box.is_vertical = next_is_vertical
                
            # 更新当前box
            curr_line_num = next_line_num
            curr_box = next_box
        
        # 添加最后一个合并结果
        text_boxes.append(merged_box)
        
        # 计算合并后的文本框数量
        curr_box_count = len(text_boxes)
        
        # 如果文本框数量没有变化，提前退出
        if curr_box_count == prev_box_count:
            LOGGER.debug(f"文本框数量未变化({curr_box_count})，在第{iteration + 1}次迭代后退出")
            return text_boxes
            
        # 为下一次迭代准备数据
        current_boxes = {i: box for i, box in enumerate(text_boxes)}
        iteration += 1
        LOGGER.debug(f"完成第{iteration}次合并迭代，文本框数量: {prev_box_count} -> {curr_box_count}")
    
    return text_boxes


def has_control_chars(text: str) -> bool:
    """检查文本是否包含可疑控制字符（除\r\n\t外的控制字符）"""
    return any((0x00 <= ord(char) <= 0x1F or 0x7F <= ord(char) <= 0x9F) 
              and char not in '\r\n\t' 
              for char in text)


def convert_pdf_to_ppocr_format(
    pdf_path: str,
    engine_dir: str,
    cal_md5: bool = False,
    limit_num: int = -1,
    draw_bbox: bool = False,
    enable_draw_table_cells: bool = True,
    enable_render_images: bool = True,
    selected_pages: list = None,
    ocr_model = None,
) -> list:
    """将PDF文件转换为PPOCRLabel格式
    Args:
        pdf_path: PDF文件路径
        engine_dir: 输出目录
        cal_md5: 是否使用文件MD5作为基础文件名
        limit_num: 限制处理的页面数量，-1表示处理所有页面
        draw_bbox: 是否在保存的图片上绘制检测框
        enable_draw_table_cells: 是否绘制表格单元格
        enable_render_images: 是否渲染PDF中的位图，默认为True
        selected_pages: 指定页数索引
    Returns:
        处理结果列表
    """
    # 初始化质量评估结果记录
    quality_results = []
    
    # 是否以文件md5作为基础导出文件基础命名
    if cal_md5:
        base_filename = calculate_file_md5(pdf_path)
    else:
        base_filename = Path(pdf_path).stem

    # 创建必要的目录结构
    os.makedirs(engine_dir, exist_ok=True)

    # 准备Label.txt文件
    label_file = os.path.join(engine_dir, "Label.txt")
    source_record_file = os.path.join(engine_dir, "source_record.txt")

    # 打开PDF文件
    pdf_by_fitz = fitz.open(pdf_path)
    pdf_by_pdfium = pdfium.PdfDocument(pdf_path)
    n_pages = len(pdf_by_pdfium)

    try:
        # 如果指定了限制数量，随机选择要处理的页面
        if not selected_pages:
            if limit_num > 0:
                # 确保 limit_num 不超过总页数
                actual_limit = min(limit_num, n_pages)
                selected_pages = sorted(random.sample(range(n_pages), actual_limit))
            else:
                selected_pages = range(n_pages)
        else:
            # 过滤掉超出页数范围的页码
            selected_pages = [page_idx for page_idx in selected_pages if 0 <= page_idx < n_pages]
            if not selected_pages:  # 如果过滤后为空，则使用所有页面
                selected_pages = range(n_pages)

        # 先检查选中页面是否包含可疑控制字符
        vaild_selected_pages = []
        for page_idx in selected_pages:
            page_by_pdfium = pdf_by_pdfium[page_idx]
            text_page = page_by_pdfium.get_textpage()
            text = text_page.get_text_range()
            if has_control_chars(text):
                LOGGER.warning(f"{base_filename} 第{page_idx}页包含可疑控制字符")
                continue
            vaild_selected_pages.append(page_idx)

        # 使用tqdm显示处理进度
        success_cnt = 0
        processed_results = []

        if len(vaild_selected_pages) > 0:
            selected_pages = vaild_selected_pages
        else:
            raise Exception(f"选中内容页数索引均包含可疑控制字符，跳过")

        for page_idx in selected_pages:
            # 获取页面
            page_by_pdfium = pdf_by_pdfium[page_idx]
            page_by_fitz = pdf_by_fitz[page_idx]

            # 返回关键标识信息
            is_include_bitmap = False
            objects = page_by_pdfium.get_objects()
            for obj in objects:
                if obj.type == pdfium_c.FPDF_PAGEOBJ_IMAGE:
                    is_include_bitmap = True

            # 如果不渲染位图，则移除页面中的所有图片对象, TODO: 暂时采用比较Low的方案
            removed_image_obj_bbox = []
            if not enable_render_images:
                for _ in range(20):
                    found_images = False
                    objects = page_by_pdfium.get_objects()
                    width = page_by_pdfium.get_width()
                    height = page_by_pdfium.get_height()

                    for obj in objects:
                        if obj.type == pdfium_c.FPDF_PAGEOBJ_IMAGE:
                            # 获取图像对象的bbox
                            obj_pos = obj.get_pos()
                            x0, y0, x1, y1 = normalize_loc(obj_pos, width, height)
                            removed_image_obj_bbox.append((x0, y0, x1, y1))
                            # 移除图像对象
                            page_by_pdfium.remove_obj(obj)
                            obj.close()
                            found_images = True

                    if not found_images:
                        break

            # 常见的 scale 值及对应 DPI：
            # scale = 1  # 72 DPI，适用于屏幕显示
            # scale = 2  # 144 DPI，适用于一般文字识别
            # scale = 3  # 216 DPI，适用于需要高清晰度的场景
            scale = 4  # 288 DPI，适用于需要极高清晰度的场景
            pil_image = page_by_pdfium.render(scale=scale).to_pil()

            # 提取文本框
            text_boxes, flags = extract_text_from_page(page_by_pdfium, page_by_fitz)
            
            # 提取表格单元格
            table_cells = extract_table_from_page(page_by_fitz)

            # 转换为标注格式
            annotations = []
            for box in text_boxes:
                text = box.text
                text, replacements = replace_chars_with_mapping(text, KANGXI_STRICT_MAPPING)
                w, h = pil_image.size
                x0, y0, x1, y1 = box.bbox
                points = [
                    [int(x0 * w), int(y0 * h)],
                    [int(x1 * w), int(y0 * h)],
                    [int(x1 * w), int(y1 * h)],
                    [int(x0 * w), int(y1 * h)]
                ]
                annotation = {
                    "transcription": text,
                    "points": points,
                    "difficult": False
                }
                annotations.append(annotation)

                if replacements:
                    LOGGER.warning(f"\n在文件 {base_filename} 中发现需要替换的内容：")
                    LOGGER.warning(f"- 原文本: {box.text}")
                    LOGGER.warning(f"- 替换后: {text}")
                    LOGGER.warning("- 替换详情:")
                    for kangxi, modern in replacements:
                        LOGGER.warning(f"  康熙字符 '{kangxi}' -> 现代字符 '{modern}'")

            if removed_image_obj_bbox:
                for box in removed_image_obj_bbox:
                    w, h = pil_image.size
                    x0, y0, x1, y1 = box
                    points = [
                        [int(x0 * w), int(y0 * h)],
                        [int(x1 * w), int(y0 * h)],
                        [int(x1 * w), int(y1 * h)],
                        [int(x0 * w), int(y1 * h)]
                    ]
                    annotation = {
                        'points': points,
                        'transcription': REMOVED_IMAGE_ANNO_FLAG,
                        'confidence': 1.0,
                        'image_removed': True  # 标记这是一个被删除的图像区域
                    }
                    annotations.append(annotation)

            # 区分页解析标注类型
            page_type = None
            if is_include_bitmap:
                if flags['is_include_special_words']:
                    page_type = PageType.ORIGINAL_WITH_BITMAP_SPECIAL.value
                else:
                    page_type = PageType.ORIGINAL_WITH_BITMAP_NORMAL.value
            else:
                if flags['is_include_special_words']:
                    page_type = PageType.ORIGINAL_NO_BITMAP_SPECIAL.value
                else:
                    page_type = PageType.ORIGINAL_NO_BITMAP_NORMAL.value

            # AI评估样本并完成分级
            quality_level, quality_details, ocr_boxes, unmatched_ocr_boxes = evaluate_quality_by_ocr(
                pil_image=pil_image,
                annotations=annotations,
                ocr_model=ocr_model
            )
            
            # 记录质量评估结果
            quality_info = {
                "page_idx": page_idx,
                "image_filename": f"{base_filename}-t{page_type}-q{quality_level.value}-p{page_idx}.jpg",
                "quality_level": quality_level.value,
                "quality_details": quality_details,
                "ocr_boxes": ocr_boxes,
                "unmatched_ocr_boxes": unmatched_ocr_boxes,
            }
            quality_results.append(quality_info)
            
            # 根据质量等级记录日志
            LOGGER.info(f"页面: {page_idx}, 质量等级: {quality_level.value}, 匹配情况: {quality_details}")
            
            # 将质量等级添加到文件名中
            image_path = os.path.join(engine_dir, quality_info["image_filename"])
            
            # 根据draw_bbox参数决定是否绘制检测框
            debug_image = None
            if draw_bbox:
                debug_image = draw_bboxes_on_image(pil_image, annotations, table_cells, enable_draw_table_cells, ocr_boxes)

            # 存储处理结果
            relative_image_path = os.path.join(Path(engine_dir).stem, quality_info["image_filename"])
            processed_results.append({
                'image': pil_image,
                'debug_image': debug_image,
                'image_path': image_path,
                'label_line': f"{relative_image_path}\t{json.dumps(annotations, ensure_ascii=False)}\n",
                'record_line': f"{quality_info['image_filename']}\t{pdf_path}\t{page_idx}\n"
            })
            success_cnt += 1

        # 全部处理成功后，一次性保存所有数据
        # 1. 保存图片
        for result in processed_results:
            image_path = Path(result['image_path'])
            debug_image_path = image_path.parent / 'debug_image'
            debug_image_path.mkdir(exist_ok=True, parents=True)
            result['image'].save(image_path)
            if result['debug_image']:
                result['debug_image'].save(debug_image_path / image_path.name)

        # 2. 写入标注文件
        with open(label_file, 'a', encoding='utf-8') as f:
            for result in processed_results:
                f.write(result['label_line'])

        # 3. 写入记录文件
        with open(source_record_file, 'a', encoding='utf-8') as record_f:
            for result in processed_results:
                record_f.write(result['record_line'])

        # 4. 写入OCR结果文件
        ocr_label_file = os.path.join(engine_dir, f"Label_{OCR_MODEL_USED}.txt")
        with open(ocr_label_file, 'a', encoding='utf-8') as ocr_f:
            for quality_info in quality_results:
                ocr_boxes_data = []
                for box in quality_info['ocr_boxes']:
                    ocr_boxes_data.append({
                        'transcription': box['text'],
                        'points': box['ocr_ori_points'],
                        'confidence': box.get('confidence', 1.0)
                    })
                relative_image_path = os.path.join(Path(engine_dir).stem, quality_info['image_filename'])
                ocr_f.write(f"{relative_image_path}\t{json.dumps(ocr_boxes_data, ensure_ascii=False)}\n")

        # 保存所有页面的质量评估结果
        if quality_results:
            part_dir = os.path.dirname(image_path)
            quality_txt = os.path.join(part_dir, "quality.txt")
            with open(quality_txt, "a", encoding="utf-8") as f:
                # 添加文件信息
                f.write(f"\n文件：{pdf_path}\n")
                # 添加每个页面的质量评估结果
                for result in quality_results:
                    page_idx = result["page_idx"]
                    image_filename = result["image_filename"]
                    quality_level = result["quality_level"]
                    quality_details = result["quality_details"]
                    unmatched_ocr_boxes = result["unmatched_ocr_boxes"]
                    f.write(f"样本：{image_filename}，质量等级：{quality_level}\n")
                    if quality_level != "L0":
                        f.write(f"  问题详情：{json.dumps(quality_details, ensure_ascii=False)}\n")
                        for box in unmatched_ocr_boxes:
                            note = box[2] if box[2] else "未找到标注"
                            if isinstance(box[1], tuple):
                                ocr_text = box[1][0]
                                auto_text = box[1][1]
                            else:
                                ocr_text = box[1]
                                auto_text = ""
                            f.write(f"  匹配详情：\n"
                                    f"    OCR: {ocr_text}\n"
                                    f"    AUTO: {auto_text}\n"
                                    f"    NOTE: {note}\n")
        
        return processed_results

    finally:
        # 确保PDF文件被正确关闭
        pdf_by_fitz.close()
        pdf_by_pdfium.close()


def get_pdf_page_count(pdf_path: str) -> int:
    """快速获取PDF页数，使用最小内存消耗
    Args:
        pdf_path: PDF文件路径
    Returns:
        页数，如果出错则返回-1
    """
    try:
        pdf = pdfium.PdfDocument(pdf_path)
        page_count = len(pdf)
        pdf.close()
        return page_count
    except Exception as e:
        LOGGER.error(f"获取PDF页数失败: {str(e)}")
        return -1


def load_process_state(output_dir: str) -> Tuple[set, dict, set]:
    """加载处理状态
    Args:
        output_dir: 输出目录
    Returns:
        已完成文件集合, 失败文件状态字典, 处理中文件集合
    """
    state_file = os.path.join(output_dir, "process_state.json")
    if not os.path.exists(state_file):
        return set(), {}, set()

    try:
        with open(state_file, 'r', encoding='utf-8') as f:
            state = json.load(f)
            return (
                set(state.get('completed', [])),
                state.get('failed', {}),
                set(state.get('processing', []))
            )
    except Exception as e:
        LOGGER.error(f"加载状态文件失败: {str(e)}")
        return set(), {}, set()


def save_process_state(output_dir: str, completed: set, failed: dict, processing: set):
    """保存处理状态
    Args:
        output_dir: 输出目录
        completed: 已完成文件集合
        failed: 失败文件状态字典
        processing: 处理中文件集合
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    state_file = os.path.join(output_dir, "process_state.json")
    state = {
        'completed': list(completed),
        'failed': failed,
        'processing': list(processing),
        'last_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

    try:
        # 先写入临时文件，再原子性地重命名
        temp_file = state_file + '.tmp'
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(state, f, ensure_ascii=False, indent=2)
        os.replace(temp_file, state_file)
    except Exception as e:
        LOGGER.error(f"保存状态文件失败: {str(e)}")


def run_process(
    pdf_dir: str,
    output_dir: str,
    cal_md5: bool = False,
    limit_num: int = -1,
    specific_pdf_names: Optional[List[str]] = None,
    random_seed: Optional[int] = None,
    draw_bbox: bool = False,
    enable_draw_table_cells: bool = True,
    enable_render_images: bool = True,
    selected_pages: list = None,
) -> bool:
    """运行处理进程
    Args:
        pdf_dir: PDF文件目录
        output_dir: 输出目录
        cal_md5: 是否使用文件MD5作为基础文件名
        limit_num: 限制处理的页面数量，-1表示处理所有页面
        specific_pdf_names: 指定要处理的PDF文件名列表
        random_seed: 随机种子
        draw_bbox: 是否在保存的图片上绘制检测框
        enable_draw_table_cells: 是否绘制表格单元格
        enable_render_images: 是否渲染PDF中的位图，默认为True
        selected_pages: 指定页码索引
    Returns:
        是否处理成功
    """
    try:
        main(
            pdf_dir=pdf_dir,
            output_dir=output_dir,
            cal_md5=cal_md5,
            limit_num=limit_num,
            specific_pdf_names=specific_pdf_names,
            random_seed=random_seed,
            draw_bbox=draw_bbox,
            enable_draw_table_cells=enable_draw_table_cells,
            enable_render_images=enable_render_images,
            selected_pages=selected_pages
        )
        return True
    except Exception as e:
        LOGGER.error(f"子进程执行失败: {str(e)}")
        return False


def run_with_monitor(
    pdf_dir: str,
    output_dir: str,
    cal_md5: bool = False,
    limit_num: int = -1,
    specific_pdf_names: list = None,
    random_seed: int = None,
    draw_bbox: bool = False,
    enable_draw_table_cells: bool = True,
    enable_render_images: bool = True,
    selected_pages: list = None,
) -> None:
    """带监控的运行入口，主要用于处理OOM导致的进程终止
    Args:
        pdf_dir: PDF文件目录
        output_dir: 输出目录
        cal_md5: 是否使用文件MD5作为基础文件名
        limit_num: 限制处理的页面数量，-1表示处理所有页面
        specific_pdf_names: 指定要处理的PDF文件名列表
        random_seed: 随机种子
        draw_bbox: 是否在保存的图片上绘制检测框
        enable_draw_table_cells: 是否绘制表格单元格
        enable_render_images: 是否渲染PDF中的位图，默认为True
        selected_pages: 指定页码索引
    """
    while True:
        # 创建子进程
        process = mp.Process(
            target=run_process,
            args=(
                pdf_dir,
                output_dir,
                cal_md5,
                limit_num,
                specific_pdf_names,
                random_seed,
                draw_bbox,
                enable_draw_table_cells,
                enable_render_images,
                selected_pages,
            )
        )
        process.start()
        process.join()

        # 检查进程退出状态
        if process.exitcode == 0:
            LOGGER.info("处理完成！")
            break
        elif process.exitcode == -9:  # SIGKILL，通常是OOM导致
            LOGGER.warning("检测到OOM终止，正在处理...")

            # 加载当前状态，将processing状态的文件标记为OOM失败
            completed_files, failed_files, processing_files = load_process_state(output_dir)
            if processing_files:
                LOGGER.warning(f"发现 {len(processing_files)} 个文件因OOM中断")
                for pdf_path in processing_files:
                    if pdf_path not in failed_files:
                        failed_files[pdf_path] = "处理失败：内存不足(OOM)"
                        LOGGER.warning(f"文件 {pdf_path} 因OOM失败")
                processing_files.clear()
                save_process_state(output_dir, completed_files, failed_files, processing_files)
                LOGGER.info("继续处理剩余文件...")
                continue
            else:
                LOGGER.error("未找到处理中的文件，程序终止")
                break
        else:
            LOGGER.error(f"子进程异常退出，退出码: {process.exitcode}")
            break


def main(
    pdf_dir: str,
    output_dir: str,
    cal_md5: bool = False,
    limit_num: int = -1,
    specific_pdf_names: list = None,
    random_seed: int = None,
    draw_bbox: bool = False,
    enable_draw_table_cells: bool = True,
    enable_render_images: bool = True,
    selected_pages: list = None,
) -> tuple[int, int]:
    """主函数
    Args:
        pdf_dir: PDF文件目录
        output_dir: 输出目录
        cal_md5: 是否使用文件MD5作为基础文件名
        limit_num: 限制处理的页面数量，-1表示处理所有页面
        specific_pdf_names: 指定要处理的PDF文件名列表
        random_seed: 随机种子
        draw_bbox: 是否在保存的图片上绘制检测框
        enable_draw_table_cells: 是否绘制表格单元格
        enable_render_images: 是否渲染PDF中的位图，默认为True
        selected_pages: 指定页码索引
    Returns:
        (总页数, 失败文件数)的元组
    """
    # 设置随机数种子
    if random_seed is not None:
        random.seed(random_seed)
        LOGGER.info(f"设置随机数种子: {random_seed}")

    # 加载处理状态
    completed_files, failed_files, processing_files = load_process_state(output_dir)

    # 获取所有PDF文件
    all_pdf_files = Path(pdf_dir).glob('**/*.pdf')
    all_pdf_files = [pdf for pdf in all_pdf_files if not pdf.stem.startswith('.')]
    total_pdfs = len(list(all_pdf_files))

    LOGGER.info(f"找到 {total_pdfs} 个PDF文件待处理")
    LOGGER.info(f"已完成: {len(completed_files)}个, 失败: {len(failed_files)}个, 处理中被中断: {len(processing_files)}个")

    # 计算需要多少组（每组100个PDF）
    total_pages = 0
    BATCH_SIZE = 100
    current_batch = 0

    # 初始化OCR模型
    from paddleocr import PaddleOCR
    ocr_model = PaddleOCR(
        lang="ch",
        use_angle_cls=True,
        det_model_dir=ALL_OCR_MODELS[OCR_MODEL_USED]["det"],
        rec_model_dir=ALL_OCR_MODELS[OCR_MODEL_USED]["rec"],
        use_gpu=USE_GPU,
        show_log=False,
    )

    # 使用tqdm显示总体处理进度
    cnt = len(completed_files)  # 从已完成的文件数开始计数
    with tqdm.tqdm(all_pdf_files, desc="处理PDF文件", unit="个") as pbar:
        for pdf_file in pbar:
            pdf_path = str(pdf_file)

            # 跳过已完成或失败的文件
            if pdf_path in completed_files or pdf_path in failed_files:
                continue

            if specific_pdf_names and pdf_file.stem not in specific_pdf_names:
                continue

            try:
                # 标记为处理中
                processing_files.add(pdf_path)
                save_process_state(output_dir, completed_files, failed_files, processing_files)

                # 快速检查页数
                page_count = get_pdf_page_count(pdf_path)
                if page_count > LIMIT_PAGE_PER_PDF:  # 页数超过限制
                    failed_files[pdf_path] = f"页数过多({page_count}页)"
                    processing_files.remove(pdf_path)
                    save_process_state(output_dir, completed_files, failed_files, processing_files)
                    continue

                # 当处理到每100个PDF时，更新batch编号
                if cnt % BATCH_SIZE == 0:
                    current_batch = cnt // BATCH_SIZE + 1
                    # 创建新的批次目录
                    batch_dir = os.path.join(output_dir, f"part_{current_batch:04d}")
                    os.makedirs(batch_dir, exist_ok=True)

                # 更新进度条描述
                pbar.set_description(f"正在处理: {pdf_file.name} (Part {current_batch:04d})")

                # 处理单个PDF文件
                processed_results = convert_pdf_to_ppocr_format(
                    pdf_path=pdf_path,
                    engine_dir=batch_dir,
                    cal_md5=cal_md5,
                    limit_num=limit_num,
                    draw_bbox=draw_bbox,
                    enable_draw_table_cells=enable_draw_table_cells,
                    enable_render_images=enable_render_images,
                    selected_pages=selected_pages,
                    ocr_model=ocr_model,
                )

                total_pages += len(processed_results)
                pbar.set_postfix({"总页数": total_pages, "当前批次": f"part_{current_batch:04d}"})
                cnt += 1

                # 处理成功，移出处理中状态，加入已完成
                processing_files.remove(pdf_path)
                completed_files.add(pdf_path)

            except Exception as e:
                LOGGER.error(f"处理PDF文件 {pdf_file} 时发生错误: {traceback.format_exc()}")
                failed_files[pdf_path] = str(e)

            finally:
                # 如果文件还在处理中状态，说明发生了异常，将其移出
                if pdf_path in processing_files:
                    processing_files.remove(pdf_path)
                # 保存当前状态
                save_process_state(output_dir, completed_files, failed_files, processing_files)

    # 输出最终统计信息
    total_batches = (total_pdfs + BATCH_SIZE - 1) // BATCH_SIZE
    LOGGER.info(f"处理完成！总计处理 {total_pdfs} 个PDF文件，生成 {total_pages} 个样本页面，分为 {total_batches} 个批次")
    if failed_files:
        LOGGER.warning(f"其中 {len(failed_files)} 个文件处理失败")
        for pdf_path, reason in failed_files.items():
            LOGGER.warning(f"- {pdf_path}: {reason}")

    return total_pages, len(failed_files)


def remove_nested_images(obj, page):
    """递归删除嵌套对象中的图片"""
    if obj.type == pdfium_c.FPDF_PAGEOBJ_FORM:
        # 处理表单对象中的嵌套内容
        for nested_obj in obj.get_objects():
            remove_nested_images(nested_obj, page)
    elif obj.type == pdfium_c.FPDF_PAGEOBJ_IMAGE:
        page.remove_obj(obj)
        obj.close()


if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description="PDF文件OCR处理脚本")
    parser.add_argument(
        "--pdf_dir",
        default="/aipdf-mlp/xelawk/datasets_debug/测试样本转换-包含垂直文本",
        help="PDF文件目录"
    )
    parser.add_argument(
        "--output_dir",
        default="/aipdf-mlp/xelawk/datasets_debug/测试样本转换-包含垂直文本-labeled",
        help="输出目录"
    )
    parser.add_argument(
        "--enable_render_images",
        action="store_true",
        help="是否渲染图片"
    )
    parser.add_argument(
        "--seed",
        type=int,
        default=666,
        help="随机种子"
    )
    parser.add_argument(
        "--limit_num",
        type=int,
        default=40,
        help="每个PDF文件处理的最大页数"
    )
    parser.add_argument(
        "--draw_bbox",
        action="store_true",
        default=False,
        help="是否绘制边界框"
    )
    
    args = parser.parse_args()
    
    # 如果输出目录存在则清空
    if os.path.exists(args.output_dir):
        shutil.rmtree(args.output_dir)
    
    # 运行处理
    run_with_monitor(
        pdf_dir=args.pdf_dir,
        output_dir=args.output_dir,
        cal_md5=False,
        limit_num=args.limit_num,
        specific_pdf_names=None,
        random_seed=args.seed,
        draw_bbox=args.draw_bbox,
        enable_draw_table_cells=False,
        enable_render_images=args.enable_render_images,
        selected_pages=None,
    )
