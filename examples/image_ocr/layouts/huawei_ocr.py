# coding: utf-8

import base64
import json

from huaweicloudsdkcore.auth.credentials import BasicCredentials
from huaweicloudsdkocr.v1.region.ocr_region import OcrRegion
from huaweicloudsdkcore.exceptions import exceptions
from huaweicloudsdkocr.v1 import *
from huaweicloudsdkocr.v1.model.recognize_smart_document_recognizer_response import RecognizeSmartDocumentRecognizerResponse

def get_image_b64(img_path):
    if isinstance(img_path, str):
        with open(img_path, "rb") as f:
            stream = f.read()

    base64_encoded_data = base64.b64encode(stream).decode('ascii')
    return base64_encoded_data

class HuaweiOcr:
    def __init__(self, ak, sk, project_id):
        self.credentials = BasicCredentials(ak, sk, project_id)
        self.client = OcrClient.new_builder() \
            .with_credentials(self.credentials) \
            .with_region(OcrRegion.value_of("cn-north-4")) \
            .build() # .with_stream_log()
    
    def rec_smart_document(self, img_path, to_json=False) -> RecognizeSmartDocumentRecognizerResponse:
        request = RecognizeSmartDocumentRecognizerRequest()
        request.body = SmartDocumentRecognizerRequestBody(
            formula=True,
            form=True,
            layout=True,
            table=True,
            # single_orientation_mode=True,
            data=get_image_b64(img_path)
        )
        response = self.client.recognize_smart_document_recognizer(request)
        if to_json:
            return json.loads(str(response))
        return response

default_huawei_ocr = HuaweiOcr("HPUA7ZKMSG8O9JGFDOJJ", "tayNWxPmfjstBemxL2zyKITmG1Qajcb5EZULMZds", "0f4be591a280f3f62f8cc0100d63f0ba")
if __name__ == "__main__":
    project_id = "0f4be591a280f3f62f8cc0100d63f0ba"
    ak = "HPUA7ZKMSG8O9JGFDOJJ"
    sk = "tayNWxPmfjstBemxL2zyKITmG1Qajcb5EZULMZds"

    huawei_ocr = HuaweiOcr(ak, sk, project_id)
    try:
        response = huawei_ocr.rec_smart_document("~/ocr/online_l.png", to_json=True)
        print(json.dumps(response, indent=2, ensure_ascii=False))
    except exceptions.ClientRequestException as e:
        print(f"e.status_code: {e.status_code}")
        print(f"e.request_id: {e.request_id}")
        print(f"e.error_code: {e.error_code}")
        print(f"e.error_msg: {e.error_msg}")