#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OCR结果处理模块

提供处理不同OCR引擎结果的函数，提取版面信息。
"""

import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from modules.utils import image_locator


def process_huawei_result(ocr_result: Dict) -> Dict:
    """
    处理华为OCR结果，提取版面信息
    
    参数:
        ocr_result (Dict): 华为OCR的原始结果
    
    返回:
        Dict: 处理后的OCR结果，包含all_layout_blocks字段
    """
    # 检查是否有result字段（华为OCR返回的顶层结构）
    if 'result' not in ocr_result:
        print("警告: OCR结果中没有result字段", file=sys.stderr)
        return ocr_result
    
    # 收集所有页面的版面块
    all_layout_blocks = []
    
    # 遍历所有页面
    for page_idx, page in enumerate(ocr_result['result']):
        # 检查页面是否有layout_result
        if 'layout_result' not in page:
            print(f"警告: 第{page_idx+1}页没有layout_result", file=sys.stderr)
            continue
            
        # 检查layout_result是否有layout_block_list
        if 'layout_block_list' not in page['layout_result']:
            print(f"警告: 第{page_idx+1}页没有layout_block_list", file=sys.stderr)
            continue
            
        # 将该页的版面块添加到总列表中
        page_blocks = page['layout_result']['layout_block_list']
        all_layout_blocks.extend(page_blocks)
    
    # 如果没有找到任何版面块
    if not all_layout_blocks:
        print("警告: 没有找到任何版面信息", file=sys.stderr)
        return ocr_result
    
    # 将收集到的所有版面块存回ocr_result的一个新字段，方便后续处理
    ocr_result['all_layout_blocks'] = all_layout_blocks
    
    return ocr_result

def process_textin_result(ocr_result: Dict) -> Dict:
    """
    处理Textin OCR结果，提取版面信息
    
    参数:
        ocr_result (Dict): Textin OCR的原始结果
    
    返回:
        Dict: 处理后的OCR结果，包含all_layout_blocks字段
    """
    # 检查是否有result字段
    if 'result' not in ocr_result:
        print("警告: OCR结果中没有result字段", file=sys.stderr)
        return ocr_result
    
    # 收集所有页面的版面块
    all_layout_blocks = []
    
    # 遍历所有页面
    for page_idx, page in enumerate(ocr_result['result'].get('pages', [])):
        # 检查页面是否有structured
        if 'structured' not in page:
            print(f"警告: 第{page_idx+1}页没有structured字段", file=sys.stderr)
            continue
        
        width = page.get("width", 0)
        height = page.get("height", 0)
        angle = page.get("angle", 0) # 默认为0， 可选值0, 90, 180, 270)，表示文档图旋转了角度
        if angle:
            width, height = image_locator.rotate_size(width, height, -angle)
        # 处理每个版面块
        for block in page['structured']:
            if 'pos' not in block or ('type' not in block and 'sub_type' not in block):
                continue
            
            # 获取类型，优先使用sub_type
            block_type = block.get('sub_type', block.get('type', ''))
            
            # 转换坐标格式从 [x1,y1,x2,y2,x3,y3,x4,y4] 到 [[x1,y1],[x2,y2],[x3,y3],[x4,y4]]
            pos = block['pos']
            # 验证pos长度是否足够
            if len(pos) < 8:
                print(f"警告: 第{page_idx+1}页的版面块坐标格式不正确", file=sys.stderr)
                continue
                
            location = [
                [pos[0], pos[1]],
                [pos[2], pos[3]],
                [pos[4], pos[5]],
                [pos[6], pos[7]]
            ]
            
            # 如果图像有旋转角度，需要对坐标进行反向旋转变换
            if angle and width and height:
                # 对每个点应用旋转变换
                rotated_location = []
                for point in location:
                    x, y = point
                    x_final, y_final = image_locator.rotate_point(x, y, -angle, width/2.0, height/2.0)
                    rotated_location.append([int(x_final), int(y_final)])
                location = rotated_location
            
            # 创建标准格式的版面块
            layout_block = {
                'location': image_locator.sort_polygon_points(location),
                'type': block_type,
                'text': block.get('text', ''),
            }
            
            all_layout_blocks.append(layout_block)
    
    # 如果没有找到任何版面块
    if not all_layout_blocks:
        print("警告: 没有找到任何版面信息", file=sys.stderr)
    
    # 将收集到的所有版面块存回ocr_result的一个新字段，方便后续处理
    ocr_result['all_layout_blocks'] = all_layout_blocks
    
    return ocr_result

if __name__ == "__main__":
    pass
