#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/2/20 19:53
# <AUTHOR> <EMAIL>
# @FileName: prepare_train_dataset

import os
import json
import random
import shutil

import numpy as np
from pathlib import Path

import pyclipper
from tqdm import tqdm
from PIL import Image, ImageDraw
from shapely.geometry import Polygon
from collections import defaultdict
from typing import List, Dict, Tuple, Set, Union, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

from modules.utils.image_utils import get_all_image_path

Image.MAX_IMAGE_PIXELS = None  # 禁用图片大小限制


# 全局配置
class Config:
    # 数据集划分比例
    TRAIN_RATIO = 0.98
    EVAL_RATIO = 0.01
    TEST_RATIO = 0.01
    
    # 检测框外扩比例
    EXPAND_X_RATIO = 1. / 12.   # 系数 * 平均字符宽度
    EXPAND_Y_RATIO = 1. / 12.   # 系数 * 平均字符高度

    # 文本框最小尺寸，像素
    MIN_TEXT_SIZE = 25
    
    # 每个part包含的最大图片数
    IMAGES_PER_PART = 1000
    
    # 输出目录结构
    OUTPUT_DIR = Path("train_data")
    DET_DIR = Path("det")
    TRAIN_DIR = Path("train")
    EVAL_DIR = Path("eval")
    TEST_DIR = Path("test")
    DEBUG_DIR = Path("debug_images")  # debug图片目录
    
    # 标注文件名
    TRAIN_LABEL = "det_gt_train.txt"
    EVAL_LABEL = "det_gt_eval.txt"
    TEST_LABEL = "det_gt_test.txt"


def expand_points(
    points: List[List[float]], 
    text: str,
    x_ratio: float = Config.EXPAND_X_RATIO,
    y_ratio: float = Config.EXPAND_Y_RATIO,
    image_width: Optional[int] = None,
    image_height: Optional[int] = None,
    min_size: int = Config.MIN_TEXT_SIZE,  # 最小尺寸要求
) -> List[List[float]]:
    """对检测框进行智能外扩
    
    Args:
        points: 原始检测框坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
        text: 检测框内的文本内容，用于计算平均字符尺寸
        x_ratio: 横向外扩比例，相对于平均字符宽度
        y_ratio: 纵向外扩比例，相对于平均字符高度
        image_width: 图片宽度，用于边界检查
        image_height: 图片高度，用于边界检查
        min_size: 检测框的最小尺寸要求
    
    Returns:
        扩展后的检测框坐标
    """
    points = np.array(points)
    
    # 计算边界框
    min_x = points[:, 0].min()
    max_x = points[:, 0].max()
    min_y = points[:, 1].min()
    max_y = points[:, 1].max()
    
    # 计算当前宽度和高度
    width = max_x - min_x
    height = max_y - min_y
    
    # 判断文本方向：宽高比小于1视为垂直文本
    is_vertical = width < height

    # 计算字符平均尺寸
    char_count = len(text)
    if char_count == 0:  # 防止除零错误
        char_count = 1
    
    # 针对垂直文本和水平文本采用不同的计算方式
    if is_vertical:  # 垂直文本：使用高度计算字符大小
        avg_char_width = width  # 字符宽度等于文本框宽度
        avg_char_height = height / char_count  # 字符高度为总高度除以字符数
    else:  # 水平文本：使用宽度计算字符大小
        avg_char_width = width / char_count
        avg_char_height = height  # 假设所有字符高度相同
    
    # 计算外扩量
    expand_x = avg_char_width * x_ratio
    expand_y = avg_char_height * y_ratio
    
    # 检查是否需要调整到最小尺寸
    if width < min_size:
        required_expand = (min_size - width) / 2
        expand_x = max(expand_x, required_expand)
    
    if height < min_size:
        required_expand = (min_size - height) / 2
        expand_y = max(expand_y, required_expand)
    
    # 边界检查：确保外扩后不会超出图片范围
    if image_width is not None:
        # 如果外扩后会超出左边界，减少左边的外扩量
        if min_x - expand_x < 0:
            expand_x = min_x
        # 如果外扩后会超出右边界，减少右边的外扩量
        if max_x + expand_x > image_width:
            expand_x = image_width - max_x
    
    if image_height is not None:
        # 如果外扩后会超出上边界，减少上边的外扩量
        if min_y - expand_y < 0:
            expand_y = min_y
        # 如果外扩后会超出下边界，减少下边的外扩量
        if max_y + expand_y > image_height:
            expand_y = image_height - max_y
    
    # 对每个点进行外扩
    expanded = points.copy()
    for i, point in enumerate(points):
        x, y = point
        # 如果点在左边界，向左外扩
        if abs(x - min_x) < 1e-6:
            expanded[i, 0] = max(0, x - expand_x)
        # 如果点在右边界，向右外扩
        elif abs(x - max_x) < 1e-6:
            expanded[i, 0] = min(image_width if image_width is not None else x + expand_x, x + expand_x)
            
        # 如果点在上边界，向上外扩
        if abs(y - min_y) < 1e-6:
            expanded[i, 1] = max(0, y - expand_y)
        # 如果点在下边界，向下外扩
        elif abs(y - max_y) < 1e-6:
            expanded[i, 1] = min(image_height if image_height is not None else y + expand_y, y + expand_y)
    
    return expanded.tolist()


def draw_debug_image(image_path: Path, annotations: List[Dict], debug_path: Path, max_size: Optional[int] = None):
    """绘制debug图片，显示检测框
    
    Args:
        image_path: 原始图片路径
        annotations: 标注信息
        debug_path: debug图片保存路径
        max_size: 可选，图片的最大分辨率（宽或高），超过此值将按比例缩放
    """
    try:
        # 打开原始图片
        image = Image.open(image_path)
        orig_width, orig_height = image.size
        
        scale_factor = 1.0  # 缩放因子
        
        # 如果指定了最大分辨率且图片尺寸超过限制，则按比例缩放
        if max_size is not None and (orig_width > max_size or orig_height > max_size):
            # 计算缩放比例
            scale_factor = min(max_size / orig_width, max_size / orig_height)
            new_width = int(orig_width * scale_factor)
            new_height = int(orig_height * scale_factor)
            
            # 缩放图片
            image = image.resize((new_width, new_height), Image.LANCZOS)
        
        draw = ImageDraw.Draw(image)
        
        # 获取图片尺寸
        width, height = image.size
        
        # 绘制原始检测框（红色）和外扩后的检测框（绿色）
        for ann in annotations:
            points = ann.get('points', [])
            if len(points) != 4:
                continue
            
            # 计算原始检测框坐标
            original_points = ann.get('original_points', points)  # 如果没有存储原始坐标，使用当前坐标
            
            # 绘制原始检测框（红色）
            draw.polygon([(p[0] * scale_factor, p[1] * scale_factor) for p in original_points], outline=(255, 0, 0), width=2)
            
            # 绘制外扩后的检测框（绿色）
            draw.polygon([(p[0] * scale_factor, p[1] * scale_factor) for p in points], outline=(0, 255, 0), width=2)
        
        # 保存debug图片
        debug_path.parent.mkdir(parents=True, exist_ok=True)
        image.save(debug_path)

    except Exception as e:
        print(f"Error creating debug image for {image_path}: {e}")


def parse_label_file(label_path: Path) -> Dict[Path, List[Dict]]:
    """解析标注文件
    
    Args:
        label_path: 标注文件路径
    
    Returns:
        解析后的标注信息 {image_path: annotations}
    """
    annotations = {}
    label_dir = label_path.parent
    is_icdar_dataset = 'icdar' in label_path.parts[-3]
    if is_icdar_dataset:
        print(f'\nFound ICDAR dataset: {label_path}, skip anno expand\n')
    
    with open(label_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            
            try:
                parts = line.split('\t')
                if len(parts) != 2:
                    continue
                    
                image_rel_path, anno_str = parts
                image_rel_path = ''.join(Path(image_rel_path).parts[1::])
                
                # 获取图片的绝对路径
                image_path = label_dir / image_rel_path
                if not image_path.exists():
                    print(f"\nWarning: Image not found: {image_path}")
                    continue
                
                # 获取图片尺寸
                try:
                    with Image.open(image_path) as img:
                        width, height = img.size
                except Exception as e:
                    print(f"Error getting image size for {image_path}: {e}")
                    continue
                
                annos = json.loads(anno_str)
                # 保存原始检测框坐标
                for anno in annos:
                    anno['original_points'] = anno['points'].copy()
                    # 对检测框进行外扩
                    if not is_icdar_dataset:
                        anno['points'] = expand_points(
                            anno['points'],
                            anno['transcription'],
                            image_width=width,
                            image_height=height
                        )

                annotations[image_path] = annos

            except Exception as e:
                print(f"Error parsing line in {label_path}: {e}")
                continue
    
    return annotations


def get_md5_from_filename(filename: Union[str, Path]) -> str:
    """从文件名中提取MD5值"""
    basename = Path(filename).stem
    parts = basename.split('-')
    if len(parts) > 1:
        return parts[0]
    return basename


def create_dataset_structure(base_dir: Path) -> Tuple[Path, Path, Path]:
    """创建数据集目录结构"""
    det_dir = base_dir / Config.DET_DIR
    train_dir = det_dir / Config.TRAIN_DIR
    eval_dir = det_dir / Config.EVAL_DIR
    test_dir = det_dir / Config.TEST_DIR
    
    for dir_path in [train_dir, eval_dir, test_dir]:
        dir_path.mkdir(parents=True, exist_ok=True)
    
    return train_dir, eval_dir, test_dir


def create_hardlink(src: Path, dst: Path):
    """创建硬链接，如果目标已存在则跳过"""
    try:
        dst.parent.mkdir(parents=True, exist_ok=True)
        if not dst.exists():
            dst.hardlink_to(src)
    except Exception as e:
        print(f"Error creating hardlink from {src} to {dst}: {e}")


def get_target_image_path(image_path: Path, target_dir: Path, image_count: int) -> Path:
    """生成目标图片路径
    
    Args:
        image_path: 源图片路径
        target_dir: 目标目录
        image_count: 当前图片计数
    
    Returns:
        目标图片路径
    """
    part_num = image_count // Config.IMAGES_PER_PART + 1
    image_num = image_count % Config.IMAGES_PER_PART + 1
    
    target_name = f"img_{image_num:06d}{image_path.suffix}"
    target_dir = target_dir / f"part_{part_num:04d}"
    
    return target_dir / target_name


def find_label_files(source_dirs: List[Union[str, Path]], recursive: bool = True) -> List[Path]:
    """搜索所有Label.txt文件
    
    Args:
        source_dirs: 源数据目录列表
        recursive: 是否递归搜索子目录
    
    Returns:
        Label.txt文件路径列表
    """
    label_files = []
    pattern = "**/*.txt" if recursive else "*.txt"
    
    for source_dir in source_dirs:
        source_path = Path(source_dir)
        if not source_path.exists():
            print(f"Warning: Source directory not found: {source_path}")
            continue
            
        # 搜索所有.txt文件
        for file_path in source_path.glob(pattern):
            # 跳过隐藏文件和隐藏目录下的文件
            if any(part.startswith('.') for part in file_path.parts):
                continue
                
            # 只保留名为Label.txt的文件
            if file_path.name == "Label.txt":
                label_files.append(file_path)
    
    return label_files


def split_by_md5(annotations: Dict[Path, List[Dict]]) -> Tuple[Set[str], Set[str], Set[str]]:
    """按MD5值划分数据集"""
    # 按MD5分组
    md5_groups = defaultdict(list)
    for image_path in annotations:
        md5 = get_md5_from_filename(image_path)
        md5_groups[md5].append(image_path)
    
    # 随机划分MD5组
    md5_list = list(md5_groups.keys())
    random.shuffle(md5_list)
    
    total = len(md5_list)
    train_size = int(total * Config.TRAIN_RATIO)
    eval_size = int(total * Config.EVAL_RATIO)
    
    train_md5s = set(md5_list[:train_size])
    eval_md5s = set(md5_list[train_size:train_size + eval_size])
    test_md5s = set(md5_list[train_size + eval_size:])
    
    return train_md5s, eval_md5s, test_md5s


def process_label_file(label_file: Path) -> Tuple[Dict[Path, List[Dict]], Dict[Path, Path]]:
    """处理单个标注文件
    
    Args:
        label_file: 标注文件路径
    
    Returns:
        (annotations, source_records)
        annotations: 标注信息
        source_records: 图片来源记录
    """
    source_dir = label_file.parent
    annotations = parse_label_file(label_file)
    
    # 记录每张图片的来源
    source_records = {image_path: source_dir for image_path in annotations.keys()}
    
    return annotations, source_records


def prepare_detection_dataset(
    source_dirs: List[Union[str, Path]],
    output_dir: Union[str, Path] = "train_data",
    recursive: bool = True,
    debug: bool = False,
    num_workers: int = 32,  # 线程数，可以根据CPU核心数调整
    sample_num: int = None,
    debug_max_size: Optional[int] = None  # 新增debug图片最大分辨率参数
) -> None:
    """处理数据集
    
    Args:
        source_dirs: 源数据目录列表
        output_dir: 输出目录
        recursive: 是否递归搜索子目录
        debug: 是否生成debug图片
        num_workers: 处理标注文件的线程数
        sample_num: 采样数量限制
        debug_max_size: debug图片的最大分辨率（宽或高），超过此值将按比例缩放
    """
    print("开始处理数据集...")
    for source_dir in source_dirs:
        cur_all_image_files = get_all_image_path(source_dir, recursive=True)
        print(f"{Path(source_dir).name}: {len(cur_all_image_files)} samples")
    
    # 转换为Path对象
    source_dirs = [Path(d) for d in source_dirs]
    output_dir = Path(output_dir)
    
    # 创建输出目录结构
    train_dir, eval_dir, test_dir = create_dataset_structure(output_dir)
    
    # 搜索所有Label.txt文件
    label_files = find_label_files(source_dirs, recursive)
    print(f"找到 {len(label_files)} 个标注文件")
    
    # 收集所有标注信息
    all_annotations = {}
    source_records = {}
    
    # 使用线程池处理标注文件
    print("解析标注文件...")
    with ThreadPoolExecutor(max_workers=num_workers) as executor:
        # 提交所有任务
        future_to_file = {
            executor.submit(process_label_file, label_file): label_file 
            for label_file in label_files
        }
        
        # 使用tqdm显示进度
        for future in tqdm(as_completed(future_to_file), total=len(label_files)):
            label_file = future_to_file[future]
            try:
                annotations, file_source_records = future.result()
                all_annotations.update(annotations)
                source_records.update(file_source_records)
            except Exception as e:
                print(f"处理文件 {label_file} 时发生错误: {e}")

    if sample_num is not None:
        random.shuffle(all_annotations)
        all_annotations = all_annotations[:sample_num]
        print(f"共采样到 {len(all_annotations)} 个有效图片")
    else:
        print(f"共找到 {len(all_annotations)} 个有效图片")
    
    # 按MD5划分数据集
    train_md5s, eval_md5s, test_md5s = split_by_md5(all_annotations)
    
    # 处理每个数据集
    datasets = [
        (train_md5s, train_dir, Config.TRAIN_LABEL),
        (eval_md5s, eval_dir, Config.EVAL_LABEL),
        (test_md5s, test_dir, Config.TEST_LABEL)
    ]
    
    # 创建source_record.txt文件
    source_record_path = output_dir / Config.DET_DIR / "source_record.txt"
    with open(source_record_path, 'w', encoding='utf-8') as f:
        f.write("# 图片数据源记录\n")
        f.write("# 格式：目标图片路径\t源图片路径\t源数据目录\n")
    
    for md5s, target_dir, label_file in datasets:
        image_count = 0
        label_path = output_dir / Config.DET_DIR / label_file
        
        with open(label_path, 'w', encoding='utf-8') as f:
            for image_path, annos in tqdm(all_annotations.items()):
                md5 = get_md5_from_filename(image_path)
                if md5 not in md5s:
                    continue

                if not os.path.exists(image_path):
                    continue
                
                # 生成目标路径并创建硬链接
                target_path = get_target_image_path(image_path, target_dir, image_count)
                create_hardlink(image_path, target_path)
                
                # 写入标注信息
                rel_path = target_path.relative_to(output_dir / Config.DET_DIR)
                f.write(f"{rel_path}\t{json.dumps(annos, ensure_ascii=False)}\n")
                
                # 记录图片来源
                with open(source_record_path, 'a', encoding='utf-8') as sf:
                    source_dir = source_records[image_path]
                    rel_source_path = image_path.relative_to(source_dir)
                    sf.write(f"{rel_path}\t{rel_source_path}\t{source_dir}\n")
                
                # 如果开启debug模式，生成debug图片
                if debug:
                    debug_dir = target_path.parent / Config.DEBUG_DIR
                    debug_path = debug_dir / target_path.name
                    draw_debug_image(image_path, annos, debug_path, debug_max_size)
                
                image_count += 1
        
        print(f"处理完成 {label_file}，共 {image_count} 张图片")


def extract_samples_by_list(
    image_list_file: Union[str, Path],
    source_records_file: Union[str, Path],
    output_dir: Union[str, Path] = "extracted_samples",
    recursive: bool = True,
    num_workers: int = 16
) -> None:
    """从指定的图片列表文件中提取对应的标注信息

    Args:
        image_list_file: 图片列表文件路径，每行一个处理后的图片路径
        source_records_file: source_records.txt 文件路径，记录了图片映射关系
        output_dir: 输出目录
        recursive: 是否递归搜索子目录
        num_workers: 处理标注文件的线程数
    """
    # 转换路径类型
    image_list_file = Path(image_list_file)
    source_records_file = Path(source_records_file)
    output_dir = Path(output_dir)
    
    # 读取图片列表
    target_images = set()
    with open(image_list_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            # 将路径转换为相对路径格式
            image_path = Path(line)
            rel_path = Path(*image_path.parts[-3:])  # 取最后4个部分: part_xxxx/img_xxxx.jpg
            target_images.add(str(rel_path))

    print(f"Found {len(target_images)} target images")

    # 读取 source_records.txt 获取原始文件映射
    original_info = {}  # {processed_path: (original_filename, source_dir)}
    with open(source_records_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            try:
                parts = line.split('\t')
                if len(parts) != 3:
                    continue
                processed_path, original_filename, source_dir = parts
                original_info[processed_path] = (original_filename, source_dir)
            except Exception as e:
                print(f"Error parsing line: {line}, error: {e}")

    print(f"Loaded {len(original_info)} source records")

    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)

    # 找出目标图片对应的原始信息
    found_samples = []  # [(source_dir, original_filename, target_path)]
    for target in target_images:
        if target in original_info:
            original_filename, source_dir = original_info[target]
            found_samples.append((source_dir, original_filename, target))
        else:
            print(f"Warning: No source record found for {target}")

    print(f"Found {len(found_samples)} matching samples")

    # 用于存储提取的标注信息
    extracted_data = []  # [(target_path, original_path, annotations)]

    # 处理每个样本
    for source_dir, original_filename, target_path in tqdm(found_samples, desc="Processing samples"):
        try:
            # 构建原始图片和标注文件路径
            source_dir = Path(source_dir)
            original_path = source_dir / original_filename
            original_filename = os.path.join(source_dir.name, original_filename)
            label_file = source_dir / "Label.txt"
            
            if not label_file.exists():
                print(f"Warning: Label file not found: {label_file}")
                continue

            # 读取标注文件
            with open(label_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    parts = line.split('\t')
                    if len(parts) != 2:
                        continue
                    img_name, anno_str = parts
                    if img_name == str(original_filename):
                        # 找到匹配的标注
                        extracted_data.append((target_path, str(original_path), json.loads(anno_str)))
                        break

        except Exception as e:
            print(f"Error processing {original_filename} in {source_dir}: {e}")

    print(f"Successfully extracted {len(extracted_data)} samples")

    # 保存提取的标注信息
    output_label_file = output_dir / "Label.txt"
    with open(output_label_file, 'w', encoding='utf-8') as f:
        for _, original_path, annos in extracted_data:


            # 创建硬链接
            target_image_path = output_dir / Path(original_path).name
            target_image_path.parent.mkdir(parents=True, exist_ok=True)
            create_hardlink(Path(original_path), target_image_path)

            # 保存标注信息，同时记录原始路径
            f.write(f"{'/'.join(target_image_path.parts[-2::])}\t{json.dumps(annos, ensure_ascii=False)}\n")

    print(f"Results saved to {output_label_file}")


def precheck_make_border(polygon):
    polygon = np.array(polygon)
    assert polygon.ndim == 2
    assert polygon.shape[1] == 2

    polygon_shape = Polygon(polygon)
    if polygon_shape.area <= 0:
        return False
    distance = (
        polygon_shape.area
        * (1 - np.power(0.4, 2))
        / polygon_shape.length
    )
    subject = [tuple(l) for l in polygon]
    padding = pyclipper.PyclipperOffset()
    padding.AddPath(subject, pyclipper.JT_ROUND, pyclipper.ET_CLOSEDPOLYGON)

    padded_polygon = padding.Execute(distance)
    if len(padded_polygon) == 0:
        return False
    return True


if __name__ == "__main__":
    # 设置随机种子确保可重复性
    random.seed(42)

    # 直接设置参数
    source_dirs = [
        "/aipdf-mlp/xelawk/datasets_release_v4/release_v2/icdar2019lsvt_full",

        "/aipdf-mlp/xelawk/datasets_release_v4/release_v2/auto_labeled_filtered_clean",
        "/aipdf-mlp/xelawk/datasets_release_v4/release_v2/auto_labeled_filtered_clean_degrade",
        "/aipdf-mlp/xelawk/datasets_release_v4/release_v2/auto_labeled_filtered_clean_adv_v2",
        "/aipdf-mlp/xelawk/datasets_release_v4/release_v2/auto_labeled_filtered_clean_adv_v2_degrade",

        "/aipdf-mlp/xelawk/datasets_release_v4/release_v2/auto_labeled_with_image_filtered_clean",
        "/aipdf-mlp/xelawk/datasets_release_v4/release_v2/auto_labeled_with_image_filtered_clean_adv_v2",
        "/aipdf-mlp/xelawk/datasets_release_v4/release_v2/auto_labeled_with_image_filtered_clean_degrade",
        "/aipdf-mlp/xelawk/datasets_release_v4/release_v2/auto_labeled_with_image_filtered_clean_adv_v2_degrade",
    ]
    output_dir = "/aipdf-mlp/xelawk/train_dataset_release_v4/auto_labeled_adv_icdar_deg_v202503272141"

    if os.path.exists(output_dir):
        shutil.rmtree(output_dir)

    debug = False
    recursive = True

    # 设置debug模式下图片的最大分辨率，超过此值将按比例缩放，可以加快可视化过程
    debug_max_size = 896  # 默认限制debug分辨率

    prepare_detection_dataset(
        source_dirs=source_dirs,
        output_dir=output_dir,
        recursive=recursive,
        debug=debug,
        debug_max_size=debug_max_size,  # 传递最大分辨率参数
    )
