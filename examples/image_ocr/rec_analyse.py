import os
import cv2
import numpy as np
import math
import random
import paddle
import yaml
import matplotlib.pyplot as plt
from collections import defaultdict

from modules.doc_degradation.core.strategy import DegradationType
from modules.doc_degradation.core.scheduler import StrategyScheduler
from modules.doc_degradation.core.degradation_pipe import DegradationPipe
from paddlex import create_model

IMG_DEGRADE_PIPE = DegradationPipe(StrategyScheduler(config_path=None))

from third_parties import init_third_parties_env
init_third_parties_env()  # 初始化第三方库环境

import tools.program as program
from ppocr.utils.save_load import load_model
from ppocr.postprocess import build_post_process
from ppocr.data import create_operators, transform
from ppocr.modeling.architectures import build_model


def resize_norm_img(img, image_shape, padding=True, interpolation=cv2.INTER_LINEAR):
    imgC, imgH, imgW = image_shape
    h = img.shape[0]
    w = img.shape[1]
    if not padding:
        resized_image = cv2.resize(img, (imgW, imgH), interpolation=interpolation)
        resized_w = imgW
    else:
        ratio = w / float(h)
        if math.ceil(imgH * ratio) > imgW:
            resized_w = imgW
        else:
            resized_w = int(math.ceil(imgH * ratio))
        resized_image = cv2.resize(img, (resized_w, imgH))
    resized_image_save = resized_image.copy()
    resized_image = resized_image.astype("float32")
    if image_shape[0] == 1:
        resized_image = resized_image / 255
        resized_image = resized_image[np.newaxis, :]
    else:
        resized_image = resized_image.transpose((2, 0, 1)) / 255
    resized_image -= 0.5
    resized_image /= 0.5
    padding_im = np.zeros((imgC, imgH, imgW), dtype=np.float32)
    padding_im[:, :, 0:resized_w] = resized_image
    padding_save = np.zeros((imgH, imgW, imgC), dtype=np.uint8)
    padding_save[:, 0:resized_w, :] = resized_image_save
    valid_ratio = min(1.0, float(resized_w / imgW))
    return padding_im, valid_ratio, padding_save


def resize_norm_img_chinese(img, image_shape):
    imgC, imgH, imgW = image_shape
    # todo: change to 0 and modified image shape
    max_wh_ratio = imgW * 1.0 / imgH
    h, w = img.shape[0], img.shape[1]
    ratio = w * 1.0 / h
    max_wh_ratio = max(max_wh_ratio, ratio)
    imgW = int(imgH * max_wh_ratio)
    if math.ceil(imgH * ratio) > imgW:
        resized_w = imgW
    else:
        resized_w = int(math.ceil(imgH * ratio))
    resized_image = cv2.resize(img, (resized_w, imgH))
    resized_image_save = resized_image.copy()
    resized_image = resized_image.astype("float32")
    if image_shape[0] == 1:
        resized_image = resized_image / 255
        resized_image = resized_image[np.newaxis, :]
    else:
        resized_image = resized_image.transpose((2, 0, 1)) / 255
    resized_image -= 0.5
    resized_image /= 0.5
    padding_im = np.zeros((imgC, imgH, imgW), dtype=np.float32)
    padding_im[:, :, 0:resized_w] = resized_image
    padding_save = np.zeros((imgH, imgW, imgC), dtype=np.uint8)
    padding_save[:, 0:resized_w, :] = resized_image_save
    valid_ratio = min(1.0, float(resized_w / imgW))
    return padding_im, valid_ratio, padding_save


def resize_norm_img_adaptive(img, image_shape, scales):
    """
    根据给定的scales列表选择最合适的缩放比例对图像进行处理
    
    Args:
        img: 输入图像
        image_shape: 目标图像形状 (C, H, W)
        scales: 候选尺寸列表，每个元素为 [width, height]
    
    Returns:
        padding_im: 填充后的图像数据
        valid_ratio: 有效区域比例
        padding_save: 用于可视化的填充图像
    """
    imgC, imgH, imgW = image_shape
    h, w = img.shape[0], img.shape[1]
    ratio = w / float(h)
    
    if len(scales) > 0:
        # 对scales按照宽高比进行排序
        scale_ratios = [(scale_w * 1.0 / scale_h, scale_w, scale_h) for scale_w, scale_h in scales]
        scale_ratios.sort()  # 按宽高比升序排序

        # 查找合适的目标尺寸
        target_h = imgH
        target_w = imgW

        # 遍历排序后的scales，找到ratio所在的区间
        # 即ratio大于前一个scale的比例，小于后一个scale的比例
        for i in range(len(scale_ratios) - 1):
            curr_ratio, curr_w, curr_h = scale_ratios[i]
            next_ratio, next_w, next_h = scale_ratios[i + 1]

            if curr_ratio <= ratio < next_ratio:
                # 找到合适的区间，使用较大比例（next）的尺寸
                target_h = next_h
                target_w = next_w
                break

        # 特殊情况处理：
        # 1. ratio小于最小的scale比例，使用第一个scale的尺寸
        if ratio < scale_ratios[0][0]:
            target_h = scale_ratios[0][2]
            target_w = scale_ratios[0][1]
        # 2. ratio大于所有scale的比例，则保留推荐的宽高比
        imgH = target_h
        imgW = target_w

    max_wh_ratio = imgW * 1.0 / imgH
    max_wh_ratio = max(max_wh_ratio, ratio)
    imgW = int(imgH * max_wh_ratio)
    if math.ceil(imgH * ratio) > imgW:
        resized_w = imgW
    else:
        resized_w = int(math.ceil(imgH * ratio))
    
    # 调整图像大小
    resized_image = cv2.resize(img, (resized_w, imgH))
    resized_image_save = resized_image.copy()
    resized_image = resized_image.astype("float32")
    
    # 标准化处理
    if image_shape[0] == 1:
        resized_image = resized_image / 255
        resized_image = resized_image[np.newaxis, :]
    else:
        resized_image = resized_image.transpose((2, 0, 1)) / 255
    resized_image -= 0.5
    resized_image /= 0.5
    
    # 填充处理
    padding_im = np.zeros((imgC, imgH, imgW), dtype=np.float32)
    padding_im[:, :, 0:resized_w] = resized_image
    padding_save = np.zeros((imgH, imgW, imgC), dtype=np.uint8)
    padding_save[:, 0:resized_w, :] = resized_image_save
    
    valid_ratio = min(1.0, float(resized_w / imgW))
    return padding_im, valid_ratio, padding_save


def filter_edit_distance(input_file, output_file, thresholds=None):
    """
    根据编辑距离阈值筛选OCR预测结果，并按编辑距离排序
    
    Args:
        input_file (str): 输入文件路径，文件格式为"图片路径\t编辑距离\t真实文本\t预测文本\t置信度"
        output_file (str): 输出文件路径
        thresholds (list): 编辑距离阈值列表，如[5, 10]表示筛选编辑距离在[5,10]范围内的结果
                          如果为None，则默认筛选编辑距离大于0的结果
    """
    if thresholds is None:
        thresholds = [1, float('inf')]  # 默认筛选编辑距离大于0的结果
    elif len(thresholds) == 1:
        thresholds = [thresholds[0], float('inf')]  # 如果只提供一个阈值，则视为下限
    
    filtered_results = []  # 存储筛选后的结果
    
    with open(input_file, 'r', encoding='utf-8') as f_in:
        for line in f_in:
            parts = line.strip().split('\t')
            if len(parts) != 6:
                continue
            
            try:
                edit_distance = float(parts[1])
                # 判断编辑距离是否在阈值范围内
                if thresholds[0] <= edit_distance <= thresholds[1]:
                    filtered_results.append((edit_distance, line))
            except ValueError:
                continue  # 跳过编辑距离无法转换为数字的行
    
    # 按编辑距离排序
    filtered_results.sort(key=lambda x: x[0])
    
    # 写入排序后的结果
    with open(output_file, 'w', encoding='utf-8') as f_out:
        for _, line in filtered_results:
            f_out.write(line)


def visualize_results(input_file, output_dir, image_shape=(3, 48, 320), scales=[], level=1, tag='normal'):
    """
    对预测结果进行可视化，将处理后的图像保存到指定目录
    
    Args:
        input_file (str): 输入文件路径，每行格式为"图片路径\t编辑距离\t真实文本\t预测文本\t置信度"
        output_dir (str): 输出图像保存的根目录
        image_shape (tuple): 图像处理的目标形状 (C, H, W)
        level (int): 图像重命名时使用的路径级别数，默认为1
        tag (str): 图像处理方式，'normal'使用resize_norm_img，'chinese'使用resize_norm_img_chinese
    """
    import os
    from PIL import Image, ImageDraw, ImageFont
    import numpy as np
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'processed'), exist_ok=True)  # 存放处理后的原始图像
    os.makedirs(os.path.join(output_dir, 'combined'), exist_ok=True)   # 存放添加文本后的图像
    
    # 设置字体，这里使用系统默认字体
    try:
        font = ImageFont.truetype(
            "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common/sys_msyh.ttc", 25)
        font_s = ImageFont.truetype(
            "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common/sys_msyh.ttc", 10)
    except:
        font = ImageFont.load_default()
        
    def get_text_width(text, selected_font):
        """获取文本渲染后的宽度"""
        img = Image.new('RGB', (1, 1), color='white')
        draw = ImageDraw.Draw(img)
        bbox = draw.textbbox((0, 0), text, font=selected_font)
        return bbox[2] - bbox[0]
        
    def create_text_image(text, width, height, selected_font):
        """创建文本图像"""
        img = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(img)
        # 获取文本大小
        bbox = draw.textbbox((0, 0), text, font=selected_font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        # 计算居中位置
        x = (width - text_width) // 2
        y = (height - text_height) // 2
        # 绘制文本
        draw.text((x, y), text, fill='black', font=selected_font)
        return np.array(img)
    
    with open(input_file, 'r', encoding='utf-8') as f:
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) < 5:
                continue
                
            # 获取图像路径和文本
            img_path = parts[0]
            gt_text = parts[2]  # 真实文本
            pred_text = parts[3]  # 预测文本
            chars_probs = parts[5]
            # chars_probs = list(map(float,  parts[5].split(',')))
            gt_text = gt_text.replace(' ', '¤')
            pred_text = pred_text.replace(' ', '¤')
            
            if not os.path.exists(img_path):
                print(f"警告：图像不存在 {img_path}")
                continue
            
            # 计算文本需要的最大宽度
            gt_text_width = get_text_width(f"G: {gt_text}", font)
            pred_text_width = get_text_width(f"P: {pred_text}", font)
            chars_probs_width = get_text_width(chars_probs, font_s)
            text_width = max(gt_text_width, pred_text_width, chars_probs_width)
            # 添加一些边距
            required_width = text_width + 50  # 左右各预留25像素边距
            
            # 读取并处理图像
            try:
                img = cv2.imread(img_path)
                if img is None:
                    print(f"警告：无法读取图像 {img_path}")
                    continue
                
                # 保存原始处理后的图像
                if tag.lower() == 'chinese':
                    _, _, processed_img_original = resize_norm_img_chinese(img, image_shape)
                elif tag.lower() == 'auto':
                    _, _, processed_img_original = resize_norm_img_adaptive(img, image_shape, scales)
                else:
                    _, _, processed_img_original = resize_norm_img(img, image_shape)
                
                # 根据文本宽度重新处理图像
                h, w = processed_img_original.shape[:2]
                ratio = w / float(h)
                new_w = required_width
                new_h = int(new_w / ratio)
                if new_h > image_shape[1]:  # 如果高度超过预设值，则按高度缩放
                    new_h = image_shape[1]
                    new_w = int(new_h * ratio)
                
                processed_img = cv2.resize(processed_img_original, (new_w, new_h))
                
                # 如果图像宽度小于所需宽度，进行padding
                if new_w < required_width:
                    pad_width = required_width - new_w
                    processed_img = cv2.copyMakeBorder(
                        processed_img, 
                        0, 0,  # 上下不填充
                        pad_width//2, pad_width - pad_width//2,  # 左右填充
                        cv2.BORDER_CONSTANT, 
                        value=[255, 255, 255]  # 白色填充
                    )
                
            except Exception as e:
                print(f"错误：处理图像失败 {img_path}: {str(e)}")
                continue
            
            # 创建文本图像
            text_height = image_shape[1]  # 使用与处理后图像相同的高度
            gt_img = create_text_image(f"G: {gt_text}", required_width, text_height, font)
            pred_img = create_text_image(f"P: {pred_text}", required_width, text_height, font)
            prob_img = create_text_image(chars_probs, required_width, text_height//2, font_s)
            
            # 垂直拼接图像
            combined_img = np.vstack([processed_img, gt_img, pred_img, prob_img])
            
            # 根据level生成新的文件名
            path_parts = img_path.replace('\\', '/').split('/')
            if level <= 0:
                level = 1
            if level > len(path_parts):
                level = len(path_parts)
            
            new_name = '_'.join(path_parts[-level:])
            new_name = str(parts[1]) + '_' + new_name
            
            # 分别保存处理后的原始图像和组合图像
            try:
                # 保存处理后的原始图像
                # processed_path = os.path.join(output_dir, 'processed', new_name)
                # cv2.imwrite(processed_path, processed_img_original)
                
                # 保存添加文本后的组合图像
                combined_path = os.path.join(output_dir, 'combined', new_name)
                cv2.imwrite(combined_path, combined_img)
            except Exception as e:
                print(f"错误：保存图像失败 {new_name}: {str(e)}")
                continue


def visualize_text_boxes(image_dir, output_dir, label_path):
    """
    可视化文本检测和识别结果

    Args:
        image_dir (str): 图像目录路径
        output_dir (str): 可视化结果保存目录
        label_path (str): 标注文件路径，每行格式为"图像相对路径\t字典列表"
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    import json
    from PIL import ImageFont, Image, ImageDraw
    import numpy as np
    
    # 加载字体
    font_path = "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common/sys_Deng.ttf"
    font_size = 20
    font = ImageFont.truetype(font_path, font_size)
    
    # 读取标注文件
    with open(label_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    for line in lines:
        try:
            # 解析每一行
            parts = line.strip().split('\t')
            if len(parts) != 2:
                continue
                
            image_rel_path, boxes_str = parts
            try:
                boxes = json.loads(boxes_str)  # 直接解析 JSON 字符串
            except json.JSONDecodeError as e:
                print(f"JSON 解析错误: {image_rel_path}, 错误: {str(e)}")
                continue
            
            # 读取图像
            image_path = os.path.join(image_dir, image_rel_path)
            if not os.path.exists(image_path):
                print(f"图像不存在: {image_path}")
                continue
                
            image = cv2.imread(image_path)
            if image is None:
                print(f"无法读取图像: {image_path}")
                continue
            
            # 绘制文本框
            for box_dict in boxes:
                points = np.array(box_dict['points'], dtype=np.int32)
                cv2.polylines(image, [points], True, (0, 255, 0), 2)  # 使用红色绘制文本框
            
            # 转换为PIL图像绘制中文
            image_pil = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(image_pil)
            
            # 绘制文本
            for box_dict in boxes:
                points = np.array(box_dict['points'], dtype=np.int32)
                text = box_dict['transcription']
                
                # 计算文本位置
                text_x = points[0][0]
                text_y = points[0][1] - font_size - 5
                text_y = max(text_y, font_size)
                
                # 绘制文本（红色）
                draw.text((text_x, text_y), text, font=font, fill=(255, 0, 0))
            
            # 转换回OpenCV格式并保存
            image = cv2.cvtColor(np.array(image_pil), cv2.COLOR_RGB2BGR)
            
            # 保存结果，保持原始目录结构
            rel_dir = os.path.dirname(image_rel_path)
            output_path = os.path.join(output_dir, rel_dir, os.path.basename(image_rel_path))
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            cv2.imwrite(output_path, image)
            print(f"已处理: {image_rel_path}")
            
        except Exception as e:
            print(f"处理图像时出错: {image_rel_path}, 错误: {str(e)}")


def analyze_aspect_ratios(root_dir, dataset_file, ratio_ranges):
    """
    统计数据集中图像的宽高比分布
    
    Args:
        root_dir (str): 数据集根目录
        dataset_file (str): 数据集文件路径，每行格式为"相对路径\t其他内容..."
        ratio_ranges (list): 需要统计的宽高比范围列表，每个元素为元组(min_ratio, max_ratio)
                           表示统计宽高比大于min_ratio且小于等于max_ratio的图像
    """
    import os
    import cv2
    from collections import defaultdict
    
    # 初始化统计结果
    stats = {
        'total_images': 0,          # 总图像数
        'valid_images': 0,          # 有效图像数（可以正确读取的图像）
        'ratio_stats': defaultdict(int),  # 各范围统计
        'ratio_percentages': {},    # 各范围百分比
        'max_ratio': 0.0,          # 最大宽高比
        'max_ratio_image': ''      # 具有最大宽高比的图像路径
    }
    
    # 读取数据集文件
    with open(dataset_file, 'r', encoding='utf-8') as f:
        for line in f:
            parts = line.strip().split('\t')
            if not parts:
                continue
            
            stats['total_images'] += 1
            img_path = os.path.join(root_dir, parts[0])
            
            try:
                # 读取图像获取尺寸
                img = cv2.imread(img_path)
                if img is None:
                    print(f"警告：无法读取图像 {img_path}")
                    continue
                    
                h, w = img.shape[:2]
                aspect_ratio = w / float(h)  # 计算宽高比
                
                stats['valid_images'] += 1
                
                # 更新最大宽高比
                if aspect_ratio > stats['max_ratio']:
                    stats['max_ratio'] = aspect_ratio
                    stats['max_ratio_image'] = img_path
                
                # 统计各个范围
                for i, (min_ratio, max_ratio) in enumerate(ratio_ranges):
                    range_key = f"({min_ratio:.1f}, {max_ratio:.1f}]"
                    if min_ratio < aspect_ratio <= max_ratio:
                        stats['ratio_stats'][range_key] += 1
                    
            except Exception as e:
                print(f"错误：处理图像失败 {img_path}: {str(e)}")
                continue
    
    # 计算百分比
    valid_count = stats['valid_images']
    if valid_count > 0:
        # 计算各范围百分比
        for range_key, count in stats['ratio_stats'].items():
            percentage = (count / valid_count) * 100
            stats['ratio_percentages'][range_key] = f"{percentage:.2f}%"
    
    # 打印统计结果
    print(f"\n数据集宽高比统计结果:")
    print(f"总图像数: {stats['total_images']}")
    print(f"有效图像数: {stats['valid_images']}")
    print(f"\n最大宽高比: {stats['max_ratio']:.2f}")
    print(f"最大宽高比图像: {stats['max_ratio_image']}")
    print("\n各范围统计:")
    for range_key in stats['ratio_stats'].keys():
        count = stats['ratio_stats'][range_key]
        percentage = stats['ratio_percentages'][range_key]
        print(f"宽高比范围 {range_key}: {count} 张 ({percentage})")
    
    return stats


def find_representative_images(root_dir, dataset_file, ratio_ranges, output_dir='representative_images'):
    """
    找出不同宽高比范围的代表图像（每个范围3张），并保存到指定目录
    
    Args:
        root_dir (str): 数据集根目录
        dataset_file (str): 数据集文件路径，每行格式为"相对路径\t其他内容..."
        ratio_ranges (list): 需要统计的宽高比范围列表，每个元素为元组(min_ratio, max_ratio)
        output_dir (str): 保存代表图像的输出目录，默认为'representative_images'
    """
    import os
    import cv2
    import shutil
    from collections import defaultdict
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    range_images = defaultdict(list)
    range_counts = defaultdict(int)
    # 记录每个范围内已经存在的宽高比，避免重复
    range_existing_ratios = defaultdict(set)
    target_count = 3
    filled_ranges = 0
    total_ranges = len(ratio_ranges)
    
    with open(dataset_file, 'r', encoding='utf-8') as f:
        for line in f:
            if filled_ranges == total_ranges:
                break
                
            parts = line.strip().split('\t')
            if not parts:
                continue
            
            img_path = os.path.join(root_dir, parts[0])
            
            try:
                img = cv2.imread(img_path)
                if img is None:
                    continue
                    
                h, w = img.shape[:2]
                aspect_ratio = round(w / float(h), 3)  # 保留3位小数，避免浮点数精度问题
                
                for i, (min_ratio, max_ratio) in enumerate(ratio_ranges):
                    range_key = f"({min_ratio:.1f}, {max_ratio:.1f}]"
                    if range_counts[range_key] >= target_count:
                        continue
                        
                    if min_ratio < aspect_ratio <= max_ratio:
                        # 检查该范围内是否已经存在相同宽高比的图片
                        if aspect_ratio in range_existing_ratios[range_key]:
                            continue
                             
                        range_images[range_key].append((img_path, aspect_ratio, img))
                        range_counts[range_key] += 1
                        range_existing_ratios[range_key].add(aspect_ratio)
                        
                        if range_counts[range_key] == target_count:
                            filled_ranges += 1
                        break
                    
            except Exception as e:
                continue
    
    print("\n各宽高比范围的代表图像：")
    for range_key, images in range_images.items():
        if not images:
            continue
            
        min_ratio = float(range_key[1:].split(',')[0])
        max_ratio = float(range_key.split(',')[1][:-1])
        target_ratio = (min_ratio + max_ratio) / 2
        
        sorted_images = sorted(images, key=lambda x: abs(x[1] - target_ratio))
        
        # 为当前范围创建子目录
        range_dir = os.path.join(output_dir, f"ratio_{min_ratio:.1f}_{max_ratio:.1f}")
        os.makedirs(range_dir, exist_ok=True)
        
        print(f"\n宽高比范围 {range_key}:")
        for idx, (img_path, ratio, img) in enumerate(sorted_images[:target_count]):
            print(f"代表图像路径: {img_path}")
            print(f"实际宽高比: {ratio:.3f}")
            
            # 保存图像到输出目录
            img_name = f"{idx+1}_ratio_{ratio:.3f}.jpg"
            dst_path = os.path.join(range_dir, img_name)
            cv2.imwrite(dst_path, img)
            print(f"已保存到: {dst_path}")


def aug_test(source_dir, output_dir):
    os.makedirs(output_dir, exist_ok=True)
    single_valid_strategies = [
        # DegradationType.BLUR,
        # DegradationType.SINC,
        # DegradationType.ESRGAN_BLUR,
        # DegradationType.IRIS_BLUR_LARGE,
        DegradationType.JPEG,
    ]
    # 组合策略   21种     50% = 2.38% * 21
    combined_valid_strategies = [
        # 组合类型 1： 变淡 + 4种模糊（去掉重）+ 4种非模糊    8种
        # 参数：模糊去掉重、random_resize(去掉重)、其它不变
        # ("type_1", [DegradationType.FADE_GLOBAL, DegradationType.BLUR]),
        # ("type_1", [DegradationType.FADE_GLOBAL, DegradationType.SINC]),
        # ("type_1", [DegradationType.FADE_GLOBAL, DegradationType.ESRGAN_BLUR]),
        ("type_1", [DegradationType.FADE_GLOBAL, DegradationType.IRIS_BLUR_LARGE]),
        # ("type_1", [DegradationType.FADE_GLOBAL, DegradationType.RANDOM_RESIZE]),
        ("type_1", [DegradationType.FADE_GLOBAL, DegradationType.JPEG]),

        # 组合类型 2：变淡 + jpeg在模糊后     6种
        # 参数：模糊去掉重、jpeg变轻
        # ("type_2", [DegradationType.FADE_GLOBAL, DegradationType.BLUR, DegradationType.JPEG]),
        # ("type_2", [DegradationType.FADE_GLOBAL, DegradationType.SINC, DegradationType.JPEG]),
        # ("type_2", [DegradationType.FADE_GLOBAL, DegradationType.ESRGAN_BLUR, DegradationType.JPEG]),
        ("type_2", [DegradationType.FADE_GLOBAL, DegradationType.IRIS_BLUR_LARGE, DegradationType.JPEG]),
        ("type_2", [DegradationType.FADE_GLOBAL, DegradationType.ARTISTIC_INK, DegradationType.JPEG]),
        ("type_2", [DegradationType.FADE_GLOBAL, DegradationType.JPEG, DegradationType.UNEVEN_LIGHTING]),

        # 组合类型 3：   变淡 + resize在模糊前 + 其它      7种
        # 参数：模糊去掉中重、resisze去掉中重、jpeg不变
        # ("type_3", [DegradationType.FADE_GLOBAL, DegradationType.RANDOM_RESIZE, DegradationType.BLUR]),
        # ("type_3", [DegradationType.FADE_GLOBAL, DegradationType.SINC]),
        # ("type_3", [DegradationType.FADE_GLOBAL, DegradationType.ESRGAN_BLUR]),
        ("type_3", [DegradationType.FADE_GLOBAL, DegradationType.IRIS_BLUR_LARGE]),
        # 变淡 + resize在前后均可，此处选择在前
        ("type_3", [DegradationType.FADE_GLOBAL, DegradationType.JPEG]),
        ("type_3", [DegradationType.FADE_GLOBAL, DegradationType.UNEVEN_LIGHTING]),
        ("type_3", [DegradationType.FADE_GLOBAL, DegradationType.ARTISTIC_INK]),
    ]

    for image_name in os.listdir(source_dir):
        image_path = os.path.join(source_dir, image_name)
        ori_image = cv2.imread(image_path)

        prob = random.random()
        choose_flag = {
            "blur_param_1": 1,
            "jpeg_param_2": 1,
            "resize_param_3": 1
        }
        if prob < 1.0:
            if len(single_valid_strategies) > 1:
                prob_single_list = [0.12] * len(single_valid_strategies)
                prob_single_list[1] = 1 - 0.12 * (len(single_valid_strategies) - 1)
                chosen_strategies = random.choices(single_valid_strategies, weights=prob_single_list, k=1)
            else:
                chosen_strategies = single_valid_strategies
        else:
            selected_type, chosen_strategies = random.choice(combined_valid_strategies)
            if selected_type == "type_1":
                choose_flag = {
                    "blur_param_1": 2,  # 只有轻中
                    "jpeg_param_2": 1,  # 分概率
                    "resize_param_3": 2  # 只有轻中
                }
            elif selected_type == "type_2":
                choose_flag = {
                    "blur_param_1": 2,  # 去掉重
                    "jpeg_param_2": 2,  # 只有轻
                    "resize_param_3": 1
                }
            else:
                choose_flag = {
                    "blur_param_1": 3,  # 只有轻
                    "jpeg_param_2": 1,  # 分概率
                    "resize_param_3": 3  # 只有轻
                }

        global IMG_DEGRADE_PIPE
        image = ori_image.copy()
        for strategy in chosen_strategies:
            image = IMG_DEGRADE_PIPE.process(image=image, custom_strategies=[strategy], choose_flag=choose_flag)

        combined_img = np.vstack([ori_image, image])
        cv2.imwrite(os.path.join(output_dir, image_name), combined_img)


def single_aug_test(source_dir, output_dir):
    os.makedirs(output_dir, exist_ok=True)

    for image_name in os.listdir(source_dir):
        image_path = os.path.join(source_dir, image_name)
        image = cv2.imread(image_path)


        encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), 10]
        _, enc_img = cv2.imencode('.jpg', image, encode_param)
        image = cv2.imdecode(enc_img, 1)

        np.uint8(np.clip(image, 0, 255))

        cv2.imwrite(os.path.join(output_dir, image_name), image)


def load_config(config_path):
    """
    加载yaml格式的配置文件
    Args:
        config_path: 配置文件路径
    Returns:
        配置字典
    """
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def init_rec_model(config):
    """
    初始化识别模型
    Args:
        config: 识别模型配置
    Returns:
        model: 识别模型
        post_process: 后处理器
        ops: 预处理操作列表
    """
    global_config = config["Global"]

    # 构建后处理器
    post_process = build_post_process(config["PostProcess"], global_config)

    # 构建模型
    if hasattr(post_process, "character"):
        char_num = len(getattr(post_process, "character"))
        if config["Architecture"]["algorithm"] in ["Distillation"]:  # 蒸馏模型
            for key in config["Architecture"]["Models"]:
                if config["Architecture"]["Models"][key]["Head"]["name"] == "MultiHead":  # 多头模型
                    out_channels_list = {}
                    if config["PostProcess"]["name"] == "DistillationSARLabelDecode":
                        char_num = char_num - 2
                    if config["PostProcess"]["name"] == "DistillationNRTRLabelDecode":
                        char_num = char_num - 3
                    out_channels_list["CTCLabelDecode"] = char_num
                    out_channels_list["SARLabelDecode"] = char_num + 2
                    out_channels_list["NRTRLabelDecode"] = char_num + 3
                    config["Architecture"]["Models"][key]["Head"]["out_channels_list"] = out_channels_list
                else:
                    config["Architecture"]["Models"][key]["Head"]["out_channels"] = char_num
        elif config["Architecture"]["Head"]["name"] == "MultiHead":  # 多头模型
            out_channels_list = {}
            if config["PostProcess"]["name"] == "SARLabelDecode":
                char_num = char_num - 2
            if config["PostProcess"]["name"] == "NRTRLabelDecode":
                char_num = char_num - 3
            out_channels_list["CTCLabelDecode"] = char_num
            out_channels_list["SARLabelDecode"] = char_num + 2
            out_channels_list["NRTRLabelDecode"] = char_num + 3
            config["Architecture"]["Head"]["out_channels_list"] = out_channels_list
        else:  # 普通单模型
            config["Architecture"]["Head"]["out_channels"] = char_num

    if config["Architecture"].get("algorithm") in ["LaTeXOCR"]:
        config["Architecture"]["Backbone"]["is_predict"] = True
        config["Architecture"]["Backbone"]["is_export"] = True
        config["Architecture"]["Head"]["is_export"] = True

    model = build_model(config["Architecture"])
    load_model(config, model)
    model.eval()

    # 创建预处理操作
    transforms = []
    for op in config["Test"]["dataset"]["transforms"]:
        op_name = list(op)[0]
        if "Label" in op_name:
            continue
        elif op_name in ["RecResizeImg"]:
            op[op_name]["infer_mode"] = True
        elif op_name == "KeepKeys":
            if config["Architecture"]["algorithm"] == "SRN":
                op[op_name]["keep_keys"] = [
                    "image",
                    "encoder_word_pos",
                    "gsrm_word_pos",
                    "gsrm_slf_attn_bias1",
                    "gsrm_slf_attn_bias2",
                ]
            elif config["Architecture"]["algorithm"] == "SAR":
                op[op_name]["keep_keys"] = ["image", "valid_ratio"]
            elif config["Architecture"]["algorithm"] == "RobustScanner":
                op[op_name]["keep_keys"] = ["image", "valid_ratio", "word_positons"]
            else:
                op[op_name]["keep_keys"] = ["image"]
        transforms.append(op)

    global_config["infer_mode"] = True
    global_config["infer_auto"] = False
    if "Train" in config and "sampler" in config["Train"] and "scales" in config["Train"]["sampler"]:
        global_config["scales"] = config["Train"]["sampler"]["scales"]
    ops = create_operators(transforms, global_config)

    return model, post_process, ops


def rotate_analyse(input_dir):
    rec_config = "/aipdf-mlp/jiacheng/exp/pp_ocr_rec/bq_svtr_v2_7M/config.yml"
    rec_model_path = "/aipdf-mlp/jiacheng/exp/pp_ocr_rec/bq_svtr_v2_7M/best_accuracy"

    rec_config = load_config(rec_config)
    rec_config["Global"]["checkpoints"] = rec_model_path
    rec_model, rec_post_process, rec_ops = init_rec_model(rec_config)

    rotate_model = create_model(model_name="PP-LCNet_x0_25_textline_ori")
    for image_name in os.listdir(input_dir):
        image_path = os.path.join(input_dir, image_name)
        output = rotate_model.predict(image_path, batch_size=1)
        for res in output:
            # print(res)
            rotate_score = res["scores"][0]
            pred_cls = res['label_names'][0]
            # print(image_name, score, pred_cls)

        image = cv2.imread(image_path)
        # 根据分类结果旋转图像
        if '180' in pred_cls:
            rotated_image = cv2.rotate(image, cv2.ROTATE_180)
        else:
            rotated_image = image

        CACHE_IMAGE_PATH = '/aipdf-mlp/jiacheng/exp/pp_ocr_rec/vis_rec_base_0318_det/temp.jpg'

        try_time = 0
        rotated_image_backup = rotated_image.copy()
        while True:
            if try_time == 1 :
                rotated_image = cv2.rotate(rotated_image_backup, cv2.ROTATE_180)
            if try_time == 2 :
                rotated_image = cv2.rotate(rotated_image_backup, cv2.ROTATE_90_CLOCKWISE)
                h, w, c = rotated_image.shape
                if h/w > 1.5:
                    break
            if try_time == 3 :
                rotated_image = cv2.rotate(rotated_image_backup, cv2.ROTATE_90_COUNTERCLOCKWISE)
                h, w, c = rotated_image.shape
                if h / w > 1.5:
                    break

            cv2.imwrite(CACHE_IMAGE_PATH, rotated_image)
            with open(CACHE_IMAGE_PATH, "rb") as f:
                image = f.read()
            data = {"image": image}
            batch = transform(data, rec_ops)
            images = np.expand_dims(batch[0], axis=0)
            images = paddle.to_tensor(images)

            # 模型预测
            preds = rec_model(images)

            # 后处理
            rec_result = rec_post_process(preds)
            if len(rec_result[0]) >= 2:
                text = rec_result[0][0]
                rec_score = float(rec_result[0][1])
            else:
                text = rec_result[0][0]
                rec_score = float(rec_result[0][1])

            if try_time == 0:
                if rotate_score < 0.9 and rec_score < 0.7:
                    print(image_name, rotate_score, pred_cls, text, rec_score)
                    try_time += 1
                    continue
                else:
                    print('****', image_name, rotate_score, pred_cls, text, rec_score)
                    break
            else:
                print('---', text, rec_score)
                try_time += 1
                if try_time > 3:
                    break


def test_onnx_speed(onnx_path, batch_size=1, num_iter=100):
    """
    测试ONNX模型的推理速度
    Args:
        onnx_path: ONNX模型路径
        batch_size: batch大小
        num_iter: 迭代次数
    """
    import onnxruntime as ort
    import time
    import sys
    
    # 强制使用CPU
    providers = ['CPUExecutionProvider']
    session = ort.InferenceSession(onnx_path, providers=providers)
    
    # 获取输入输出名称
    input_name = session.get_inputs()[0].name
    
    # 创建随机输入数据
    dummy_input = np.random.randn(batch_size, 3, 48, 320).astype(np.float32)
    
    # 预热
    print("Warmup...")
    for _ in range(20):
        session.run(None, {input_name: dummy_input})
    
    # 测速
    print(f"Testing with batch_size={batch_size}, iterations={num_iter}")
    times = []
    width = 100
    for i in range(num_iter):
        # dummy_input = np.random.randn(batch_size, 3, 48, width).astype(np.float32)
        start = time.time()
        session.run(None, {input_name: dummy_input})
        end = time.time()
        times.append(end - start)
        # width += 10
    
    # 计算统计信息
    times = np.array(times)
    mean_time = np.mean(times)
    std_time = np.std(times)
    min_time = np.min(times)
    max_time = np.max(times)
    p50_time = np.percentile(times, 50)
    p95_time = np.percentile(times, 95)
    p99_time = np.percentile(times, 99)
    
    print("\nInference Statistics:")
    print(f"Mean Time: {mean_time*1000:.2f}ms")
    print(f"Std Time: {std_time*1000:.2f}ms")
    print(f"Min Time: {min_time*1000:.2f}ms")
    print(f"Max Time: {max_time*1000:.2f}ms")
    print(f"P50 Time: {p50_time*1000:.2f}ms")
    print(f"P95 Time: {p95_time*1000:.2f}ms")
    print(f"P99 Time: {p99_time*1000:.2f}ms")
    print(f"Throughput: {batch_size/mean_time:.2f} images/sec")
    
    return {
        'mean': mean_time,
        'std': std_time,
        'min': min_time,
        'max': max_time,
        'p50': p50_time,
        'p95': p95_time,
        'p99': p99_time,
        'throughput': batch_size/mean_time
    }


def plot_training_curves(log_file, save_dir, keys=None):
    """
    解析训练日志并绘制曲线图，每个指标保存为单独的图片，并生成一个汇总图

    Args:
        log_file (str): 日志文件路径
        save_dir (str): 图像保存目录
        keys (list): 需要绘制的指标名称列表，如果为None则绘制所有指标
    """
    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)
    
    # 用于存储每个指标的值
    metrics = defaultdict(list)
    epochs = []
    
    # 读取日志文件
    with open(log_file, 'r', encoding='utf-8') as f:
        for line in f:
            # 检查是否包含训练信息
            if "epoch:" not in line or "global_step:" not in line:
                continue
            
            try:
                # 去除日志前缀
                if "] " in line:
                    line = line.split("] ")[-1].strip()
                if "INFO:" in line:
                    line = line.split("INFO:")[-1].strip()
                
                # 提取epoch
                if "epoch: [" in line:
                    epoch_str = line.split("epoch: [")[1].split("/")[0]
                    epochs.append(float(epoch_str))
                else:
                    continue
                
                # 提取所有指标
                parts = line.split(", ")
                for part in parts:
                    if ":" not in part:
                        continue
                        
                    key, value = [x.strip() for x in part.split(":", 1)]
                    
                    # 跳过不需要的指标
                    if keys and key not in keys:
                        continue
                        
                    # 跳过非数值指标
                    if any(skip in value for skip in ['days', 'MB', 'samples/s']):
                        continue
                        
                    # 处理带单位的值
                    if " " in value:  # 如果值后面有单位，只取数值部分
                        value = value.split()[0]
                        
                    # 尝试转换为浮点数
                    try:
                        value = float(value)
                        metrics[key].append(value)
                    except ValueError:
                        continue
                        
            except Exception as e:
                print(f"解析行时出错: {e}")
                continue
    
    if not metrics:
        print("未找到任何指标数据")
        return
        
    if not epochs:
        print("未找到epoch信息")
        return
    
    # 检查数据一致性
    n_epochs = len(epochs)
    for key, values in metrics.items():
        if len(values) != n_epochs:
            print(f"警告：指标 {key} 的数据点数量 ({len(values)}) 与epoch数量 ({n_epochs}) 不匹配")
    
    # 如果没有指定keys，使用所有数值型指标
    if keys is None:
        keys = [k for k, v in metrics.items() if v and isinstance(v[0], (int, float))]
    
    # 保存每个指标的单独图片
    for key in keys:
        if key not in metrics:
            print(f"警告：未找到指标 {key}")
            continue
            
        plt.figure(figsize=(10, 6))
        plt.plot(epochs, metrics[key], 'b-', label=key)
        plt.title(key)
        plt.xlabel('Epoch')
        plt.ylabel('Value')
        plt.grid(True)
        plt.legend()
        
        # 保存单个指标图片
        save_path = os.path.join(save_dir, f"{key}.png")
        plt.savefig(save_path)
        plt.close()
        print(f"已保存指标 {key} 的曲线图到: {save_path}")
    
    # 创建汇总图
    n_metrics = len(keys)
    n_cols = min(3, n_metrics)  # 最多3列
    n_rows = (n_metrics + n_cols - 1) // n_cols
    
    plt.figure(figsize=(6*n_cols, 4*n_rows))
    
    for i, key in enumerate(keys, 1):
        if key not in metrics:
            continue
            
        plt.subplot(n_rows, n_cols, i)
        plt.plot(epochs, metrics[key], 'b-', label=key)
        plt.title(key)
        plt.xlabel('Epoch')
        plt.ylabel('Value')
        plt.grid(True)
        plt.legend()
    
    # 调整子图布局
    plt.tight_layout()
    
    # 保存汇总图
    summary_path = os.path.join(save_dir, "summary.png")
    plt.savefig(summary_path)
    plt.close()
    print(f"已保存汇总图到: {summary_path}")


if __name__ == '__main__':
    # Test ONNX speed
    # onnx_path = "/aipdf-mlp/jiacheng/exp/pp_ocr_rec/temp_bqrepsvtr/origin/infer/inference.onnx"
    # onnx_path = "/aipdf-mlp/jiacheng/exp/pp_ocr_rec/bq_repsvtr_v2_7M/best_infer_model/inference.onnx"
    # onnx_path = "/aipdf-mlp/jiacheng/exp/pp_ocr_rec/temp_bqrepsvtr/origin/small/inference.onnx"
    # if os.path.exists(onnx_path):
    #     print(f"Testing ONNX model: {onnx_path}")
    #     test_onnx_speed(onnx_path, batch_size=1, num_iter=100)
        
    # 利用编辑距离筛选推理结果
    # input_file = '/aipdf-mlp/jiacheng/exp/pp_ocr_rec/infer_res_temp/rec_gt_bench_bq_doc_4.5M.txt'
    # output_file = '/aipdf-mlp/jiacheng/exp/pp_ocr_rec/infer_res_temp/prec_gt_bench_bq_doc_4.5M_filtered.txt'
    # filter_edit_distance(input_file, output_file)
    
    # 可视化示例
    # output_file = '/aipdf-mlp/jiacheng/exp/pp_ocr_rec/infer_res_temp/rec_gt_bench_bq_doc_7M.txt'
    # output_dir = '/aipdf-mlp/jiacheng/exp/pp_ocr_rec/infer_res_temp/dir_rec_gt_bench_bq_doc_7M'
    # visualize_results(
    #     output_file,
    #     output_dir,
    #     scales=[[960, 32], [640, 32], [320, 32], [320, 48], [320, 64], [128, 64]],
    #     level=2,
    #     tag='chinese'
    # )  # 使用两级路径名
    # print('Visualize Done.')
    
    # 统计数据集宽高比示例
    # root_dir = '/aipdf-mlp/shared/ocr_rec_dataset'
    # dataset_file = '/aipdf-mlp/shared/ocr_rec_dataset/pdf_train.txt'
    # ratio_ranges = [
    #     (0, 1),
    #     (1, 2),
    #     (2, 3),
    #     (3, 4),
    #     (4, 5),
    #     (5, 6),
    #     (6, 7),
    #     (7, 8),
    #     (8, 9),
    #     (9, 10),
    # ]
    # # ratio_ranges = [
    # #     # (0, 10),
    # #     # (10, 20),
    # #     # (20, 30),
    # #     # (30, 40),
    # #     # (40, 50),
    # #     # (50, 60),
    # #     # (60, 70),
    # #     (70, 80),
    # #     # (80, 90),
    # #     (90, float('inf'))
    # # ]
    # stats = analyze_aspect_ratios(root_dir, dataset_file, ratio_ranges)
    # find_representative_images(root_dir, dataset_file, ratio_ranges,
    #                            '/aipdf-mlp/shared/ocr_rec_dataset/pdf_rec_new/train_re')

    # source_dir = "/aipdf-mlp/jiacheng/code/text_render/synth_temp/images"
    # output_dir = "/aipdf-mlp/jiacheng/code/text_render/synth_temp/pre_images"
    # single_input_dir = "/aipdf-mlp/jiacheng/code/text_render/synth_temp/single"
    # aug_test(source_dir, output_dir)
    # single_aug_test(source_dir, single_input_dir)

    # rotate_input_path = "/aipdf-mlp/jiacheng/exp/temp/5_0/crop_vis/hard/fb4a2246114f74b5b126324edeb5c056_page002"
    # rotate_analyse(rotate_input_path)

    # image_dir = "/aipdf-mlp/xelawk/debug/ocr_benchmark_results/kuake/tmp_save"
    # output_dir = "/aipdf-mlp/jiacheng/exp/pipeline_infer/competitor/kuake"
    # label_path = "/aipdf-mlp/xelawk/debug/ocr_benchmark_results/kuake/original/Label.txt"
    # visualize_text_boxes(image_dir, output_dir, label_path)
    # print("done")

    # 只绘制特定指标
    plot_training_curves(
        "/aipdf-mlp/jiacheng/exp/pp_ocr_rec/bq_svtrv2_teach_mv4m_ft_distill_7M/train_250429.log", 
        "/aipdf-mlp/jiacheng/exp/pp_ocr_rec/bq_svtrv2_teach_mv4m_ft_distill_7M/log_250429_visual",  # 这是一个目录
        keys=['loss', 'dkd_0', 'loss_ctc_Student_0', 'loss_nrtr_Student_0', 'ctc_logits_0']
    )