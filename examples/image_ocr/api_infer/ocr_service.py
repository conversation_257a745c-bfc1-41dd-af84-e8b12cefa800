#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/03/18 17:46
# <AUTHOR> <EMAIL>
# @FileName: ocr_service.py

from typing import Dict, List, Union, Any, Optional

import os
import io
import cv2
import json
import time
import yaml
import uuid
import base64
import hashlib
import requests
import numpy as np
import urllib.parse
from PIL import Image
from pathlib import Path
from requests.exceptions import RequestException, Timeout, ConnectionError

# 阿里云OCR依赖
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_darabonba_stream.client import Client as StreamClient
from alibabacloud_ocr_api20210707 import models as ocr_api_20210707_models
from alibabacloud_ocr_api20210707.client import Client as ocr_api20210707Client

# 华为云OCR依赖
from huaweicloudsdkocr.v1.ocr_client import OcrClient
from huaweicloudsdkocr.v1.region.ocr_region import OcrRegion
from huaweicloudsdkcore.auth.credentials import BasicCredentials
from huaweicloudsdkocr.v1.model.general_text_request_body import GeneralTextRequestBody
from huaweicloudsdkocr.v1.model.recognize_general_text_request import RecognizeGeneralTextRequest

# 腾讯云OCR依赖
from tencentcloud.common import credential
from tencentcloud.ocr.v20181119 import ocr_client, models
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.profile.client_profile import ClientProfile


class InputDTO:
    """
    统一输入数据传输对象
    """
    def __init__(self, image: Union[str, Path, Image.Image]):
        """
        初始化输入对象
        
        Args:
            image: 图像路径字符串、Path对象或PIL Image对象
        """
        self.image = image
        self.image_path = None
        self.pil_image = None
        
        # 如果输入是字符串路径
        if isinstance(image, str):
            self.image_path = Path(image)
            self.pil_image = Image.open(self.image_path)
        # 如果输入是Path对象
        elif isinstance(image, Path):
            self.image_path = image
            self.pil_image = Image.open(self.image_path)
        # 如果输入是PIL Image对象
        elif isinstance(image, Image.Image):
            self.pil_image = image
            # 创建临时文件路径
            self.image_path = self._save_temp_image(image)
    
    def _save_temp_image(self, image: Image.Image) -> Path:
        """
        将PIL Image保存为临时文件
        
        Args:
            image: PIL Image对象
            
        Returns:
            临时文件路径
        """
        temp_dir = Path(__file__).parent / "temp"
        temp_dir.mkdir(exist_ok=True)
        temp_path = temp_dir / f"temp_{id(image)}.jpg"
        image.save(temp_path)
        return temp_path
    
    def get_image_path(self) -> Path:
        """
        获取图像文件路径
        
        Returns:
            图像文件路径
        """
        return self.image_path
    
    def get_pil_image(self) -> Image.Image:
        """
        获取PIL图像对象
        
        Returns:
            PIL图像对象
        """
        return self.pil_image
    
    def get_image_base64(self) -> str:
        """
        获取图像的base64编码
        
        Returns:
            base64编码的图像字符串
        """
        buffered = io.BytesIO()
        self.pil_image.save(buffered, format="JPEG")
        return base64.b64encode(buffered.getvalue()).decode("utf-8")


class OutputDTO:
    """
    统一输出数据传输对象
    """
    def __init__(self, ocr_results: List[Dict], raw_response: Any = None, corrected_image: np.ndarray = None):
        """
        初始化输出对象
        
        Args:
            ocr_results: 统一格式的OCR结果列表
            raw_response: 原始API响应
            corrected_image: 矫正后的图像（如果API返回）
        """
        self.ocr_results = ocr_results  # PaddleOCR标准格式
        self.raw_response = raw_response  # 保存原始响应
        self.corrected_image = corrected_image  # 保存矫正后的图像
    
    def get_ocr_results(self) -> List[Dict]:
        """
        获取统一格式的OCR结果
        
        Returns:
            OCR结果列表，符合PaddleOCR格式
        """
        return self.ocr_results
    
    def get_raw_response(self) -> Any:
        """
        获取原始API响应
        
        Returns:
            原始API响应
        """
        return self.raw_response
    
    def get_corrected_image(self) -> Optional[np.ndarray]:
        """
        获取矫正后的图像（如果可用）
        
        Returns:
            矫正后的图像，如果不可用则返回None
        """
        return self.corrected_image
    
    def to_dict(self) -> Dict:
        """
        转换为字典格式
        
        Returns:
            字典格式的结果
        """
        return {
            "ocr_results": self.ocr_results,
            "raw_response": self.raw_response,
            "has_corrected_image": self.corrected_image is not None
        }
    
    def to_json(self) -> str:
        """
        转换为JSON字符串
        
        Returns:
            JSON字符串
        """
        return json.dumps(self.to_dict(), ensure_ascii=False)


class OCRService:
    """
    OCR服务类，统一调度不同的OCR API
    """
    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """
        初始化OCR服务
        
        Args:
            config_path: 配置文件路径，默认为None，使用默认配置路径
        """
        assert os.path.exists(config_path)
        config_path = Path(config_path)
        self.config = self._load_config(config_path)
        self.supported_apis = ["ali", "baidu", "huawei", "kuake", "tencent", "textin"]
    
    def _load_config(self, config_path: Path) -> Dict:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置字典
        """
        with open(config_path, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)
        return config
    
    def recognize(self, image: Union[str, Path, Image.Image], api_type: str = "ali") -> OutputDTO:
        """
        使用指定的API进行OCR识别
        
        Args:
            image: 图像路径或PIL Image对象
            api_type: API类型，支持ali、baidu、huawei、kuake、tencent、textin，默认为ali
            
        Returns:
            统一格式的OCR结果
        
        Raises:
            ValueError: 不支持的API类型
        """
        # 检查API类型
        if api_type not in self.supported_apis:
            raise ValueError(f"不支持的API类型: {api_type}, 支持的类型有: {self.supported_apis}")
        
        # 准备输入
        input_dto = InputDTO(image)
        
        # 根据API类型调用不同的OCR接口
        if api_type == "ali":
            return self.retry_ocr_api_call(self._recognize_with_ali, input_dto)
        elif api_type == "baidu":
            return self.retry_ocr_api_call(self._recognize_with_baidu, input_dto)
        elif api_type == "huawei":
            return self.retry_ocr_api_call(self._recognize_with_huawei, input_dto)
        elif api_type == "kuake":
            return self.retry_ocr_api_call(self._recognize_with_kuake, input_dto)
        elif api_type == "tencent":
            return self.retry_ocr_api_call(self._recognize_with_tencent, input_dto)
        elif api_type == "textin":
            return self.retry_ocr_api_call(self._recognize_with_textin, input_dto)
    
    def _recognize_with_ali(self, input_dto: InputDTO) -> OutputDTO:
        """
        使用阿里云OCR进行识别
        
        Args:
            input_dto: 输入数据传输对象
            
        Returns:
            统一格式的OCR结果
        """
        # 获取配置
        ali_config = self.config.get("ali_ocr", {})
        access_key_id = ali_config.get("access_key_id")
        access_key_secret = ali_config.get("access_key_secret")
        endpoint = ali_config.get("endpoint", "ocr-api.cn-hangzhou.aliyuncs.com")
        ocr_type = ali_config.get("type", "General")
        output_coordinate = ali_config.get("output_coordinate", "points")
        
        # 创建阿里云OCR客户端
        config = open_api_models.Config(
            access_key_id=access_key_id,
            access_key_secret=access_key_secret
        )
        config.endpoint = endpoint
        client = ocr_api20210707Client(config)
        
        # 读取图像文件
        image_path = str(input_dto.get_image_path())
        body_stream = StreamClient.read_from_file_path(image_path)
        
        # 构建请求
        recognize_request = ocr_api_20210707_models.RecognizeAllTextRequest(
            type=ocr_type,
            body=body_stream,
            output_coordinate=UtilClient.to_bytes(output_coordinate)
        )
        
        runtime = util_models.RuntimeOptions()
        try:
            # 调用API
            response = client.recognize_all_text_with_options(recognize_request, runtime)
            raw_response = response.body.to_map()
            
            # 转换为统一格式
            ocr_results = []
            if "Data" in raw_response:
                # 处理子图像，如果存在
                if "SubImages" in raw_response["Data"] and raw_response["Data"]["SubImages"]:
                    for sub_image in raw_response["Data"]["SubImages"]:
                        if "BlockInfo" in sub_image and "BlockDetails" in sub_image["BlockInfo"]:
                            for block in sub_image["BlockInfo"]["BlockDetails"]:
                                points = []
                                if "BlockPoints" in block:
                                    # 从BlockPoints中提取坐标点
                                    for point in block["BlockPoints"]:
                                        if "X" in point and "Y" in point:
                                            points.append([int(point["X"]), int(point["Y"])])
                                
                                # 添加到结果列表
                                ocr_results.append({
                                    "transcription": block.get("BlockContent", ""),
                                    "points": points,
                                    "difficult": False  # 阿里OCR没有该字段，默认为False
                                })
                # 如果没有SubImages，尝试处理单个图像的结果
                elif "DetectedTexts" in raw_response["Data"]:
                    for item in raw_response["Data"]["DetectedTexts"]:
                        points = []
                        # 阿里OCR返回的是一个points字符串，需要解析
                        if "Points" in item:
                            points_str = item["Points"]
                            # 解析points字符串为坐标列表
                            point_pairs = points_str.split(";")
                            for pair in point_pairs:
                                if "," in pair:
                                    x, y = pair.split(",")
                                    points.append([int(float(x)), int(float(y))])
                        
                        ocr_results.append({
                            "transcription": item.get("Text", ""),
                            "points": points,
                            "difficult": False  # 阿里OCR没有该字段，默认为False
                        })
            
            return OutputDTO(ocr_results, raw_response)
        
        except Exception as e:
            print(f"阿里云OCR API调用失败: {e}")
            return OutputDTO([], {"error": str(e)})
    
    def _recognize_with_baidu(self, input_dto: InputDTO) -> OutputDTO:
        """
        使用百度OCR进行识别
        
        Args:
            input_dto: 输入数据传输对象
            
        Returns:
            统一格式的OCR结果
        """
        # 获取配置
        baidu_config = self.config.get("baidu_ocr", {})
        api_key = baidu_config.get("api_key")
        secret_key = baidu_config.get("secret_key")
        detect_direction = baidu_config.get("detect_direction", False)
        vertexes_location = baidu_config.get("vertexes_location", True)
        paragraph = baidu_config.get("paragraph", False)
        probability = baidu_config.get("probability", True)
        
        # 获取access_token
        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {"grant_type": "client_credentials", "client_id": api_key, "client_secret": secret_key}
        response = requests.post(url, params=params)
        access_token = response.json().get("access_token")
        
        if not access_token:
            print("百度OCR API授权失败")
            return OutputDTO([], {"error": "获取access_token失败"})
        
        # 调用OCR API
        ocr_url = f"https://aip.baidubce.com/rest/2.0/ocr/v1/accurate?access_token={access_token}"
        
        # 获取图像base64编码
        with open(input_dto.get_image_path(), "rb") as f:
            image_base64 = base64.b64encode(f.read()).decode("utf8")
            if True:  # urlencoded
                image_base64 = urllib.parse.quote_plus(image_base64)
        
        # 构建请求
        payload = f'image={image_base64}'
        payload = payload + f'&detect_direction={str(detect_direction).lower()}'
        payload = payload + f'&vertexes_location={str(vertexes_location).lower()}'
        payload = payload + f'&paragraph={str(paragraph).lower()}'
        payload = payload + f'&probability={str(probability).lower()}'
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
        }
        
        try:
            response = requests.request("POST", ocr_url, headers=headers, data=payload)
            raw_response = response.json()
            
            # 转换为统一格式
            ocr_results = []
            if "words_result" in raw_response:
                for item in raw_response["words_result"]:
                    points = []
                    # 优先使用vertexes_location获取坐标点
                    if "vertexes_location" in item:
                        vertexes = item["vertexes_location"]
                        for vertex in vertexes:
                            points.append([vertex["x"], vertex["y"]])
                    # 如果有min_finegrained_vertexes_location，也可以用这个
                    elif "min_finegrained_vertexes_location" in item:
                        vertexes = item["min_finegrained_vertexes_location"]
                        for vertex in vertexes:
                            points.append([vertex["x"], vertex["y"]])
                    # 如果有finegrained_vertexes_location，也可以用这个
                    elif "finegrained_vertexes_location" in item:
                        vertexes = item["finegrained_vertexes_location"]
                        for vertex in vertexes:
                            points.append([vertex["x"], vertex["y"]])
                    # 最后尝试使用location构建四边形
                    elif "location" in item:
                        # 如果没有顶点信息，使用矩形框的四个角点
                        left = item["location"]["left"]
                        top = item["location"]["top"]
                        width = item["location"]["width"]
                        height = item["location"]["height"]
                        points = [
                            [left, top],
                            [left + width, top],
                            [left + width, top + height],
                            [left, top + height]
                        ]
                    
                    ocr_results.append({
                        "transcription": item.get("words", ""),
                        "points": points,
                        "difficult": False  # 百度OCR没有该字段，默认为False
                    })
            
            return OutputDTO(ocr_results, raw_response)
        
        except Exception as e:
            print(f"百度OCR API调用失败: {e}")
            return OutputDTO([], {"error": str(e)})
    
    def _recognize_with_huawei(self, input_dto: InputDTO) -> OutputDTO:
        """
        使用华为云OCR进行识别
        
        Args:
            input_dto: 输入数据传输对象
            
        Returns:
            统一格式的OCR结果
        """
        # 获取配置
        huawei_config = self.config.get("huawei_ocr", {})
        endpoint = huawei_config.get("endpoint", "ocr.cn-north-4.myhuaweicloud.com")
        project_id = huawei_config.get("project_id")
        ak = huawei_config.get("ak")
        sk = huawei_config.get("sk")
        detect_direction = huawei_config.get("detect_direction", False)
        
        # 初始化认证
        credentials = BasicCredentials(ak, sk)
        
        # 初始化客户端
        client = OcrClient.new_builder() \
            .with_credentials(credentials) \
            .with_region(OcrRegion.value_of(endpoint.split('.')[1])) \
            .build()
        
        try:
            # 读取图像文件
            with open(input_dto.get_image_path(), "rb") as f:
                image_base64 = base64.b64encode(f.read()).decode("utf8")
            
            # 构建请求
            request = RecognizeGeneralTextRequest()
            request.body = GeneralTextRequestBody(
                image=image_base64,
                detect_direction=detect_direction
            )
            
            # 调用API
            response = client.recognize_general_text(request)
            raw_response = response.to_dict()
            
            # 转换为统一格式
            ocr_results = []
            if "result" in raw_response and "words_block_list" in raw_response["result"]:
                for item in raw_response["result"]["words_block_list"]:
                    points = []
                    # 华为OCR返回的是二维数组格式的坐标点
                    if "location" in item:
                        # 确保location是有效的坐标点数组
                        location = item["location"]
                        if isinstance(location, list) and len(location) > 0:
                            for point in location:
                                if isinstance(point, list) and len(point) >= 2:
                                    points.append([int(point[0]), int(point[1])])
                    
                    ocr_results.append({
                        "transcription": item.get("words", ""),
                        "points": points,
                        "difficult": False  # 华为OCR没有该字段，默认为False
                    })
            
            return OutputDTO(ocr_results, raw_response)
        
        except Exception as e:
            print(f"华为云OCR API调用失败: {e}")
            return OutputDTO([], {"error": str(e)})
    
    def _recognize_with_kuake(self, input_dto: InputDTO) -> OutputDTO:
        """
        使用夸克OCR进行识别
        
        Args:
            input_dto: 输入数据传输对象
            
        Returns:
            统一格式的OCR结果
            
        Raises:
            ValueError: 夸克OCR API返回结果中没有坐标信息
        """
        # 获取配置
        kuake_config = self.config.get("kuake_ocr", {})
        client_id = kuake_config.get("client_id")
        client_secret = kuake_config.get("client_secret")
        business = kuake_config.get("business", "general_ocr_v2")
        sign_method = kuake_config.get("sign_method", "SHA3-256")
        
        try:
            # 获取图像路径
            image_path = str(input_dto.get_image_path())
            
            # 创建签名和请求参数
            sign_nonce = uuid.uuid4().hex
            timestamp = int(time.time() * 1000)
            signature = self._get_kuake_signature(client_id, client_secret, business, sign_method, sign_nonce, timestamp)
            
            # 获取图像base64编码
            with open(image_path, "rb") as image_file:
                encoded_string = base64.b64encode(image_file.read()).decode()
            img_base64 = 'data:image/jpg;base64,' + encoded_string
            
            # 构建参数
            param = {
                "outputConfigs": '{"need_return_image":"true"}',
                "clientId": client_id,
                "business": business,
                "signMethod": sign_method,
                "signNonce": sign_nonce,
                "timestamp": timestamp,
                "imgBase64": img_base64,
                "signature": signature
            }
            
            # 发送请求
            req_id = uuid.uuid4().hex
            url = f"https://scan-business.quark.cn/api/ocr/handleBase64?reqId={req_id}"
            headers = {
                "Content-Type": "application/json",
            }
            
            response = requests.post(url, data=json.dumps(param), headers=headers)
            
            if response.status_code == 200:
                raw_response = response.json()
                
                # 检查API返回状态
                code = raw_response.get("code")
                if code != "00000":  # 夸克OCR成功状态码是"00000"
                    error_msg = f"夸克OCR API返回错误: 状态码 {code}, {raw_response.get('message', '未知错误')}"
                    print(error_msg)
                    return OutputDTO([], {"error": error_msg, "raw_response": raw_response})
                
                # 初始化矫正后的图像变量
                corrected_image = None
                
                # 转换为统一格式
                ocr_results = []
                
                # 处理API返回的结果
                if "data" in raw_response:
                    # 提取矫正后的图像（如果存在）
                    if "ImageInfo" in raw_response["data"] and raw_response["data"]["ImageInfo"]:
                        for img_info in raw_response["data"]["ImageInfo"]:
                            if "ImageBase64" in img_info and img_info["ImageBase64"]:
                                # 解码base64图像
                                try:
                                    # 去除可能的前缀
                                    base64_data = img_info["ImageBase64"]
                                    if base64_data.startswith("data:image/"):
                                        # 提取逗号后的base64数据
                                        base64_data = base64_data.split(",", 1)[1]
                                    
                                    # 解码base64数据为图像
                                    img_data = base64.b64decode(base64_data)
                                    nparr = np.frombuffer(img_data, np.uint8)
                                    corrected_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                                except Exception as e:
                                    print(f"解析夸克OCR返回的图像失败: {e}")
                    
                    # 处理OCR信息
                    if "OcrInfo" in raw_response["data"] and raw_response["data"]["OcrInfo"]:
                        for ocr_info in raw_response["data"]["OcrInfo"]:
                            # 处理Detail字段中的每个文本块
                            if "Detail" in ocr_info and isinstance(ocr_info["Detail"], list):
                                for detail in ocr_info["Detail"]:
                                    # 跳过不包含必要字段的标注
                                    if not detail.get("Position") or not detail.get("Value"):
                                        continue
                                        
                                    # 跳过base64编码的图片数据
                                    if isinstance(detail["Value"], str) and detail["Value"].startswith('/9j/'):
                                        continue
                                        
                                    # 检查内容类型
                                    valid_types = {'PrintedText', 'WrittenText', 'PrintedFormula', 'WrittenFormula'}
                                    content_type = detail.get("Type")
                                    if content_type not in valid_types:
                                        continue
                                    
                                    # 去除文本末尾的换行符
                                    transcription = detail["Value"].rstrip('\n')
                                    
                                    # 添加到结果列表
                                    ocr_results.append({
                                        "transcription": transcription,
                                        "points": detail["Position"],
                                        "difficult": False  # 默认设置为False
                                    })
                
                return OutputDTO(ocr_results, raw_response, corrected_image)
            else:
                error_msg = f"夸克OCR API HTTP请求失败: {response.status_code}"
                print(error_msg)
                return OutputDTO([], {"error": error_msg})
        
        except Exception as e:
            print(f"夸克OCR API调用失败: {e}")
            return OutputDTO([], {"error": str(e)})
    
    def _get_kuake_signature(self, client_id, client_secret, business, sign_method, sign_nonce, timestamp):
        """
        获取夸克OCR API签名
        
        Args:
            client_id: 客户端ID
            client_secret: 客户端密钥
            business: 业务类型
            sign_method: 签名方法
            sign_nonce: 随机字符串
            timestamp: 时间戳
            
        Returns:
            签名字符串
        """
        raw_str = f"{client_id}_{business}_{sign_method}_{sign_nonce}_{timestamp}_{client_secret}"
        utf8_bytes = raw_str.encode("utf-8")
        
        # 根据sign_method选择不同的摘要算法
        if sign_method.lower() == "sha256":
            digest = hashlib.sha256(utf8_bytes).hexdigest()
        elif sign_method.lower() == "sha1":
            digest = hashlib.sha1(utf8_bytes).hexdigest()
        elif sign_method.lower() == "md5":
            digest = hashlib.md5(utf8_bytes).hexdigest()
        elif sign_method.lower() in ["sha3-256", "sha3_256"]:
            digest = hashlib.sha3_256(utf8_bytes).hexdigest()
        else:
            raise ValueError("Unsupported sign method")
        
        # 将摘要转换为小写十六进制字符串
        sign = digest.lower()
        return sign
    
    def _recognize_with_tencent(self, input_dto: InputDTO) -> OutputDTO:
        """
        使用腾讯OCR进行识别
        
        Args:
            input_dto: 输入数据传输对象
            
        Returns:
            统一格式的OCR结果
        """
        # 获取配置
        tencent_config = self.config.get("tencent_ocr", {})
        secret_id = tencent_config.get("secret_id")
        secret_key = tencent_config.get("secret_key")
        region = tencent_config.get("region", "ap-guangzhou")
        is_fast = tencent_config.get("is_fast", False)
        
        # 创建客户端
        cred = credential.Credential(secret_id, secret_key)
        http_profile = HttpProfile()
        http_profile.endpoint = "ocr.tencentcloudapi.com"
        client_profile = ClientProfile()
        client_profile.httpProfile = http_profile
        client = ocr_client.OcrClient(cred, region, client_profile)
        
        try:
            # 读取图像文件
            with open(input_dto.get_image_path(), "rb") as f:
                image_base64 = base64.b64encode(f.read()).decode("utf8")
            
            # 构建请求
            req = models.GeneralBasicOCRRequest()
            req.ImageBase64 = image_base64
            
            # 调用API
            response = client.GeneralBasicOCR(req)
            
            # 解析响应
            if isinstance(response, str):
                raw_response = json.loads(response)
            else:
                raw_response = response.to_json_string()
                if isinstance(raw_response, str):
                    raw_response = json.loads(raw_response)
            
            # 转换为统一格式
            ocr_results = []
            if "TextDetections" in raw_response:
                for item in raw_response["TextDetections"]:
                    points = []
                    # 腾讯OCR返回的是Polygon坐标点
                    if "Polygon" in item and isinstance(item["Polygon"], list):
                        for point in item["Polygon"]:
                            if "X" in point and "Y" in point:
                                points.append([int(point["X"]), int(point["Y"])])
                    # 如果没有Polygon，尝试使用ItemPolygon构建四边形
                    elif "ItemPolygon" in item:
                        polygon = item["ItemPolygon"]
                        if "X" in polygon and "Y" in polygon and "Width" in polygon and "Height" in polygon:
                            x = int(polygon["X"])
                            y = int(polygon["Y"])
                            width = int(polygon["Width"])
                            height = int(polygon["Height"])
                            points = [
                                [x, y],
                                [x + width, y],
                                [x + width, y + height],
                                [x, y + height]
                            ]
                    
                    ocr_results.append({
                        "transcription": item.get("DetectedText", ""),
                        "points": points,
                        "difficult": False  # 腾讯OCR没有该字段，默认为False
                    })
            
            return OutputDTO(ocr_results, raw_response)
        
        except Exception as e:
            print(f"腾讯OCR API调用失败: {e}")
            return OutputDTO([], {"error": str(e)})
    
    def _recognize_with_textin(self, input_dto: InputDTO) -> OutputDTO:
        """
        使用Textin OCR进行识别
        
        Args:
            input_dto: 输入数据传输对象
            
        Returns:
            统一格式的OCR结果
        """
        # 获取配置
        textin_config = self.config.get("textin_ocr", {})
        app_id = textin_config.get("app_id")
        secret_code = textin_config.get("secret_code")
        
        try:
            # TextIn API URL
            url = 'https://api.textin.com/ai/service/v2/recognize/multipage'
            
            # 构建请求头
            headers = {
                'x-ti-app-id': app_id,
                'x-ti-secret-code': secret_code,
                'Content-Type': 'application/octet-stream'
            }
            
            # 构建请求体
            image_path = input_dto.get_image_path()
            if isinstance(image_path, Path):
                image_path = str(image_path)
            
            # 读取图像文件
            with open(image_path, 'rb') as fp:
                image_data = fp.read()
            
            # 发送请求
            response = requests.post(url, data=image_data, headers=headers)
            
            if response.status_code == 200:
                try:
                    raw_response = json.loads(response.text)
                except json.JSONDecodeError as e:
                    print(f"TextIn OCR API返回的JSON解析失败: {e}")
                    return OutputDTO([], {"error": f"JSON解析错误: {str(e)}", "raw_text": response.text})
                
                # 检查API返回状态 - 根据实际返回，通过code和message字段判断成功
                if raw_response.get("code") != 200 or raw_response.get("message") != "success":
                    error_msg = f"TextIn OCR API返回错误: 代码={raw_response.get('code', '未知')}, 消息={raw_response.get('message', '未知错误')}"
                    print(error_msg)
                    return OutputDTO([], {"error": error_msg, "raw_response": raw_response})
                
                # 转换为统一格式
                ocr_results = []
                
                # 根据实际返回数据结构解析结果
                if "result" in raw_response and "pages" in raw_response["result"] and raw_response["result"]["pages"]:
                    # 处理第一页的数据（多页数据采用第一页）
                    lines = raw_response["result"]["pages"][0].get("lines", [])
                    
                    for line in lines:
                        # 跳过不包含必要字段的标注或扫描全能王水印信息
                        if not line.get("position") or not line.get("text"):
                            continue
                            
                        # 扫描全能王水印过滤
                        text = line["text"]
                        if "扫描全能王" in text or "亿人都在用的扫描App" in text:
                            continue
                        
                        # 去除文本末尾的换行符
                        transcription = text.rstrip('\n')
                        
                        # 处理坐标点
                        position = line["position"]
                        points = []
                        
                        # TextIn返回的是一维数组格式的坐标点 [x1,y1,x2,y2,x3,y3,x4,y4]
                        if isinstance(position, list) and len(position) == 8:
                            # 每2个元素构成一个点的坐标
                            points = [[position[i], position[i+1]] for i in range(0, 8, 2)]
                        else:
                            print(f"TextIn OCR返回的坐标点格式不正确: {position}")
                            continue
                        
                        # 添加到结果列表
                        ocr_results.append({
                            "transcription": transcription,
                            "points": points,
                            "difficult": False  # 默认设置为False
                        })
                        
                return OutputDTO(ocr_results, raw_response)
            else:
                error_msg = f"TextIn OCR API调用失败: HTTP状态码 {response.status_code}"
                print(error_msg)
                try:
                    error_response = response.json()
                    error_msg += f", {error_response.get('message', '')}"
                except:
                    error_msg += f", {response.text}"
                return OutputDTO([], {"error": error_msg})
        
        except Exception as e:
            print(f"TextIn OCR API调用失败: {e}")
            return OutputDTO([], {"error": str(e)})

    def retry_ocr_api_call(self, func, input_dto, max_retries=3, delay=1):
        """
        简单的OCR API调用重试函数
        
        Args:
            func: 要调用的OCR API函数
            input_dto: 输入数据传输对象
            max_retries: 最大重试次数
            delay: 重试延迟时间(秒)
            
        Returns:
            OCR识别结果
        """
        for attempt in range(max_retries):
            try:
                result = func(input_dto)
                # 检查返回结果是否有效
                if result and result.ocr_results and len(result.ocr_results) > 0:
                    return result
                elif attempt < max_retries - 1:
                    print(f"OCR识别未返回有效结果，第{attempt+1}次尝试...")
                    time.sleep(delay)
                else:
                    # 最后一次尝试仍然没有结果，直接返回
                    print(f"OCR识别未能获取有效结果，达到最大尝试次数({max_retries})")
                    return result
            except (RequestException, Timeout, ConnectionError) as e:
                if attempt < max_retries - 1:
                    print(f"OCR API调用出现网络错误: {e}，第{attempt+1}次尝试...")
                    time.sleep(delay)
                else:
                    print(f"OCR API调用持续失败，达到最大尝试次数({max_retries})")
                    return OutputDTO([], {"error": f"网络错误: {str(e)}"})
        
        # 如果执行到这里，说明出现了意外情况
        return OutputDTO([], {"error": "未知错误，重试失败"})
