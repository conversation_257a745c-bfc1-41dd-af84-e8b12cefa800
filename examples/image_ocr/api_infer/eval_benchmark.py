#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/3/24 15:08
# <AUTHOR> <EMAIL>
# @FileName: eval_benchmark

"""
OCR模型性能评估工具

本脚本旨在量化评估自研OCR模型与商业API及开源基线模型之间的识别准确率差异。
评估方法：
1. 首先按照自然阅读顺序（自左至右, 自上而下）对检测框进行重排序
2. 将重排序后的各检测框中的文本按顺序拼接，形成完整的预测文本序列
3. 与标准答案（Ground Truth）文本进行对比
4. 采用Levenshtein编辑距离计算相似度，评估模型识别准确率

该方法能够全面评估OCR系统在保持文档整体结构和内容完整性方面的能力。

提供输入目录：包含各方法的结果
提供输出目录：可保存可视化结果，指标记录等
"""

from typing import Dict, List, Tuple, Optional, Set, Union, Any

import os
import json
import argparse
from pathlib import Path

import cv2
import numpy as np
from PIL import Image
import re
from Levenshtein import distance as lev_distance

from modules.utils.log import LOGGER


class CustomParallelDetMetric:
    """自定义并行检测评估指标，支持处理图片尺寸不匹配问题

    依赖: 无
    被依赖: 无
    """

    def __init__(self, metric_options: Dict[str, Any]):
        """初始化评估指标

        Args:
            metric_options: 评估参数选项
        """
        # 初始化代码占位
        pass

    def __call__(self, gt_info_list: List[Dict], det_info_list: List[Dict]) -> Dict[str, float]:
        """评估函数

        Args:
            gt_info_list: ground truth信息列表
            det_info_list: 检测结果信息列表

        Returns:
            评估指标字典

        依赖: 无
        被依赖: 无
        """
        # 评估逻辑代码占位
        pass


def get_image_size(image_path: str) -> Tuple[int, int]:
    """获取图片尺寸

    Args:
        image_path: 图片路径

    Returns:
        图片的宽度和高度 (width, height)

    依赖: 无
    被依赖: prepare_eval_data
    """
    if not os.path.exists(image_path):
        return -1, -1
    img = cv2.imread(image_path)
    if img is None:
        return -1, -1
    return img.shape[1], img.shape[0]  # width, height


def get_box_center(text_box: Dict) -> Tuple[float, float]:
    """获取文本框的宽高，并根据图像尺寸计算相对阈值

        Args:
            text_box: 单个文本框字典：{"transcription": 文本, "points": 检测框（[[x, y]...]）}

        Returns:
            文本框的中心点 center_x, center_y

        依赖: 无
        被依赖: sort_boxes_by_reading_order
        """

    points = text_box['points']
    min_x = min(p[0] for p in points)
    max_x = max(p[0] for p in points)
    min_y = min(p[1] for p in points)
    max_y = max(p[1] for p in points)
    center_x = (min_x + max_x) / 2
    center_y = (min_y + max_y) / 2

    return center_x, center_y


def adjust_points_by_ratio(text_boxes: List[Dict], width_ratio: float, height_ratio: float) -> List[Dict]:
    """根据缩放比例调整坐标点

    Args:
        text_boxes: 文本框字典列表。字典格式：{"transcription": 文本, "points": 检测框（[[x, y]...]）}
        width_ratio: 宽度缩放比例
        height_ratio: 高度缩放比例

    Returns:
        调整后的坐标点列表

    依赖: 无
    被依赖: prepare_eval_data
    """

    for box_idx in range(len(text_boxes)):
        box_point = text_boxes[box_idx]["points"]
        box_point = [[pts[0] * width_ratio, pts[1] * height_ratio] for pts in box_point]
        text_boxes[box_idx]["points"] = box_point

    return text_boxes


def find_image_path(image_name: str, base_dirs: List[str]) -> Optional[str]:
    """智能查找图片路径

    Args:
        image_name: 图片名称
        base_dirs: 基础目录列表

    Returns:
        找到的图片完整路径，如果未找到则返回None

    依赖: 无
    被依赖: prepare_eval_data, evaluate_ocr_performance
    """
    # 查找图片路径代码占位
    pass


def parse_gt_data(gt_path: str) -> List[Dict]:
    """解析ground truth数据

    Args:
        gt_path: ground truth文件路径

    Returns:
        ground truth数据列表

    依赖: 无
    被依赖: prepare_eval_data
    """

    gt_results = {}
    with open(os.path.join(gt_path, 'Label.txt'), 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split('\t')
            if len(parts) != 2:
                continue
            image_path = parts[0]
            image_path = "bench/" + image_path.replace("_final", "")
            boxes = json.loads(parts[1])
            gt_results[image_path] = boxes

    return [gt_results]


def parse_local_pred_data(pred_path: str) -> List[Dict]:
    """解析 OCR 本地客户端的预测数据

        Args:
            pred_path: 预测结果文件路径

        Returns:
            预测数据列表

        依赖: 无
        被依赖: prepare_eval_data
        """

    pred_result_list = []
    for algo in os.listdir(pred_path):
        pred_results = {
            'algo': algo,
            'pred': {}
        }

        if not os.path.isdir(os.path.join(pred_path, algo)):
            continue

        label_path = os.path.join(pred_path, algo, 'all_results.txt')
        if not os.path.exists(label_path):
            continue
        with open(label_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                parts = line.split('\t')
                if len(parts) != 2:
                    continue
                image_path, boxes_str = parts
                image_path = image_path.replace("\\", "/")

                boxes = json.loads(boxes_str)
                norm_boxes = []
                for box in boxes:
                    norm_dict = {
                        "transcription": box["text"],
                        "points": box["points"]
                    }
                    norm_boxes.append(norm_dict)
                pred_results['pred'][image_path] = norm_boxes

        pred_result_list.append(pred_results)

    return pred_result_list


def parse_dev_pred_data(pred_path: str) -> List[Dict]:
    """解析自研 OCR pipeline 预测数据

        Args:
            pred_path: 预测结果文件路径

        Returns:
            预测数据列表

        依赖: 无
        被依赖: prepare_eval_data
        """

    pred_result_list = []
    for algo in os.listdir(pred_path):
        pred_results = {
            'algo': algo,
            'pred': {}
        }

        if not os.path.isdir(os.path.join(pred_path, algo)):
            continue

        label_path = os.path.join(pred_path, algo, 'predict_res.txt')
        if not os.path.exists(label_path):
            continue
        with open(label_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                parts = line.split('\t')
                if len(parts) != 2:
                    continue
                image_path, boxes_str = parts

                boxes = json.loads(boxes_str)
                pred_results['pred'][image_path] = boxes

        pred_result_list.append(pred_results)

    return pred_result_list


def parse_third_party_pred_data(pred_path: str) -> List[Dict]:
    """解析第三方预测数据

    Args:
        pred_path: 预测结果文件路径

    Returns:
        预测数据列表

    依赖: 无
    被依赖: prepare_eval_data
    """

    # 由于夸克的bounding box是基于旋转后的结果，因此针对夸克评测时以下例子需要排除
    invalid_cases = [
        '16d2106419f96df35642644006ba7cb1_page003',
        '16d2106419f96df35642644006ba7cb1_page004',
        'e7e03d3b97cb8406028d964a1928d1ae_page001',
        '64aae53ece466f6a6825f677b8c6ff68_page001',
        'a1126c1b66b459ba629445d3cbad510c_page001',
        'code_page002.png',
        'dee0ddded1c64a9a51a490b98ddeec74_page003',
        'dee0ddded1c64a9a51a490b98ddeec74_page004',
        'dee0ddded1c64a9a51a490b98ddeec74_page005',
        'fb4a2246114f74b5b126324edeb5c056_page002',
        '1a0c17849a5519054529cd4302ba24e7_page001',
        '472ddabc189615fb92b3a7171aeac8fc_page001',
        '859321af712b875a9f21ded24455ea9c_page016'
    ]

    pred_result_list = []
    for algo in os.listdir(pred_path):
        pred_results = {
            'algo': algo,
            'pred': {}
        }

        if not os.path.isdir(os.path.join(pred_path, algo)):
            continue

        label_path = os.path.join(pred_path, algo, 'original', 'Label.txt')
        if not os.path.exists(label_path):
            continue
        with open(label_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                parts = line.split('\t')
                if len(parts) != 2:
                    continue
                image_path, boxes_str = parts

                if algo == 'kuake':
                    invalid = False
                    for invalid_case in invalid_cases:
                        if invalid_case in image_path:
                            invalid = True
                            break
                    if invalid:
                        continue

                boxes = json.loads(boxes_str)
                pred_results['pred'][image_path] = boxes

        pred_result_list.append(pred_results)

    return pred_result_list


def prepare_eval_data(gt_dir: str, pred_dir: str, pred_type: str) -> Tuple[List[Dict], List[Dict]]:
    """准备评估数据

    Args:
        gt_dir: ground truth目录
        pred_dir: 预测结果目录
        pred_type: 预测结果类型

    Returns:
        ground truth列表和预测结果列表

    依赖: parse_gt_data, parse_local_pred_data, parse_dev_pred_data,  parse_third_party_pred_data
        find_image_path, get_image_size, adjust_points_by_ratio
    被依赖: evaluate_ocr_performance
    """

    # 先获取gt和预测结果的原始数据
    gt_res = parse_gt_data(gt_dir)
    if pred_type == 'local':
        pred_res = parse_local_pred_data(pred_dir)
    elif pred_type == 'dev':
        pred_res = parse_dev_pred_data(pred_dir)
    else:
        pred_res = parse_third_party_pred_data(pred_dir)

    # TODO: 当前仅考虑gt为单个数据集
    # 以gt图片为基准，调整相同图像的比例
    for relative_image_path in gt_res[0]:

        gt_image_path = os.path.join(gt_dir, relative_image_path)
        gt_width, gt_height = get_image_size(gt_image_path)

        for algo_pred_idx in range(len(pred_res)):
            algo_pred_dict = pred_res[algo_pred_idx]
            algo_name = algo_pred_dict['algo']
            if relative_image_path in algo_pred_dict['pred']:

                if pred_type == 'third_party':
                    if algo_name == 'kuake':
                        pred_image_path = os.path.join(pred_dir, algo_name, 'visualized', relative_image_path)
                    else:
                        pred_image_path = os.path.join(pred_dir, algo_name, 'original', relative_image_path)
                    pred_width, pred_height = get_image_size(pred_image_path)
                elif pred_type == 'local':
                    pred_image_path = os.path.join(pred_dir, algo_name, 'visualization', relative_image_path)
                    if not os.path.exists(pred_image_path):
                        pred_image_path = os.path.join(pred_dir, algo_name, 'visualization', relative_image_path.split('.png')[0] + "_wechatocr" + ".png")
                    pred_width, pred_height = get_image_size(pred_image_path)
                else:
                    pred_width = gt_width
                    pred_height = gt_height

                # 计算缩放比例
                w_ratio = gt_width / pred_width
                h_ratio = gt_height / pred_height

                pred_res[algo_pred_idx]['pred'][relative_image_path] = adjust_points_by_ratio(
                    algo_pred_dict['pred'][relative_image_path], w_ratio, h_ratio)

    return gt_res, pred_res


def normalize_text_for_comparison(text: str) -> str:
    """
    标准化文本用于宽松比较：
    1. 保留英文字母间的空格
    2. 去除其他空格
    3. 将全角括号、冒号、问号、感叹号转换为半角

    Args:
        text: 输入文本

    Returns:
        str: 标准化后的文本
    """
    if not text:
        return ""

    # 将连续的英文字母及其间的空格提取出来
    eng_words = re.findall(r'[a-zA-Z]+(?:\s+[a-zA-Z]+)*', text)
    eng_dict = {}
    for i, word in enumerate(eng_words):
        placeholder = f"__ENG_{i}__"
        text = text.replace(word, placeholder)
        eng_dict[placeholder] = word

    # 去除所有空格
    text = text.replace(" ", "")

    # 全角转半角字符映射
    full_to_half = {
        "（": "(",
        "）": ")",
        "：": ":",
        "？": "?",
        "！": "!"
    }

    # 转换全角为半角
    for full, half in full_to_half.items():
        text = text.replace(full, half)

    # 还原英文单词
    for placeholder, word in eng_dict.items():
        text = text.replace(placeholder, word)

    return text


def calculate_text_similarity(pred_text: str, gt_text: str) -> Tuple[int, float, int, float]:
    """计算文本相似度

    Args:
        pred_text: 预测文本
        gt_text: ground truth文本

    Returns:
        严格编辑距离，严格字准确率，宽松编辑距离，宽松字准确率

    依赖: 无
    被依赖: evaluate_ocr_performance
    """

    # 计算严格编辑距离
    strict_distance = lev_distance(pred_text, gt_text)
    # 使用真实文本长度归一化
    strict_distance_ratio = strict_distance / len(gt_text) if gt_text else 1
    strict_distance_ratio = 1 - strict_distance_ratio

    # 计算宽松编辑距离
    norm_gt = normalize_text_for_comparison(gt_text)
    norm_pred = normalize_text_for_comparison(pred_text)

    if norm_gt == norm_pred:
        relaxed_distance = 0
    else:
        relaxed_distance = lev_distance(norm_gt, norm_pred)
    # 使用归一化后的真实文本长度归一化
    relaxed_distance_ratio = relaxed_distance / len(norm_gt) if norm_gt else 1
    relaxed_distance_ratio = 1- relaxed_distance_ratio

    return strict_distance, strict_distance_ratio, relaxed_distance, relaxed_distance_ratio


def sort_boxes_by_reading_order(text_boxes: List[Dict], x_threshold, y_threshold) -> Tuple[List[Dict], str]:
    """按照自然阅读顺序（从左到右，从上到下）对检测框进行排序

    Args:
        text_boxes: 文本框字典列表。字典格式：{"transcription": 文本, "points": 检测框（[[x, y]...]）}
        x_threshold: 按列分组的阈值
        y_threshold: 按行分组的阈值

    Returns:
        排序后的文本框字典列表，以及排序合并后的文本

    依赖: get_box_center
    被依赖: evaluate_ocr_performance
    """

    if len(text_boxes) == 0:
        return [], ""

    # 按中心点y坐标进行初步排序
    sorted_boxes = sorted(text_boxes, key=lambda x: get_box_center(x)[1])

    # 按y坐标分组
    # 分组的目的是为了保证在一定y范围内的
    lines = [[sorted_boxes[0]]]
    last_center_y = get_box_center(sorted_boxes[0])[1]
    for box in sorted_boxes[1:]:
        current_center_y = get_box_center(box)[1]
        if abs(current_center_y - last_center_y) <= y_threshold:
            lines[-1].append(box)
        else:
            lines.append([box])
            last_center_y = current_center_y

    # 对每一行内的文本框按最小x和最小y的和排序
    reading_order_boxes = []
    result_text = ""
    for line in lines:
        sorted_line = sorted(line, key=lambda x: min(p[0] for p in x['points']) + 2 * min(p[1] for p in x['points']))
        reading_order_boxes.extend(sorted_line)
        line_text = "".join(box['transcription'] for box in sorted_line)
        result_text += line_text + "\n"

    return reading_order_boxes, result_text


def group_boxes_by_line(text_boxes: List[Dict], x_threshold: float, y_threshold: float) -> List[List[Dict]]:
    """按y坐标对框进行分组（同一行）

    Args:
        text_boxes: 文本框列表
        x_threshold: x方向阈值
        y_threshold: y方向阈值

    Returns:
        分组后的文本框列表
    """
    if not text_boxes:
        return []

    # 获取每个框的中心点
    boxes_with_centers = []
    for box in text_boxes:
        center_x, center_y = get_box_center(box)
        boxes_with_centers.append((box, center_x, center_y))

    # 按y坐标排序
    sorted_boxes = sorted(boxes_with_centers, key=lambda x: x[2])

    # 分组
    lines = [[sorted_boxes[0][0]]]
    last_y = sorted_boxes[0][2]

    for box, center_x, center_y in sorted_boxes[1:]:
        if abs(center_y - last_y) <= y_threshold:
            lines[-1].append(box)
        else:
            lines.append([box])
            last_y = center_y

    # 对每一行内的文本框按x坐标排序
    for line in lines:
        line.sort(key=lambda box: get_box_center(box)[0])

    return lines


def visualize_results(
    image_path: str,
    gt_boxes: List[List[float]],
    det_boxes: List[List[float]],
    output_path: str
) -> None:
    """可视化评估结果

    Args:
        image_path: 图片路径
        gt_boxes: ground truth检测框
        det_boxes: 预测检测框
        output_path: 输出路径

    依赖: 无
    被依赖: evaluate_ocr_performance
    """
    # 可视化代码占位
    pass


def longest_common_substring(s1: str, s2: str) -> int:
    """计算两个字符串的最长公共子串长度，先进行宽松转换

    Args:
        s1: 第一个字符串
        s2: 第二个字符串

    Returns:
        最长公共子串长度
    """
    # 先进行宽松转换
    s1 = normalize_text_for_comparison(s1)
    s2 = normalize_text_for_comparison(s2)

    if not s1 or not s2:
        return 0

    m = len(s1)
    n = len(s2)
    dp = [[0] * (n + 1) for _ in range(m + 1)]
    max_len = 0

    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if s1[i - 1] == s2[j - 1]:
                dp[i][j] = dp[i - 1][j - 1] + 1
                max_len = max(max_len, dp[i][j])

    return max_len


def find_best_match(text: str, texts: List[str], start_idx: int, used_indices: Set[int], min_common_len: int = 2) -> Tuple[int, int, int]:
    """在文本列表中找到最佳匹配的文本

    Args:
        text: 要匹配的文本
        texts: 文本列表
        start_idx: 开始搜索的索引
        used_indices: 已使用的文本索引集合
        min_common_len: 最小公共子串长度阈值

    Returns:
        (最佳匹配索引, 最佳编辑距离, 最佳公共子串长度)
    """
    best_idx = -1
    best_common_len = 0
    best_distance = float('inf')

    # 向后搜索
    for i in range(min(start_idx, len(texts)), len(texts)):
        if i in used_indices:  # 跳过已使用的文本
            continue
        # 先计算编辑距离
        distance = lev_distance(text, texts[i])
        if distance <= 2:  # 编辑距离小于等于2直接匹配
            best_idx = i
            best_distance = distance
            best_common_len = min(len(text), len(texts[i]))  # 使用较短文本长度作为公共长度
            if distance == 0:  # 找到完全匹配就立即返回
                break
        else:  # 编辑距离大于2时计算最长公共子串
            common_len = longest_common_substring(text, texts[i])
            if common_len >= min_common_len:
                # 如果有多个满足条件的匹配，选择最长公共子串更长的，或者编辑距离更小的
                if common_len > best_common_len or (common_len == best_common_len and distance < best_distance):
                    best_common_len = common_len
                    best_distance = distance
                    best_idx = i

    # 如果向后没找到，向前搜索
    if best_idx == -1:
        for i in range(min(start_idx - 1, len(texts) - 1), -1, -1):
            if i in used_indices:  # 跳过已使用的文本
                continue
            # 先计算编辑距离
            distance = lev_distance(text, texts[i])
            if distance <= 2:  # 编辑距离小于等于2直接匹配
                best_idx = i
                best_distance = distance
                best_common_len = min(len(text), len(texts[i]))  # 使用较短文本长度作为公共长度
                if distance == 0:  # 找到完全匹配就立即返回
                    break
            else:  # 编辑距离大于2时计算最长公共子串
                common_len = longest_common_substring(text, texts[i])
                if common_len >= min_common_len:
                    # 如果有多个满足条件的匹配，选择最长公共子串更长的，或者编辑距离更小的
                    if common_len > best_common_len or (common_len == best_common_len and distance < best_distance):
                        best_common_len = common_len
                        best_distance = distance
                        best_idx = i

    return best_idx, best_distance, best_common_len


def evaluate_ocr_performance(gt_dir: str, pred_dir: str, output_dir: str, pred_type: str, page_acc_error) -> Dict[str, float]:
    """评估第三方OCR性能

    Args:
        gt_dir: ground truth目录
        pred_dir: 预测结果目录
        output_dir: 输出目录
        pred_type: 预测结果类型
        page_acc_error: 页准确率允许误差

    Returns:
        评估指标字典

    依赖: prepare_eval_data, find_image_path, sort_boxes_by_reading_order,
         calculate_text_similarity, visualize_results
    被依赖: 主函数
    """

    print(f"正在准备评估数据")
    gt_res, pred_res = prepare_eval_data(gt_dir, pred_dir, pred_type)

    metric_res = {}
    for algo_pred_dict in pred_res:
        print(f"正在评估： {algo_pred_dict['algo']} ")
        compare_detail_save_path = os.path.join(output_dir, algo_pred_dict['algo']+'_compare_details.txt')
        total_images = 0
        strict_normalized_distance_sum = 0
        relaxed_normalized_distance_sum = 0
        strict_page_correct_num = 0
        relaxed_page_correct_num = 0

        # 按行对比的指标
        strict_page_line_normalized_distance_sum = 0
        relaxed_page_line_normalized_distance_sum = 0

        with open(compare_detail_save_path, 'w', encoding='utf-8') as out_f:

            for relative_image_path in algo_pred_dict['pred']:
                if relative_image_path in gt_res[0]:
                    total_images += 1

                    # 计算得到原始图像的阈值
                    gt_image_path = os.path.join(gt_dir, relative_image_path)
                    gt_width, gt_height = get_image_size(gt_image_path)
                    x_threshold = gt_width * 0.015
                    y_threshold = gt_height * 0.015

                    gt_text_boxes = gt_res[0][relative_image_path]
                    pred_text_boxes = algo_pred_dict['pred'][relative_image_path]

                    # 获取按阅读顺序排序的文本（整页）
                    gt_reading_order_text_boxes, gt_text = sort_boxes_by_reading_order(gt_text_boxes, x_threshold, y_threshold)
                    pred_reading_order_text_boxes, pred_text = sort_boxes_by_reading_order(pred_text_boxes, x_threshold, y_threshold)

                    # 计算整页文本相似度
                    strict_distance, strict_normalized_distance, relaxed_distance, relaxed_normalized_distance = calculate_text_similarity(pred_text, gt_text)

                    strict_normalized_distance_sum += strict_normalized_distance
                    relaxed_normalized_distance_sum += relaxed_normalized_distance

                    if strict_normalized_distance + page_acc_error > 1.:
                        strict_page_correct_num += 1
                    if relaxed_normalized_distance + page_acc_error > 1.:
                        relaxed_page_correct_num += 1

                    # 按行分组对比
                    gt_lines = group_boxes_by_line(gt_text_boxes, x_threshold, y_threshold)
                    pred_lines = group_boxes_by_line(pred_text_boxes, x_threshold, y_threshold)

                    # 获取每行的文本
                    gt_line_texts = [" ".join(box['transcription'] for box in line) for line in gt_lines]
                    pred_line_texts = [" ".join(box['transcription'] for box in line) for line in pred_lines]

                    # 记录已匹配的预测行索引
                    used_pred_indices = set()

                    # 写入对比结果
                    out_f.write(f"{relative_image_path}\n")
                    out_f.write("-" * 20 + "\n")
                    out_f.write("整页对比结果：\n")
                    out_f.write("真实文本：\n")
                    out_f.write(gt_text + "\n\n")
                    out_f.write("预测文本：\n")
                    out_f.write(pred_text + "\n\n")
                    out_f.write(f"严格编辑距离：{strict_distance} (严格字准确率: {strict_normalized_distance:.4f})\n")
                    out_f.write(f"宽松编辑距离：{relaxed_distance} (宽松字准确率: {relaxed_normalized_distance:.4f})\n")
                    
                    out_f.write("\n按行对比结果：\n")

                    # 对每个真实行，寻找最佳匹配的预测行
                    total_lines = 0
                    strict_line_normalized_distance_sum = 0
                    relaxed_line_normalized_distance_sum = 0
                    for gt_idx, gt_line_text in enumerate(gt_line_texts):
                        total_lines += 1
                        best_pred_idx, best_distance, best_common_len = find_best_match(
                            gt_line_text, pred_line_texts, gt_idx, used_pred_indices)

                        if best_pred_idx != -1:
                            used_pred_indices.add(best_pred_idx)
                            pred_line_text = pred_line_texts[best_pred_idx]

                            # 计算行文本相似度
                            line_strict_distance, line_strict_normalized_distance, line_relaxed_distance, line_relaxed_normalized_distance = calculate_text_similarity(pred_line_text, gt_line_text)

                            strict_line_normalized_distance_sum += line_strict_normalized_distance
                            relaxed_line_normalized_distance_sum += line_relaxed_normalized_distance

                            # 写入行对比结果
                            out_f.write(f"{line_strict_distance}\t{line_relaxed_distance}\t{gt_line_text}\t{pred_line_text}\n")
                        else:
                            # 将未匹配的行视为完全错误
                            strict_line_normalized_distance_sum += 0
                            relaxed_line_normalized_distance_sum += 0
                            # 未找到匹配的预测行
                            out_f.write(f"0\t0\t{gt_line_text}\t{pred_line_text}\n")
                    strict_page_line_normalized_distance_sum += strict_line_normalized_distance_sum / total_lines
                    relaxed_page_line_normalized_distance_sum += relaxed_line_normalized_distance_sum / total_lines

                    # 检查未匹配的预测行（过检）
                    for pred_idx, pred_line_text in enumerate(pred_line_texts):
                        if pred_idx not in used_pred_indices:
                            out_f.write(f"-1\t-1\t \t{pred_line_text}\n")

                    out_f.write("\n" + "=" * 50 + "\n\n")

        if total_images < 1:
            metric_res[algo_pred_dict['algo']] = {
                'strict_char_acc': 0,
                'relaxed_char_acc': 0,
                'strict_line_char_acc': 0,
                'relaxed_line_char_acc': 0,
                'strict_page_acc': 0,
                'relaxed_page_acc': 0
            }
        else:
            # 计算总的评估指标
            strict_avg_distance_ratio = strict_normalized_distance_sum / total_images
            relaxed_avg_distance_ratio = relaxed_normalized_distance_sum / total_images
            strict_page_acc = strict_page_correct_num / total_images
            relaxed_page_acc = relaxed_page_correct_num / total_images

            # 计算行级别的评估指标
            strict_line_avg_distance_ratio = strict_page_line_normalized_distance_sum / total_images
            relaxed_line_avg_distance_ratio = relaxed_page_line_normalized_distance_sum / total_images

            metric_res[algo_pred_dict['algo']] = {
                'strict_char_acc': strict_avg_distance_ratio,
                'relaxed_char_acc': relaxed_avg_distance_ratio,
                'strict_line_char_acc': strict_line_avg_distance_ratio,
                'relaxed_line_char_acc': relaxed_line_avg_distance_ratio,
                'strict_page_acc': strict_page_acc,
                'relaxed_page_acc': relaxed_page_acc
            }

    return metric_res


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='OCR模型评估工具')
    parser.add_argument('--gt_dir', type=str, required=True, help='ground truth目录路径')
    parser.add_argument('--pred_dir', type=str, required=True, help='预测结果目录路径')
    parser.add_argument('--output_dir', type=str, default='./eval_results', help='输出目录路径')
    parser.add_argument('--pred_type', type=str, default='local', help='预测结果类型，客户端结果 local，内部自研结果 dev，第三方结果 third_party')
    parser.add_argument('--page_acc_error', type=float, default=0.01, help='页准确率误差')
    return parser.parse_args()


if __name__ == '__main__':
    """主函数"""
    args = parse_args()

    # 设置日志
    LOGGER.info(f"开始评估OCR模型性能")

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)


    # 评估OCR性能
    metrics = evaluate_ocr_performance(
        gt_dir=args.gt_dir,
        pred_dir=args.pred_dir,
        output_dir=args.output_dir,
        pred_type=args.pred_type,
        page_acc_error=args.page_acc_error,
    )

    # 保存评估结果
    with open(os.path.join(args.output_dir, 'all_metrics.json'), 'w', encoding='utf-8') as f:
        json.dump(metrics, f, ensure_ascii=False, indent=4)


    # 输出评估结果
    LOGGER.info(f"OCR评估结果: {metrics}")
