#!/usr/bin/env python
# -*- coding: utf-8 -*-
# Time: 2024-08-15
# Author: <EMAIL>
# FileName: compress_subdirs.py

import os
import tarfile
import argparse
import logging
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed


def setup_logger():
    """设置日志记录器"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    return logging.getLogger(__name__)


def compress_directory(src_dir, output_file, logger):
    """
    将指定目录压缩为tar.gz文件
    
    Args:
        src_dir: 源目录路径
        output_file: 输出文件路径
        logger: 日志记录器
    
    Returns:
        bool: 压缩是否成功
    """
    try:
        with tarfile.open(output_file, "w:gz") as tar:
            base_dir = os.path.basename(src_dir)
            tar.add(src_dir, arcname=base_dir)
        logger.info(f"成功压缩 {src_dir} 到 {output_file}")
        return True
    except Exception as e:
        logger.error(f"压缩 {src_dir} 失败: {str(e)}")
        return False


def compress_specific_directories(dir_list, output_dir, max_workers=4):
    """
    压缩指定的目录列表
    
    Args:
        dir_list: 要压缩的目录列表
        output_dir: 输出目录路径
        max_workers: 最大工作线程数
    
    Returns:
        tuple: (成功数量, 失败数量)
    """
    logger = setup_logger()
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    success_count = 0
    failure_count = 0
    
    # 过滤不存在的目录
    valid_dirs = []
    for dir_path in dir_list:
        if os.path.isdir(dir_path):
            valid_dirs.append(dir_path)
        else:
            logger.warning(f"目录不存在，已跳过: {dir_path}")
            failure_count += 1
    
    if not valid_dirs:
        logger.warning("没有找到有效的目录")
        return 0, failure_count
    
    logger.info(f"开始压缩 {len(valid_dirs)} 个目录")
    
    # 使用线程池并行压缩
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {}
        for dir_path in valid_dirs:
            dir_name = os.path.basename(dir_path)
            output_file = os.path.join(output_dir, f"{dir_name}.tar.gz")
            future = executor.submit(compress_directory, dir_path, output_file, logger)
            futures[future] = dir_path
        
        # 使用tqdm显示进度
        with tqdm(total=len(futures), desc="压缩进度") as pbar:
            for future in as_completed(futures):
                dir_path = futures[future]
                try:
                    success = future.result()
                    if success:
                        success_count += 1
                    else:
                        failure_count += 1
                except Exception as e:
                    logger.error(f"处理 {dir_path} 时发生异常: {str(e)}")
                    failure_count += 1
                finally:
                    pbar.update(1)
    
    logger.info(f"压缩完成，成功: {success_count}，失败: {failure_count}")
    return success_count, failure_count


def compress_subdirectories(parent_dir, output_dir=None, max_workers=4, level=1, current_level=0):
    """
    压缩指定目录下的所有子目录
    
    Args:
        parent_dir: 父目录路径
        output_dir: 输出目录路径，默认为父目录
        max_workers: 最大工作线程数
        level: 要压缩的目录层级，1表示直接子目录，2表示子目录的子目录，以此类推
        current_level: 当前处理的层级
    
    Returns:
        tuple: (成功数量, 失败数量)
    """
    logger = setup_logger()
    
    if not os.path.isdir(parent_dir):
        logger.error(f"指定的父目录不存在: {parent_dir}")
        return 0, 0
    
    # 如果未指定输出目录，则使用父目录
    if output_dir is None:
        output_dir = parent_dir
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    success_count = 0
    failure_count = 0
    
    # 如果当前层级小于目标层级，则递归处理子目录
    if current_level < level - 1:
        for item in os.listdir(parent_dir):
            item_path = os.path.join(parent_dir, item)
            if os.path.isdir(item_path):
                s, f = compress_subdirectories(
                    item_path, 
                    output_dir, 
                    max_workers, 
                    level, 
                    current_level + 1
                )
                success_count += s
                failure_count += f
        return success_count, failure_count
    
    # 获取所有子目录
    subdirs = []
    for item in os.listdir(parent_dir):
        item_path = os.path.join(parent_dir, item)
        if os.path.isdir(item_path):
            subdirs.append(item_path)
    
    if not subdirs:
        logger.warning(f"在 {parent_dir} 中没有找到子目录")
        return 0, 0
    
    logger.info(f"开始压缩 {parent_dir} 下的 {len(subdirs)} 个子目录")
    
    # 使用线程池并行压缩
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {}
        for subdir in subdirs:
            subdir_name = os.path.basename(subdir)
            output_file = os.path.join(output_dir, f"{subdir_name}.tar.gz")
            future = executor.submit(compress_directory, subdir, output_file, logger)
            futures[future] = subdir
        
        # 使用tqdm显示进度
        with tqdm(total=len(futures), desc="压缩进度") as pbar:
            for future in as_completed(futures):
                subdir = futures[future]
                try:
                    success = future.result()
                    if success:
                        success_count += 1
                    else:
                        failure_count += 1
                except Exception as e:
                    logger.error(f"处理 {subdir} 时发生异常: {str(e)}")
                    failure_count += 1
                finally:
                    pbar.update(1)
    
    logger.info(f"压缩完成，成功: {success_count}，失败: {failure_count}")
    return success_count, failure_count


def main():
    parser = argparse.ArgumentParser(description="将目录压缩为tar.gz文件")
    subparsers = parser.add_subparsers(dest="command", help="子命令")
    
    # 按层级压缩子目录的子命令
    level_parser = subparsers.add_parser("level", help="按层级压缩子目录")
    level_parser.add_argument("parent_dir", help="要处理的父目录路径")
    level_parser.add_argument("-o", "--output-dir", help="输出目录路径，默认为父目录")
    level_parser.add_argument("-w", "--workers", type=int, default=4, help="最大工作线程数，默认为4")
    level_parser.add_argument("-l", "--level", type=int, default=1, 
                        help="要压缩的目录层级，1表示直接子目录，2表示子目录的子目录，以此类推，默认为1")
    
    # 压缩指定目录的子命令
    specific_parser = subparsers.add_parser("specific", help="压缩指定的目录")
    specific_parser.add_argument("dirs", nargs="+", help="要压缩的目录路径列表")
    specific_parser.add_argument("-o", "--output-dir", required=True, help="输出目录路径")
    specific_parser.add_argument("-w", "--workers", type=int, default=4, help="最大工作线程数，默认为4")
    
    # 压缩单个目录的子命令
    single_parser = subparsers.add_parser("single", help="压缩单个目录")
    single_parser.add_argument("dir", help="要压缩的目录路径")
    single_parser.add_argument("-o", "--output-file", required=True, help="输出文件路径（包括.tar.gz后缀）")
    
    args = parser.parse_args()
    
    if args.command == "level":
        success, failure = compress_subdirectories(
            args.parent_dir, 
            args.output_dir, 
            args.workers, 
            args.level
        )
        print(f"总结: 成功压缩 {success} 个目录，失败 {failure} 个目录")
    
    elif args.command == "specific":
        success, failure = compress_specific_directories(
            args.dirs,
            args.output_dir,
            args.workers
        )
        print(f"总结: 成功压缩 {success} 个目录，失败 {failure} 个目录")
    
    elif args.command == "single":
        logger = setup_logger()
        success = compress_directory(args.dir, args.output_file, logger)
        if success:
            print(f"成功压缩目录 {args.dir} 到 {args.output_file}")
        else:
            print(f"压缩目录 {args.dir} 失败")
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main() 