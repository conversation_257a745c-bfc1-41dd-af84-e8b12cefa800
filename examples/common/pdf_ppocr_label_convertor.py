#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/9 21:28
# <AUTHOR> <EMAIL>
# @FileName: pdf_ppocr_label_convertor

import os
import json
import pdf2image
import pdfplumber

from PIL import Image
from tqdm import tqdm
from pathlib import Path


def convert_pdf_to_ppocr_format(pdf_path, output_dir):
    # 创建必要的目录结构
    images_dir = os.path.join(output_dir, "images")
    os.makedirs(images_dir, exist_ok=True)
    
    # 准备Label.txt文件
    label_file = os.path.join(images_dir, "Label.txt")
    
    # 1. 将PDF转换为图片
    print(f"{Path(pdf_path).name}: 正在转换PDF为图片...")
    images = pdf2image.convert_from_path(pdf_path)

    # 2. 使用pdfplumber提取文本位置信息
    with pdfplumber.open(pdf_path) as pdf, open(label_file, 'w', encoding='utf-8') as label_f:
        print(f"正在处理 {len(images)} 页...")
        for page_num, (page, image) in enumerate(tqdm(zip(pdf.pages, images), total=len(images), desc="处理进度")):
            # 获取页面中的文字和位置信息
            words = page.extract_words()
            
            # 保存图片
            image_filename = f"page_{page_num}.jpg"
            image_path = os.path.join(images_dir, image_filename)
            image.save(image_path)
            
            # 获取PDF页面和保存图片的尺寸，用于坐标映射
            pdf_width = float(page.width)
            pdf_height = float(page.height)
            img_width, img_height = image.size
            
            # 计算缩放比例
            scale_x = img_width / pdf_width
            scale_y = img_height / pdf_height
            
            # 准备标注数据
            annotations = []
            for word in words:
                # 获取文本框坐标，并进行缩放映射
                x0 = int(word['x0'] * scale_x)
                y0 = int(word['top'] * scale_y)
                x1 = int(word['x1'] * scale_x)
                y1 = int(word['bottom'] * scale_y)
                
                # 创建标准格式的四点坐标
                points = [
                    [x0, y0],  # 左上
                    [x1, y0],  # 右上
                    [x1, y1],  # 右下
                    [x0, y1]   # 左下
                ]
                
                # 判断是否为难以识别的文本, TODO: 后续可以根据需要加入 困难样本的标记
                # 这里可以根据实际需求添加判断条件，比如文本长度、特殊字符等
                is_difficult = False
                # if len(word['text'].strip()) < 2 or any(ord(c) > 0x4e00 for c in word['text']):
                #     is_difficult = True
                
                # 创建标准格式的标注项
                annotation = {
                    "transcription": word['text'],
                    "points": points,
                    "difficult": is_difficult
                }
                annotations.append(annotation)
            
            # 写入标注信息（使用相对路径）
            relative_image_path = os.path.join("images", image_filename)
            label_line = f"{relative_image_path}\t{json.dumps(annotations, ensure_ascii=False)}\n"
            label_f.write(label_line)


def main():
    pdf_dir = "/aipdf-mlp/xelawk/datasets/202501/ai_scanner/my_support/tmp/test_pdf"
    output_dir = "/aipdf-mlp/xelawk/datasets/202501/ai_scanner/my_support/tmp/test_pdf_ppocr_labeled"

    all_pdfs = Path(pdf_dir).glob('*.pdf')
    all_pdfs = [pdf for pdf in all_pdfs if not pdf.stem.startswith('.')]

    for pdf_path in all_pdfs:
        cur_output_dir = os.path.join(output_dir, pdf_path.stem)
        convert_pdf_to_ppocr_format(pdf_path, cur_output_dir)


if __name__ == "__main__":
    main()