import os
import pickle
import json
import numpy as np
import cv2
import shutil
from datetime import datetime
from pathlib import Path


def load_pickle(file_path):
    """
    加载pickle文件并返回其内容
    
    Args:
        file_path: pickle文件路径
        
    Returns:
        pickle文件中的数据对象
    """
    try:
        with open(file_path, 'rb') as f:
            return pickle.load(f)
    except Exception as e:
        print(f"加载pickle文件时出错: {e}")
        return None


def convert_label_to_json(image_path, gt_pkl_path, output_dir=None, line_threshold=0.5, sample_points=30):
    """
    转换标注格式并检测表格线条，只生成JSON标注文件
    
    Args:
        image_path: 原始图像路径（_org.png）
        gt_pkl_path: 原始标注路径（_gt.pkl）
        output_dir: 输出目录，如果为None则使用图像所在目录
        line_threshold: 判断实线的阈值，越大越严格
        sample_points: 每条边采样的点数
        
    Returns:
        标注文件路径
    """
    # 确保输出目录存在
    if output_dir is None:
        output_dir = os.path.dirname(image_path)
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成输出文件路径
    base_name = os.path.basename(image_path).replace('_org.png', '')
    gt_json_path = os.path.join(output_dir, f"{base_name}_gt.json")
    
    # 加载原始标注数据
    gt_data = load_pickle(gt_pkl_path)
    if gt_data is None:
        print(f"错误: 无法加载标注文件 {gt_pkl_path}")
        return None
    
    # 创建基本的JSON数据结构
    json_data = {
        "table_ind": gt_data.get("table_ind", 0),
        "image_path": os.path.basename(image_path),
        "type": 2,  # 默认为无线表格
        "cells": []
    }
    
    # 正确处理gt_data的结构
    cells_data = []
    if isinstance(gt_data, dict) and "cells_anno" in gt_data:
        cells_data = gt_data["cells_anno"]
    elif isinstance(gt_data, list):
        cells_data = gt_data
    
    # 处理每个单元格
    for cell_idx, cell in enumerate(cells_data):
        if not isinstance(cell, dict):
            continue
        
        # 提取bbox，格式为[x1,y1,x2,y2,x3,y3,x4,y4]
        bbox = cell.get("bbox", [0,0,0,0,0,0,0,0])
        if isinstance(bbox, list) and len(bbox) >= 8:
            p1 = [int(bbox[0]), int(bbox[1])]
            p2 = [int(bbox[2]), int(bbox[3])]
            p3 = [int(bbox[4]), int(bbox[5])]
            p4 = [int(bbox[6]), int(bbox[7])]
        else:
            p1, p2, p3, p4 = [0,0], [0,0], [0,0], [0,0]
        
        # 提取lloc，格式为[start-row,end-row,start-col,end-col]
        lloc = cell.get("lloc", [0,0,0,0])
        if isinstance(lloc, list) and len(lloc) >= 4:
            lloc_dict = {
                "start_row": int(lloc[0]),
                "end_row": int(lloc[1]),
                "start_col": int(lloc[2]),
                "end_col": int(lloc[3])
            }
        else:
            lloc_dict = {
                "start_row": 0,
                "end_row": 0,
                "start_col": 0,
                "end_col": 0
            }
        
        # 提取content，将字符串转换为标准格式
        content = cell.get("content", "")
        content_list = []
        if content and isinstance(content, str):
            content_list = [{
                "bbox": [],
                "direction": 0,
                "text": content,
                "score": 1.0
            }]
        
        # 创建单元格数据
        cell_data = {
            "cell_ind": cell.get("cell_ind", cell_idx),
            "header": cell.get("header", False),
            "bbox": {
                "p1": p1,
                "p2": p2,
                "p3": p3,
                "p4": p4
            },
            "lloc": lloc_dict,
            "border": {
                "style": {
                    "top": 0,
                    "right": 0,
                    "bottom": 0,
                    "left": 0
                }
            },
            "content": content_list
        }
        
        # 添加到cells列表
        json_data["cells"].append(cell_data)
    
    # 检测表格线条
    try:
        # 读取原始图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"错误: 无法读取图像: {image_path}")
            return None
        
        # 更新图像尺寸
        height, width = image.shape[:2]
        json_data["width"] = width
        json_data["height"] = height
        
        # 如果是彩色图像，转换为灰度图进行分析
        if len(image.shape) == 3:
            gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray_image = image.copy()
        
        # 遍历每个单元格
        for cell_idx, cell in enumerate(json_data.get("cells", [])):
            # 获取单元格的四个角点
            bbox = cell.get("bbox", {})
            if not isinstance(bbox, dict):
                continue
            
            # 直接从字典中提取四个角点
            p1 = bbox["p1"]
            p2 = bbox["p2"]
            p3 = bbox["p3"]
            p4 = bbox["p4"]
            
            # 检测四条边的线条状态
            top_line = check_line(gray_image, p1, p2, sample_points, line_threshold)
            right_line = check_line(gray_image, p2, p3, sample_points, line_threshold)
            bottom_line = check_line(gray_image, p3, p4, sample_points, line_threshold)
            left_line = check_line(gray_image, p4, p1, sample_points, line_threshold)
            
            # 更新结果
            if "border" not in cell:
                cell["border"] = {"style": {}}
            if "style" not in cell["border"]:
                cell["border"]["style"] = {}
            
            cell["border"]["style"]["top"] = 1 if top_line else 0
            cell["border"]["style"]["right"] = 1 if right_line else 0
            cell["border"]["style"]["bottom"] = 1 if bottom_line else 0
            cell["border"]["style"]["left"] = 1 if left_line else 0
        
        # 保存JSON标注
        with open(gt_json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=4)
        print(f"成功将标注保存到: {gt_json_path}")
        
        return gt_json_path
        
    except Exception as e:
        print(f"处理图像时出错: {e}")
        import traceback
        traceback.print_exc()
        return None


def check_line(gray_image, p1, p2, sample_points=10, threshold=0.5):
    """
    检测两点之间是否存在实线
    
    Args:
        gray_image: 灰度图像
        p1: 起始点坐标 (x, y)
        p2: 终点坐标 (x, y)
        sample_points: 采样点数
        threshold: 判断实线的阈值，越小越严格
        
    Returns:
        如果存在实线返回True，否则返回False
    """
    # 获取图像尺寸
    height, width = gray_image.shape[:2]
    
    # 处理点坐标，确保在图像范围内
    try:
        p1 = (max(0, min(width-1, int(p1[0]))), max(0, min(height-1, int(p1[1]))))
        p2 = (max(0, min(width-1, int(p2[0]))), max(0, min(height-1, int(p2[1]))))
    except Exception as e:
        print(f"警告: 处理点时出错: {e}, 使用默认值")
        return False
    
    # 计算两点之间的方向向量
    dx = p2[0] - p1[0]
    dy = p2[1] - p1[1]
    # 距离在这里不需要使用，因为我们使用均匀采样
    
    # 在两点之间均匀采样
    dark_points = 0
    total_points = sample_points
    
    for i in range(sample_points):
        # 计算采样点的坐标
        t = i / (sample_points - 1) if sample_points > 1 else 0.5
        x = int(p1[0] + t * dx)
        y = int(p1[1] + t * dy)
        
        # 确保坐标在图像范围内
        x = max(0, min(width-1, x))
        y = max(0, min(height-1, y))
        
        # 检查该点的灰度值
        if gray_image[y, x] < 128:
            dark_points += 1
    
    # 计算暗点比例并返回结果
    dark_ratio = dark_points / total_points
    return dark_ratio >= threshold


def draw_line(image, p1, p2, is_solid, color=(0, 0, 255), thickness=2):
    """
    在图像上绘制实线或虚线
    
    Args:
        image: 要绘制的图像
        p1: 起始点坐标 (x, y)
        p2: 终点坐标 (x, y)
        is_solid: 是否为实线，如果为True则绘制实线，否则绘制虚线
        color: 线条颜色，默认为红色
        thickness: 线条粗细，默认为2
    """
    # 获取图像尺寸
    height, width = image.shape[:2]
    
    # 处理点坐标，确保在图像范围内
    p1 = (max(0, min(width-1, p1[0])), max(0, min(height-1, p1[1])))
    p2 = (max(0, min(width-1, p2[0])), max(0, min(height-1, p2[1])))
    
    # 根据线条类型选择颜色
    line_color = (0, 0, 255) if is_solid else (0, 255, 0)  # 实线为红色，无线为绿色
    
    # 绘制线条
    cv2.line(image, p1, p2, line_color, thickness)


def draw_logic_visualization(image_shape, cells, output_image):
    """
    绘制逻辑位置可视化图像，在纯白背景上绘制表格线条和逻辑位置信息
    
    Args:
        image_shape: 原始图像尺寸 (height, width)
        cells: 单元格数据列表，包含bbox和lloc信息
        output_image: 输出图像数组，已初始化为纯白背景
    
    Returns:
        无返回值，直接修改output_image
    """
    # 使用传入的图像数组
    logic_vis_image = output_image
    
    # 遍历每个单元格
    for cell in cells:
        # 获取单元格的四个角点
        bbox = cell.get("bbox", {})
        if not isinstance(bbox, dict):
            continue
        
        # 直接从字典中提取四个角点
        p1 = bbox["p1"]
        p2 = bbox["p2"]
        p3 = bbox["p3"]
        p4 = bbox["p4"]
        
        # 获取边界线条状态
        border = cell.get("border", {}).get("style", {})
        top_line = border.get("top", False)
        right_line = border.get("right", False)
        bottom_line = border.get("bottom", False)
        left_line = border.get("left", False)
        
        # 绘制边框线条
        draw_line(logic_vis_image, p1, p2, top_line, color=(0, 0, 0), thickness=2)
        draw_line(logic_vis_image, p2, p3, right_line, color=(0, 0, 0), thickness=2)
        draw_line(logic_vis_image, p3, p4, bottom_line, color=(0, 0, 0), thickness=2)
        draw_line(logic_vis_image, p4, p1, left_line, color=(0, 0, 0), thickness=2)
        
        # 获取逻辑位置信息
        lloc = cell.get("lloc", {})
        if isinstance(lloc, dict):
            start_row = lloc.get("start_row", 0)
            end_row = lloc.get("end_row", 0)
            start_col = lloc.get("start_col", 0)
            end_col = lloc.get("end_col", 0)
            
            # 创建逻辑位置文本
            text = f"{start_row}-{end_row}; {start_col}-{end_col}"
            
            # 计算单元格中心点
            center_x = int((p1[0] + p2[0] + p3[0] + p4[0]) / 4)
            center_y = int((p1[1] + p2[1] + p3[1] + p4[1]) / 4)
            
            # 计算单元格大小，用于自适应文本大小
            cell_width = max(abs(p2[0] - p1[0]), abs(p3[0] - p4[0]))
            cell_height = max(abs(p4[1] - p1[1]), abs(p3[1] - p2[1]))
            
            # 根据单元格大小调整文本大小，最小为0.3，最大为1.0
            font_scale = min(1.0, max(0.3, min(cell_width, cell_height) / 100))
            
            # 获取文本尺寸
            (text_width, text_height), _ = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)
            
            # 计算文本位置，使其居中
            text_x = center_x - text_width // 2
            text_y = center_y + text_height // 2
            
            # 绘制文本
            cv2.putText(logic_vis_image, text, (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), 1, cv2.LINE_AA)


def create_visualization(image_path, json_path, output_path=None):
    """
    根据JSON标注文件生成可视化结果
    
    Args:
        image_path: 原始图像路径（_org.png）
        json_path: JSON标注文件路径（_gt.json）
        output_path: 输出可视化结果路径，如果为None则使用默认路径
    
    Returns:
        可视化结果路径
    """
    # 如果输出路径为None，生成默认路径
    if output_path is None:
        base_name = os.path.basename(json_path).replace('_gt.json', '')
        output_dir = os.path.dirname(json_path)
        output_path = os.path.join(output_dir, f"{base_name}_vis.png")
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    try:
        # 读取JSON标注文件
        with open(json_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        # 读取原始图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"错误: 无法读取图像: {image_path}")
            return None
        
        # 创建可视化图像，使用原始图像的副本
        vis_image = image.copy() if len(image.shape) == 3 else cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
        
        # 遍历每个单元格
        for cell_idx, cell in enumerate(json_data.get("cells", [])):
            # 获取单元格的四个角点
            bbox = cell.get("bbox", {})
            if not isinstance(bbox, dict):
                continue
            
            # 直接从字典中提取四个角点
            p1 = bbox["p1"]
            p2 = bbox["p2"]
            p3 = bbox["p3"]
            p4 = bbox["p4"]
            
            # 获取边框样式
            border_style = cell.get("border", {}).get("style", {})
            top_line = border_style.get("top", 0) == 1
            right_line = border_style.get("right", 0) == 1
            bottom_line = border_style.get("bottom", 0) == 1
            left_line = border_style.get("left", 0) == 1
            
            # 绘制边框线
            draw_line(vis_image, p1, p2, top_line)
            draw_line(vis_image, p2, p3, right_line)
            draw_line(vis_image, p3, p4, bottom_line)
            draw_line(vis_image, p4, p1, left_line)
        
        # 创建逻辑位置可视化图像
        logic_vis_image = np.ones((image.shape[0], image.shape[1], 3), dtype=np.uint8) * 255
        draw_logic_visualization(image.shape, json_data.get("cells", []), logic_vis_image)
        
        # 使用已读取的图像，避免重复读取
        org_image = image.copy() if len(image.shape) == 3 else cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
        
        # 确保所有图像具有相同的尺寸
        if org_image.shape != vis_image.shape:
            org_image = cv2.resize(org_image, (vis_image.shape[1], vis_image.shape[0]))
        if logic_vis_image.shape != vis_image.shape:
            logic_vis_image = cv2.resize(logic_vis_image, (vis_image.shape[1], vis_image.shape[0]))
        
        # 计算宽高比
        aspect_ratio = vis_image.shape[1] / vis_image.shape[0]
        
        # 根据宽高比决定拼接方式
        if aspect_ratio > 1.5:
            # 宽高比大于1.5，使用纵向拼接（上下堆叠）
            print(f"图像宽高比为{aspect_ratio:.2f}，使用纵向拼接")
            combined_image = np.vstack((org_image, vis_image, logic_vis_image))
        else:
            # 宽高比小于等于1.5，使用横向拼接（左右并排）
            print(f"图像宽高比为{aspect_ratio:.2f}，使用横向拼接")
            combined_image = np.hstack((org_image, vis_image, logic_vis_image))
        
        # 保存可视化结果
        cv2.imwrite(output_path, combined_image)
        print(f"成功将可视化结果保存到: {output_path}")
        
        return output_path
        
    except Exception as e:
        print(f"生成可视化结果时出错: {e}")
        import traceback
        traceback.print_exc()
        return None
    

def batch_process(pkl_dir, image_dir, json_output_dir, line_threshold=0.5, sample_points=30, max_files=None):
    """
    批量处理图像和标注文件，仅生成JSON标注文件，不生成可视化结果
    
    Args:
        pkl_dir: 标注文件目录（包含.pkl文件）
        image_dir: 图像文件目录（包含_org.png文件）
        json_output_dir: JSON标注输出目录
        line_threshold: 判断实线的阈值
        sample_points: 每条边采样的点数
        max_files: 最大处理文件数量，如果为None则处理所有文件
    
    输出文件命名规则：
        - JSON标注：xxx_gt.json
    """
    # 确保输出目录存在
    os.makedirs(json_output_dir, exist_ok=True)
    
    # 创建错误日志文件
    error_log_path = os.path.join(json_output_dir, 'error_log.txt')
    with open(error_log_path, 'w', encoding='utf-8') as error_log:
        error_log.write(f"处理时间: {os.path.basename(__file__)} {os.path.basename(pkl_dir)} {os.path.basename(image_dir)}\n")
        error_log.write("以下文件处理失败:\n")
    
    # 查找所有pkl文件
    pkl_files = []
    for root, dirs, files in os.walk(pkl_dir):
        for file in files:
            if file.endswith('.pkl'):
                pkl_files.append(os.path.join(root, file))
    
    print(f"找到 {len(pkl_files)} 个pkl标注文件")
    
    # 处理计数
    processed_count = 0
    error_count = 0
    
    # 处理每个pkl文件
    for pkl_path in pkl_files:
        # 提取文件名（不含扩展名）
        base_name = os.path.basename(pkl_path)
        file_name_without_ext = os.path.splitext(base_name)[0]
        
        # 构造对应的图像文件路径
        image_path = os.path.join(image_dir, f"{file_name_without_ext}_org.png")
        
        # 检查图像文件是否存在
        if not os.path.exists(image_path):
            print(f"警告: 图像文件不存在: {image_path}")
            with open(error_log_path, 'a', encoding='utf-8') as error_log:
                error_log.write(f"图像文件不存在: {image_path}\n")
            error_count += 1
            continue
        
        print(f"处理: {file_name_without_ext}")
        
        try:
            # 构造输出路径
            json_output_path = os.path.join(json_output_dir, f"{file_name_without_ext}_gt.json")
            
            # 转换标注并检测线条，只生成JSON文件
            gt_json_path = convert_label_to_json(
                image_path, 
                pkl_path, 
                output_dir=json_output_dir,
                line_threshold=line_threshold,
                sample_points=sample_points
            )
            
            if gt_json_path is None:
                print(f"错误: 处理失败: {file_name_without_ext}")
                with open(error_log_path, 'a', encoding='utf-8') as error_log:
                    error_log.write(f"处理失败: {file_name_without_ext}\n")
                error_count += 1
                continue
            
            processed_count += 1
            print(f"成功处理: {file_name_without_ext}")
            print(f"  标注文件: {gt_json_path}")
            
        except Exception as e:
            print(f"错误: 处理文件时出现异常: {file_name_without_ext}, {str(e)}")
            with open(error_log_path, 'a', encoding='utf-8') as error_log:
                error_log.write(f"处理异常: {file_name_without_ext}, {str(e)}\n")
            error_count += 1

        # 如果设置了最大文件数量限制，则检查是否达到限制
        if max_files is not None and processed_count >= max_files:
            print(f"已达到指定的最大处理文件数量: {max_files}")
            break
    
    # 打印处理统计
    print(f"\n处理完成:")
    print(f"总文件数: {len(pkl_files)}")
    print(f"成功处理: {processed_count}")
    print(f"处理失败: {error_count}")
    print(f"错误日志: {error_log_path}")


def batch_visualize(json_dir, image_dir, vis_output_dir, max_files=None):
    """
    批量生成可视化结果，基于JSON标注文件和原始图像
    
    Args:
        json_dir: JSON标注文件目录（包含_gt.json文件）
        image_dir: 图像文件目录（包含_org.png文件）
        vis_output_dir: 可视化结果输出目录
        max_files: 最大处理文件数量，如果为None则处理所有文件
    
    输出文件命名规则：
        - 可视化结果：xxx_vis.png（包含原图、线条检测和逻辑位置信息）
    """
    # 确保输出目录存在
    os.makedirs(vis_output_dir, exist_ok=True)
    
    # 创建错误日志文件
    error_log_path = os.path.join(vis_output_dir, 'error_log.txt')
    with open(error_log_path, 'w', encoding='utf-8') as error_log:
        error_log.write(f"处理时间: {os.path.basename(__file__)} {os.path.basename(json_dir)} {os.path.basename(image_dir)}\n")
        error_log.write("以下文件处理失败:\n")
    
    # 查找所有JSON标注文件
    json_files = []
    for root, dirs, files in os.walk(json_dir):
        for file in files:
            if file.endswith('_gt.json'):
                json_files.append(os.path.join(root, file))
    
    print(f"找到 {len(json_files)} 个JSON标注文件")
    
    # 处理计数
    processed_count = 0
    error_count = 0
    
    # 处理每个JSON文件
    for json_path in json_files:
        # 提取文件名（不含扩展名）
        base_name = os.path.basename(json_path)
        file_name_without_ext = base_name.replace('_gt.json', '')
        
        # 构造对应的图像文件路径
        image_path = os.path.join(image_dir, f"{file_name_without_ext}_org.png")
        
        # 检查图像文件是否存在
        if not os.path.exists(image_path):
            print(f"警告: 图像文件不存在: {image_path}")
            with open(error_log_path, 'a', encoding='utf-8') as error_log:
                error_log.write(f"图像文件不存在: {image_path}\n")
            error_count += 1
            continue
        
        print(f"处理: {file_name_without_ext}")
        
        try:
            # 构造输出路径
            vis_output_path = os.path.join(vis_output_dir, f"{file_name_without_ext}_vis.png")
            
            # 生成可视化结果
            vis_path = create_visualization(
                image_path, 
                json_path, 
                output_path=vis_output_path
            )
            
            if vis_path is None:
                print(f"错误: 处理失败: {file_name_without_ext}")
                with open(error_log_path, 'a', encoding='utf-8') as error_log:
                    error_log.write(f"处理失败: {file_name_without_ext}\n")
                error_count += 1
                continue
            
            processed_count += 1
            print(f"成功处理: {file_name_without_ext}")
            print(f"  可视化结果: {vis_path}")
            
        except Exception as e:
            print(f"错误: 处理文件时出现异常: {file_name_without_ext}, {str(e)}")
            with open(error_log_path, 'a', encoding='utf-8') as error_log:
                error_log.write(f"处理异常: {file_name_without_ext}, {str(e)}\n")
            error_count += 1

        # 如果设置了最大文件数量限制，则检查是否达到限制
        if max_files is not None and processed_count >= max_files:
            print(f"已达到指定的最大处理文件数量: {max_files}")
            break
    
    # 打印处理统计
    print(f"\n处理完成:")
    print(f"总文件数: {len(json_files)}")
    print(f"成功处理: {processed_count}")
    print(f"处理失败: {error_count}")
    print(f"错误日志: {error_log_path}")


def main():
    """
    主函数，设置批处理参数并调用批处理函数
    """

    root_dir = "/aipdf-mlp/shared/tsr_dataset/TableGraph-24K/tablegraph24k"

    # 设置参数
    pkl_dir = os.path.join(root_dir, 'gt')  # pkl标注文件夹
    image_dir = os.path.join(root_dir, 'image')  # 图像文件夹
    json_output_dir = os.path.join(root_dir, 'json_v250618')  # JSON输出文件夹
    vis_output_dir = os.path.join(root_dir, 'vis_v250618')  # 可视化结果输出文件夹
    line_threshold = 0.5  # 判断实线的阈值
    sample_points = 30  # 每条边采样的点数
    max_files = None  # 默认最大处理文件数量，设置为None可处理所有文件
    
    # 确保输出目录存在
    os.makedirs(json_output_dir, exist_ok=True)
    os.makedirs(vis_output_dir, exist_ok=True)
    
    # 调用批处理函数 - 修正参数匹配问题
    batch_process(
        pkl_dir,
        image_dir,
        json_output_dir,
        line_threshold=line_threshold,
        sample_points=sample_points,
        max_files=max_files
    )
    
    # 调用可视化批处理函数
    print("\n开始生成可视化结果...")
    batch_visualize(
        json_output_dir,
        image_dir,
        vis_output_dir,
        max_files=max_files
    )


def filter_dataset(vis_dir, json_dir, cls_dir=None, verify_classified=True):
    """
    对数据集进行初筛，将数据分为三类：有线表、无线表和舍弃数据
    
    Args:
        vis_dir: 可视化文件夹路径，包含_vis.png文件
        json_dir: JSON标注文件夹路径，包含_gt.json文件
        cls_dir: 分类后的JSON结果存储路径，如果为None则使用json_dir的父目录下的filtered_data
        verify_classified: 是否验证已分类文件的位置，如果为True，则检查记录中的文件是否在对应的目标文件夹中
    """
    
    # 确保输入目录存在
    if not os.path.exists(vis_dir):
        print(f"错误: 可视化文件夹不存在: {vis_dir}")
        return
    if not os.path.exists(json_dir):
        print(f"错误: JSON文件夹不存在: {json_dir}")
        return
    
    # 确定输出基础目录
    output_base_dir = cls_dir if cls_dir else os.path.join(os.path.dirname(json_dir), "filtered_data")
    
    # 创建三个目标文件夹
    lined_table_dir = os.path.join(output_base_dir, "lined_tables")
    unlined_table_dir = os.path.join(output_base_dir, "unlined_tables")
    discarded_dir = os.path.join(output_base_dir, "discarded")
    
    os.makedirs(lined_table_dir, exist_ok=True)
    os.makedirs(unlined_table_dir, exist_ok=True)
    os.makedirs(discarded_dir, exist_ok=True)
    
    # 记录文件路径
    record_file = os.path.join(output_base_dir, "classification_record.json")
    
    # 加载记录文件
    classification_record = {}
    if os.path.exists(record_file):
        try:
            with open(record_file, 'r', encoding='utf-8') as f:
                classification_record = json.load(f)
            print(f"已加载分类记录，共 {len(classification_record)} 个文件已分类")
        except Exception as e:
            print(f"加载分类记录文件时出错: {e}")
            classification_record = {}
    
    # 验证已分类文件的位置
    if verify_classified and classification_record:
        print("正在验证已分类文件的位置...")
        files_to_remove = []
        
        for file_name, record in classification_record.items():
            json_path = os.path.join(json_dir, f"{file_name}_gt.json")
            category = record.get("category")
            
            # 检查文件是否存在于对应的目标文件夹中
            if category == 1:  # 有线表
                target_path = os.path.join(lined_table_dir, f"{file_name}_gt.json")
            elif category == 2:  # 无线表
                target_path = os.path.join(unlined_table_dir, f"{file_name}_gt.json")
            elif category == 3:  # 舍弃数据
                target_path = os.path.join(discarded_dir, f"{file_name}_gt.json")
            else:
                continue
            
            # 如果文件不在对应的目标文件夹中，标记为未分类
            if not os.path.exists(target_path):
                files_to_remove.append(file_name)
        
        # 从记录中移除不一致的文件
        for file_name in files_to_remove:
            del classification_record[file_name]
        
        print(f"验证完成，移除了 {len(files_to_remove)} 个不一致的记录")
        
        # 更新记录文件
        with open(record_file, 'w', encoding='utf-8') as f:
            json.dump(classification_record, f, ensure_ascii=False, indent=4)
    
    # 查找所有可视化文件
    vis_files = []
    for root, dirs, files in os.walk(vis_dir):
        for file in files:
            if file.endswith('_vis.png'):
                vis_files.append(os.path.join(root, file))
    
    print(f"找到 {len(vis_files)} 个可视化文件")
    
    # 过滤掉已分类的文件
    unclassified_vis_files = []
    for vis_path in vis_files:
        base_name = os.path.basename(vis_path).replace('_vis.png', '')
        if base_name not in classification_record:
            unclassified_vis_files.append(vis_path)
    
    # 计算总进度，包括已分类和未分类的文件
    total_files = len(vis_files)
    already_classified = len(vis_files) - len(unclassified_vis_files)
    
    print(f"总文件数: {total_files}")
    print(f"已分类: {already_classified} 个文件 ({already_classified/total_files*100:.1f}%)")
    print(f"未分类: {len(unclassified_vis_files)} 个文件")
    
    if not unclassified_vis_files:
        print("所有文件已分类完成！")
        return
    
    # 开始分类过程
    print("\n开始分类过程...")
    print("按键说明: 1=有线表, 2=无线表, 3=舍弃数据, ESC=退出")
    
    # 分类计数
    current_count = 0
    total_count = total_files
    
    # 遍历未分类的文件
    for vis_path in unclassified_vis_files:
        # 提取文件名（不含扩展名）
        base_name = os.path.basename(vis_path).replace('_vis.png', '')
        
        # 构造对应的JSON文件路径
        json_path = os.path.join(json_dir, f"{base_name}_gt.json")
        
        # 检查JSON文件是否存在
        if not os.path.exists(json_path):
            print(f"警告: JSON文件不存在: {json_path}")
            continue
        
        # 读取并显示可视化图像
        image = cv2.imread(vis_path)
        if image is None:
            print(f"错误: 无法读取图像: {vis_path}")
            continue
        
        # 调整图像大小以适应屏幕，等比例放大，宽不超过1800，高不超过950
        # max_width, max_height = 1800, 950
        max_width, max_height = 1300, 750
        h, w = image.shape[:2]
        
        # 计算缩放比例
        scale_w = max_width / w
        scale_h = max_height / h
        scale = min(scale_w, scale_h)  # 选择较小的缩放比例以确保两个维度都不超过限制
        
        # 计算新尺寸
        new_w, new_h = int(w * scale), int(h * scale)
        
        # 调整图像大小
        image = cv2.resize(image, (new_w, new_h))
        
        # 显示图像
        window_name = "filter"
        cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)  # 使用WINDOW_AUTOSIZE确保窗口大小与图像一致
        cv2.moveWindow(window_name, 10, 10)  # 设置窗口位置为左上角(10, 10)
        cv2.imshow(window_name, image)
        
        # 显示控制台进度信息
        current_progress = already_classified + current_count + 1
        progress_percentage = current_progress / total_count * 100
        print(f"\r当前进度: {current_progress}/{total_count} ({progress_percentage:.1f}%) - 文件: {base_name}", end="")
        
        # 等待按键
        key = cv2.waitKey(0) & 0xFF
        
        # 处理按键
        category = None
        if key == ord('1'):  # 有线表
            target_dir = lined_table_dir
            category = 1
        elif key == ord('2'):  # 无线表
            target_dir = unlined_table_dir
            category = 2
        elif key == ord('3'):  # 舍弃数据
            target_dir = discarded_dir
            category = 3
        elif key == 27:  # ESC键，退出
            print("\n用户中断，分类过程已暂停")
            break
        else:
            # 无效按键，重新显示当前图像
            continue
        
        # 复制JSON文件到目标文件夹
        target_path = os.path.join(target_dir, f"{base_name}_gt.json")
        try:
            # 如果是有线表，需要修改JSON文件中的type字段
            if category == 1:  # 有线表
                # 读取JSON文件
                with open(json_path, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                
                # 修改type字段为1（有线表）
                if 'type' in json_data:
                    json_data['type'] = 1
                
                # 写入目标文件
                with open(target_path, 'w', encoding='utf-8') as f:
                    json.dump(json_data, f, ensure_ascii=False, indent=4)
                
                print(f"已将 {base_name}_gt.json 复制到 {os.path.basename(target_dir)} 并修改type为1（有线表）")
            else:
                # 其他类别直接复制
                shutil.copy2(json_path, target_path)
                print(f"已将 {base_name}_gt.json 复制到 {os.path.basename(target_dir)}")
            
            # 更新分类记录
            classification_record[base_name] = {
                "category": category,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # 更新记录文件
            with open(record_file, 'w', encoding='utf-8') as f:
                json.dump(classification_record, f, ensure_ascii=False, indent=4)
            
            current_count += 1
            print()  # 换行，下一个进度信息会显示在新行
            
        except Exception as e:
            print(f"复制文件时出错: {e}")
    
    # 关闭所有窗口
    cv2.destroyAllWindows()
    
    # 打印分类统计
    print(f"\n分类完成:")
    print(f"总文件数: {total_count}")
    print(f"已分类: {already_classified + current_count}")
    print(f"未分类: {total_count - (already_classified + current_count)}")
    print(f"分类记录保存在: {record_file}")


def group_and_filter_samples(image_dir, label_dir, output_dir, output_tag, batch_size=700):
    """
    过滤样本分组：将图像和标签文件复制到新文件夹并按批次分组
    
    Args:
        image_dir: 图像文件夹路径，包含xxx_org.png文件
        label_dir: 标签文件夹路径，包含xxx_gt.json文件
        output_dir: 输出文件夹路径
        output_tag: 输出标签名称，用于生成子文件夹名称，如output_tag_001
        batch_size: 每组样本数量，默认为700
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取标签文件列表
    label_files = [f for f in os.listdir(label_dir) if f.endswith('_gt.json')]
    print(f"找到 {len(label_files)} 个标签文件")
    
    # 初始化计数器和批次编号
    count = 0
    batch_num = 1
    current_batch_dir = os.path.join(output_dir, f"{output_tag}_{batch_num:03d}")
    os.makedirs(current_batch_dir, exist_ok=True)
    
    # 遍历标签文件
    for label_file in label_files:
        # 提取基本名称（不含后缀）
        base_name = label_file.replace('_gt.json', '')
        
        # 构建对应的图像文件路径
        image_file = f"{base_name}_org.png"
        image_path = os.path.join(image_dir, image_file)
        label_path = os.path.join(label_dir, label_file)
        
        # 检查图像文件是否存在
        if not os.path.exists(image_path):
            print(f"警告: 找不到对应的图像文件: {image_path}，跳过此样本")
            continue
        
        # 如果当前批次已满，创建新批次目录
        if count > 0 and count % batch_size == 0:
            batch_num += 1
            current_batch_dir = os.path.join(output_dir, f"{output_tag}_{batch_num:03d}")
            os.makedirs(current_batch_dir, exist_ok=True)
            print(f"创建新批次目录: {current_batch_dir}")
        
        # 构建目标文件路径
        target_image = os.path.join(current_batch_dir, f"{base_name}.png")
        target_label = os.path.join(current_batch_dir, f"{base_name}_table_annotation.json")
        
        # 复制文件并重命名
        try:
            shutil.copy2(image_path, target_image)
            shutil.copy2(label_path, target_label)
            count += 1
            
            if count % 100 == 0:
                print(f"已处理 {count} 个样本，当前批次: {batch_num}")
                
        except Exception as e:
            print(f"复制文件时出错: {e}，跳过此样本")
    
    print(f"处理完成，共处理 {count} 个样本，分为 {batch_num} 个批次")
    print(f"输出目录: {output_dir}")


if __name__ == '__main__':
    # main()
    # filter_dataset(
    #     vis_dir=r'D:\dataset\TSR\tablegraph24k\vis_v250618',
    #     json_dir=r'D:\dataset\TSR\tablegraph24k\json_v250618',
    #     cls_dir=r'D:\dataset\TSR\tablegraph24k\cls_filter_v250618'
    # )

    # 使用示例
    group_and_filter_samples(
        image_dir=r'D:\dataset\TSR\tablegraph24k\image',  # 包含xxx_org.png文件的图像文件夹
        label_dir=r"D:\dataset\TSR\tablegraph24k\cls_filter_v250618\unlined_tables",  # 包含xxx_gt.json文件的标签文件夹
        output_dir=r"D:\dataset\TSR\tablegraph24k\to_be_labeled", # 输出文件夹路径
        output_tag="unlined"          # 输出标签名称，将生成如dataset_001的子文件夹
    )
