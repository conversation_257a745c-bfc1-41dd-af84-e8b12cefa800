#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/19 11:29
# <AUTHOR> <EMAIL>
# @FileName: create_human_cls_dataset

import random
from shutil import copy2

from PIL import Image
from tqdm import tqdm
from pathlib import Path
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks
from modules.utils.image_utils import get_all_image_path


# 初始化检测模型
retina_face_detection = pipeline(
    Tasks.face_detection,
    'damo/cv_resnet50_face-detection_retinaface'
)


def save_image_to_folder(image_path, save_dir, category, dataset_type, idx):
    part_num = idx // 500 + 1
    img_name = f"{idx % 500 + 1}.jpg"
    part_dir = save_dir / dataset_type / f"part_{part_num:04d}" / category
    part_dir.mkdir(parents=True, exist_ok=True)
    copy2(image_path, part_dir / img_name)


def cal_max_face_ratio(image_path):
    image_path = str(image_path)
    image = Image.open(image_path)
    width, height = image.width, image.height
    results = retina_face_detection(image_path)

    face_max_area = 0
    for box, score in zip(results['boxes'], results['scores']):
        if score > 0.95:
            x1, y1, x2, y2 = box
            face_width = x2 - x1
            face_height = y2 - y1
            current_face_area = face_width * face_height
            face_max_area = max(face_max_area, current_face_area)

    face_area_ratio = 100 * face_max_area / (width * height)
    return face_area_ratio


def process_images(src_img_pathes, save_dir, limit):
    human_train_count, no_human_train_count = 0, 0
    human_test_count, no_human_test_count = 0, 0
    idx_train_human, idx_train_no_human, idx_test_human, idx_test_no_human, idx_uncertain = 0, 0, 0, 0, 0

    # 处理图像并打印进度
    for img_path in tqdm(src_img_pathes, desc="Processing images", unit="image"):
        # 训练集和测试集的终止条件
        if (human_train_count >= limit['train'] and no_human_train_count >= limit['train'] and
                human_test_count >= limit['test'] and no_human_test_count >= limit['test']):
            break

        # 归档数据时的最终判断依据
        uncertain = False
        has_person = False
        max_face_area_ratio = cal_max_face_ratio(img_path)

        if max_face_area_ratio == 0:
            has_person = False
        elif 0.6 <= max_face_area_ratio <= 25:  # 人脸占比1%~15%
            has_person = True
        else:
            uncertain = True

        if uncertain:
            dataset_type = 'backup'
            category = 'uncertain'
            idx = idx_uncertain
            if idx < 20000:
                save_image_to_folder(img_path, save_dir, category, dataset_type, idx)
            idx_uncertain += 1
            continue

        if has_person:
            category = 'human'
            if human_train_count < limit['train']:
                dataset_type = 'train'
                idx = idx_train_human
                human_train_count += 1
                idx_train_human += 1
            elif human_test_count < limit['test']:
                dataset_type = 'test'
                idx = idx_test_human
                human_test_count += 1
                idx_test_human += 1
            else:
                continue
        else:
            category = 'no_human'
            if no_human_train_count < limit['train']:
                dataset_type = 'train'
                idx = idx_train_no_human
                no_human_train_count += 1
                idx_train_no_human += 1
            elif no_human_test_count < limit['test']:
                dataset_type = 'test'
                idx = idx_test_no_human
                no_human_test_count += 1
                idx_test_no_human += 1
            else:
                continue

        # 保存图片到对应的目录
        save_image_to_folder(img_path, save_dir, category, dataset_type, idx)


def main():
    src_img_dirs = [
        ("/mnt/appdata_nfs/dataset/open/coco2017", -1),
        ("/mnt/appdata_nfs/dataset/open/DIV2K_train_HR", -1),
        ("/mnt/appdata_nfs/dataset/open/Flickr2K", -1),
        ("/mnt/appdata2/dataset/BG-20K/train", -1)
    ]
    additional_img_dirs = [
        ("/mnt/appdata2/dataset/kantu_classifier/tmp/raw_image_30w_v202408202019", 300000)
    ]
    save_dir = Path("/mnt/appdata2/dataset/kantu_classifier/cls_human_v202408211100")

    src_img_pathes = []
    for _dir in src_img_dirs:
        _dir, select_num = _dir
        cur_img_pathes = get_all_image_path(_dir, recursive=True, path_op=Path)
        if select_num != -1:
            random.shuffle(cur_img_pathes)
            cur_img_pathes = cur_img_pathes[:select_num]
        src_img_pathes += cur_img_pathes
    random.shuffle(src_img_pathes)
    print(f"标准图源问题：{len(src_img_pathes)}")

    add_img_pathes = []
    for _dir in additional_img_dirs:
        _dir, select_num = _dir
        cur_img_pathes = get_all_image_path(_dir, recursive=True, path_op=Path)
        if select_num != -1:
            random.shuffle(cur_img_pathes)
            cur_img_pathes = cur_img_pathes[:select_num]
        add_img_pathes += cur_img_pathes
    random.shuffle(add_img_pathes)
    print(f"扩展图源问题：{len(add_img_pathes)}")

    src_img_pathes += add_img_pathes

    # src_img_pathes += add_img_pathes[0:10000]
    # random.shuffle(src_img_pathes)
    # src_img_pathes += add_img_pathes[10000::]

    limit = {
        'train': 10500,
        'test': 1050
    }

    process_images(src_img_pathes, save_dir, limit)


if __name__ == "__main__":
    main()
