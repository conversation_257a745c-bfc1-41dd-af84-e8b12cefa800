#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/9/29 10:32
# <AUTHOR> <EMAIL>
# @FileName: sample_raw_part1_captured_snapdoc

import os
import shutil
import random
import numpy as np
from PIL import Image
from tqdm import tqdm
from pathlib import Path
from modules.utils.image_utils import get_all_image_path

# src资源
midv500_data = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/midv500_data"
midv2019_data = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/midv2019_data"
pdf_doc_captured = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/pdf_doc_captured"
pdf_doc_really_clean = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/pdf_doc_really_clean"
uvdoc_textures = "/aicamera-mlp/xelawk_train_space/datasets/ai_scanner/UVDoc_final/textures"
uvdoc_wraped = "/aicamera-mlp/xelawk_train_space/datasets/ai_scanner/UVDoc_final/img"
smart_doc_extented = "/aicamera-mlp/xelawk_train_space/datasets/ai_scanner/SmartDocExtented/dataset/train"
doc3d_img = "/aicamera-mlp/xelawk_train_space/datasets/ai_scanner/Doc3D_critical/img"
doc_custom = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/doc_custom"
doc_laynet = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/doc_laynet_5k_png/snapshot_doc"

cls_blur_test = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/release/cls_clear_blur_v202408191637/train"
cls_human_test = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/release/cls_human_v202408211100/train"
cls_texted_test = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/release/cls_texted_v202408211550/train"
cls_watermarked_test = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/release/cls_watermarked_v202408271510/train"

captured_doc_add = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/doc_and_nodoc_clean/doc_shot/captured"
snap_doc_add = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/doc_and_nodoc_clean/doc_shot/snap"
nodoc_raw = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/doc_and_nodoc_clean/nodoc"
doc_raw = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/release/cls_snap_captured_v202409302008"

black_data1 = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/deblack/delight_train_dataset/images"
noblack_data1 = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/deblack/delight_train_dataset/gts"
black_noblack_source_data2 = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/deblack/RealDAE"

deblur_source = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/DeblurData/BaiduCompetition/deblur"
deblur_source2 = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/DeblurData/TDD"

deshadow_source1 = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/deshadow/RDD_data"
deshadow_source2 = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/deshadow/SD7K"

# dst资源
dst_dir1 = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/snapshot_and_captured_doc/normal"
dst_dir2 = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/snapshot_and_captured_doc/midv"
dst_dir3 = "/Users/<USER>/Downloads/doc_laynet_5k_png"
dst_dir4 = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/doc_and_nodoc"
dst_dir5 = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/snapshot_and_captured_doc_add/normal"
dst_dir6 = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/doc_and_nodoc_release"
dst_dir7 = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/black_and_noblack"
dst_dir8 = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/doc_blur_and_noblur"
dst_dir9 = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/shadow_and_noshadow"

captured_doc_flag = "captured_doc"
snapshot_doc_flag = "snapshot_doc"
nodoc_flag = "nodoc"
doc_flag = "doc"
black_flag = "black"
noblack_flag = "noblack"
blur_doc_flag = "blur_doc"
noblur_doc_flag = "noblur_doc"
shadow_flag = "shadow"
noshadow_flag = "noshadow"
GROUP_SIZE = 500  # 每组图片的数量


def is_pure_black_or_white(image_path):
    """
    判断图片是否是纯白或纯黑。

    :param image_path: 图片文件路径
    :return: 如果图片是纯白或纯黑，返回 True，否则返回 False
    """
    # 打开图片并转换为灰度模式
    img = Image.open(image_path).convert("L")

    # 将图片转为 NumPy 数组
    img_array = np.array(img)

    # 获取像素总和
    total_sum = img_array.sum()

    # 获取像素总数
    pixel_count = img_array.size

    # 纯黑图片的总和应该是 0
    if total_sum == 0:
        return True

    # 取反后，纯白图片的总和应该是 0
    inverted_img_array = 255 - img_array
    if inverted_img_array.sum() == 0:
        return True

    # 如果都不满足，返回 False
    return False


def get_grouped_dir_index(_dir):
    """
    获取当前分组目录下的最大 part_index，和最新 part_index 下的样本 index。
    如果目录不存在，返回默认初始值 1, 1。
    :param _dir: 目标目录 captured_doc 或 snapshot_doc
    :return: (part_index, sample_index)
    """
    if not _dir.exists():
        return 1, 1  # 如果目录不存在，返回默认的索引

    part_dirs = sorted([d for d in _dir.glob('part_*') if d.is_dir()])
    if not part_dirs:
        return 1, 1  # 如果没有任何分组，返回默认的索引

    latest_part_dir = part_dirs[-1]
    sample_files = sorted(latest_part_dir.glob('*.png'))
    if not sample_files:
        return int(latest_part_dir.stem.split('_')[1]), 1

    latest_sample = sample_files[-1]
    latest_sample_index = int(latest_sample.stem)

    return int(latest_part_dir.stem.split('_')[1]), latest_sample_index + 1


def ensure_part_dir(dst, flag, sample_idx):
    """
    确保分组目录存在，依据 sample_idx 判断是否需要创建新的分组目录。
    :param dst: 目标目录
    :param flag: captured_doc 或 snapshot_doc
    :param sample_idx: 当前的样本索引
    :return: 分组目录路径, 可能已经递增了 part_idx
    """
    # 通过 sample_idx 判断是否需要开启新分组
    part_idx = (sample_idx - 1) // GROUP_SIZE + 1  # 根据 sample_idx 推断当前应该在哪个 part 目录下
    part_dir = dst / flag / f"part_{part_idx:04d}"
    part_dir.mkdir(parents=True, exist_ok=True)

    return part_dir, part_idx


def sample_from_midv(src, dst):
    src = Path(src)
    dst = Path(dst)

    # 获取 captured_doc 和 snapshot_doc 目录的分组和样本索引
    cap_part_idx, cap_sample_idx = get_grouped_dir_index(dst / captured_doc_flag)
    snap_part_idx, snap_sample_idx = get_grouped_dir_index(dst / snapshot_doc_flag)

    all_caps = []
    all_snaps = []

    # 遍历源目录，采样图片，显示进度条
    src_dirs = list(src.glob('*'))
    for _dir in tqdm(src_dirs, desc="Processing directories", unit="dir"):
        if _dir.stem.startswith('.') or not _dir.is_dir():
            continue

        snaps = []
        caps = []
        for img_path in _dir.glob('*.png'):
            if img_path.stem.startswith('.') or img_path.is_dir():
                continue

            if img_path.stem.endswith('groundtruth'):
                snaps.append(img_path)
            else:
                caps.append(img_path)

        if snaps:
            all_snaps += snaps

        if caps:
            all_caps += random.sample(caps, min(5, len(caps)))

    # 处理总的采样图片，显示进度条
    for snap in all_snaps:
        snap_part_dir, snap_part_idx = ensure_part_dir(dst, snapshot_doc_flag, snap_sample_idx)
        snap_dst_path = snap_part_dir / f"{snap_sample_idx:08d}.png"
        shutil.copy(snap, snap_dst_path)
        snap_sample_idx += 1


    for cap in all_caps:
        # 处理 captured_doc
        cap_part_dir, cap_part_idx = ensure_part_dir(dst, captured_doc_flag, cap_sample_idx)
        cap_dst_path = cap_part_dir / f"{cap_sample_idx:08d}.png"
        shutil.copy(cap, cap_dst_path)
        cap_sample_idx += 1


def sample_from_pdf_doc(src, dst, doc_flag, num_samples_per_id=-1, check_w_or_b=False):
    src = Path(src)
    dst = Path(dst)

    # 遍历资源
    id_to_img_paths = dict()
    all_image_paths = list(src.glob("**/*.png"))

    num_invalid = 0
    pbar = tqdm(total=len(all_image_paths), desc="sample_from_pdf_doc, iterating image sources")
    for img_path in all_image_paths:
        if img_path.stem.startswith('.') or img_path.is_dir():
            pbar.update(1)
            continue

        try:
            if check_w_or_b and is_pure_black_or_white(img_path):
                num_invalid += 1
                pbar.update(1)
                continue
        except:
            num_invalid += 1
            pbar.update(1)
            continue

        _id = img_path.stem.split('_')[0]
        if _id in id_to_img_paths:
            id_to_img_paths[_id].append(img_path)
        else:
            id_to_img_paths[_id] = [img_path]

        pbar.set_description(f"sample_from_pdf_doc, iterating image sources, invalid: {num_invalid}")
        pbar.update(1)

    pbar.close()

    # 获取目录的分组和样本索引
    part_idx, sample_idx = get_grouped_dir_index(dst / doc_flag)
    pbar = tqdm(total=len(id_to_img_paths), desc="sample_from_pdf_doc, sampling images")
    for _id, img_path_list in id_to_img_paths.items():
        if num_samples_per_id > 0:
            samples = random.sample(img_path_list, min(num_samples_per_id, len(img_path_list)))
        else:
            samples = img_path_list

        for img_path in samples:
            part_dir, part_idx = ensure_part_dir(dst, doc_flag, sample_idx)
            dst_path = part_dir / f"{sample_idx:08d}.png"
            shutil.copy(img_path, dst_path)
            sample_idx += 1

        pbar.update(1)

    pbar.close()


def sample_from_general_doc(src, dst, doc_flag, num_sample=-1):
    src = Path(src)
    dst = Path(dst)

    # 遍历资源
    id_to_img_paths = dict()
    all_image_paths = list(src.glob("**/*.png"))

    all_clean_image_paths = []
    for img_path in all_image_paths:
        if img_path.stem.startswith('.'):
            continue
        if img_path.stem.endswith('gt'):
            continue
        if img_path.is_dir():
            continue
        all_clean_image_paths.append(img_path)

    # 获取目录的分组和样本索引
    if num_sample > 0:
        all_image_paths = random.sample(all_clean_image_paths, min(num_sample, len(all_clean_image_paths)))
    else:
        all_image_paths = all_clean_image_paths

    part_idx, sample_idx = get_grouped_dir_index(dst / doc_flag)
    pbar = tqdm(total=len(all_image_paths), desc="sample_from_general_doc, sampling images")
    for img_path in all_image_paths:
        part_dir, part_idx = ensure_part_dir(dst, doc_flag, sample_idx)
        dst_path = part_dir / f"{sample_idx:08d}.png"
        shutil.copy(img_path, dst_path)

        sample_idx += 1
        pbar.update(1)

    pbar.close()


def sample_from_general_doc_v2(src, dst, doc_flag, num_sample=-1):
    src = Path(src)
    dst = Path(dst)

    # 遍历资源
    id_to_img_paths = dict()
    all_image_paths = [Path(os.path.join(src, p)) for p in os.listdir(src)]

    # all_clean_image_paths = []
    # for img_path in all_image_paths:
    #     if img_path.stem.startswith('.'):
    #         continue
    #     if img_path.stem.endswith('gt'):
    #         continue
    #     if img_path.is_dir():
    #         continue
    #     all_clean_image_paths.append(img_path)

    # 获取目录的分组和样本索引
    if num_sample > 0:
        all_image_paths = random.sample(all_image_paths, min(num_sample, len(all_image_paths)))
    else:
        all_image_paths = all_image_paths

    part_idx, sample_idx = get_grouped_dir_index(dst / doc_flag)
    pbar = tqdm(total=len(all_image_paths), desc="sample_from_general_doc, sampling images")
    for img_path in all_image_paths:
        part_dir, part_idx = ensure_part_dir(dst, doc_flag, sample_idx)
        dst_path = part_dir / f"{sample_idx:08d}.png"
        shutil.copy(img_path, dst_path)

        sample_idx += 1
        pbar.update(1)

    pbar.close()


def general_sampling(src, dst, flag, num_sample=-1):
    src = Path(src)
    dst = Path(dst)

    # 遍历资源
    id_to_img_paths = dict()
    all_image_paths = get_all_image_path(src, recursive=True, path_op=Path)

    all_clean_image_paths = []
    for img_path in all_image_paths:
        if img_path.parent.stem == "blur":
            continue
        if img_path.stem.startswith('.'):
            continue
        if img_path.stem.endswith('gt'):
            continue
        if img_path.is_dir():
            continue
        all_clean_image_paths.append(img_path)

    # 获取目录的分组和样本索引
    if num_sample > 0:
        all_image_paths = random.sample(all_clean_image_paths, min(num_sample, len(all_clean_image_paths)))
    else:
        all_image_paths = all_clean_image_paths

    part_idx, sample_idx = get_grouped_dir_index(dst / flag)
    pbar = tqdm(total=len(all_image_paths), desc="general_sampling, sampling images...")
    for img_path in all_image_paths:
        part_dir, part_idx = ensure_part_dir(dst, flag, sample_idx)
        dst_path = part_dir / f"{sample_idx:08d}.png"
        shutil.copy(img_path, dst_path)

        sample_idx += 1
        pbar.update(1)

    pbar.close()


def general_sampling_v2(src, dst, flag, num_sample=-1, parent_pattern=None, file_pattern=None):
    src = Path(src)
    dst = Path(dst)

    # 遍历资源
    id_to_img_paths = dict()
    all_image_paths = get_all_image_path(src, recursive=True, path_op=Path)

    all_clean_image_paths = []
    for img_path in all_image_paths:
        if img_path.is_dir():
            continue
        if img_path.stem.startswith('.'):
            continue
        if parent_pattern and parent_pattern not in img_path.parent.stem:
            continue
        if file_pattern and file_pattern not in img_path.stem:
            continue
        all_clean_image_paths.append(img_path)

    # 获取目录的分组和样本索引
    if num_sample > 0:
        all_image_paths = random.sample(all_clean_image_paths, min(num_sample, len(all_clean_image_paths)))
    else:
        all_image_paths = all_clean_image_paths

    part_idx, sample_idx = get_grouped_dir_index(dst / flag)
    pbar = tqdm(total=len(all_image_paths), desc="general_sampling, sampling images...")
    for img_path in all_image_paths:
        part_dir, part_idx = ensure_part_dir(dst, flag, sample_idx)
        dst_path = part_dir / f"{sample_idx:08d}.png"
        shutil.copy(img_path, dst_path)

        sample_idx += 1
        pbar.update(1)

    pbar.close()


if __name__ == '__main__':
    # sample_from_midv(midv500_data, dst_dir2)
    # sample_from_midv(midv2019_data, dst_dir2)

    # sample_from_pdf_doc(pdf_doc_captured, dst_dir1, doc_flag=captured_doc_flag, num_samples_per_id=-1)
    # sample_from_pdf_doc(pdf_doc_really_clean, dst_dir1, doc_flag=snapshot_doc_flag, num_samples_per_id=8, check_w_or_b=True)
    # sample_from_general_doc(doc_custom, dst_dir1, doc_flag=snapshot_doc_flag, num_sample=1500)
    # sample_from_general_doc(uvdoc_textures, dst_dir1, doc_flag=snapshot_doc_flag, num_sample=-1)
    # sample_from_general_doc(uvdoc_wraped, dst_dir1, doc_flag=captured_doc_flag, num_sample=5515)
    # sample_from_general_doc(smart_doc_extented, dst_dir1, doc_flag=captured_doc_flag, num_sample=2913)
    # sample_from_general_doc(doc3d_img, dst_dir1, doc_flag=captured_doc_flag, num_sample=3000)

    # sample_from_general_doc(doc_laynet, dst_dir1, doc_flag=snapshot_doc_flag, num_sample=3998)

    # for p in [cls_human_test, cls_blur_test, cls_texted_test, cls_watermarked_test]:
    #     general_sampling(p, dst_dir4, flag=nodoc_flag, num_sample=4000)

    # sample_from_general_doc(captured_doc_add, dst_dir5, doc_flag=captured_doc_flag)
    # sample_from_general_doc(snap_doc_add, dst_dir5, doc_flag=snapshot_doc_flag)

    # general_sampling(nodoc_raw, dst_dir6, flag=nodoc_flag)
    # general_sampling(doc_raw, dst_dir6, flag=doc_flag)

    # general_sampling_v2(black_data1, dst_dir7, flag=black_flag)
    # general_sampling_v2(noblack_data1, dst_dir7, flag=noblack_flag)
    # general_sampling_v2(black_noblack_source_data2, dst_dir7, flag=black_flag, file_pattern='in')
    # general_sampling_v2(black_noblack_source_data2, dst_dir7, flag=noblack_flag, file_pattern='gt')

    # general_sampling_v2(deblur_source, dst_dir8, flag=blur_doc_flag, parent_pattern='blur_image')
    # general_sampling_v2(deblur_source2, dst_dir8, flag=blur_doc_flag, parent_pattern='blur')
    # general_sampling_v2(deblur_source, dst_dir8, flag=noblur_doc_flag, parent_pattern='gt_image')
    # general_sampling_v2(deblur_source2, dst_dir8, flag=noblur_doc_flag, parent_pattern='orig')
    general_sampling_v2(deshadow_source1, dst_dir9, flag=shadow_flag, parent_pattern='img')
    general_sampling_v2(deshadow_source1, dst_dir9, flag=noshadow_flag, parent_pattern='gt')
    general_sampling_v2(deshadow_source2, dst_dir9, flag=shadow_flag, parent_pattern='input')
    general_sampling_v2(deshadow_source2, dst_dir9, flag=noshadow_flag, parent_pattern='target')