#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/9/26 16:32
# <AUTHOR> <EMAIL>
# @FileName: sampling_from_midv_datasets

import os
import random
from PIL import Image
from pathlib import Path
from tqdm import tqdm  # 导入 tqdm 库，用于进度显示


def sample_midv_500():
    # 源数据和目标目录
    midv_500_data_dir = "/aicamera-mlp/xelawk_train_space/datasets/ai_scanner/midv500_data/midv500"
    midv_500_sample_dir = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/midv500_data"

    # 获取所有子目录
    part_dirs = [d for d in Path(midv_500_data_dir).glob('*') if d.is_dir() and not d.stem.startswith('.')]

    # 用 tqdm 包装 part_dirs，显示进度条
    for part_dir in tqdm(part_dirs, desc="Processing datasets"):
        part_image_dir = part_dir / 'images'

        # 获取 ground truth tif 文件，并保存为 png
        gt_tif_files = list(part_image_dir.glob('*.tif'))
        if gt_tif_files:
            gt_tif = gt_tif_files[0]
            gt_tif_name = gt_tif.stem  # ground truth 文件名
            gt_save_path = os.path.join(midv_500_sample_dir, gt_tif_name, "1_groundtruth.png")
            os.makedirs(Path(gt_save_path).parent, exist_ok=True)
            Image.open(gt_tif).save(gt_save_path)

            # 遍历子文件夹并随机选择 tif 文件
            part_tif_dirs = [d for d in part_image_dir.glob('*') if d.is_dir()]
            for part_tif_dir in part_tif_dirs:
                all_part_tifs = list(part_tif_dir.glob('*.tif'))
                if len(all_part_tifs) > 2:
                    # 随机选择 2 个 tif 文件
                    selected_tifs = random.sample(all_part_tifs, 2)
                else:
                    # 如果文件少于 2 个，全选
                    selected_tifs = all_part_tifs

                # 保存随机选中的 tif 文件为 png
                for tif_file in selected_tifs:
                    tif_save_path = os.path.join(midv_500_sample_dir, gt_tif_name, f"{tif_file.stem}.png")
                    os.makedirs(Path(tif_save_path).parent, exist_ok=True)
                    Image.open(tif_file).save(tif_save_path)


def sample_midv_2019():
    types = ["DG", "DX", "LG", "LX"]
    midv_2019_dir = "/aicamera-mlp/xelawk_train_space/datasets/ai_scanner/midv500_data/midv2019"
    midv_2019_sample_dir = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/tmp/midv2019_data"

    # 图像文件夹路径
    midv_image_dir = Path(midv_2019_dir) / 'images'

    # 获取所有 groundtruth tif 图像文件
    groundtruths = list(midv_image_dir.glob('*.tif'))

    # 用 tqdm 包装 groundtruths，显示进度条
    for gt in tqdm(groundtruths, desc="Processing MIDV-2019 datasets"):
        # 提取 groundtruth 文件的索引，例如 "001" 之类的
        idx = gt.stem.split('_')[0]

        # groundtruth 文件保存路径
        gt_save_path = os.path.join(midv_2019_sample_dir, idx, "1_groundtruth.png")
        os.makedirs(Path(gt_save_path).parent, exist_ok=True)
        Image.open(gt).save(gt_save_path)

        # 遍历 DG, DX, LG, LX 文件夹，从每个文件夹中随机选择图像
        for t in types:
            part_tif_dir = midv_image_dir / t
            # 匹配当前类型（如 DG001_*.tif）的所有 tif 文件
            part_tif_files = list(part_tif_dir.glob(f"{t}{idx}_*.tif"))

            if len(part_tif_files) > 2:
                # 随机选择 2 个 tif 文件
                selected_tifs = random.sample(part_tif_files, 2)
            else:
                # 如果文件少于 2 个，选择全部
                selected_tifs = part_tif_files

            # 保存随机选中的 tif 文件为 png
            for tif_file in selected_tifs:
                tif_save_path = os.path.join(midv_2019_sample_dir, idx, f"{tif_file.stem}.png")
                os.makedirs(Path(tif_save_path).parent, exist_ok=True)
                Image.open(tif_file).save(tif_save_path)


if __name__ == '__main__':
    sample_midv_2019()
