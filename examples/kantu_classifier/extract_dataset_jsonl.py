#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/22 15:57
# <AUTHOR> <EMAIL>
# @FileName: extract_dataset_jsonl

import os
import json
import random
from tqdm import tqdm
from pathlib import Path
from collections import defaultdict

from modules.utils.log import LOGGER
from modules.utils.image_utils import get_all_image_path

SPECIALS = [
    'cls_texted',
    'cls_shadow',
    'cls_doc_blur',
    'cls_doc_nodoc',
    'cls_black_noblack',
    'cls_snap_captured',
    'cls_cert_doc',
]

# 样本数量需求：key 是 data_mode，value 是需要的每个 label 的样本数量
SAMPLE_REQUIREMENTS = {
    'train': 11000,  # 训练集每个 label 所需要的样本数
    'test': 1100,   # 测试集每个 label 所需要的样本数
    # 根据需要添加更多 data_mode
}

def sample_images(image_list, required_count):
    """根据需要的样本数量进行采样，支持重采样"""
    if len(image_list) >= required_count:
        # 如果样本数量足够，打乱后采样
        return random.sample(image_list, required_count)
    else:
        # 如果样本数量不够，进行重采样
        return random.choices(image_list, k=required_count)

def main():
    dataset_dir = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/release_v20241106"
    sub_dataset_dir = []

    for _dir in Path(dataset_dir).glob("cls_*"):
        if not _dir.is_dir():
            continue
        if _dir.stem.startswith('.'):
            continue
        sub_dataset_dir.append(_dir)

    LOGGER.info(f"Found existing dataset dir:")
    for _dir in sub_dataset_dir:
        LOGGER.info(f"{_dir}")

    # 删除旧的 jsonl 文件
    test_jsonl_path = os.path.join(dataset_dir, "test.jsonl")
    if os.path.exists(test_jsonl_path):
        os.remove(test_jsonl_path)

    train_jsonl_path = os.path.join(dataset_dir, "train.jsonl")
    if os.path.exists(train_jsonl_path):
        os.remove(train_jsonl_path)

    LOGGER.info(f"Starting to extract dataset jsonl...")

    # 记录每个 data_mode + label 的样本
    samples_by_mode_and_label = defaultdict(lambda: defaultdict(list))

    for _dir in sub_dataset_dir:
        all_image_path = get_all_image_path(_dir, recursive=True, path_op=Path)

        pbar = tqdm(total=len(all_image_path), desc=f"Extracting {_dir.stem}")
        for image_path in all_image_path:
            parts = image_path.parts
            src_name = parts[-5]
            data_mode = parts[-4]  # 比如 'train' 或 'test'

            if data_mode == 'backup':
                continue

            is_part_prefix_first = True
            for src_type in SPECIALS:
                if src_type in src_name:
                    is_part_prefix_first = False
                    break

            # 根据是否特殊类型来决定 label 的位置
            if is_part_prefix_first:
                label = parts[-2]
            else:
                label = parts[-3]

            # 将样本按 data_mode 和 label 分类
            samples_by_mode_and_label[data_mode][label].append({
                "image_path": '/'.join(parts[-5:]),
                "data_type": src_name.split('_v')[0],
                "label": label,
            })

            pbar.update(1)

        pbar.close()

    # 进行采样并写入对应的 jsonl 文件
    for data_mode, labels in samples_by_mode_and_label.items():
        required_count = SAMPLE_REQUIREMENTS.get(data_mode, None)

        if required_count:
            for label, samples in labels.items():
                # 对每个 label 进行采样
                sampled_images = sample_images(samples, required_count)

                # 将采样后的样本写入对应的 jsonl 文件
                with open(os.path.join(dataset_dir, f"{data_mode}.jsonl"), "a", encoding="utf-8") as f:
                    for sample in sampled_images:
                        f.write(f"{json.dumps(sample)}\n")

                LOGGER.info(f"Processed {data_mode} with label {label}: {len(sampled_images)} samples.")


if __name__ == "__main__":
    random.seed(42)  # 保证可重复的采样结果
    main()