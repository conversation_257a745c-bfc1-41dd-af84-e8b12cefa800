#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/22 11:35
# <AUTHOR> <EMAIL>
# @FileName: export_ncnn_model

"""
Reference: https://github.com/Tencent/ncnn/wiki/use-ncnn-with-pytorch-or-onnx
"""

import os
import random

import pnnx
import ncnn
import torch
import numpy as np
from PIL import Image
from torchvision import transforms

from modules.utils.torch_utils import convert_batchnorm_to_sync_batchnorm
from networks.mobilenet.model.kantu_classifier import MultiTaskMobileNetV3Small
from networks.mobilenet.model.kantu_classifier_large import MultiTaskMobileNetV3Large
from networks.mobilenet.model.small_kantu_classifier_mbv4 import MultiTaskMobileNetV4Small

backbone_name = "mbv3_small_backbone"
texted_fc_name = "cls_texted_head"
human_fc_name = "cls_human_head"
blur_fc_name = "cls_blur_head"
watermarked_fc_name = "cls_watermarked_head"
seed = random.randint(1, 10000)

# 定义ImageNet的标准化参数
img_transform = transforms.Compose([
    transforms.Resize((512, 512)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])
sigmoid = torch.nn.Sigmoid()


def infer_and_export_pnnx_files(model_class, ckpt_path=None, fp16=False):
    # 定义模型
    opt_model = model_class()
    opt_model = convert_batchnorm_to_sync_batchnorm(opt_model)

    if ckpt_path is not None:
        state_dict = torch.load(ckpt_path, map_location='cpu')
        opt_model.load_state_dict(state_dict, strict=True)
    opt_model = opt_model.eval()

    x = Image.open(img_path).convert('RGB')
    x = img_transform(x).contiguous()
    x = x.unsqueeze(0)

    with torch.no_grad():
        feats = opt_model.backbone(x)
        cls_texted_outs = opt_model.cls_texted(feats)
        cls_watermarked_outs = opt_model.cls_watermarked(feats)
        cls_human_outs = opt_model.cls_human(feats)
        cls_blur_outs = opt_model.cls_blur(feats)

    pnnx.export(opt_model.backbone, os.path.join(save_dir, f"{backbone_name}.pt"), x, fp16=fp16)
    pnnx.export(opt_model.cls_texted, os.path.join(save_dir, f"{texted_fc_name}.pt"), feats, fp16=fp16)
    pnnx.export(opt_model.cls_watermarked, os.path.join(save_dir, f"{watermarked_fc_name}.pt"), feats, fp16=fp16)
    pnnx.export(opt_model.cls_blur, os.path.join(save_dir, f"{blur_fc_name}.pt"), feats, fp16=fp16)
    pnnx.export(opt_model.cls_human, os.path.join(save_dir, f"{human_fc_name}.pt"), feats, fp16=fp16)

    return feats, (cls_texted_outs, cls_watermarked_outs, cls_human_outs, cls_blur_outs)


def load_ncnn_and_infer():
    x = Image.open(img_path).convert('RGB')
    x = img_transform(x).contiguous()
    x = x.unsqueeze(0)

    with ncnn.Net() as net:
        net.load_param(os.path.join(save_dir, f"{backbone_name}.ncnn.param"))
        net.load_model(os.path.join(save_dir, f"{backbone_name}.ncnn.bin"))

        with net.create_extractor() as ex:
            ex.input("in0", ncnn.Mat(x.squeeze(0).numpy()).clone())
            _, out0 = ex.extract("out0")
            feats = torch.from_numpy(np.asarray(out0)).unsqueeze(0)

    with ncnn.Net() as net:
        net.load_param(os.path.join(save_dir, f"{texted_fc_name}.ncnn.param"))
        net.load_model(os.path.join(save_dir, f"{texted_fc_name}.ncnn.bin"))

        with net.create_extractor() as ex:
            ex.input("in0", ncnn.Mat(feats.numpy()).clone())

            _, out0 = ex.extract("out0")
            cls_texted_outs = torch.from_numpy(np.array(out0))

    with ncnn.Net() as net:
        net.load_param(os.path.join(save_dir, f"{watermarked_fc_name}.ncnn.param"))
        net.load_model(os.path.join(save_dir, f"{watermarked_fc_name}.ncnn.bin"))

        with net.create_extractor() as ex:
            ex.input("in0", ncnn.Mat(feats.numpy()).clone())

            _, out0 = ex.extract("out0")
            cls_watermarked_outs = torch.from_numpy(np.array(out0))

    with ncnn.Net() as net:
        net.load_param(os.path.join(save_dir, f"{human_fc_name}.ncnn.param"))
        net.load_model(os.path.join(save_dir, f"{human_fc_name}.ncnn.bin"))

        with net.create_extractor() as ex:
            ex.input("in0", ncnn.Mat(feats.numpy()).clone())

            _, out0 = ex.extract("out0")
            cls_human_outs = torch.from_numpy(np.array(out0))

    with ncnn.Net() as net:
        net.load_param(os.path.join(save_dir, f"{blur_fc_name}.ncnn.param"))
        net.load_model(os.path.join(save_dir, f"{blur_fc_name}.ncnn.bin"))

        with net.create_extractor() as ex:
            ex.input("in0", ncnn.Mat(feats.numpy()).clone())

            _, out0 = ex.extract("out0")
            cls_blur_outs = torch.from_numpy(np.array(out0))

    return feats, (cls_texted_outs, cls_watermarked_outs, cls_human_outs, cls_blur_outs)


if __name__ == "__main__":
    ckpt = "/mnt/appdata/tmp/kantuclassifiers/large_kantu_classifier_v202408271715/best_model/model.bin"

    save_dir = "/mnt/appdata/tmp/kantuclassifiers/large_kantu_classifier_v202408271715/ncnn_export"
    os.makedirs(save_dir, exist_ok=True)

    img_path = "/mnt/workspace/497.jpg"
    feats, outputs = infer_and_export_pnnx_files(MultiTaskMobileNetV3Large, ckpt, fp16=False)
    cls_texted_outs, cls_watermarked_outs, cls_human_outs, cls_blur_outs = outputs

    ncnn_feats, ncnn_outputs = load_ncnn_and_infer()
    ncnn_cls_texted_outs, ncnn_cls_watermarked_outs, ncnn_cls_human_outs, ncnn_cls_blur_outs = ncnn_outputs

    print(f"feats diff: {(abs(feats - ncnn_feats)).mean():.8f}")
    for i, (ori_out, ncnn_out) in enumerate(zip(outputs, ncnn_outputs)):
        print(f"[{i}] ori_out: {ori_out[0][0]:.4f},{sigmoid(ori_out[0][0]):.4f}; "
              f"ncnn_out: {ncnn_out[0][0]:.4f},{sigmoid(ncnn_out[0][0]):.4f}; "
              f"output diff: {(abs(ori_out - ncnn_out)).mean().item():.8f}")
