#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/20 11:29
# <AUTHOR> <EMAIL>
# @FileName: label_questioned_samples

import json
from PIL import Image
from tqdm import tqdm
from pathlib import Path
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks
from modules.utils.image_utils import get_all_image_path

# 初始化检测模型
retina_face_detection = pipeline(Tasks.face_detection, 'damo/cv_resnet50_face-detection_retinaface')

# 问题样本目录
questioned_sample_dir = "/mnt/appdata2/dataset/kantu_classifier/tmp/参考样本2"

# 获取所有样本的路径
all_samples = get_all_image_path(questioned_sample_dir, path_op=Path)

# 遍历每一个样本并保存检测结果为JSON文件
for sample in tqdm(all_samples):
    image = Image.open(sample)
    width, height = image.width, image.height
    results = retina_face_detection(str(sample))

    face_max_area = 0
    for box, score in zip(results['boxes'], results['scores']):
        if score > 0.95:
            x1, y1, x2, y2 = box
            face_width = x2 - x1
            face_height = y2 - y1
            current_face_area = face_width * face_height
            face_max_area = max(face_max_area, current_face_area)

    face_area_ratio = face_max_area / (width * height)
    face_area_ratio = f"{100 * face_area_ratio:.2f}%"
    output_data = {
        "max_face_area_ratio": face_area_ratio,
    }

    # 生成与图片同名的JSON文件路径
    json_path = sample.with_suffix('.json')

    # 将结果保存为JSON文件
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=4)
