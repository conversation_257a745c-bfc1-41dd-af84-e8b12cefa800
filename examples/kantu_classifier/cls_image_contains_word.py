#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/14 20:41
# <AUTHOR> <EMAIL>
# @FileName: cls_image_contains_word

"""
从样本集中区分出包含文字和不包含文字的，构造文字检测分类模型的数据集
"""

import os
import random
import logging
from tqdm import tqdm
from io import BytesIO
from PIL import Image
from paddleocr import PaddleOCR
from datasets import DatasetDict, concatenate_datasets

logging.disable(logging.DEBUG)  # 关闭DEBUG日志的打印
logging.disable(logging.WARNING)  # 关闭WARNING日志的打印

det_model_dir = '/mnt/appdata_nfs/aicache/paddle/ch_PP-OCRv4_det_server_infer'
rec_model_dir = '/mnt/appdata_nfs/aicache/paddle/ch_PP-OCRv4_rec_server_infer'
ocr = PaddleOCR(
    lang="ch",
    det_model_dir=det_model_dir,
    rec_model_dir=rec_model_dir,
    use_gpu=True,
    use_angle_cls=False,
)


# 创建目录
def create_dir(base_dir, part_num):
    dir_path = os.path.join(base_dir, f'part_{part_num:05d}')
    os.makedirs(dir_path, exist_ok=True)
    return dir_path


# 获取数据集
def get_src_dataset(data_dir, select_num=-1, seed=-1):
    dataset = []
    if seed == -1:
        seed = random.randint(1, 2000000)
    cur_dataset = DatasetDict.load_from_disk(data_dir)['train']
    if select_num > 0:
        cur_dataset = cur_dataset.shuffle(seed=seed)
        cur_dataset = cur_dataset.select(range(select_num))
    dataset.append(cur_dataset)
    dataset = concatenate_datasets(dataset)
    dataset = dataset.shuffle(seed=seed)
    return dataset


# 判断是否包含文字
def is_contains_word(image_bytes):
    result = ocr.ocr(image_bytes)
    if result[0]:
        return True
    else:
        return False


# 保存图片
def save_image(image_bytes, save_dir, img_index):
    img = Image.open(BytesIO(image_bytes))
    img = img.convert("RGB")
    img.save(os.path.join(save_dir, f'{img_index:05d}.jpg'), 'JPEG')


# 主函数
def main():
    target_num = 22000
    dataset_save_dir = "/mnt/appdata2/dataset/kantu_classifier/tmp/normal_and_texted_samples"
    source_dataset_dir = "/mnt/appdata2/dataset/xelawk/hres_nowm_samples_150w_hf/train"

    contains_words_dir = os.path.join(dataset_save_dir, "contains_words")
    normal_dir = os.path.join(dataset_save_dir, "normal")

    source_dataset = get_src_dataset(source_dataset_dir)

    contains_words_count = 0
    normal_count = 0
    contains_words_part = 1
    normal_part = 1

    contains_words_save_dir = create_dir(contains_words_dir, contains_words_part)
    normal_save_dir = create_dir(normal_dir, normal_part)

    pbar = tqdm(source_dataset, desc="Processing images", unit="image")
    for item in pbar:
        if contains_words_count >= target_num and normal_count >= target_num:
            break

        image_bytes = item['image_bytes']

        if is_contains_word(image_bytes):
            if contains_words_count < target_num:
                if contains_words_count > 0 and contains_words_count % 500 == 0:
                    contains_words_part += 1
                    contains_words_save_dir = create_dir(contains_words_dir, contains_words_part)
                contains_words_count += 1
                save_image(image_bytes, contains_words_save_dir, contains_words_count)
        else:
            if normal_count < target_num:
                if normal_count > 0 and normal_count % 500 == 0:
                    normal_part += 1
                    normal_save_dir = create_dir(normal_dir, normal_part)
                normal_count += 1
                save_image(image_bytes, normal_save_dir, normal_count)

        # 更新进度条信息
        pbar.set_postfix({
            "contains_words": contains_words_count,
            "normal": normal_count
        })

    print("处理完成！")


if __name__ == "__main__":
    main()
