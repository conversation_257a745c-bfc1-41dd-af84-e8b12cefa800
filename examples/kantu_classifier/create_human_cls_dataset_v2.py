#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/19 11:29
# <AUTHOR> <EMAIL>
# @FileName: create_human_cls_dataset

import random
from PIL import Image
from shutil import copy2

from tqdm import tqdm
from pathlib import Path
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks
from modules.utils.image_utils import get_all_image_path


# 初始化检测模型
model_id = 'damo/cv_hrnetw48_human-wholebody-keypoint_image'
wholebody_2d_keypoints = pipeline(Tasks.human_wholebody_keypoint, model=model_id)


def save_image_to_folder(image_path, save_dir, category, dataset_type, idx):
    part_num = idx // 500 + 1
    img_name = f"{idx % 500 + 1}.jpg"
    part_dir = save_dir / dataset_type / f"part_{part_num:04d}" / category
    part_dir.mkdir(parents=True, exist_ok=True)
    copy2(image_path, part_dir / img_name)


def cal_max_face_ratio(image_path):
    image_path = str(image_path)
    image = Image.open(image_path)
    width, height = image.width, image.height

    try:
        result = wholebody_2d_keypoints(image_path)
        keypoints = result['keypoints']
        boxes = result['boxes']
    except IndexError:
        keypoints = []
        boxes = []
    except Exception:
        print(f"发生未知错误：{image_path}")
        return None

    face_max_area = 0
    for keypoint in keypoints:
        left_eyebrow = keypoint[42]
        if left_eyebrow[0] < 0 or left_eyebrow[1] < 0:
            continue
        right_eyebrow = keypoint[47]
        if right_eyebrow[0] < 0 or right_eyebrow[1] < 0:
            continue
        left_cheek = keypoint[23]
        if left_cheek[0] < 0 or left_cheek[1] < 0:
            continue
        right_cheek = keypoint[39]
        if right_cheek[0] < 0 or right_cheek[1] < 0:
            continue
        chin = keypoint[31]
        if chin[0] < 0 or chin[1] < 0:
            continue

        face_width = abs(right_cheek[0] - left_cheek[0])
        face_height = abs((left_eyebrow[1] + right_eyebrow[1]) / 2 - chin[1])
        area = face_width * face_height
        face_max_area = max(face_max_area, area)

    face_area_ratio = 100 * face_max_area / (width * height)
    return face_area_ratio


def process_images(src_img_pathes, save_dir, limit):
    human_train_count, no_human_train_count = 0, 0
    human_test_count, no_human_test_count = 0, 0
    idx_train_human, idx_train_no_human, idx_test_human, idx_test_no_human, idx_uncertain = 0, 0, 0, 0, 0

    # 处理图像并打印进度
    for img_path in tqdm(src_img_pathes, desc="Processing images", unit="image"):
        # 训练集和测试集的终止条件
        if (human_train_count >= limit['train'] and no_human_train_count >= limit['train'] and
                human_test_count >= limit['test'] and no_human_test_count >= limit['test']):
            break

        # 归档数据时的最终判断依据
        uncertain = False
        has_person = False
        max_face_area_ratio = cal_max_face_ratio(img_path)

        if max_face_area_ratio is None:
            continue

        if max_face_area_ratio == 0:
            has_person = False
        elif 1 <= max_face_area_ratio <= 5:  # 人脸占比1%~5%
            has_person = True
        else:
            uncertain = True

        if uncertain:
            dataset_type = 'backup'
            category = 'uncertain'
            idx = idx_uncertain
            idx_uncertain += 1
            save_image_to_folder(img_path, save_dir, category, dataset_type, idx)
            continue

        if has_person:
            category = 'human'
            if human_train_count < limit['train']:
                dataset_type = 'train'
                idx = idx_train_human
                human_train_count += 1
                idx_train_human += 1
            elif human_test_count < limit['test']:
                dataset_type = 'test'
                idx = idx_test_human
                human_test_count += 1
                idx_test_human += 1
            else:
                continue
        else:
            category = 'no_human'
            if no_human_train_count < limit['train']:
                dataset_type = 'train'
                idx = idx_train_no_human
                no_human_train_count += 1
                idx_train_no_human += 1
            elif no_human_test_count < limit['test']:
                dataset_type = 'test'
                idx = idx_test_no_human
                no_human_test_count += 1
                idx_test_no_human += 1
            else:
                continue

        # 保存图片到对应的目录
        save_image_to_folder(img_path, save_dir, category, dataset_type, idx)


def main():
    src_img_dirs = [
        ("/mnt/appdata_nfs/dataset/open/coco2017", -1),
        ("/mnt/appdata_nfs/dataset/open/DIV2K_train_HR", -1),
        ("/mnt/appdata_nfs/dataset/open/Flickr2K", -1),
        ("/mnt/appdata2/dataset/BG-20K/train", -1)
    ]
    additional_img_dirs = [
        ("/mnt/appdata2/dataset/kantu_classifier/tmp/raw_image_15w_v202408201630", 150000)
    ]
    save_dir = Path("/mnt/appdata2/dataset/kantu_classifier/cls_human_v202408202000")

    src_img_pathes = []
    for _dir in src_img_dirs:
        _dir, select_num = _dir
        cur_img_pathes = get_all_image_path(_dir, recursive=True, path_op=Path)
        if select_num != -1:
            random.shuffle(cur_img_pathes)
            cur_img_pathes = cur_img_pathes[:select_num]
        src_img_pathes += cur_img_pathes
    random.shuffle(src_img_pathes)
    print(f"标准图源问题：{len(src_img_pathes)}")

    add_img_pathes = []
    for _dir in additional_img_dirs:
        _dir, select_num = _dir
        cur_img_pathes = get_all_image_path(_dir, recursive=True, path_op=Path)
        if select_num != -1:
            random.shuffle(cur_img_pathes)
            cur_img_pathes = cur_img_pathes[:select_num]
        add_img_pathes += cur_img_pathes
    random.shuffle(add_img_pathes)
    print(f"扩展图源问题：{len(add_img_pathes)}")

    src_img_pathes += add_img_pathes[0:10000]
    random.shuffle(src_img_pathes)
    src_img_pathes += add_img_pathes[10000::]

    limit = {
        'train': 10500,
        'test': 1050
    }

    process_images(src_img_pathes, save_dir, limit)


if __name__ == "__main__":
    main()
