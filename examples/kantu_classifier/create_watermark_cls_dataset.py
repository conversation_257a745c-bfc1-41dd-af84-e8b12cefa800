#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/21 15:04
# <AUTHOR> <EMAIL>
# @FileName: create_watermark_cls_dataset

import os
import random
from datasets import DatasetDict, concatenate_datasets
from PIL import Image
from io import BytesIO
from tqdm import tqdm  # 导入进度条库


def get_src_dataset(data_dir, select_num=-1, seed=-1):
    dataset = []
    if seed == -1:
        seed = random.randint(1, 2000000)

    # 根据条件筛选数据集
    cur_dataset = DatasetDict.load_from_disk(data_dir)['train']
    filter_conditions = {"crop_type": "local_crop"}
    cur_dataset = cur_dataset.filter(
        lambda example: all(example[key] == value for key, value in filter_conditions.items())
    )

    if select_num > 0:
        cur_dataset = cur_dataset.shuffle(seed=seed)
        cur_dataset = cur_dataset.select(range(select_num))
    dataset.append(cur_dataset)
    dataset = concatenate_datasets(dataset)
    dataset = dataset.shuffle(seed=seed)

    return dataset


def save_image(image_bytes, save_path):
    """将字节流保存为图片"""
    image = Image.open(BytesIO(image_bytes)).convert('RGB')
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    image.save(save_path)


def save_dataset(dataset, save_dir, dataset_type, start_idx=0):
    """按指定分组将数据集样本保存为图像文件"""
    part_idx = 1
    img_idx = start_idx
    for i, sample in enumerate(tqdm(dataset, desc=f"Saving {dataset_type} dataset")):
        clean_image_bytes = sample['target_image_bytes']
        watermarked_image_bytes = sample['watermarked_image_bytes']

        if i % 500 == 0 and i > 0:
            part_idx += 1

        clean_save_path = os.path.join(
            save_dir, dataset_type, f'part_{part_idx:04d}', 'clean', f'{img_idx + 1:06d}.jpg')
        watermarked_save_path = os.path.join(
            save_dir, dataset_type, f'part_{part_idx:04d}', 'watermarked', f'{img_idx + 1:06d}.jpg')

        save_image(clean_image_bytes, clean_save_path)
        save_image(watermarked_image_bytes, watermarked_save_path)

        img_idx += 1


def main():
    save_dir = "/mnt/appdata2/dataset/kantu_classifier/release/cls_watermarked_v202408271510"
    source_dataset_dir = "/mnt/appdata/tmp/release/watermark_dataset_many-size_v202408271140_hf"

    # 获取训练集和测试集
    train_num, test_num = 10000, 1000
    source_dataset = get_src_dataset(source_dataset_dir, train_num + test_num)
    train_dataset = source_dataset.select(range(train_num))
    test_dataset = source_dataset.select(range(train_num, train_num + test_num))

    # 保存训练集和测试集
    save_dataset(train_dataset, save_dir, 'train')
    save_dataset(test_dataset, save_dir, 'test', start_idx=train_num)  # 测试集全局索引从10000开始


if __name__ == '__main__':
    main()
