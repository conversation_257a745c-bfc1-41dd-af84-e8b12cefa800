#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/16 10:19
# <AUTHOR> <EMAIL>
# @FileName: restore_texted_image

import random
from PIL import Image
from tqdm import tqdm
from pathlib import Path
from modules.utils.image_utils import get_all_image_path

src_img_dirs = [
    ("/mnt/appdata2/dataset/kantu_classifier/tmp/normal_and_texted_samples/normal", -1),
]
src_img_pathes = []
for _dir in src_img_dirs:
    _dir, select_num = _dir
    cur_img_pathes = get_all_image_path(_dir, recursive=True, path_op=Path)
    if select_num != -1:
        random.shuffle(cur_img_pathes)
        cur_img_pathes = cur_img_pathes[:select_num]
    src_img_pathes += cur_img_pathes
random.shuffle(src_img_pathes)
print(f"总数图源：{len(src_img_pathes)}")

label = "normal"
new_save_dir = "/mnt/appdata2/dataset/kantu_classifier/cls_texted_v202408201512"

# 创建保存目录
train_dir = Path(new_save_dir) / "train" / label
test_dir = Path(new_save_dir) / "test" / label
train_dir.mkdir(parents=True, exist_ok=True)
test_dir.mkdir(parents=True, exist_ok=True)

def save_images(image_paths, save_dir, start_idx=0, part_size=500):
    for idx, img_path in enumerate(tqdm(image_paths, desc=f"Processing {save_dir}")):
        # 打开图片并转换为RGB
        with Image.open(img_path).convert("RGB") as img:
            # 计算当前part目录
            part_idx = (start_idx + idx) // part_size + 1
            part_dir = save_dir / f"part_{part_idx:04d}"
            part_dir.mkdir(parents=True, exist_ok=True)
            # 保存图片，文件名为当前总索引
            img.save(part_dir / f"{start_idx + idx + 1}.jpg", "JPEG")

# 随机采样n张训练集，n张测试集
train_images = src_img_pathes[:10000]
test_images = src_img_pathes[10000:11000]

# 保存图片并显示进度条
save_images(train_images, train_dir, start_idx=0)
save_images(test_images, test_dir, start_idx=10000)

print("图片处理完成")
