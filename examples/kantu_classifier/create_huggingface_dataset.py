#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/22 15:55
# <AUTHOR> <EMAIL>
# @FileName: create_huggingface_dataset

import os
import random
from pathlib import Path
from datasets import load_dataset, DatasetDict, concatenate_datasets

from modules.utils.log import LOGGER


def make_example_map_func(root_dir):
    def fun(example):
        image_path = os.path.join(root_dir, example['image_path'])
        with open(image_path, 'rb') as f:
            example['image_bytes'] = f.read()
        return example

    return fun


if __name__ == '__main__':
    source_dataset_dir = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/release_v20241106"
    dataset_save_dir = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/release_hf/v202411061545"

    all_dataset_jsonl = Path(source_dataset_dir).glob('*.jsonl')
    all_dataset_jsonl = [jsonl for jsonl in all_dataset_jsonl if not jsonl.stem.startswith('.')]

    datasets_by_mode = {}
    for dataset_info_jsonl in all_dataset_jsonl:
        if dataset_info_jsonl.parent.stem.startswith('.'):
            continue
        if dataset_info_jsonl.stem.startswith('.'):
            continue

        data_mode = dataset_info_jsonl.stem
        LOGGER.info(f"{data_mode}: {dataset_info_jsonl}")

        # 定义映射函数
        map_fun = make_example_map_func(str(dataset_info_jsonl.parent))

        # 加载数据集
        hf_dataset = load_dataset('json', data_files=str(dataset_info_jsonl))
        hf_dataset = hf_dataset.map(map_fun)

        if data_mode not in datasets_by_mode:
            datasets_by_mode[data_mode] = []
        datasets_by_mode[data_mode].append(hf_dataset['train'])

    hf_datasets = {}
    for data_mode, datasets in datasets_by_mode.items():
        combined_dataset = concatenate_datasets(datasets)
        combined_dataset = combined_dataset.shuffle(seed=random.randint(0, 12580))  # 随机打乱数据集
        hf_datasets[data_mode] = combined_dataset

    hf_datasets = DatasetDict(hf_datasets)

    # 保存数据集
    hf_datasets.save_to_disk(dataset_save_dir)
    print(f"{dataset_save_dir}: success")
    print(hf_datasets)
