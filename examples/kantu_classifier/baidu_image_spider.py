import os
import time
import hashlib
import urllib.request
from PIL import Image
from io import BytesIO
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from threading import Timer


class SeleniumCrawler:
    def __init__(self, save_dir="./images", delay=0.1, images_per_part=500, md5_file="downloaded_md5.txt"):
        self.save_dir = save_dir
        self.time_sleep = delay
        self.images_per_part = images_per_part  # 每组图片的数量限制
        self.image_count = 0  # 累计下载当前 part 的图片数
        self.part_count = 1  # 当前 part 序号
        self.md5_file = os.path.join(self.save_dir, md5_file)  # MD5 记录文件路径
        self.downloaded_md5_set = set()  # 已下载图片的 MD5 哈希集合

        # 初始化Chrome driver
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # 无头模式，不打开浏览器窗口
        chrome_options.add_argument("--disable-gpu")
        service = Service(executable_path='/usr/local/bin/chromedriver')  # 指定 chromedriver 路径
        self.driver = webdriver.Chrome(service=service, options=chrome_options)

        # 检查保存目录
        if not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)

        # 加载已下载的 MD5 记录
        self._load_downloaded_md5()

        # 统计已有的有效图片数量
        self._count_existing_images()

        # 初始化第一个 part 目录
        self._create_new_part_dir()

    def _load_downloaded_md5(self):
        """加载已下载的 MD5 记录文件"""
        if os.path.exists(self.md5_file):
            with open(self.md5_file, 'r') as f:
                for line in f:
                    md5_hash = line.strip()
                    if md5_hash:
                        self.downloaded_md5_set.add(md5_hash)
            print(f"加载了 {len(self.downloaded_md5_set)} 个已下载的 MD5 记录")

    def _count_existing_images(self):
        """统计当前保存目录下的有效图片数量"""
        total_images = 0
        for root, dirs, files in os.walk(self.save_dir):
            for file in files:
                if file.endswith(('.jpg', '.jpeg', '.png', '.bmp', '.gif')):
                    total_images += 1
        print(f"目标目录中已有 {total_images} 张图片")

    def _create_new_part_dir(self):
        """创建新的 part 目录"""
        part_dir_name = f"part_{self.part_count:04d}"
        self.current_save_dir = os.path.join(self.save_dir, part_dir_name)
        if not os.path.exists(self.current_save_dir):
            os.makedirs(self.current_save_dir)
        print(f"创建新目录: {self.current_save_dir}")
        self.part_count += 1

    def get_md5(self, content):
        """计算MD5哈希值"""
        md5 = hashlib.md5()
        md5.update(content)
        return md5.hexdigest()

    def save_image(self, img_url, word):
        """保存图片，并使用RGB的MD5作为文件名"""
        try:
            # 下载图片内容
            with urllib.request.urlopen(img_url) as response:
                image_data = response.read()

            # 使用PIL读取图片
            image = Image.open(BytesIO(image_data))
            # 将图片转换为RGB格式
            image_rgb = image.convert('RGB')

            # 获取RGB数据并计算MD5
            rgb_data = image_rgb.tobytes()
            md5_hash = self.get_md5(rgb_data)

            # 检查 MD5 是否已经下载过
            if md5_hash in self.downloaded_md5_set:
                print(f"图片已下载过 (MD5: {md5_hash})，跳过")
                return

            # 保存路径
            filepath = os.path.join(self.current_save_dir, f"{md5_hash}.jpg")

            # 保存图片
            image_rgb.save(filepath, 'JPEG')
            print(f"图片已保存: {filepath}")
            self.image_count += 1  # 增加计数

            # 将 MD5 记录到文件中，防止重复下载
            with open(self.md5_file, 'a') as f:
                f.write(f"{md5_hash}\n")

            # 同时更新内存中的 MD5 集合
            self.downloaded_md5_set.add(md5_hash)

            # 如果当前 part 目录下的图片数量达到了 images_per_part 的限制，创建新的目录
            if self.image_count >= self.images_per_part:
                print(f"达到 {self.images_per_part} 张图片，创建新 part 目录")
                self.image_count = 0  # 重置计数
                self._create_new_part_dir()

        except Exception as e:
            print(f"保存图片失败: {e}")

    def crawl(self, word):
        """抓取图片"""
        search_url = f"https://image.baidu.com/search/index?tn=baiduimage&word={word}"
        print(f"正在访问: {search_url}")
        self.driver.get(search_url)

        # 等待页面加载
        time.sleep(2)

        # 持续下滑页面，加载更多图片，直到没有更多资源
        while True:
            # 查找图片元素
            img_elements = self.driver.find_elements(By.XPATH, '//img[contains(@src, "http")]')

            # 记录找到的图片数量
            found_images = len(img_elements)
            print(f"找到 {found_images} 张图片")

            if found_images == 0:
                print("没有找到新的图片，结束爬取")
                break

            # 下载图片
            for img_element in img_elements:
                img_url = img_element.get_attribute('src')
                if img_url:
                    print(f"下载图片: {img_url}")
                    self.save_image(img_url, word)

            # 模拟下滑加载更多图片
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(self.time_sleep)

    def close(self):
        """关闭Selenium driver"""
        self.driver.quit()


def crawl_with_timeout(crawler, keyword, timeout):
    """带超时控制的抓取函数"""
    timer = Timer(timeout, crawler.close)  # 设置超时关闭driver
    timer.start()  # 开始计时

    try:
        crawler.crawl(keyword)  # 抓取图片
    except Exception as e:
        print(f"爬取过程中出现错误: {e}")
    finally:
        crawler.close()  # 确保 driver 被关闭
        timer.cancel()  # 如果抓取完成，取消计时器


if __name__ == '__main__':
    # 将关键词字符串拆分为列表
    # search_keywords = ["证书", "证件", "护照", "执照", "外国身份证", "毕业证", "户口本"]
    # search_keywords = ["驾驶证", "学生证", "社保卡", "签证", "房产证", "学位证", "驾驶执照", "餐饮服务许可证", "医师执照", "律师执照"]
    search_keywords = ["印章文档", "带印章的文档", "文档印象"]
    save_dir = "/Volumes/BigDataSSD/工作数据/看图分类模型/baidu_image_spider/带印章的文档"
    timeout_in_seconds = 1800  # 每个关键词最大爬取时间，单位秒

    for keyword in search_keywords:
        print(f"开始抓取关键词: {keyword}")
        crawler = SeleniumCrawler(save_dir=save_dir, delay=1, images_per_part=500)
        crawl_with_timeout(crawler, keyword, timeout_in_seconds)
        print(f"完成抓取关键词: {keyword}")