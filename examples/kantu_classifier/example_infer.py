#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/27 16:08
# <AUTHOR> <EMAIL>
# @FileName: example_infer

import os
import ncnn
import numpy as np
from PIL import Image

# 该目录包含backbone和各类分类头的bin和param文件
KANTU_CLASSIFIER_MODEL_SRC = "/mnt/workspace/image_lab/models/kantu_classifier_cache/small_kantu_classifier_v202408231900"
BACKBONE_NAME = "mbv3_small_backbone"
SEQ_OUTS = ["cls_texted_head", "cls_human_head", "cls_watermarked_head", "cls_blur_head"]
SEQ_OUT_NAMES = ["是否包含文字", "是否包含人体", "是否包含水印", "是否模糊"]
SEQ_OUT_THRESHOLDS = [0.5, 0.5, 0.5, 0.5]  # 预设阈值


class KantuClassifierPipeline:
    def __init__(self):
        self.model_cache = None

    def __call__(self, img: Image.Image, model_name: str = None):
        self.init_model()

        img_resized, img_cropped = self.process_img(img)

        resized_feats = self.extract_features(img_resized)
        cropped_feats = self.extract_features(img_cropped)

        results = []
        for head_name, task_name, thres in zip(SEQ_OUTS, SEQ_OUT_NAMES, SEQ_OUT_THRESHOLDS):
            features = cropped_feats if head_name == "cls_blur_head" else resized_feats
            score = self.classify(features, head_name)
            results.append([task_name, score > thres, score])

        return results

    def init_model(self):
        if self.model_cache is not None:
            return

        self.model_cache = {}
        for flag in [BACKBONE_NAME] + SEQ_OUTS:
            ncnn_bin_path = os.path.join(KANTU_CLASSIFIER_MODEL_SRC, f"{flag}.ncnn.bin")
            ncnn_param_path = os.path.join(KANTU_CLASSIFIER_MODEL_SRC, f"{flag}.ncnn.param")

            net = ncnn.Net()
            net.load_param(ncnn_param_path)
            net.load_model(ncnn_bin_path)
            self.model_cache[flag] = net

    def process_img(self, img, target_size=(512, 512)):
        img = img.convert("RGB")
        img_resized = img.resize(target_size, Image.LANCZOS)
        img_center_crop = center_crop_pil_images([img], target_size)[0]

        return self.normalize_image(img_resized), self.normalize_image(img_center_crop)

    def extract_features(self, img):
        with self.model_cache[BACKBONE_NAME].create_extractor() as ex:
            ex.input("in0", ncnn.Mat(img).clone())
            _, features = ex.extract("out0")
        return np.asarray(features, dtype=np.float32)

    def classify(self, features, head_name):
        with self.model_cache[head_name].create_extractor() as ex:
            ex.input("in0", ncnn.Mat(features).clone())
            _, output = ex.extract("out0")
        return self.sigmoid(output)

    @staticmethod
    def normalize_image(img):
        img_np = np.asarray(img).astype(np.float32) / 255.0
        mean = np.array([0.485, 0.456, 0.406])
        std = np.array([0.229, 0.224, 0.225])

        # 标准化处理
        img_normalized = (img_np - mean) / std

        # 转置通道顺序并转换为内存连续格式
        img_transposed = np.transpose(img_normalized, (2, 0, 1)).astype(np.float32)
        img_contiguous = np.ascontiguousarray(img_transposed)

        return img_contiguous

    @staticmethod
    def sigmoid(x):
        return 1 / (1 + np.exp(-np.asarray(x, dtype=np.float32)))


def center_crop_pil_images(images, target_size):
    ref_size = images[0].size
    assert all(img.size == ref_size for img in images), "所有图像必须具有相同的尺寸"

    ori_w, ori_h = ref_size
    target_w, target_h = target_size

    resize_ratio = max(target_h / ori_h, target_w / ori_w) if ori_h < target_h or ori_w < target_w else 1.0
    resized_images = [img.resize((int(ori_w * resize_ratio), int(ori_h * resize_ratio)), Image.LANCZOS) for img in images]

    x = (resized_images[0].width - target_w) // 2
    y = (resized_images[0].height - target_h) // 2

    return [img.crop((x, y, x + target_w, y + target_h)) for img in resized_images]


if __name__ == '__main__':
    image_path = "/mnt/workspace/image_lab/models/387.jpg"
    image = Image.open(image_path)

    pipe = KantuClassifierPipeline()
    results = pipe(image)

    for ret in results:
        task_name, is_task_detected, score = ret
        is_task_detected_flag = "是" if is_task_detected else "否"
        print(f"{task_name}: {is_task_detected_flag}, 置信度: {100 * score[0]:.2f}%")
