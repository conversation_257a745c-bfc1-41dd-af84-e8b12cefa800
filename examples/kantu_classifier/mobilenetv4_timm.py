#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/28 10:13
# <AUTHOR> <EMAIL>
# @FileName: mobilenetv4_timm

from urllib.request import urlopen
from PIL import Image
import timm

img = Image.open("/mnt/appdata2/dataset/tmp/1.jpeg")

model = timm.create_model(
    'mobilenetv4_conv_small.e2400_r224_in1k',
    pretrained=True,
    num_classes=0,  # remove classifier nn.Linear
)
model = model.eval()

# get model specific transforms (normalization, resize)
data_config = timm.data.resolve_model_data_config(model)
transforms = timm.data.create_transform(**data_config, is_training=False)

output = model(transforms(img).unsqueeze(0))  # output is (batch_size, num_features) shaped tensor

print(output.shape)

