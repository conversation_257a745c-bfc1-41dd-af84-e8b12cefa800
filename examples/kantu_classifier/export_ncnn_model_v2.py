#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/22 11:35
# <AUTHOR> <EMAIL>
# @FileName: export_ncnn_model_v2

"""
Reference: https://github.com/Tencent/ncnn/wiki/use-ncnn-with-pytorch-or-onnx
"""

import os
import shutil
from pathlib import Path

import pnnx
import ncnn
import torch
import numpy as np
from tqdm import tqdm
from enum import Enum
from PIL import Image
from torchvision import transforms
from torch.utils.data import DataLoader

from modules.utils.train_utils import calculate_fpr_recall_at_thresholds
from modules.utils.torch_utils import convert_batchnorm_to_sync_batchnorm
from networks.mobilenet.model.kantu_classifier import MultiTaskMobileNetV3Small
from networks.mobilenet.model.kantu_classifier_large import MultiTaskMobileNetV3LargeOldVersion
from my_datasets.kantu_classifier.base_dataset_v2 import MyCustomDataset, walk_dataloaders

# 用于模型保存与加载
backbone_name       = "backbone"
texted_fc_name      = "cls_texted_head"
blur_fc_name        = "cls_blur_head"
watermarked_fc_name = "cls_watermarked_head"
human_fc_name       = "cls_human_head"
doc_black_fc_name = "cls_doc_black_head"
doc_blur_fc_name = "cls_doc_blur_head"
doc_scene_fc_name = "cls_doc_scene_head"
doc_shadow_fc_name = "cls_doc_shadow_head"
doc_capture_fc_name = "cls_doc_capture_head"


# 用于加载测试集
class DatasetFlag(Enum):
    CLS_TEXED = 'cls_texted'
    CLS_CLEAR_BLUR = 'cls_clear_blur'
    CLS_WATERMARKED = 'cls_watermarked'
    CLS_HUMAN = 'cls_human'
    CLS_BLACK_NOBLACK = 'cls_black_noblack'
    CLS_DOC_BLUR_AND_NOBLUR = 'cls_doc_blur_and_noblur'
    CLS_DOC_NODOC = 'cls_doc_nodoc'
    CLS_SHADOW_AND_NOSHADOW = 'cls_shadow_and_noshadow'
    CLS_SNAP_CAPTURED = 'cls_snap_captured'

# 定义ImageNet的标准化参数
img_transform = transforms.Compose([
    transforms.Resize((512, 512)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])
sigmoid = torch.nn.Sigmoid()


def infer_and_export_pnnx_files(model_class, ckpt_path=None, fp16=False):
    # 定义模型
    opt_model = model_class()
    opt_model = convert_batchnorm_to_sync_batchnorm(opt_model)

    if ckpt_path is not None:
        state_dict = torch.load(ckpt_path, map_location='cpu')
        opt_model.load_state_dict(state_dict, strict=True)
    opt_model = opt_model.eval()

    x = Image.open(img_path).convert('RGB')
    x = img_transform(x).contiguous()
    x = x.unsqueeze(0)

    outputs = dict()
    with torch.no_grad():
        # 模型Backbone输出
        feats = opt_model.backbone(x)
        outputs[backbone_name] = feats

        # 非文档场景输出
        outputs[texted_fc_name] = opt_model.cls_texted(feats)
        outputs[blur_fc_name] = opt_model.cls_blur(feats)
        outputs[watermarked_fc_name] = opt_model.cls_watermarked(feats)
        outputs[human_fc_name] = opt_model.cls_human(feats)

        # 文档场景输出
        outputs[doc_black_fc_name] = opt_model.cls_doc_black(feats)
        outputs[doc_blur_fc_name] = opt_model.cls_doc_blur(feats)
        outputs[doc_scene_fc_name] = opt_model.cls_doc_scene(feats)
        outputs[doc_shadow_fc_name] = opt_model.cls_doc_shadow(feats)
        outputs[doc_capture_fc_name] = opt_model.cls_doc_capture(feats)

    # 模型Backbone导出
    pnnx.export(opt_model.backbone, os.path.join(save_dir, f"{backbone_name}.pt"), x, fp16=fp16)

    # 非文档场景输出
    pnnx.export(opt_model.cls_texted, os.path.join(save_dir, f"{texted_fc_name}.pt"), feats, fp16=fp16)
    pnnx.export(opt_model.cls_blur, os.path.join(save_dir, f"{blur_fc_name}.pt"), feats, fp16=fp16)
    pnnx.export(opt_model.cls_watermarked, os.path.join(save_dir, f"{watermarked_fc_name}.pt"), feats, fp16=fp16)
    pnnx.export(opt_model.cls_human, os.path.join(save_dir, f"{human_fc_name}.pt"), feats, fp16=fp16)

    # 文档场景导出
    pnnx.export(opt_model.cls_doc_black, os.path.join(save_dir, f"{doc_black_fc_name}.pt"), feats, fp16=fp16)
    pnnx.export(opt_model.cls_doc_blur, os.path.join(save_dir, f"{doc_blur_fc_name}.pt"), feats, fp16=fp16)
    pnnx.export(opt_model.cls_doc_scene, os.path.join(save_dir, f"{doc_scene_fc_name}.pt"), feats, fp16=fp16)
    pnnx.export(opt_model.cls_doc_shadow, os.path.join(save_dir, f"{doc_shadow_fc_name}.pt"), feats, fp16=fp16)
    pnnx.export(opt_model.cls_doc_capture, os.path.join(save_dir, f"{doc_capture_fc_name}.pt"), feats, fp16=fp16)

    all_files = Path(save_dir).glob('*')
    for file in all_files:
        if file.is_dir():
            shutil.rmtree(file)
            continue
        if file.stem.startswith('.'):
            os.remove(file)
            continue
        if not (file.name.endswith('ncnn.bin') or file.name.endswith('ncnn.param')):
            os.remove(file)
            continue

    return outputs


def load_ncnn_and_infer_diff(ori_outputs):
    x = Image.open(img_path).convert('RGB')
    x = img_transform(x).contiguous()
    x = x.unsqueeze(0)

    ncnn_outputs = dict()
    with ncnn.Net() as net:
        net.load_param(os.path.join(save_dir, f"{backbone_name}.ncnn.param"))
        net.load_model(os.path.join(save_dir, f"{backbone_name}.ncnn.bin"))

        with net.create_extractor() as ex:
            ex.input("in0", ncnn.Mat(x.squeeze(0).numpy()).clone())
            _, out0 = ex.extract("out0")
            ncnn_feats = torch.from_numpy(np.asarray(out0)).unsqueeze(0)
            ncnn_outputs[backbone_name] = ncnn_feats

    for head_name in ori_outputs:
        if head_name ==  backbone_name:
            continue

        with ncnn.Net() as net:
            net.load_param(os.path.join(save_dir, f"{head_name}.ncnn.param"))
            net.load_model(os.path.join(save_dir, f"{head_name}.ncnn.bin"))

            with net.create_extractor() as ex:
                ex.input("in0", ncnn.Mat(ncnn_feats.numpy()).clone())
                _, out0 = ex.extract("out0")
                ncnn_outputs[head_name] = torch.from_numpy(np.array(out0))

    ori_feats = ori_outputs[backbone_name]
    print(f"{backbone_name}, feats diff: {(abs(ori_feats - ncnn_feats)).mean():.8f}")

    for head_name, ori_output in ori_outputs.items():
        if head_name == backbone_name:
            continue
        ori_out = ori_output
        ncnn_out = ncnn_outputs[head_name]
        print(f"[{head_name}] ori_out: {ori_out[0][0]:.4f},{sigmoid(ori_out[0][0]):.4f}; "
              f"ncnn_out: {ncnn_out[0][0]:.4f},{sigmoid(ncnn_out[0][0]):.4f}; "
              f"output diff: {(abs(ori_out - ncnn_out)).mean().item():.8f}")


def load_ncnn_and_val_infer(data_dir):
    # 初始化模型
    backbone = ncnn.Net()
    backbone.load_param(os.path.join(save_dir, f"{backbone_name}.ncnn.param"))
    backbone.load_model(os.path.join(save_dir, f"{backbone_name}.ncnn.bin"))

    cls_heads = dict()
    for head_name in [texted_fc_name, watermarked_fc_name, human_fc_name, blur_fc_name, doc_black_fc_name,
                      doc_blur_fc_name, doc_scene_fc_name, doc_shadow_fc_name, doc_capture_fc_name]:
        net = ncnn.Net()
        net.load_param(os.path.join(save_dir, f"{head_name}.ncnn.param"))
        net.load_model(os.path.join(save_dir, f"{head_name}.ncnn.bin"))
        cls_heads[head_name] = net

    # 初始化数据
    _, test_loaders = prepare_dataloaders(data_dir, 'test', 1, 4)

    # 初始化存储所有样本的预测值和标签的列表
    all_preds = {flag.value: [] for flag in DatasetFlag}
    all_labels = {flag.value: [] for flag in DatasetFlag}

    test_dataloader_total_steps = len(test_loaders)
    test_pbar = tqdm(total=test_dataloader_total_steps, desc='testing...')
    for step, (flag, batch) in enumerate(walk_dataloaders(test_loaders)):
        image, label = batch['image'], batch['label']
        image = image.squeeze(0).numpy()
        label = label.squeeze(0).numpy()

        # backbone提取特征
        with backbone.create_extractor() as ex:
            ex.input("in0", ncnn.Mat(image).clone())
            _, out0 = ex.extract("out0")
            feat = torch.from_numpy(np.asarray(out0)).unsqueeze(0)
            feat = feat.numpy()

        # 根据flag选择前馈模型
        if flag == DatasetFlag.CLS_CLEAR_BLUR.value:
            head_net = cls_heads[blur_fc_name]
        elif flag == DatasetFlag.CLS_HUMAN.value:
            head_net = cls_heads[human_fc_name]
        elif flag == DatasetFlag.CLS_WATERMARKED.value:
            head_net = cls_heads[watermarked_fc_name]
        elif flag == DatasetFlag.CLS_TEXED.value:
            head_net = cls_heads[texted_fc_name]
        elif flag == DatasetFlag.CLS_BLACK_NOBLACK.value:
            head_net = cls_heads[doc_black_fc_name]
        elif flag == DatasetFlag.CLS_DOC_BLUR_AND_NOBLUR.value:
            head_net = cls_heads[doc_blur_fc_name]
        elif flag == DatasetFlag.CLS_DOC_NODOC.value:
            head_net = cls_heads[doc_scene_fc_name]
        elif flag == DatasetFlag.CLS_SHADOW_AND_NOSHADOW.value:
            head_net = cls_heads[doc_shadow_fc_name]
        elif flag == DatasetFlag.CLS_SNAP_CAPTURED.value:
            head_net = cls_heads[doc_capture_fc_name]
        else:
            raise ValueError(f"Unrecognized flag: {flag}")

        with head_net.create_extractor() as ex:
            ex.input("in0", ncnn.Mat(feat).clone())
            _, out0 = ex.extract("out0")
            pred = np.asarray(out0)
            pred = sigmoid(torch.tensor(pred))[0][0].item()

        # 将预测值和标签存储下来
        all_preds[flag].append(pred)
        all_labels[flag].append(label[0])
        test_pbar.update(1)

    backbone.clear()
    for _, net in cls_heads.items():
        net.clear()

    test_pbar.close()
    record_file = os.path.join(save_dir, 'roc_record.txt')
    record = open(record_file, "w", encoding='utf-8')
    for flag in all_preds:
        preds = np.asarray(all_preds[flag])
        labels = np.asarray(all_labels[flag])

        # 打印ROC曲线
        fpr, tpr, thresholds = calculate_fpr_recall_at_thresholds(labels, preds, n_thresholds=100)
        msg = f"**************** ROC of {flag} ****************"
        record.write(msg + "\n")
        for i in range(len(thresholds)):
            msg = f"{flag} threshold: {thresholds[i]:.2f}, fpr: {fpr[i]:.2f}, recall: {tpr[i]:.2f}"
            record.write(msg + "\n")
        record.write("\n")
    record.close()
    print(f"ROC results of every task have been saved to {record_file}")


def prepare_dataloaders(data_dir, mode, batch_size, num_workers=1):
    all_datasets = []
    for flag in DatasetFlag:
        all_datasets.append(MyCustomDataset(data_dir, flag.value, mode))

    all_loaders = []
    for flag, cur_dataset in zip(DatasetFlag, all_datasets):
        cur_loader = DataLoader(
            cur_dataset,
            shuffle=True,
            batch_size=batch_size,
            num_workers=num_workers,
        )
        all_loaders.append((flag.value, cur_loader))

    return all_datasets, all_loaders


if __name__ == "__main__":
    fp16 = True
    model_class = MultiTaskMobileNetV3LargeOldVersion
    ckpt = "/aicamera-mlp/xelawk_train_space/tmp/分类模型导出/mblv3_v202410102206_f1_302500/model.bin"

    img_path = "assets/test_images/010002.jpg"
    test_data_dir = "/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/release_v20241106"

    # ---- 开始导出并验证结果 ----
    ncnn_export_dir = f"{'fp16' if fp16 else 'fp32'}_ncnn_{Path(ckpt).parent.stem}"
    save_dir = os.path.join(Path(ckpt).parent.parent, ncnn_export_dir)
    os.makedirs(save_dir, exist_ok=True)

    outputs = infer_and_export_pnnx_files(model_class, ckpt, fp16=fp16)
    load_ncnn_and_val_infer(test_data_dir)

    load_ncnn_and_infer_diff(outputs)
