#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/26 17:23
# <AUTHOR> <EMAIL>
# @FileName: infer

import torch
import numpy as np
from torchvision import transforms

from networks.mobilenetv3.model.kantu_classifier import MultiTaskMobileNetV3Small
from modules.utils.torch_utils import set_seed, convert_batchnorm_to_sync_batchnorm

normalize = transforms.Normalize(
    mean=[0.485, 0.456, 0.406],
    std=[0.229, 0.224, 0.225]
)

ckpt = "/mnt/appdata/tmp/kantuclassifiers/small_kantu_classifier_v202408231900/best_model/model.bin"
model = MultiTaskMobileNetV3Small()
