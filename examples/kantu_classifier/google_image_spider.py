from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os
import wget
import requests


def download_google_images(search_query, num_images=10, output_dir='downloaded_images'):
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 设置Chrome选项
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument('--headless')  # 无界面模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')

    # 初始化Chrome驱动
    driver = webdriver.Chrome(options=chrome_options)

    try:
        # 访问Google图片
        driver.get("https://www.google.com/imghp")

        # 找到搜索框并输入搜索词
        search_box = driver.find_element(By.NAME, "q")
        search_box.send_keys(search_query)
        search_box.send_keys(Keys.RETURN)

        # 等待图片加载
        time.sleep(2)

        # 滚动页面以加载更多图片
        last_height = driver.execute_script("return document.body.scrollHeight")
        while True:
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            new_height = driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                break
            last_height = new_height

        # 获取图片元素
        images = driver.find_elements(By.CSS_SELECTOR, "img.rg_i")
        count = 0

        # 下载图片
        for image in images:
            if count >= num_images:
                break

            try:
                image.click()
                time.sleep(2)

                # 等待大图加载
                large_image = WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "img.n3VNCb"))
                )

                # 获取图片URL
                image_url = large_image.get_attribute('src')

                if image_url and image_url.startswith('http'):
                    # 下载图片
                    response = requests.get(image_url)
                    if response.status_code == 200:
                        file_path = os.path.join(output_dir, f"{search_query}_{count}.jpg")
                        with open(file_path, 'wb') as f:
                            f.write(response.content)
                        count += 1
                        print(f"Downloaded image {count}/{num_images}")

            except Exception as e:
                print(f"Error downloading image: {str(e)}")
                continue

    finally:
        driver.quit()

    print(f"Downloaded {count} images to {output_dir}")


# 使用示例
if __name__ == "__main__":
    search_query = "cute cats"  # 搜索关键词
    num_images = 5  # 需要下载的图片数量
    download_google_images(search_query, num_images)