#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/8/20 11:29
# <AUTHOR> <EMAIL>
# @FileName: label_questioned_samples

import math
import json
from PIL import Image
from tqdm import tqdm
from pathlib import Path
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks
from modules.utils.image_utils import get_all_image_path

# 初始化检测模型
model_id = 'damo/cv_hrnetw48_human-wholebody-keypoint_image'
wholebody_2d_keypoints = pipeline(Tasks.human_wholebody_keypoint, model=model_id)

# 问题样本目录
questioned_sample_dir = "/mnt/appdata2/dataset/kantu_classifier/tmp/参考样本"

# 获取所有样本的路径
all_samples = get_all_image_path(questioned_sample_dir, path_op=Path)

# 遍历每一个样本并保存检测结果为JSON文件
for sample in tqdm(all_samples):
    image = Image.open(sample)
    width, height = image.width, image.height

    try:
        result = wholebody_2d_keypoints(str(sample))
        keypoints = result['keypoints']
        boxes = result['boxes']
    except IndexError:
        keypoints = []
        boxes = []

    face_max_area = 0
    for keypoint in keypoints:
        left_eyebrow = keypoint[42]
        if left_eyebrow[0] < 0 or left_eyebrow[1] < 0:
            continue
        right_eyebrow = keypoint[47]
        if right_eyebrow[0] < 0 or right_eyebrow[1] < 0:
            continue
        left_cheek = keypoint[23]
        if left_cheek[0] < 0 or left_cheek[1] < 0:
            continue
        right_cheek = keypoint[39]
        if right_cheek[0] < 0 or right_cheek[1] < 0:
            continue
        chin = keypoint[31]
        if chin[0] < 0 or chin[1] < 0:
            continue

        face_width = abs(right_cheek[0] - left_cheek[0])
        face_height = abs((left_eyebrow[1] + right_eyebrow[1]) / 2 - chin[1])
        area = face_width * face_height
        face_max_area = max(face_max_area, area)

    face_area_ratio = face_max_area / (width * height)
    face_area_ratio = f"{100 * face_area_ratio:.2f}%"
    output_data = {
        "max_face_area_ratio": face_area_ratio,
    }

    # 生成与图片同名的JSON文件路径
    json_path = sample.with_suffix('.json')

    # 将结果保存为JSON文件
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=4)
