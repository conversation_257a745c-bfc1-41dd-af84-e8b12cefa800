#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/9/24 17:34
# <AUTHOR> <EMAIL>
# @FileName: vis_plane_dataset_v1

import os
import random
from PIL import Image

import torch
import numpy as np
from torch.utils.data import DataLoader

from modules.doc_dewrap.dto import GRID_SIZE, IMG_SIZE
from modules.doc_dewrap.image_utils import visualize_uvdoc_data
from my_datasets.doc_dewarp.plane_dataset_v1 import PlaneDataset


# 设置随机种子
def set_seed(seed=42):
    random.seed(seed)  # Python 内置随机库的种子
    np.random.seed(seed)  # NumPy 的随机种子
    torch.manual_seed(seed)  # PyTorch 的CPU随机种子
    torch.cuda.manual_seed(seed)  # 如果使用GPU，设置GPU的随机种子
    torch.backends.cudnn.deterministic = True  # 确保每次卷积操作的一致性
    torch.backends.cudnn.benchmark = False  # 禁用CuDNN的自动优化


# 调用函数设置种子
set_seed(50)

result_save_dir = "/aicamera-mlp/xelawk_train_space/tmp/PlaneDoc_raw_release2"
os.makedirs(result_save_dir, exist_ok=True)

plane_dataset = PlaneDataset(
    data_path="/aicamera-mlp/xelawk_train_space/datasets/kantu_classifier/release_v20241106/cls_snap_captured_v202411061440",
    split='train',
    img_size=IMG_SIZE,
    grid_size=GRID_SIZE,
    resample_num=100000,
    seed=42,
)

trainloader = DataLoader(
    plane_dataset, batch_size=1, num_workers=0, shuffle=True, pin_memory=True
)

# 统计坐标范围
grid2D_xmin_sum, grid2D_xmax_sum = 0, 0
grid2D_ymin_sum, grid2D_ymax_sum = 0, 0
grid3D_xmin_sum, grid3D_xmax_sum = 0, 0
grid3D_ymin_sum, grid3D_ymax_sum = 0, 0
grid3D_zmin_sum, grid3D_zmax_sum = 0, 0

batch_count = 0

cnt = 0
cnt_limit = 100
for batch in trainloader:
    img_RGB, img_RGB_unwarped, grid2D, grid3D = batch
    print(f"img_RGB: {img_RGB.shape}")
    print(f"img_RGB_unwarped: {img_RGB_unwarped.shape}")
    print(f"grid2D: {grid2D.shape}")
    print(f"grid3D: {grid3D.shape}")

    # 统计 grid2D 的坐标范围
    grid2D_xmin = grid2D[..., 0].min().item()
    grid2D_xmax = grid2D[..., 0].max().item()
    grid2D_ymin = grid2D[..., 1].min().item()
    grid2D_ymax = grid2D[..., 1].max().item()

    grid2D_xmin_sum += grid2D_xmin
    grid2D_xmax_sum += grid2D_xmax
    grid2D_ymin_sum += grid2D_ymin
    grid2D_ymax_sum += grid2D_ymax

    # 统计 grid3D 的坐标范围
    grid3D_xmin = grid3D[..., 0].min().item()
    grid3D_xmax = grid3D[..., 0].max().item()
    grid3D_ymin = grid3D[..., 1].min().item()
    grid3D_ymax = grid3D[..., 1].max().item()
    grid3D_zmin = grid3D[..., 2].min().item()
    grid3D_zmax = grid3D[..., 2].max().item()

    grid3D_xmin_sum += grid3D_xmin
    grid3D_xmax_sum += grid3D_xmax
    grid3D_ymin_sum += grid3D_ymin
    grid3D_ymax_sum += grid3D_ymax
    grid3D_zmin_sum += grid3D_zmin
    grid3D_zmax_sum += grid3D_zmax

    batch_count += 1

    # 可视化和保存图片部分
    data_list = []
    data_titles = []

    # 原始结果
    data_list.append([img_RGB, img_RGB_unwarped, grid2D, grid3D])
    data_titles.append('UVDoc')

    # 可视化结果
    img_pil = Image.fromarray((255. * img_RGB[0]).permute(1, 2, 0).cpu().numpy().astype(np.uint8))
    result_img_pil = visualize_uvdoc_data(data_list, data_titles, dpi=300, show=False)

    cnt += 1
    result_img_pil.save(f"{result_save_dir}/{cnt}.png")

    if cnt >= cnt_limit:
        break

# 计算 grid2D 和 grid3D 坐标范围的均值
grid2D_xmin_avg = grid2D_xmin_sum / batch_count
grid2D_xmax_avg = grid2D_xmax_sum / batch_count
grid2D_ymin_avg = grid2D_ymin_sum / batch_count
grid2D_ymax_avg = grid2D_ymax_sum / batch_count

grid3D_xmin_avg = grid3D_xmin_sum / batch_count
grid3D_xmax_avg = grid3D_xmax_sum / batch_count
grid3D_ymin_avg = grid3D_ymin_sum / batch_count
grid3D_ymax_avg = grid3D_ymax_sum / batch_count
grid3D_zmin_avg = grid3D_zmin_sum / batch_count
grid3D_zmax_avg = grid3D_zmax_sum / batch_count

# 打印平均值
print(f"grid2D 坐标范围均值 - xmin: {grid2D_xmin_avg}, xmax: {grid2D_xmax_avg}, ymin: {grid2D_ymin_avg}, ymax: {grid2D_ymax_avg}")
print(f"grid3D 坐标范围均值 - xmin: {grid3D_xmin_avg}, xmax: {grid3D_xmax_avg}, ymin: {grid3D_ymin_avg}, ymax: {grid3D_ymax_avg}, zmin: {grid3D_zmin_avg}, zmax: {grid3D_zmax_avg}")

"""
grid2D 坐标范围均值 - xmin: -0.9343694794178009, xmax: 0.8019797521829605, ymin: -0.9082134759426117, ymax: 0.8014580509066582
grid3D 坐标范围均值 - xmin: 0.18152807533740997, xmax: 0.7992457503080368, ymin: 0.18650347426533698, ymax: 0.7989165997505188, zmin: 0.19090124145150184, zmax: 0.7985542207956314
"""