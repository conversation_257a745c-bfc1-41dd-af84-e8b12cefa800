#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/6/18 21:15
# <AUTHOR> <EMAIL>
# @FileName: create_hf_datasets

import os
from pathlib import Path
from datasets import load_dataset, DatasetDict

def make_example_map_func(root_dir):
    def fun(example):
        image_file = os.path.join(root_dir, example['image_path'])
        with open(image_file, 'rb') as f:
            example['image_bytes'] = f.read()
            example['filename'] = Path(image_file).name
        return example
    return fun


if __name__ == '__main__':
    dataset_save_dir = "/mnt/appdata/tmp/hres_nowm_custom_docs_hf"
    dataset_dir = "/mnt/appdata/tmp/custom_docs"
    test_dataset_jsonl = os.path.join(dataset_dir, "test.jsonl")
    train_dataset_jsonl = os.path.join(dataset_dir, "train.jsonl")

    # 定义映射函数
    map_fun = make_example_map_func(dataset_dir)

    # 加载测试数据集
    test_dataset = load_dataset('json', data_files=test_dataset_jsonl)
    test_dataset = test_dataset.map(map_fun)
    test_dataset = test_dataset.remove_columns(['image_path'])
    test_dataset = DatasetDict({"test": test_dataset['train']})  # 此处应该是'test'而非'train'

    # 保存测试数据集
    test_dataset_save_dir = os.path.join(dataset_save_dir, "test")
    test_dataset.save_to_disk(test_dataset_save_dir)
    print(f"{test_dataset_save_dir}: success")
    print(test_dataset['test'])

    # 加载训练数据集
    train_dataset = load_dataset('json', data_files=train_dataset_jsonl)
    train_dataset = train_dataset.map(map_fun)
    train_dataset = train_dataset.remove_columns(['image_path'])
    train_dataset = DatasetDict({"train": train_dataset['train']})  # 将'test'改为'train'

    # 保存训练数据集
    train_dataset_save_dir = os.path.join(dataset_save_dir, "train")
    train_dataset.save_to_disk(train_dataset_save_dir)
    print(f"{train_dataset_save_dir}: success")
    print(train_dataset['train'])
