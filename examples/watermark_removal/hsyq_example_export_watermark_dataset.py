#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/7/17 18:03
# <AUTHOR> <EMAIL>
# @FileName: example_export_watermark_dataset

import os
import shutil
from pathlib import Path
from tqdm import tqdm
from omegaconf import OmegaConf

import torch.utils.data
from modules.utils.image_utils import denormalize_tensor_image_rgb, denormalize_mask
from my_datasets.watermark_removal_ai.base_dataset_v2 import (
    WatermarkDataset,
    custom_collate_fn,
    WATERMARK_TYPE_FLAG,
    LOGO_DIR_NAME_FLAG
)


mode = 'train'
expected_resolutions = [512, 1024]
source_dataset_to_export = [
    ("/mnt/aicamera-mlp/xelawk_train_space/datasets/watermark_removal_ai/source_dataset/hres_nowm_baidu_docs_hf", -1),
    ("/mnt/aicamera-mlp/xelawk_train_space/datasets/watermark_removal_ai/source_dataset/hres_nowm_custom_docs_hf", -1),
    ("/mnt/aicamera-mlp/xelawk_train_space/datasets/watermark_removal_ai/source_dataset/HQBG20K_hf", -1),
    ("/mnt/aicamera-mlp/xelawk_train_space/datasets/watermark_removal_ai/source_dataset/hres_nowm_samples_150w_hf", 130000),
]
dataset_config_file = 'configs/watermark_removal_ai/hsyq_dataset_v20240717_cfg.yaml'
export_dir = "/mnt/aicamera-mlp/xelawk_train_space/datasets/tmp/new_watermark_dataset_v202407252045"

for source_dir, select_num in source_dataset_to_export:
    # 配置数据集资源
    source_dataset = os.path.join(source_dir, mode)
    save_dir = os.path.join(export_dir, f"{mode}/{Path(source_dataset).parts[-2]}")

    # 开始制作数据集
    if os.path.exists(save_dir):
        shutil.rmtree(save_dir)
    os.makedirs(save_dir, exist_ok=True)

    train_data_dir = [(source_dataset, select_num)]
    train_dataset = WatermarkDataset(
        resolutions=expected_resolutions,
        data_dirs=train_data_dir,
        mode=mode,
        seed=42,
        dataset_config_file=dataset_config_file
    )

    train_dataloader = torch.utils.data.DataLoader(
        train_dataset,
        shuffle=True,
        batch_size=4,
        num_workers=4,
        collate_fn=lambda batch: custom_collate_fn(batch, train_dataset, debug=True)
    )

    watermark_type_flags = dict()
    for wm_type, idx in WATERMARK_TYPE_FLAG.items():
        watermark_type_flags[idx] = wm_type

    logo_type_flags = dict()
    for logo_name, idx in LOGO_DIR_NAME_FLAG.items():
        logo_type_flags[idx] = logo_name

    cnt = 0
    total_samples = 0
    statistics = dict()

    pbar = tqdm(total=len(train_dataloader), desc=f'{Path(source_dataset).parts[-2]}: processing data...')
    for batch in train_dataloader:
        target_images = batch["target_images"]
        watermarked_images = batch["watermarked_images"]
        watermark_masks = batch["watermark_masks"]
        crop_flags = batch["crop_type_flags"]
        wm_flags = batch["watermark_type_flags"]
        color_reverse_flags = batch["has_reverse_color_flags"]
        logo_dir_name_flag = batch["logo_dir_name_flags"]

        for target_image, watermarked_image, watermark_mask, crop_flag, wm_flag, color_reverse_flag, logo_flag in zip(
                target_images, watermarked_images, watermark_masks, crop_flags, wm_flags, color_reverse_flags, logo_dir_name_flag
        ):
            target_image = denormalize_tensor_image_rgb(target_image)
            watermarked_image = denormalize_tensor_image_rgb(watermarked_image)
            watermark_mask = denormalize_mask(watermark_mask)

            crop_flag = crop_flag.item()
            wm_flag = wm_flag.item()
            color_reverse_flag = color_reverse_flag.item()
            logo_flag = logo_flag.item()
            size = target_image.size[0]

            directory_hierarchy = ""
            if logo_flag != -1:
                logo_flag_name = logo_type_flags[logo_flag]
            else:
                logo_flag_name = "ttf_fonts"
            directory_hierarchy = os.path.join(directory_hierarchy, logo_flag_name)

            directory_hierarchy = os.path.join(directory_hierarchy, f"{size}")

            if crop_flag == 0:
                directory_hierarchy = os.path.join(directory_hierarchy, "max_crop")
            else:
                directory_hierarchy = os.path.join(directory_hierarchy, "local_crop")

            if color_reverse_flag == 1:
                directory_hierarchy = os.path.join(directory_hierarchy, "color_reverse")
            else:
                directory_hierarchy = os.path.join(directory_hierarchy, "default")

            cur_save_dir = os.path.join(save_dir, directory_hierarchy)
            os.makedirs(cur_save_dir, exist_ok=True)

            target_image.save(os.path.join(cur_save_dir, f"{cnt}_target.jpg"))
            watermarked_image.save(os.path.join(cur_save_dir, f"{cnt}_watermarked.jpg"))
            watermark_mask.save(os.path.join(cur_save_dir, f"{cnt}_watermark_mask.jpg"))

            total_samples += 1
            if logo_flag_name in statistics:
                statistics[logo_flag_name] += 1
            else:
                statistics[logo_flag_name] = 1

            cnt += 1

        statistics_final = {'total_samples': total_samples, 'resolutions': expected_resolutions}
        for k, v in statistics.items():
            data = dict()
            data['num_samples'] = v
            data['ratio'] = f"{100 * v / total_samples:.2f}%"
            statistics_final[k] = data

        conf = OmegaConf.create(statistics_final)
        OmegaConf.save(conf, os.path.join(save_dir, "statistics.yaml"))

        pbar.update(1)
