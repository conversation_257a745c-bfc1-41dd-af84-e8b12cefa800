import argparse

def parse_args():
    parser = argparse.ArgumentParser(description="数据保存为hf格式并追加水印并保存为hf格式")
    parser.add_argument(
        "--dataset_dir",
        type=str,
        default=None,
        help="原始数据集路径 jsonl",
    )
    parser.add_argument(
        "--dataset_save_dir_hf",
        type=str,
        default=None,
        help="原始数据保存为hf格式路径 hf dataset，原始路径名称追加_hf，必须_hf结尾",
    )

    parser.add_argument(
        "--dataset_config_file",
        type=str,
        default=None,
        help="数据增加水印的策略配置文件路径",
    )
    parser.add_argument(
        "--export_dir",
        type=str,
        default=None,
        help="增加水印的数据导出路径，原始图像格式（png，jpg，jpeg...）",
    )

    parser.add_argument(
        "--watermark_dataset_save_dir_hf",
        type=str,
        default=None,
        help="水印数据集保存为hf格式路径 ",
    )

    args = parser.parse_args()

    return args