#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/6/20 15:48
# <AUTHOR> <EMAIL>
# @FileName: convert_dir_to_jsonl

import os
import json
import random
from tqdm import tqdm
from pathlib import Path
import sys
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, current_dir)
from modules.utils.image_utils import get_all_image_path


def save_samples_to_jsonl(samples, file_path, description):
    if os.path.exists(file_path):
        os.remove(file_path)
        print(f"已删除文件: {file_path}")

    with open(file_path, "a", encoding='utf-8') as f:
        pbar = tqdm(total=len(samples), desc=description)
        for sample in samples:
            image_path_rel = os.path.join(*sample.parts[-2::])
            data = {"image_path": image_path_rel}
            f.write(f"{json.dumps(data)}\n")
            pbar.update(1)
        pbar.close()


# 主程序入口
if __name__ == "__main__":
    dataset_dir = "/mnt/appdata/tmp/custom_docs"
    all_samples = get_all_image_path(dataset_dir, recursive=True, path_op=Path)

    # 打印前5个样本以验证
    print(f"示例: {all_samples[:5]}")

    # 打乱样本顺序
    random.shuffle(all_samples)
    ratio = 0.1
    test_samples = all_samples[:int(len(all_samples) * ratio)]
    train_samples = all_samples[int(len(all_samples) * ratio):]  # 剩余样本用于训练集

    # 保存测试集和训练集样本到JSONL文件
    save_samples_to_jsonl(test_samples, os.path.join(dataset_dir, "test.jsonl"), "保存测试集到jsonl文件")
    save_samples_to_jsonl(train_samples, os.path.join(dataset_dir, "train.jsonl"), "保存训练集到jsonl文件")

    print("完成保存测试集和训练集。")