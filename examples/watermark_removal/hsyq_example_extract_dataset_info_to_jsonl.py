#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/7/18 10:57
# <AUTHOR> <EMAIL>
# @FileName: example_extract_dataset_info_to_jsonl

import os
import json
from tqdm import tqdm
from pathlib import Path
from modules.utils.image_utils import get_all_image_path


def is_data_vaild(target_image_path: Path):
    base_dir = target_image_path.parent
    image_name = target_image_path.name
    path2check = []
    watermarked_image_path = os.path.join(base_dir, image_name.replace('target', 'watermarked'))
    path2check.append(watermarked_image_path)
    watermark_mask_path = os.path.join(base_dir, image_name.replace('target', 'watermark_mask'))
    path2check.append(watermark_mask_path)

    for p in path2check:
        if not os.path.exists(p):
            return False

    return True


def save_samples_to_jsonl(samples, jsonl_path, description):
    with open(jsonl_path, "a", encoding='utf-8') as f:
        pbar = tqdm(total=len(samples), desc=description)
        for sample in samples:
            if not is_data_vaild(sample):  # 数据不完整则跳过
                pbar.update(1)
                continue

            parts = sample.parts
            dataset_source = parts[-6]
            watermark_type = parts[-5]
            image_size = parts[-4]
            crop_type = parts[-3]
            color_mode = parts[-2]
            image_name = parts[-1]

            rel_dir = os.path.join(*parts[-5:-1])
            target_image_rel_path = os.path.join(rel_dir, image_name)
            watermarked_image_rel_path = os.path.join(rel_dir, image_name.replace('target', 'watermarked'))
            watermark_mask_rel_path = os.path.join(rel_dir, image_name.replace('target', 'watermark_mask'))

            data = {
                "dataset_source": dataset_source,
                "target_image": target_image_rel_path,
                "watermarked_image": watermarked_image_rel_path,
                "watermark_mask": watermark_mask_rel_path,
                "image_size": image_size,
                "watermark_type": watermark_type,
                "crop_type": crop_type,
                "color_mode": color_mode
            }
            f.write(f"{json.dumps(data)}\n")
            pbar.update(1)

        pbar.close()


# 主程序入口
if __name__ == "__main__":
    dataset_dir = "/mnt/aicamera-mlp/xelawk_train_space/datasets/tmp/new_watermark_dataset_v202407232045"

    for data_dir in Path(dataset_dir).iterdir():
        if data_dir.stem.startswith('.'):
            continue
        if not data_dir.is_dir():
            continue
        if data_dir.stem not in ['train', 'test']:
            continue

        for src_data_dir in data_dir.iterdir():
            if src_data_dir.stem.startswith('.'):
                continue
            if not src_data_dir.stem.endswith('hf'):
                continue

            print(f"extracting: {src_data_dir}")
            for part_src_data_dir in src_data_dir.iterdir():
                if part_src_data_dir.stem.startswith('.'):
                    continue
                if not part_src_data_dir.is_dir():
                    continue
                print(part_src_data_dir)

                desc = f"{part_src_data_dir.stem}: extracting info..."
                all_cur_samples = get_all_image_path(part_src_data_dir, recursive=True, path_op=Path, pattern='target')
                jsonl_path = os.path.join(src_data_dir, "dataset_info.jsonl")
                save_samples_to_jsonl(all_cur_samples, jsonl_path, desc)


print(f"All dataset info have been extracted to {jsonl_path}")
