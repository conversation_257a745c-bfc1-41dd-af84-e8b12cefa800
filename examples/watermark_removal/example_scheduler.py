#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/7/17 14:26
# <AUTHOR> <EMAIL>
# @FileName: example_scheduler
import random

from modules.watermark_removal_ai.watermark_scheduler.scheduler import WatermarkScheduler

scheduler = WatermarkScheduler(config_file="configs/watermark_removal_ai/dataset_v20240717_cfg.yaml")
for i in range(10000):
    wm_type = scheduler.generate_watermark_type(random.randint(1, 10000))
