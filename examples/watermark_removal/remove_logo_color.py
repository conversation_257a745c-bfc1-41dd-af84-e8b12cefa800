#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/7/13 23:40
# <AUTHOR> <EMAIL>
# @FileName: color_to_binary

from PIL import Image
import cv2
import numpy as np
from pathlib import Path
from tqdm import tqdm
from modules.utils.image_utils import get_all_image_path

def convert_to_binary(image_path):
    # 打开图片并转换为numpy数组
    image = Image.open(image_path)
    image_np = np.array(image)

    # 分离alpha通道
    if image_np.shape[2] == 4:
        alpha_channel = image_np[:, :, 3]
        image_np = image_np[:, :, :3]
    else:
        alpha_channel = None

    # 转换为灰度图
    gray_image = cv2.cvtColor(image_np, cv2.COLOR_RGB2GRAY)

    # 应用自适应阈值处理实现二值化
    binary_image = cv2.adaptiveThreshold(gray_image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)

    # 转换为PIL图像
    binary_image_pil = Image.fromarray(binary_image)

    # 如果有alpha通道，合并alpha通道
    if alpha_channel is not None:
        binary_image_pil.putalpha(Image.fromarray(alpha_channel))

    return binary_image_pil

src_dir = "/mnt/appdata2/dataset/xelawk/watermark_removal_dataset/release/tmp/custom_very_logo"
dst_dir = "/mnt/appdata2/dataset/xelawk/watermark_removal_dataset/release/tmp/custom_very_logo_binary"

# 创建保存目录
Path(dst_dir).mkdir(parents=True, exist_ok=True)

all_image_path = get_all_image_path(src_dir, recursive=False, path_op=Path)

for image_path in tqdm(all_image_path, desc="Processing images"):
    binary_image = convert_to_binary(image_path)

    # 构建保存路径
    dst_path = Path(dst_dir) / image_path.name
    binary_image.save(dst_path, "PNG")

print("All images have been processed and saved.")
