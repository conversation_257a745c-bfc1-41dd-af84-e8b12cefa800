#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/7/18 17:12
# <AUTHOR> <EMAIL>
# @FileName: example_training_dataloader

import torch.utils.data

from tqdm import tqdm
from my_datasets.watermark_removal_ai.custom_watermark_dataset import WatermarkDataset, walk_dataloaders

hf_dataset_dir = "/mnt/appdata/tmp/release/watermark_removal_dataset_hf"

resolutions = [256, 384, 512]
train_datasets = []

for res in resolutions:
    train_dataset = WatermarkDataset(hf_dataset_dir, res, mode='test')
    train_datasets.append(train_dataset)

train_dataloaders = []
for dataset in train_datasets:
    dataloader = torch.utils.data.DataLoader(
        dataset,
        shuffle=True,
        batch_size=4,
        num_workers=8,
    )
    train_dataloaders.append(dataloader)

total_steps = 0
for loader in train_dataloaders:
    total_steps += len(loader)

epoch = 3
for i in range(epoch):
    pbar = tqdm(total=total_steps, desc=f"Epoch {i+1}")
    for step, batch in enumerate(walk_dataloaders(train_dataloaders)):
        target_image = batch["target_image"]
        watermarked_image = batch["watermarked_image"]
        watermark_mask = batch["watermark_mask"]
        watermark_image = batch["watermark_image"]
        # TODO: do forward and backward
        pbar.update(1)