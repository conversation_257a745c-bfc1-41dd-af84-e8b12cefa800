from datasets import load_from_disk
from io import BytesIO
from PIL import Image


hf_dataset_dir = "/mnt/appdata/tmp/pdf_doc_hf/test"
dataset = load_from_disk(hf_dataset_dir)
print(dataset.keys())
print(dataset["test"][0].keys())

for i in range(10):
    img = Image.open(BytesIO(dataset["test"][i]["image_bytes"])).convert("RGB")
    print(img.size, dataset["test"][i]["filename"], dataset["test"][i]['image_cls'])