#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/7/18 10:55
# <AUTHOR> <EMAIL>
# @FileName: example_restore_huggingface_dataset

import os
import random
from pathlib import Path
from datasets import load_dataset, DatasetDict, concatenate_datasets


def make_example_map_func(root_dir):
    def fun(example):
        target_image_path = os.path.join(root_dir, example['target_image'])
        watermarked_image_path = os.path.join(root_dir, example['watermarked_image'])
        watermark_mask_path = os.path.join(root_dir, example['watermark_mask'])
        example['file_idx'] = int(Path(target_image_path).stem.split('_')[0])
        with open(target_image_path, 'rb') as f:
            example['target_image_bytes'] = f.read()
        with open(watermarked_image_path, 'rb') as f:
            example['watermarked_image_bytes'] = f.read()
        with open(watermark_mask_path, 'rb') as f:
            example['watermark_mask_bytes'] = f.read()
        return example

    return fun


if __name__ == '__main__':
    source_dataset_dir = "/mnt/appdata/tmp/release/watermark_dataset_many-size_v202408271140"
    dataset_save_dir = "/mnt/appdata/tmp/release/watermark_dataset_many-size_v202408271140_hf"

    all_dataset_jsonl = Path(source_dataset_dir).glob('*/*/dataset_info.jsonl')
    all_dataset_jsonl = [jsonl for jsonl in all_dataset_jsonl if not jsonl.stem.startswith('.')]

    datasets_by_mode = {}
    for dataset_info_jsonl in all_dataset_jsonl:
        if dataset_info_jsonl.parent.stem.startswith('.'):
            continue
        if dataset_info_jsonl.stem.startswith('.'):
            continue

        parts = dataset_info_jsonl.parts
        data_mode = parts[-3]
        data_source = parts[-2]
        print(f"{data_mode}: {data_source}, {dataset_info_jsonl}")

        # 定义映射函数
        map_fun = make_example_map_func(str(dataset_info_jsonl.parent))

        # 加载数据集
        hf_dataset = load_dataset('json', data_files=str(dataset_info_jsonl))
        hf_dataset = hf_dataset.map(map_fun)
        hf_dataset = hf_dataset.remove_columns(['target_image', 'watermarked_image', 'watermark_mask'])

        if data_mode not in datasets_by_mode:
            datasets_by_mode[data_mode] = []
        datasets_by_mode[data_mode].append(hf_dataset['train'])

    hf_datasets = {}
    for data_mode, datasets in datasets_by_mode.items():
        combined_dataset = concatenate_datasets(datasets)
        combined_dataset = combined_dataset.shuffle(seed=random.randint(0, 1000))  # 随机打乱数据集
        hf_datasets[data_mode] = combined_dataset

    hf_datasets = DatasetDict(hf_datasets)

    # 保存数据集
    hf_datasets.save_to_disk(dataset_save_dir)
    print(f"{dataset_save_dir}: success")
    print(hf_datasets)
