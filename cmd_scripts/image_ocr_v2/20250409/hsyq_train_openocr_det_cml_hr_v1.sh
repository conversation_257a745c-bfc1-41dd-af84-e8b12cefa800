#!/bin/bash

# -*- coding: utf-8 -*-
# @Time    : 2025/03/27 14:55
# @Modified  : <EMAIL>
# @FileName: hsyq_train_openocr_det_distributed_finetune.sh

# 火山引擎机器学习平台：https://console.volcengine.com/ml-platform，环境变量获取并格式化分布式训练配置
# Node Num      :  ${MLP_WORKER_NUM}
# Node Rank     :  ${MLP_ROLE_INDEX}
# Master Host   :  ${MLP_WORKER_0_HOST}
# Node GPU Num  :  ${MLP_WORKER_GPU}
# All node IP   :  ${MLP_WORKER_ALL_HOSTS}

# 安装固定依赖
pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple \
            -r third_parties/paddleocr/requirements.txt

# 构建GPU设备列表
GPU_LIST=""
for ((i=0; i<${MLP_WORKER_GPU:-4}; i++))
do
    if [ $i -eq 0 ]; then
        GPU_LIST="$i"
    else
        GPU_LIST="$GPU_LIST,$i"
    fi
done

# 自定义环境变量配置
export PYTHONPATH=.
export NCCL_DEBUG=INFO
export NCCL_SOCKET_IFNAME=eth1
export GLOO_SOCKET_IFNAME=eth1
export TRAIN_PORT=29500
export NCCL_TIMEOUT=7200000

# 配置torchrun所需参数
NODES=${MLP_WORKER_NUM}
NODE_RANK=${MLP_ROLE_INDEX}
GPUS_PER_NODE=${MLP_WORKER_GPU}
MASTER_ADDR=${MLP_WORKER_0_HOST}
MASTER_PORT=${TRAIN_PORT}

export LOG_DIR="./torchrun_log/node_${NODE_RANK}_$(date '+%Y%m%d_%H%M%S')"
mkdir -p ${LOG_DIR}

echo "================== PyTorch 分布式训练配置 =================="
echo "主节点地址: ${MASTER_ADDR}:${MASTER_PORT}"
echo "节点数: ${NODES}"
echo "当前节点序号: ${NODE_RANK}"
echo "所有节点IP列表: ${MLP_WORKER_ALL_HOSTS}"
echo "当前节点GPU列表: ${GPU_LIST}"
echo "当前节点GPU数量: ${GPUS_PER_NODE}, 总GPU数量: $((NODES * GPUS_PER_NODE))"
echo "当前日志目录: ${LOG_DIR}"
echo "=========================================================="

# 非主节点延迟30秒等待主节点启动
if [ ${NODE_RANK} -ne 0 ]; then
    echo "非主节点 (NODE_RANK=${NODE_RANK})，延迟30秒等待主节点启动..."
    sleep 30
    echo "延迟结束，开始启动训练..."
fi

# 执行训练命令
torchrun --nnodes=${NODES} \
         --node_rank=${NODE_RANK} \
         --master_addr=${MASTER_ADDR} \
         --master_port=${MASTER_PORT} \
         --nproc_per_node=${GPUS_PER_NODE} \
         --log_dir=${LOG_DIR} \
         training_loops/image_ocr/train_openocr_det.py \
         -c "configs/image_ocr/OpenOCR/20250409/hsyq_effvitsam_db_cml_v1.yaml" \
         -o Global.distributed=true
