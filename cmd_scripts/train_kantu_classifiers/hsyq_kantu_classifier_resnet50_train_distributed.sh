#!/bin/bash

# 程序调度环境设置
export PYTHONPATH=.:src

# 初始化accelerate配置
export ACC_CFG=hsyq_accelerate_rank${MLP_ROLE_INDEX}_config.yaml
python train_accelerate_config_fmt.py && cat ${ACC_CFG}

# 第三方框架缓存环境设置
export AICACHE_DIR=/aicamera-mlp/xelawk_train_space/aicache
export HF_ENDPOINT=https://hf-mirror.com
export HF_HOME=${AICACHE_DIR}/huggingface
export TORCH_HOME=${AICACHE_DIR}/torch
export MODELSCOPE_CACHE=${AICACHE_DIR}/modelscope
export TRANSPACE_DIR=/aicamera-mlp/xelawk_train_space

# 任务启动入口
accelerate launch --config_file ${ACC_CFG} training_loops/kantu_classifier/train_distributed_kantu_classifier.py \
    --model_type resnet50 \
    --train_data_dir ${TRANSPACE_DIR}/datasets/kantu_classifier/release_v20241106 \
    --output_dir ${TRANSPACE_DIR}/training_outputs/release_202411/resnet50_kantu_classifier_distributed_v202411081103 \
    --save_every_n_epoch 100 \
    --num_train_epochs 1000 \
    --train_batch_size 60 \
    --dataloader_num_workers 16 \
    --num_ckpt_to_keep 20 \
    --learning_rate 1e-4 \
    --save_steps 1000 \
    --mixed_precision no \
    --lr_scheduler cosine_with_restarts \
    --lr_num_cycles 1 \
    --dropout 0.2
