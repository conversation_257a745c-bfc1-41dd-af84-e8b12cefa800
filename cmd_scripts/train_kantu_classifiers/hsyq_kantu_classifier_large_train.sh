#!/bin/bash

# 程序调度环境设置
export PYTHONPATH=.

# 第三方框架缓存环境设置
export AICACHE_DIR=/aicamera-mlp/xelawk_train_space/aicache
export HF_ENDPOINT=https://hf-mirror.com
export HF_HOME=${AICACHE_DIR}/huggingface
export TORCH_HOME=${AICACHE_DIR}/torch
export MODELSCOPE_CACHE=${AICACHE_DIR}/modelscope
export TRANSPACE_DIR=/aicamera-mlp/xelawk_train_space

export CUDA_VISIBLE_DEVICES=0

# 任务启动入口
accelerate launch training_loops/kantu_classifier/train_kantu_classifier.py \
    --model_type mbv3_large \
    --train_data_dir ${TRANSPACE_DIR}/datasets/kantu_classifier/release_hf/v202410091945 \
    --output_dir ${TRANSPACE_DIR}/training_outputs/release_202410/large_kantu_classifier_v202410092100 \
    --save_every_n_epoch 100 \
    --num_train_epochs 1000 \
    --train_batch_size 64 \
    --dataloader_num_workers 2 \
    --num_ckpt_to_keep 10 \
    --learning_rate 1e-4 \
    --save_steps 1250 \
    --mixed_precision no \
    --lr_scheduler cosine_with_restarts \
    --lr_num_cycles 1 \
    --dropout 0.2
