#!/bin/bash

export PYTHONPATH=.
export CUDA_VISIBLE_DEVICES=3

python examples/image_ocr/paddle2onnx.py \
       --model_type cls \
       --model_dir /aipdf-mlp/xelawk/models/our_ocr_models/cls \
       --save_file /aipdf-mlp/xelawk/models/our_ocr_models/cls/onnx/default_fp16.onnx \
       --dynamic_shape \
       --format onnx \
       --keep_onnx \
       --simplify \
       --test \
       --fp16