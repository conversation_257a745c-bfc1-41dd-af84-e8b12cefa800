#!/bin/bash

# 程序调度环境设置
export PYTHONPATH=.
export CUDA_VISIBLE_DEVICES=3

# 设定数据集模型，Test or Eval，具体查看指定的配置文件
export MODE="Test"
export CFG="configs/image_ocr/OpenOCR/hsyq_effvitsam_db_ft_v20250402_v3_pretrained.yaml"
export OUTPUT_DIR="/aipdf-mlp/xelawk/debug/v20250402/grid_search_results_openocr_v6"

python training_loops/image_ocr/grid_search_openocr_db_params.py \
       --output_dir ${OUTPUT_DIR} \
       --config ${CFG}
