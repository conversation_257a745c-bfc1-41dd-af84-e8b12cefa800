#!/bin/bash

# 安装固定依赖
pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple \
            -r third_parties/paddleocr/requirements.txt

# 程序调度环境设置
export PYTHONPATH=.

# 设定数据集模型，Test or Eval，具体查看指定的配置文件
export MODE="Eval"
export CFG="configs/image_ocr/OpenOCR/hsyq_effvitsam_db_ft_v20250402_v1.yaml"
export DATA_DIR="/aipdf-mlp/xelawk/train_dataset_release_v4/auto_labeled_adv_icdar_deg_v202503272141/det"
export LABEL_TXT="${DATA_DIR}/det_gt_eval.txt"
export DET_CHECKPOINTS="/aipdf-mlp/xelawk/training_outputs/20250331/release/det_efficientvitsam_db_v2130/epoch_81.pth"

python training_loops/image_ocr/test_openocr_det.py \
       -c ${CFG} \
       -o Global.checkpoints=${DET_CHECKPOINTS} \
          ${MODE}.dataset.data_dir=${DATA_DIR} \
          ${MODE}.dataset.label_file_list=${LABEL_TXT} \
          Global.distributed=false
