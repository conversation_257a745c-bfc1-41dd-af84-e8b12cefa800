#!/bin/bash

# 程序调度环境设置
export PYTHONPATH=.
export CUDA_VISIBLE_DEVICES=1

# 设定数据集模型，Test or Eval，具体查看指定的配置文件
export MODE="Test"
export CFG="configs/image_ocr/ch_PP-OCRv4/ch_PP-OCRv4_det_teacher_local_zero.yml"
export OUTPUT_DIR="/aipdf-mlp/xelawk/debug/v20250310/grid_search_results_full"

python training_loops/image_ocr/grid_search_db_params.py \
       --output_dir ${OUTPUT_DIR} \
       --config ${CFG}
