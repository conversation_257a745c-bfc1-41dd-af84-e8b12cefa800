#!/bin/bash
# -*- coding: utf-8 -*-
# @Time    : 2025/02/26 21:26
# <AUTHOR> <EMAIL>
# @FileName: hsyq_train_ppocr_rec_distributed.sh

# 火山引擎机器学习平台：https://console.volcengine.com/ml-platform，环境变量获取并格式化分布式训练配置
# Node Num      :  ${MLP_WORKER_NUM}
# Node Rank     :  ${MLP_ROLE_INDEX}
# Master Host   :  ${MLP_WORKER_0_HOST}
# Node GPU Num  :  ${MLP_WORKER_GPU}
# All node IP   :  ${MLP_WORKER_ALL_HOSTS}

# 安装固定依赖
pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple \
            -r third_parties/paddleocr/requirements.txt

# 构建GPU设备列表
GPU_LIST=""
for ((i=0; i<${MLP_WORKER_GPU}; i++))
do
    if [ $i -eq 0 ]; then
        GPU_LIST="$i"
    else
        GPU_LIST="$GPU_LIST,$i"
    fi
done

# 自定义环境变量配置
export PYTHONPATH=.
export TRAIN_PORT=5000
export NCCL_DEBUG=INFO
export NCCL_SOCKET_IFNAME=eth1
export GLOO_SOCKET_IFNAME=eth1
export MASTER_ADDR=${MLP_WORKER_0_HOST}:${TRAIN_PORT}

export LOG_DIR="./launch_log/node_${MLP_ROLE_INDEX}_$(date '+%Y%m%d_%H%M%S')"
mkdir -p ${LOG_DIR}

echo "================== Paddle 分布式训练配置 =================="
echo "主节点地址: ${MASTER_ADDR}"
echo "节点数: ${MLP_WORKER_NUM}"
echo "当前节点序号: ${MLP_ROLE_INDEX}"
echo "所有节点IP列表: ${MLP_WORKER_ALL_HOSTS}"
echo "当前节点GPU列表: ${GPU_LIST}"
echo "当前节点GPU数量: ${MLP_WORKER_GPU}, 总GPU数量: $((MLP_WORKER_NUM * MLP_WORKER_GPU))"
echo "当前日志目录: ${LOG_DIR}"
echo "=========================================================="

# 非主节点延迟30秒等待主节点启动
if [ ${MLP_ROLE_INDEX} -ne 0 ]; then
    echo "非主节点 (NODE_RANK=${MLP_ROLE_INDEX})，延迟30秒等待主节点启动..."
    sleep 30
    echo "延迟结束，开始启动训练..."
fi

# 启动训练
python -m paddle.distributed.launch \
      --devices=${GPU_LIST} \
      --nnodes=${MLP_WORKER_NUM} \
      --master=${MASTER_ADDR} \
      --host=${MLP_WORKER_0_HOST} \
      --ips=${MLP_WORKER_ALL_HOSTS} \
      --log_dir=${LOG_DIR} \
      training_loops/image_ocr/train_paddleocr.py \
      -c ./configs/image_ocr/ch_PP-OCRv4/ch_PP-OCRv4_det_teacher_hsyq_zero_v2.yml \
      -o Global.distributed=true \
         Global.use_gpu=true
