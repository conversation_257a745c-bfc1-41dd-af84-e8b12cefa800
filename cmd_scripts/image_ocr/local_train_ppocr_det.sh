#!/bin/bash

# 安装固定依赖
pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple \
            -r third_parties/paddleocr/requirements.txt

# 程序调度环境设置
export PYTHONPATH=.
export NCCL_DEBUG=INFO

python -m paddle.distributed.launch \
       --devices="0,1,2,3" \
       training_loops/image_ocr/train_paddleocr.py \
       -c configs/image_ocr/ch_PP-OCRv4/ch_PP-OCRv4_det_teacher_hsyq_finetune_v2.yml \
       -o Global.distributed=true \
          Global.use_gpu=true