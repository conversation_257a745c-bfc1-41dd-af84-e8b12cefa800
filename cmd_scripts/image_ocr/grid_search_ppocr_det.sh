#!/bin/bash

# 安装固定依赖
pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple \
            -r third_parties/paddleocr/requirements.txt

# 程序调度环境设置
export PYTHONPATH=.
export NCCL_DEBUG=INFO

# 设置搜索参数, 每次实验训练轮数
CONFIG_PATH=configs/image_ocr/ch_PP-OCRv4/ch_PP-OCRv4_det_teacher_grid_search.yml
OUTPUT_DIR=/aipdf-mlp/xelawk/training_outputs/grid_search_results/$(date +%Y%m%d_%H%M%S)
MAX_EPOCHS=10

# 预训练模型路径和别名
PRETRAINED_MODELS=(
    "/aipdf-mlp/xelawk/models/PaddleOCRModels/ch_PP-OCRv4_det_server_train/best_accuracy"
)
MODEL_ALIASES=(
    "finetune"
)

# 参数网格 - 使用min_shrink_ratio和max_shrink_ratio参数替代原来的shrink_ratio
# 启动网格搜索
python training_loops/image_ocr/gs_train_paddleocr.py \
       -c ${CONFIG_PATH} \
       -o ${OUTPUT_DIR} \
       --devices "0,1,2,3" \
       --max_epochs ${MAX_EPOCHS} \
       --min_shrink_ratio 0.40 0.42 0.44 0.46 0.48 0.50 \
       --max_shrink_ratio 0.54 0.56 0.58 0.60 0.62 0.64 \
       --pretrained_models "${PRETRAINED_MODELS[@]}" \
       --model_aliases "${MODEL_ALIASES[@]}"