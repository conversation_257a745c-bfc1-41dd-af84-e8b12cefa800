export PYTHONPATH=.

python examples/image_ocr/api_infer/eval_benchmark.py \
      --gt_dir "/aipdf-mlp/shared/ocr_rec_dataset/benchmarks" \
      --pred_dir "/aipdf-mlp/jiacheng/exp/pipeline_infer/20250514_local" \
      --output_dir "/aipdf-mlp/jiacheng/exp/pipeline_infer/20250514_local/eval_res" \
      --pred_type local;

#python examples/image_ocr/api_infer/eval_benchmark.py \
#      --gt_dir "/aipdf-mlp/shared/ocr_rec_dataset/benchmarks" \
#      --pred_dir "/aipdf-mlp/jiacheng/exp/pipeline_infer/20250429" \
#      --output_dir "/aipdf-mlp/jiacheng/exp/pipeline_infer/20250429/eval_res" \
#      --pred_type dev;

#python examples/image_ocr/api_infer/eval_benchmark.py \
#      --gt_dir "/aipdf-mlp/shared/ocr_rec_dataset/benchmarks" \
#      --pred_dir "/aipdf-mlp/xelawk/debug/ocr_benchmark_results" \
#      --output_dir "/aipdf-mlp/jiacheng/exp/ocr_benchmark_res_v3" \
#      --pred_type third_party;
