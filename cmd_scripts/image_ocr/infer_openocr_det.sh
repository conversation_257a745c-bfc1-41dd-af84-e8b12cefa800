#!/bin/bash

# 程序调度环境设置
export PYTHONPATH=.
export MODE='Test'
export CUDA_VISIBLE_DEVICES=0

#export DET_CHECKPOINTS=/aipdf-mlp/xelawk/training_outputs/20250331/release/det_efficientvitsam_db_v2130/epoch_81.pth
export DET_CHECKPOINTS=/aipdf-mlp/xelawk/training_outputs/20250331/release/det_efficientvitsam_db_v2130/best.pth

# 设定待测试图片路径
export INFER_RESOURCE=/aipdf-mlp/ouhanqi/data/benchmark/det/bench
#export CFG=configs/image_ocr/OpenOCR/hsyq_effvitsam_db_ft_v20250402_v1.yaml
export CFG=/aipdf-mlp/xelawk/training_outputs/20250331/release/det_efficientvitsam_db_v2130/config.yml
#export INFER_RES_SAVE_PATH=/aipdf-mlp/xelawk/debug/ocr_inference/ft_efficient_db_v20250331_e81_r2048
export INFER_RES_SAVE_PATH=/aipdf-mlp/jiacheng/exp/temp/5_3/

python training_loops/image_ocr/infer_openocr_det.py \
       -c ${CFG} \
       -o Global.save_res_dir=$INFER_RES_SAVE_PATH  \
          Global.checkpoints=$DET_CHECKPOINTS \
          Global.infer_img=$INFER_RESOURCE \
          PostProcess.thresh=0.3 \
          PostProcess.box_thresh=0.6 \
          PostProcess.unclip_ratio=1.5 \
          PostProcess.min_text_size=2 \
          PostProcess.min_text_size_delta=4
