#!/bin/bash

# 程序调度环境设置
export PYTHONPATH=.

# 初始化accelerate配置，以及安装必要依赖
pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple \
               omegaconf==2.3.0 \
               huggingface_hub==0.23.2 \
               albucore==0.0.24 \
               albumentations==1.3.1

export ACC_CFG=hsyq_accelerate_rank${MLP_ROLE_INDEX}_config.yaml
python train_accelerate_config_fmt.py && cat ${ACC_CFG}

# 第三方框架缓存环境设置
export AICACHE_DIR=/aipdf-mlp/shared/aicache
export HF_ENDPOINT=https://hf-mirror.com
export HF_HOME=${AICACHE_DIR}/huggingface
export TORCH_HOME=${AICACHE_DIR}/torch
export MODELSCOPE_CACHE=${AICACHE_DIR}/modelscope

export TRAIN_CFG=configs/table_structure_recognition/cycle_centernet/cycle_centernet_config.yaml
export OUTPUT_DIR=/aipdf-mlp/xelawk/training_outputs/tsr_training/cycle-centernet/debug_distributed

# 任务启动入口，配置参考：单卡 RTX 4090，分辨率 1024x1024，batch size 设置为 16
accelerate launch --config_file ${ACC_CFG} training_loops/table_structure_recognition/train_cycle_centernet.py -c ${TRAIN_CFG} \
           -o basic.only_vis_log=true \
              basic.output_dir=${OUTPUT_DIR} \
              training.gradient.clip_norm=false \
              training.gradient.clip_value=35 \
              training.batch_size=32 \
              training.epochs=1000 \
              data.loader.num_workers=32 \
              checkpoint.save.steps=100
