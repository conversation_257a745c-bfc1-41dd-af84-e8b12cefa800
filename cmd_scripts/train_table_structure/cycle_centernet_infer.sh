#!/bin/bash

# Cycle-CenterNet 表格结构识别推理脚本
# 基于训练好的模型对图片进行推理和可视化
#
# 使用方法:
#   1. 修改下面的配置参数
#   2. 运行: bash cmd_scripts/train_table_structure/cycle_centernet_infer.sh
#
# 作者: <EMAIL>
# 时间: 2025-07-30

# 程序调度环境设置
export PYTHONPATH=.

# 安装必要依赖（如果需要）
# 注释掉以下行如果依赖已安装
# pip install -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple \
#                omegaconf==2.3.0 \
#                huggingface_hub==0.23.2 \
#                albucore==0.0.24 \
#                albumentations==1.3.1

# 第三方框架缓存环境设置（可选）
export AICACHE_DIR=/aipdf-mlp/shared/aicache
export HF_ENDPOINT=https://hf-mirror.com
export HF_HOME=${AICACHE_DIR}/huggingface
export TORCH_HOME=${AICACHE_DIR}/torch
export MODELSCOPE_CACHE=${AICACHE_DIR}/modelscope

# ============================================================================
# 推理配置参数
# ============================================================================

# 配置文件路径
export INFER_CFG=configs/table_structure_recognition/cycle_centernet/cycle_centernet_ms_wired_config.yaml

# 模型检查点路径（请根据实际情况修改）
# 示例路径，请修改为您的实际检查点路径
export CHECKPOINT_PATH=${CHECKPOINT_PATH:-"/aipdf-mlp/xelawk/models/tsr_models/cv_dla34_table-structure-recognition_cycle-centernet.pt"}

# 输入图片目录（请根据实际情况修改）
# 示例路径，请修改为您的实际图片目录
export INPUT_DIR=${INPUT_DIR:-"assets/vis4tsr"}

# 输出结果目录
export OUTPUT_DIR="/aipdf-mlp/jiacheng/exp/temp"

# 推理设备
export DEVICE=cuda:0

# 混合精度类型
export MIXED_PRECISION=no

# ============================================================================
# 推理参数配置
# ============================================================================

# 最大处理样本数（设置为空则处理所有图片）
export MAX_SAMPLES=

# 置信度阈值
export CONFIDENCE_THRESHOLD=0.3

# NMS阈值
export NMS_THRESHOLD=0.3

# ============================================================================
# 执行推理
# ============================================================================

echo "============================================================================"
echo "开始 Cycle-CenterNet 表格结构识别推理..."
echo "============================================================================"
echo "配置文件: ${INFER_CFG}"
echo "检查点路径: ${CHECKPOINT_PATH}"
echo "输入目录: ${INPUT_DIR}"
echo "输出目录: ${OUTPUT_DIR}"
echo "推理设备: ${DEVICE}"
echo "混合精度: ${MIXED_PRECISION}"
echo "置信度阈值: ${CONFIDENCE_THRESHOLD}"
echo "NMS阈值: ${NMS_THRESHOLD}"
echo "============================================================================"

# 检查必要文件是否存在
echo "检查必要文件..."

if [ ! -f "${INFER_CFG}" ]; then
    echo "错误: 配置文件不存在: ${INFER_CFG}"
    echo "请确保配置文件路径正确"
    exit 1
fi

if [ ! -e "${CHECKPOINT_PATH}" ]; then
    echo "错误: 检查点不存在: ${CHECKPOINT_PATH}"
    echo "请确保检查点路径正确，可以是文件或目录"
    exit 1
fi

if [ ! -d "${INPUT_DIR}" ]; then
    echo "错误: 输入目录不存在: ${INPUT_DIR}"
    echo "请确保输入目录存在且包含图片文件"
    exit 1
fi

# 检查输入目录中是否有图片文件
IMAGE_COUNT=$(find "${INPUT_DIR}" -type f \( -iname "*.jpg" -o -iname "*.jpeg" -o -iname "*.png" -o -iname "*.bmp" -o -iname "*.tiff" -o -iname "*.tif" \) | wc -l)
if [ "${IMAGE_COUNT}" -eq 0 ]; then
    echo "警告: 输入目录中没有找到支持的图片文件"
    echo "支持的格式: .jpg, .jpeg, .png, .bmp, .tiff, .tif"
    exit 1
fi

echo "找到 ${IMAGE_COUNT} 张图片待处理"

# 创建输出目录
mkdir -p "${OUTPUT_DIR}"

# 执行推理命令
python training_loops/table_structure_recognition/infer_cycle_centernet.py \
    --config "${INFER_CFG}" \
    --checkpoint "${CHECKPOINT_PATH}" \
    --input_dir "${INPUT_DIR}" \
    --output_dir "${OUTPUT_DIR}" \
    --device "${DEVICE}" \
    --mixed_precision "${MIXED_PRECISION}" \
    --confidence_threshold "${CONFIDENCE_THRESHOLD}" \
    --nms_threshold "${NMS_THRESHOLD}" \
    ${MAX_SAMPLES:+--max_samples "${MAX_SAMPLES}"}

# 检查推理结果
if [ $? -eq 0 ]; then
    echo "推理完成！结果保存在: ${OUTPUT_DIR}"
    echo "可视化结果目录结构:"
    ls -la "${OUTPUT_DIR}"
else
    echo "推理失败，请检查错误信息"
    exit 1
fi

# ============================================================================
# 使用说明
# ============================================================================

cat << 'EOF'

使用说明:
1. 修改脚本中的路径配置:
   - CHECKPOINT_PATH: 训练好的模型检查点路径
   - INPUT_DIR: 包含待推理图片的目录
   - OUTPUT_DIR: 推理结果保存目录

2. 可选配置:
   - DEVICE: 推理设备 (cuda:0, cuda:1, cpu等)
   - MIXED_PRECISION: 混合精度类型 (fp16, bf16, no)
   - CONFIDENCE_THRESHOLD: 置信度阈值
   - NMS_THRESHOLD: NMS阈值
   - MAX_SAMPLES: 最大处理样本数

3. 输出结果:
   - step_0/: 包含所有推理结果的可视化图片
   - latest/: 最新推理结果的副本
   - logs/: 推理日志文件

4. 支持的图片格式:
   - .jpg, .jpeg, .png, .bmp, .tiff, .tif

示例用法:
    # 基础推理
    bash cmd_scripts/train_table_structure/cycle_centernet_infer.sh
    
    # 自定义参数
    export CHECKPOINT_PATH=/path/to/your/checkpoint
    export INPUT_DIR=/path/to/your/images
    export OUTPUT_DIR=/path/to/output
    bash cmd_scripts/train_table_structure/cycle_centernet_infer.sh

EOF
