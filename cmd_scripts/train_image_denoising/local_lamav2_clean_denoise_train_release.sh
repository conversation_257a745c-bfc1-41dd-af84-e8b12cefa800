#!/bin/bash

# 程序调度环境设置
export PYTHONPATH=.:src

# 第三方框架缓存环境设置
export AICACHE_DIR=/aicamera-mlp/xelawk_train_space/aicache
export HF_ENDPOINT=https://hf-mirror.com
export HF_HOME=${AICACHE_DIR}/huggingface
export TORCH_HOME=${AICACHE_DIR}/torch
export MODELSCOPE_CACHE=${AICACHE_DIR}/modelscope
export TRANSPACE_DIR=/aicamera-mlp/xelawk_train_space

# 任务启动入口
export CUDA_VISIBLE_DEVICES=0
accelerate launch training_loops/image_denoising/train_lama_rgb_denoise.py \
  --debug \
  --validation_image_dir /aicamera-mlp/xelawk_train_space/tmp/val_denoise_lama_train \
  --clean_doc_dataset_dir /aicamera-mlp/xelawk_train_space/datasets/ai_scanner/MyOnlyClearPDFV1 \
  --output_dir /aicamera-mlp/xelawk_train_space/training_outputs/lama_smart_filter_v2/debug/v202412091730 \
  --res4cleandoc 768 896 1024 \
  --num_train_epochs 10 \
  --train_batch_size 2 \
  --dataloader_num_workers 32 \
  --gen_lr 5e-4 \
  --disc_lr 5e-5 \
  --save_steps 100 \
  --num_ckpt_to_keep 20 \
  --mixed_precision no \
  --lr_num_cycles 1 \
  --lr_scheduler cosine_with_restarts \
  --loss_cfg_yaml configs/image_denoising/lama_denoise/losses_rgb.yaml \
  --generator_cfg_yaml configs/image_denoising/lama_denoise/generator_rgb.yaml \
  --discriminator_cfg_yaml configs/image_denoising/lama_denoise/discriminator.yaml \
  --reuse_checkpoint /aicamera-mlp/xelawk_train_space/aicache/torch/lama_pretrained_official \
  --use_adam \
  --adam_weight_decay 0
