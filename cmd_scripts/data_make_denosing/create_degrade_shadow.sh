#!/bin/bash
export PYTHONPATH=.

# shadow_texture_dir存放黑底阴影模版，不需要进行修改
shadow_texture_dir="/aicamera-mlp/yangyunfei/PrintProject/YFData/dataset/beauty_template"
save_dir="/aipdf-mlp/jiacheng/code/text_render/synth_temp/pre_images"
num_shadow_per_image=1
num_processes=10
batch_size=50

# ----------------------------数据源
source_gt_image_dirs=(
   "/aipdf-mlp/jiacheng/code/text_render/synth_temp/images"
)


# --gamma_type 指定该参数表面数据集gt加了gamma操作，如果没有，请勿指定
# --rename_open 指定是否对图片进行重命名（重新编排名称），如果希望保持原文件名，请勿指定
# --该脚本要求单个数据源中的文件名都是不同的，不适用于有重复的文件名
# --并且该脚本生成的结果，不会按照原本的顺序排列，例如 gt 的 part_0001 中存放了1-500.png，但是合成结果中part_0001中可能包含501-1000.png命名的图片，这是由于未控制多进程的处理顺序


python examples/image_denoising/create_baidu_and_realdae_dataset.py \
    --source_gt_image_dirs "${source_gt_image_dirs[@]}" \
    --shadow_texture_dir "$shadow_texture_dir" \
    --save_dir "$save_dir" \
    --num_shadow_per_image "$num_shadow_per_image" \
    --num_processes "$num_processes" \
    --batch_size "$batch_size" \
    --enable_degrade \
    --enable_shadow


