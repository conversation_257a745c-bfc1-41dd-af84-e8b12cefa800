#!/bin/bash
export PYTHONPATH=.
# Set the paths to your raw dataset and output dataset
#RAW_DATASET_PATH="/aicamera-mlp/yangyunfei/PrintProject/YFData/ai_scanner/UVDoc_raw_custom_v2"
RAW_DATASET_PATH="/aicamera-mlp/yangyunfei/PrintProject/YFData/YF-uvdoc/UVDoc_raw_custom"
OUTPUT_DATASET_PATH="/aicamera-mlp/yangyunfei/PrintProject/YFData/MySmartDocProducedV9/uvdoc-receipt"


# RAW_DATASET_PATH    ：模版文件，不需要进行修改
# OUTPUT_DATASET_PATH ：输出路径
# 执行脚本之前，将 用于合成的 gt 数据源放在 RAW_DATASET_PATH 的 textures 文件夹下
# --n-sample，该参数需要指定为 用于合成的 gt 数据源的数量

python examples/image_denoising/create_uvdoc_enhanced_dataset.py \
  --raw_dataset "$RAW_DATASET_PATH" \
  --output_dataset "$OUTPUT_DATASET_PATH" \
  --name "final_debug_yf_v9_receipt" \
  --n-sample 1080 \
  --img-size 3200 4608 \
  --subprocess 40 \
  --no_color_transfer \
  --enable_texture_degrade \
  --create_smart_filter_dataset
