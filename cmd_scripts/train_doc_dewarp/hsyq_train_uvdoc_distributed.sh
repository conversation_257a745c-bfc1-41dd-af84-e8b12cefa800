#!/bin/bash

source /aicamera-mlp/hz/venv/bin/activate
ln -snf /aicamera-mlp /mnt/aicamera-mlp

# 程序调度环境设置
#export PYTHONPATH=.:src
export PYTHONPATH=.

# 初始化accelerate配置
export ACC_CFG=hsyq_accelerate_rank${MLP_ROLE_INDEX}_config.yaml
python train_accelerate_config_fmt.py && cat ${ACC_CFG}

# 第三方框架缓存环境设置
export AICACHE_DIR=/aicamera-mlp/xelawk_train_space/aicache
export HF_ENDPOINT=https://hf-mirror.com
export HF_HOME=${AICACHE_DIR}/huggingface
export TORCH_HOME=${AICACHE_DIR}/torch
export MODELSCOPE_CACHE=${AICACHE_DIR}/modelscope
export TRANSPACE_DIR=/aicamera-mlp/xelawk_train_space
export NCCL_COLLNET_TIMEOUT=7200000000 #设置nccl 超时设置
export NCCL_IB_TIMEOUT=22
export NCCL_DEBUG=INFO # 查看nccl

# 一张卡 bs=80 64*4=256
# --load_birefNet /mnt/aicamera-mlp/hz_datasets/model_pth/birefNet_doc/model.bin \
# 官网权重 /mnt/aicamera-mlp/hz_datasets/model_pth/uvdoc/official
# 线上权重 /mnt/aicamera-mlp/hz_datasets/model_pth/uvdoc/117300
# --reuse_checkpoint /mnt/aicamera-mlp/hz_datasets/model_pth/uvdoc/117300\
# --num_train_epochs 1000 \# 总的训练步数
# --ep_lambda_img_start 100 图像重建开始的步数
# --resnet_size 选择不同resnet size大小
# 任务启动入口
accelerate launch --config_file ${ACC_CFG} training_loops/doc_dewarp/train_uvdoc.py \
    --doc3d_data_dir /mnt/aicamera-mlp/hz_datasets/UVDOC_dataset/Doc3D_grid \
    --uvdoc_data_dir /mnt/aicamera-mlp/hz_datasets/UVDOC_dataset/UVDoc_final \
    --perspect_data_dir /mnt/aicamera-mlp/hz_datasets/UVDOC_dataset/perspect_grid2 \
    --Doc3D_grid_perspect /mnt/aicamera-mlp/hz_datasets/UVDOC_dataset/Doc3D_grid_perspect2\
    --user_data_uvdoc_final /mnt/aicamera-mlp/hz_datasets/UVDOC_dataset/user_data_uvdoc_final\
    --validation_image_dir /mnt/aicamera-mlp/hz_datasets/UVDOC_dataset/test_samples \
    --grid_locally_enhanced_method 0 \
    --reuse_checkpoint /mnt/aicamera-mlp/hz_datasets/model_pth/uvdoc/117300 \
    --output_dir /mnt/aicamera-mlp/hz_datasets/save_model_result/training_outputs/train_uvdoc/release/v202412231532_resnet_101 \
    --resnet_size 1 \
    --use_grid 0 \
    --use_grid_3d 1 \
    --num_train_epochs 80 \
    --ep_lambda_img_start 10 \
    --train_batch_size 32 \
    --dataloader_num_workers 4 \
    --learning_rate 2e-4 \
    --save_every_n_epoch 10 \
    --save_steps 3000 \
    --crop_tight_p 1 \
    --mixed_precision no \
    --lr_scheduler cosine_with_restarts \
    --lr_num_cycles 1 \
    --lambda_img 1. \
    --lambda_2d 5. \
    --lambda_3d 5. \
    --p_grid_locally_enhanced 0.6 \
    --a_aug visual noise color \
    --g_uvdoc_aug rotate perspective

