#!/bin/bash

# 程序调度环境设置
export PYTHONPATH=.:src

# 初始化accelerate配置
export ACC_CFG=hsyq_accelerate_rank${MLP_ROLE_INDEX}_config.yaml
python train_accelerate_config_fmt.py && cat ${ACC_CFG}

# 第三方框架缓存环境设置
export AICACHE_DIR=/aicamera-mlp/xelawk_train_space/aicache
export HF_ENDPOINT=https://hf-mirror.com
export HF_HOME=${AICACHE_DIR}/huggingface
export TORCH_HOME=${AICACHE_DIR}/torch
export MODELSCOPE_CACHE=${AICACHE_DIR}/modelscope
export TRANSPACE_DIR=/aicamera-mlp/xelawk_train_space

# 任务启动入口
accelerate launch --config_file ${ACC_CFG} training_loops/doc_dewarp/train_uvdoc.py \
    --reuse_checkpoint /aicamera-mlp/xelawk_train_space/training_outputs/train_uvdoc/release/v202410211100/best_model \
    --doc3d_data_dir /aicamera-mlp/xelawk_train_space/datasets/ai_scanner/Doc3D_grid \
    --uvdoc_data_dir /aicamera-mlp/xelawk_train_space/datasets/ai_scanner/UVDoc_final \
    --validation_image_dir /aicamera-mlp/xelawk_train_space/training_outputs/train_uvdoc/test_samples \
    --output_dir /aicamera-mlp/xelawk_train_space/training_outputs/train_uvdoc/release/v202410222150 \
    --num_train_epochs 30 \
    --ep_lambda_img_start 10 \
    --train_batch_size 16 \
    --dataloader_num_workers 32 \
    --learning_rate 2e-4 \
    --save_every_n_epoch 5 \
    --save_steps 3000 \
    --mixed_precision no \
    --lr_scheduler cosine_with_restarts \
    --lr_num_cycles 1 \
    --lambda_img 1. \
    --lambda_2d 5. \
    --lambda_3d 5. \
    --p_grid_locally_enhanced 0.6 \
    --a_aug visual noise color \
    --g_uvdoc_aug rotate perspective
