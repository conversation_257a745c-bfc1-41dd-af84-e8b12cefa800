#!/bin/bash


source /aicamera-mlp/hz/venv/bin/activate
ln -snf /aicamera-mlp /mnt/aicamera-mlp


# 程序调度环境设置
export PYTHONPATH=.

# 初始化accelerate配置
export ACC_CFG=hsyq_accelerate_rank${MLP_ROLE_INDEX}_config.yaml
python train_accelerate_config_fmt.py && cat ${ACC_CFG}

# 第三方框架缓存环境设置
export AICACHE_DIR=/aicamera-mlp/xelawk_train_space/aicache
export HF_ENDPOINT=https://hf-mirror.com
export HF_HOME=${AICACHE_DIR}/huggingface
export TORCH_HOME=${AICACHE_DIR}/torch
export MODELSCOPE_CACHE=${AICACHE_DIR}/modelscope
export TRANSPACE_DIR=/aicamera-mlp/xelawk_train_space
# 原来的配置
#--dataset_dir /aicamera-mlp/hz_datasets/doc_segs_buchong \
#--reuse_checkpoint /mnt/hz/image-lab/models/birefnet_doc_seg \
#--reuse_checkpoint /aicamera-mlp/xelawk_train_space/aicache/torch/birefnet/general_e244 \
# --save_steps 500 \ 每隔500步就验证一波
#--preproc_type 'flip', 'enhance', 'rotate', 'pepper', 'crop'\
# 任务启动入口, 每张L20: bs=4
accelerate launch --config_file ${ACC_CFG} training_loops/image_matting/train_birefnet.py \
    --resolutions 256,384,512,768 \
    --dataset_dir /aicamera-mlp/hz_datasets/doc_segs \
    --reuse_checkpoint /aicamera-mlp/xelawk_train_space/aicache/torch/birefnet/general_e244 \
    --output_dir /mnt/aicamera-mlp/hz_datasets/save_model_result/training_outputs/train_birefnet/release/v202411201926 \
    --validation_image_dir /aicamera-mlp/xelawk_train_space/training_outputs/train_birefnet/test_sample_with_mask_v2/image \
    --preproc_type 'flip,enhance,rotate,pepper,crop'\
    --num_train_epochs 15 \
    --num_ckpt_to_keep 15 \
    --save_every_n_epoch 5 \
    --save_steps 500 \
    --train_batch_size 16 \
    --dataloader_num_workers 32 \
    --learning_rate 1e-6 \
    --mixed_precision no \
    --lr_num_cycles 1 \
    --lr_scheduler cosine_with_restarts
