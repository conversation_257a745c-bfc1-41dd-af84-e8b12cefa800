#!/bin/bash

# 程序调度环境设置
export PYTHONPATH=.

# 第三方框架缓存环境设置
export AICACHE_DIR=/aicamera-mlp/xelawk_train_space/aicache
export HF_ENDPOINT=https://hf-mirror.com
export HF_HOME=${AICACHE_DIR}/huggingface
export TORCH_HOME=${AICACHE_DIR}/torch
export MODELSCOPE_CACHE=${AICACHE_DIR}/modelscope

# --extral_data_paths ${BASE_MODEL_DIR}/datasets/watermark_removal_ai/training_dataset/watermark_dataset_512-1024_v202407280000_hf \
# --extral_data_selects 150000 \

# 任务启动入口
export TRANSPACE_DIR=/aicamera-mlp/fq_proj
export BASE_MODEL_DIR=/aicamera-mlp/xelawk_train_space
accelerate launch training_loops/watermark_removal_ai/train_wm_mvanet_old.py \
    --dataset_dir ${TRANSPACE_DIR}/datasets/watermark_dataset_doc_512-1024_202408140000_hf \
    --output_dir ${TRANSPACE_DIR}/results/mva_doc_20240814 \
    --swin_b_pretrained ${BASE_MODEL_DIR}/aicache/torch/swin_base_patch4_window12_384_22kto1k.pth \
    --resolutions 512,1024 \
    --num_train_epochs 20 \
    --train_batch_size 16 \
    --dataloader_num_workers 4 \
    --learning_rate 5e-5 \
    --num_ckpt_to_keep 20 \
    --save_steps 5000 \
    --validation_image_dir ${TRANSPACE_DIR}/datasets/watermark_test_sub \
    --mixed_precision no \
    --lr_scheduler cosine_with_restarts \
    --lr_num_cycles 2 \
    --lambda_loc_loss 1. \
    --lambda_glb_loss 0.3 \
    --lambda_map_loss 0.3 \
    --seed 2145 \
    --tracker_project_name train_mavnet_doc \
    --gradient_accumulation_steps 4
