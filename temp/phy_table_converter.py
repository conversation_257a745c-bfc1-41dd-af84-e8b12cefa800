"""
物理表格标注转换器
用于将不同格式的表格标注转换为HTML格式，以便使用TEDS评估指标
"""

import json
from typing import List, Dict, Any


def phy_json_to_html(json_file_path: str) -> str:
    """
    将JSON格式的真实标注转换为HTML格式

    Args:
        json_file_path: JSON文件路径
            JSON格式包含以下主要字段:
            - cells: 单元格列表，每个单元格包含:
              - cell_ind: 单元格索引
              - header: 是否为表头
              - content: 单元格内容列表
              - bbox: 边界框坐标 (p1, p2, p3, p4)
              - lloc: 逻辑位置 (start_row, end_row, start_col, end_col)
              - border: 边框样式

    Returns:
        str: HTML格式的表格字符串，只包含逻辑结构信息
            格式: <html><body><table>...</table></body></html>

    Example:
        >>> html = phy_json_to_html('cs_data/wired_optimizer/533771053059540074_table_annotation.json')
        >>> print(html)
        <html><body><table><tr><td>序号</td><td>姓名</td>...</tr>...</table></body></html>
    """
    # 加载JSON文件
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"[ERROR] Failed to read JSON file {json_file_path}: {e}")
        return '<html><body><table></table></body></html>'

    # 1. 解析cells列表
    cells = data.get('cells', [])
    if not cells:
        return '<html><body><table></table></body></html>'

    # 2. 根据lloc信息构建表格逻辑结构
    # 找出最大行列数
    max_row = 0
    max_col = 0
    valid_cells = []

    for cell in cells:
        lloc = cell.get('lloc', {})
        if not lloc:
            continue

        start_row = lloc.get('start_row', 0)
        end_row = lloc.get('end_row', 0)
        start_col = lloc.get('start_col', 0)
        end_col = lloc.get('end_col', 0)

        # 验证坐标有效性
        if (start_row < 0 or end_row < 0 or start_col < 0 or end_col < 0 or
            start_row > end_row or start_col > end_col):
            continue

        max_row = max(max_row, end_row)
        max_col = max(max_col, end_col)

        # 提取文本内容
        content = cell.get('content', [])
        text = ''
        if content and len(content) > 0:
            text = content[0].get('text', '')

        valid_cells.append({
            'start_row': start_row,
            'end_row': end_row,
            'start_col': start_col,
            'end_col': end_col,
            'text': text
        })

    if not valid_cells:
        return '<html><body><table></table></body></html>'

    # 创建表格矩阵，初始化为None
    table_matrix = [[None for _ in range(max_col + 1)] for _ in range(max_row + 1)]

    # 3. 处理单元格合并 (colspan, rowspan) 并填充单元格信息
    for cell in valid_cells:
        start_row = cell['start_row']
        end_row = cell['end_row']
        start_col = cell['start_col']
        end_col = cell['end_col']

        # 计算跨行跨列
        rowspan = end_row - start_row + 1
        colspan = end_col - start_col + 1

        # 创建单元格信息
        cell_info = {
            'text': cell['text'],
            'rowspan': rowspan if rowspan > 1 else None,
            'colspan': colspan if colspan > 1 else None
        }

        # 在起始位置放置单元格信息
        table_matrix[start_row][start_col] = cell_info

        # 在跨行跨列的其他位置标记为占位
        for r in range(start_row, end_row + 1):
            for c in range(start_col, end_col + 1):
                if r != start_row or c != start_col:
                    table_matrix[r][c] = 'OCCUPIED'

    # 4. 生成HTML字符串
    html_parts = ['<html><body><table>']

    for row in table_matrix:
        html_parts.append('<tr>')
        for cell in row:
            if cell is None:
                html_parts.append('<td></td>')
            elif cell == 'OCCUPIED':
                continue
            else:
                attrs = []
                if cell['rowspan']:
                    attrs.append(f'rowspan="{cell["rowspan"]}"')
                if cell['colspan']:
                    attrs.append(f'colspan="{cell["colspan"]}"')

                attr_str = ' ' + ' '.join(attrs) if attrs else ''
                # 只保留逻辑结构信息，不包含文本内容
                html_parts.append(f'<td{attr_str}></td>')

        html_parts.append('</tr>')

    html_parts.append('</table></body></html>')

    return ''.join(html_parts)


def phy_txt_to_html(txt_file_path: str, tolerance_factor: float = 0.1,
                   min_tolerance: float = 2.0, max_tolerance: float = 10.0,
                   boundary_strategy: str = "median") -> str:
    """
    将TXT文件中的物理坐标转换为HTML表格

    Args:
        txt_file_path: TXT文件路径，每行包含一个单元格的四个角点坐标
            格式: x1,y1;x2,y2;x3,y3;x4,y4

    Returns:
        str: HTML格式的表格字符串
            格式: <html><body><table>...</table></body></html>
    """
    # 1. 解析TXT文件
    cells = []
    try:
        with open(txt_file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    # 解析坐标：x1,y1;x2,y2;x3,y3;x4,y4
                    coords = line.split(';')
                    if len(coords) != 4:
                        continue

                    points = []
                    for coord in coords:
                        x, y = map(float, coord.split(','))
                        points.append((x, y))

                    # 计算边界框
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]

                    left = min(x_coords)
                    right = max(x_coords)
                    top = min(y_coords)
                    bottom = max(y_coords)

                    cell = {
                        'id': line_num - 1,
                        'left': left,
                        'right': right,
                        'top': top,
                        'bottom': bottom,
                        'center_x': (left + right) / 2,
                        'center_y': (top + bottom) / 2,
                        'width': right - left,
                        'height': bottom - top
                    }

                    cells.append(cell)

                except (ValueError, IndexError) as e:
                    print(f"解析第{line_num}行失败: {line} - {e}")
                    continue

    except Exception as e:
        print(f"读取文件失败: {e}")
        return '<html><body><table></table></body></html>'

    if not cells:
        return '<html><body><table></table></body></html>'

    # 2. 计算自适应容差（使用传入的参数）
    x_coords = []
    y_coords = []
    for cell in cells:
        x_coords.extend([cell['left'], cell['right']])
        y_coords.extend([cell['top'], cell['bottom']])

    x_coords = sorted(set(x_coords))
    y_coords = sorted(set(y_coords))

    min_x_gap = min([x_coords[i+1] - x_coords[i] for i in range(len(x_coords)-1)]) if len(x_coords) > 1 else 10
    min_y_gap = min([y_coords[i+1] - y_coords[i] for i in range(len(y_coords)-1)]) if len(y_coords) > 1 else 10

    tolerance = max(min_tolerance, min(max_tolerance, min(min_x_gap, min_y_gap) * tolerance_factor))

    # 3. 改进的边界检测（使用聚类算法）
    def merge_boundaries(coords, tol):
        if not coords:
            return []

        sorted_coords = sorted(set(coords))
        clusters = []
        current_cluster = [sorted_coords[0]]

        for coord in sorted_coords[1:]:
            if coord - current_cluster[-1] <= tol:
                current_cluster.append(coord)
            else:
                clusters.append(current_cluster)
                current_cluster = [coord]

        clusters.append(current_cluster)

        # 使用指定策略作为代表值
        boundaries = []
        for cluster in clusters:
            if len(cluster) == 1:
                boundaries.append(cluster[0])
            else:
                if boundary_strategy == "median":
                    boundaries.append(cluster[len(cluster) // 2])
                elif boundary_strategy == "mean":
                    boundaries.append(sum(cluster) / len(cluster))
                elif boundary_strategy == "min":
                    boundaries.append(min(cluster))
                elif boundary_strategy == "max":
                    boundaries.append(max(cluster))
                else:  # 默认使用中位数
                    boundaries.append(cluster[len(cluster) // 2])

        return boundaries

    row_coords = [coord for cell in cells for coord in [cell['top'], cell['bottom']]]
    col_coords = [coord for cell in cells for coord in [cell['left'], cell['right']]]

    row_boundaries = merge_boundaries(row_coords, tolerance)
    col_boundaries = merge_boundaries(col_coords, tolerance)

    # 4. 构建逻辑网格（改进版本，避免冲突）
    rows = len(row_boundaries) - 1
    cols = len(col_boundaries) - 1

    if rows <= 0 or cols <= 0:
        return '<html><body><table></table></body></html>'

    matrix = [[None for _ in range(cols)] for _ in range(rows)]
    cell_positions = {}

    def find_boundary_index(coord, boundaries, tol):
        for i, boundary in enumerate(boundaries):
            if abs(coord - boundary) <= tol:
                return i
        distances = [abs(coord - boundary) for boundary in boundaries]
        return distances.index(min(distances))

    # 填充网格（避免冲突）
    for cell in cells:
        start_row = find_boundary_index(cell['top'], row_boundaries, tolerance)
        end_row = find_boundary_index(cell['bottom'], row_boundaries, tolerance)
        start_col = find_boundary_index(cell['left'], col_boundaries, tolerance)
        end_col = find_boundary_index(cell['right'], col_boundaries, tolerance)

        # 边界检查和修正
        start_row = max(0, min(start_row, rows - 1))
        start_col = max(0, min(start_col, cols - 1))
        end_row = max(start_row, min(end_row - 1, rows - 1))
        end_col = max(start_col, min(end_col - 1, cols - 1))

        # 计算跨度
        row_span = end_row - start_row + 1
        col_span = end_col - start_col + 1

        # 检查冲突
        conflict = False
        for row in range(start_row, end_row + 1):
            for col in range(start_col, end_col + 1):
                if 0 <= row < rows and 0 <= col < cols:
                    if matrix[row][col] is not None:
                        conflict = True
                        break
            if conflict:
                break

        # 如果没有冲突，填充矩阵
        if not conflict:
            for row in range(start_row, end_row + 1):
                for col in range(start_col, end_col + 1):
                    if 0 <= row < rows and 0 <= col < cols:
                        matrix[row][col] = cell['id']

            cell_positions[cell['id']] = {
                'row': start_row,
                'col': start_col,
                'row_span': row_span,
                'col_span': col_span,
                'is_merged': row_span > 1 or col_span > 1
            }

    # 5. 智能HTML生成（只为必要位置生成td）
    html_parts = ['<html><body><table>']
    processed = [[False for _ in range(cols)] for _ in range(rows)]

    # 不再需要空位置检测函数，直接跳过空单元格

    for row_idx in range(rows):
        html_parts.append('<tr>')

        for col_idx in range(cols):
            if processed[row_idx][col_idx]:
                continue

            cell_id = matrix[row_idx][col_idx]

            if cell_id is None:
                # 取消自动填充空单元格，保持TXT和HTML单元格数量一致
                # 不生成空的td标签
                processed[row_idx][col_idx] = True
            else:
                # 处理有内容的单元格
                pos_info = cell_positions.get(cell_id)
                if pos_info and pos_info['row'] == row_idx and pos_info['col'] == col_idx:
                    # 这是合并单元格的起始位置
                    row_span = pos_info['row_span']
                    col_span = pos_info['col_span']

                    # 生成td标签
                    if row_span > 1 and col_span > 1:
                        html_parts.append(f'<td rowspan="{row_span}" colspan="{col_span}"></td>')
                    elif row_span > 1:
                        html_parts.append(f'<td rowspan="{row_span}"></td>')
                    elif col_span > 1:
                        html_parts.append(f'<td colspan="{col_span}"></td>')
                    else:
                        html_parts.append('<td></td>')

                    # 标记被合并的位置
                    for r in range(row_idx, row_idx + row_span):
                        for c in range(col_idx, col_idx + col_span):
                            if r < rows and c < cols:
                                processed[r][c] = True
                else:
                    # 这是被合并单元格的一部分，跳过
                    processed[row_idx][col_idx] = True

        html_parts.append('</tr>')

    html_parts.append('</table></body></html>')
    return ''.join(html_parts)