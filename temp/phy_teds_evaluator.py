import os
import glob
from typing import List, Tuple, Dict, Any

# 导入真实的TEDS库
from table_recognition_metric import TEDS

try:
    from .phy_table_converter import phy_json_to_html, phy_txt_to_html
except ImportError:
    from phy_table_converter import phy_json_to_html, phy_txt_to_html


class TEDSEvaluator:
    """
    TEDS评估器，用于批量计算原始标注和预测标注之间的TEDS分数
    """
    
    def __init__(self, save_pred_html: bool = False, html_output_dir: str = "pred_html_outputs"):
        self.teds = TEDS()
        self.save_pred_html = save_pred_html
        self.html_output_dir = html_output_dir
    
    def find_matching_files(self, gt_dir: str, pred_dir: str) -> List[Tuple[str, str]]:
        """
        在两个目录中找到匹配的文件对
        
        Args:
            gt_dir: 原始标注目录（JSON文件）
            pred_dir: 预测标注目录（TXT文件）
            
        Returns:
            匹配的文件对列表 [(gt_file, pred_file), ...]
        """
        matching_pairs = []
        
        # 获取所有JSON文件
        json_files = glob.glob(os.path.join(gt_dir, "*.json"))
        
        for json_file in json_files:
            # 从JSON文件名提取基础名称
            base_name = os.path.basename(json_file)
            
            # 尝试不同的匹配模式
            possible_pred_names = []
            
            # 模式1: 直接替换扩展名
            possible_pred_names.append(base_name.replace('.json', '.txt'))
            
            # 模式2: 移除_table_annotation.json后缀，添加.txt
            if base_name.endswith('_table_annotation.json'):
                image_name = base_name.replace('_table_annotation.json', '')
                possible_pred_names.extend([
                    f"{image_name}.txt",
                    f"{image_name}.jpg.txt",
                    f"{image_name}.png.txt"
                ])
            
            # 查找匹配的预测文件
            for pred_name in possible_pred_names:
                pred_file = os.path.join(pred_dir, pred_name)
                if os.path.exists(pred_file):
                    matching_pairs.append((json_file, pred_file))
                    break
        
        return matching_pairs

    def _save_pred_html_file(self, pred_file: str, pred_html: str, conversion_rounds: int):
        """
        保存预测HTML的最终版本

        Args:
            pred_file: 预测标注文件路径
            pred_html: 预测的HTML内容
            conversion_rounds: HTML转换轮数
        """
        import os

        # 创建输出目录
        os.makedirs(self.html_output_dir, exist_ok=True)

        # 获取文件基础名称
        base_name = os.path.splitext(os.path.basename(pred_file))[0]

        # 保存预测HTML，文件名包含转换轮数信息
        pred_output_path = os.path.join(self.html_output_dir, f"{base_name}_pred_final_rounds{conversion_rounds}.html")
        with open(pred_output_path, 'w', encoding='utf-8') as f:
            f.write(pred_html)

        print(f"Saved final prediction HTML: {pred_output_path}")
    
    def evaluate_single_pair(self, gt_file: str, pred_file: str, debug: bool = False,
                           optimize_low_scores: bool = True, score_threshold: float = 0.9) -> float:
        """
        评估单个文件对的TEDS分数

        Args:
            gt_file: 原始标注文件路径
            pred_file: 预测标注文件路径
            debug: 是否打印调试信息

        Returns:
            TEDS分数
        """
        try:
            conversion_rounds = 1  # 初始转换算作第1轮

            # 转换为HTML格式（使用默认参数）
            gt_html = phy_json_to_html(gt_file)
            pred_html = phy_txt_to_html(pred_file)
            final_pred_html = pred_html

            # 验证HTML是否有效
            if not gt_html or gt_html == '<html><body><table></table></body></html>':
                print(f"Warning: Empty or invalid ground truth HTML for {gt_file}")
                return 0.0

            if not pred_html or pred_html == '<html><body><table></table></body></html>':
                print(f"Warning: Empty or invalid prediction HTML for {pred_file}")
                return 0.0

            # 计算初始TEDS分数
            score = self.teds(gt_html, pred_html)

            # 验证分数的有效性
            if score < 0:
                print(f"Warning: Negative TEDS score {score} for {os.path.basename(gt_file)}")
                score = max(0.0, score)

            # 如果分数低于阈值且启用优化，则尝试参数优化
            if optimize_low_scores and score < score_threshold:
                optimized_result = self._optimize_parameters(gt_file, pred_file, gt_html, score, debug)
                if optimized_result['score'] > score:
                    if debug:
                        print(f"Parameter optimization improved score from {score:.4f} to {optimized_result['score']:.4f}")
                    score = optimized_result['score']
                    final_pred_html = optimized_result['html']
                    conversion_rounds += optimized_result['rounds']  # 累加优化轮数

            # 保存最终的预测HTML
            if self.save_pred_html:
                self._save_pred_html_file(pred_file, final_pred_html, conversion_rounds)

            return score

        except Exception as e:
            print(f"Error processing {gt_file} and {pred_file}: {e}")
            import traceback
            traceback.print_exc()
            return 0.0

    def evaluate_single_pair_with_rounds(self, gt_file: str, pred_file: str, debug: bool = False,
                                       optimize_low_scores: bool = True, score_threshold: float = 0.9) -> Dict[str, Any]:
        """
        评估单个文件对的TEDS分数，返回分数和转换轮数

        Returns:
            dict: {'score': float, 'rounds': int} 包含分数和转换轮数
        """
        try:
            conversion_rounds = 1  # 初始转换算作第1轮

            # 转换为HTML格式（使用默认参数）
            gt_html = phy_json_to_html(gt_file)
            pred_html = phy_txt_to_html(pred_file)
            final_pred_html = pred_html

            # 验证HTML是否有效
            if not gt_html or gt_html == '<html><body><table></table></body></html>':
                print(f"Warning: Empty or invalid ground truth HTML for {gt_file}")
                return {'score': 0.0, 'rounds': conversion_rounds}

            if not pred_html or pred_html == '<html><body><table></table></body></html>':
                print(f"Warning: Empty or invalid prediction HTML for {pred_file}")
                return {'score': 0.0, 'rounds': conversion_rounds}

            # 计算初始TEDS分数
            score = self.teds(gt_html, pred_html)

            # 验证分数的有效性
            if score < 0:
                print(f"Warning: Negative TEDS score {score} for {os.path.basename(gt_file)}")
                score = max(0.0, score)

            # 如果分数低于阈值且启用优化，则尝试参数优化
            if optimize_low_scores and score < score_threshold:
                optimized_result = self._optimize_parameters(gt_file, pred_file, gt_html, score, debug)
                if optimized_result['score'] > score:
                    if debug:
                        print(f"Parameter optimization improved score from {score:.4f} to {optimized_result['score']:.4f}")
                    score = optimized_result['score']
                    final_pred_html = optimized_result['html']
                    conversion_rounds += optimized_result['rounds']  # 累加优化轮数

            # 保存最终的预测HTML
            if self.save_pred_html:
                self._save_pred_html_file(pred_file, final_pred_html, conversion_rounds)

            return {'score': score, 'rounds': conversion_rounds}

        except Exception as e:
            print(f"Error processing {gt_file} and {pred_file}: {e}")
            import traceback
            traceback.print_exc()
            return {'score': 0.0, 'rounds': 1}

    def _optimize_parameters(self, gt_file: str, pred_file: str, gt_html: str,
                           baseline_score: float, debug: bool = False) -> Dict[str, Any]:
        """
        针对低分数文件进行参数优化

        Args:
            gt_file: 真实标注文件路径
            pred_file: 预测标注文件路径
            gt_html: 真实标注的HTML
            baseline_score: 基线分数
            debug: 是否打印调试信息

        Returns:
            dict: {'score': float, 'html': str, 'rounds': int} 包含最佳分数、HTML和转换轮数
        """
        best_score = baseline_score
        best_params = None
        best_pred_html = None
        tested_rounds = 0  # 统计测试的配置数量

        # 扩展参数搜索空间，增加更多配置组合以提高转换成功率
        param_configs = [
            # 原有的5种基础配置
            {"tolerance_factor": 0.05, "min_tolerance": 1.0, "max_tolerance": 5.0, "boundary_strategy": "median"},
            {"tolerance_factor": 0.15, "min_tolerance": 3.0, "max_tolerance": 15.0, "boundary_strategy": "mean"},
            {"tolerance_factor": 0.25, "min_tolerance": 5.0, "max_tolerance": 25.0, "boundary_strategy": "median"},
            {"tolerance_factor": 0.1, "min_tolerance": 2.0, "max_tolerance": 10.0, "boundary_strategy": "mean"},
            {"tolerance_factor": 0.1, "min_tolerance": 2.0, "max_tolerance": 10.0, "boundary_strategy": "min"},

            # 新增的扩展配置 - 极高精度组合
            {"tolerance_factor": 0.02, "min_tolerance": 0.5, "max_tolerance": 3.0, "boundary_strategy": "median"},
            {"tolerance_factor": 0.03, "min_tolerance": 0.8, "max_tolerance": 4.0, "boundary_strategy": "mean"},

            # 新增的扩展配置 - 极高容差组合
            {"tolerance_factor": 0.3, "min_tolerance": 8.0, "max_tolerance": 30.0, "boundary_strategy": "median"},
            {"tolerance_factor": 0.35, "min_tolerance": 10.0, "max_tolerance": 35.0, "boundary_strategy": "mean"},
            {"tolerance_factor": 0.4, "min_tolerance": 12.0, "max_tolerance": 40.0, "boundary_strategy": "min"},

            # 新增的扩展配置 - 中间值精细调整
            {"tolerance_factor": 0.08, "min_tolerance": 1.5, "max_tolerance": 8.0, "boundary_strategy": "median"},
            {"tolerance_factor": 0.12, "min_tolerance": 2.5, "max_tolerance": 12.0, "boundary_strategy": "mean"},
            {"tolerance_factor": 0.18, "min_tolerance": 4.0, "max_tolerance": 18.0, "boundary_strategy": "min"},
            {"tolerance_factor": 0.22, "min_tolerance": 6.0, "max_tolerance": 22.0, "boundary_strategy": "median"},

            # 新增的扩展配置 - max策略组合
            {"tolerance_factor": 0.1, "min_tolerance": 2.0, "max_tolerance": 10.0, "boundary_strategy": "max"},
            {"tolerance_factor": 0.15, "min_tolerance": 3.0, "max_tolerance": 15.0, "boundary_strategy": "max"},
            {"tolerance_factor": 0.2, "min_tolerance": 4.0, "max_tolerance": 20.0, "boundary_strategy": "max"},

            # 新增的扩展配置 - 特殊比例组合
            {"tolerance_factor": 0.06, "min_tolerance": 1.2, "max_tolerance": 6.0, "boundary_strategy": "mean"},
            {"tolerance_factor": 0.14, "min_tolerance": 2.8, "max_tolerance": 14.0, "boundary_strategy": "median"},
            {"tolerance_factor": 0.28, "min_tolerance": 7.0, "max_tolerance": 28.0, "boundary_strategy": "min"},
        ]

        if debug:
            print(f"Optimizing parameters for {os.path.basename(pred_file)} (baseline: {baseline_score:.4f})")

        for i, config in enumerate(param_configs):
            try:
                tested_rounds += 1  # 每次尝试算作一轮

                # 使用新参数转换HTML
                pred_html = phy_txt_to_html(
                    pred_file,
                    tolerance_factor=config["tolerance_factor"],
                    min_tolerance=config["min_tolerance"],
                    max_tolerance=config["max_tolerance"],
                    boundary_strategy=config["boundary_strategy"]
                )

                # 验证HTML有效性
                if not pred_html or pred_html == '<html><body><table></table></body></html>':
                    continue

                # 计算新分数
                score = self.teds(gt_html, pred_html)

                if score > best_score:
                    best_score = score
                    best_params = config
                    best_pred_html = pred_html
                    if debug:
                        print(f"  Config {i+1}: {score:.4f} (improved!) - {config}")
                elif debug:
                    print(f"  Config {i+1}: {score:.4f} - {config}")

            except Exception as e:
                if debug:
                    print(f"  Config {i+1}: Error - {e}")
                continue

        if best_params and debug:
            print(f"Best parameters: {best_params}")

        return {
            'score': best_score,
            'html': best_pred_html if best_pred_html else None,
            'rounds': tested_rounds
        }
    
    def evaluate_directories(self, gt_dir: str, pred_dir: str, verbose: bool = True,
                           optimize_low_scores: bool = True, score_threshold: float = 0.9) -> dict:
        """
        评估两个目录中所有匹配文件的TEDS分数
        
        Args:
            gt_dir: 原始标注目录
            pred_dir: 预测标注目录
            verbose: 是否打印详细信息
            optimize_low_scores: 是否对低分数文件进行参数优化
            score_threshold: 触发优化的分数阈值

        Returns:
            评估结果字典，包含平均分数、所有分数等信息
        """
        # 找到匹配的文件对
        matching_pairs = self.find_matching_files(gt_dir, pred_dir)
        
        if not matching_pairs:
            print(f"No matching files found between {gt_dir} and {pred_dir}")
            return {
                'average_score': 0.0,
                'total_pairs': 0,
                'scores': [],
                'file_pairs': []
            }
        
        if verbose:
            print(f"Found {len(matching_pairs)} matching file pairs")
        
        scores = []
        processed_pairs = []
        optimized_count = 0

        for i, (gt_file, pred_file) in enumerate(matching_pairs):
            if verbose:
                print(f"Processing {i+1}/{len(matching_pairs)}: {os.path.basename(gt_file)}")

            result = self.evaluate_single_pair_with_rounds(gt_file, pred_file,
                                                         optimize_low_scores=optimize_low_scores,
                                                         score_threshold=score_threshold)
            score = result['score']
            rounds = result['rounds']

            scores.append(score)
            processed_pairs.append((gt_file, pred_file))

            if verbose:
                if rounds > 1:
                    print(f"  TEDS Score: {score:.4f} ({rounds} rounds)")
                else:
                    print(f"  TEDS Score: {score:.4f} (1 round)")
        
        # 计算平均分数
        average_score = sum(scores) / len(scores) if scores else 0.0
        
        result = {
            'average_score': average_score,
            'total_pairs': len(scores),
            'scores': scores,
            'file_pairs': processed_pairs
        }
        
        if verbose:
            print(f"\nEvaluation Results:")
            print(f"Total file pairs: {result['total_pairs']}")
            print(f"Average TEDS Score: {result['average_score']:.4f}")
            print(f"Min Score: {min(scores):.4f}" if scores else "N/A")
            print(f"Max Score: {max(scores):.4f}" if scores else "N/A")
        
        return result


def main():
    """
    主函数，可以通过命令行参数或直接修改路径来使用
    """
    import sys
    
    # 默认路径，可以根据需要修改
    default_gt_dir = "./cs_data/wired_optimizer"
    default_pred_dir = "./cs_data/demo_wired_teds_test/center"
    
    # 如果提供了命令行参数，使用命令行参数
    if len(sys.argv) >= 3:
        gt_dir = sys.argv[1]
        pred_dir = sys.argv[2]
    else:
        gt_dir = default_gt_dir
        pred_dir = default_pred_dir
        print(f"Using default directories:")
        print(f"  Ground Truth: {gt_dir}")
        print(f"  Predictions: {pred_dir}")
        print(f"To use custom directories, run: python teds_evaluator.py <gt_dir> <pred_dir>")
        print()
    
    # 检查目录是否存在
    if not os.path.exists(gt_dir):
        print(f"Error: Ground truth directory does not exist: {gt_dir}")
        return
    
    if not os.path.exists(pred_dir):
        print(f"Error: Prediction directory does not exist: {pred_dir}")
        return
    
    # 创建评估器并运行评估（启用参数优化和HTML保存）
    # 可以手动修改这里的参数来控制HTML保存
    save_html = True  # 手动开关：是否保存HTML
    html_output_dir = "./pred_html_outputs_5"  # 手动配置：保存路径

    evaluator = TEDSEvaluator(save_pred_html=save_html, html_output_dir=html_output_dir)
    print("启用参数优化功能：对TEDS分数低于0.9的文件进行参数调优")
    if save_html:
        print(f"启用HTML保存功能：保存预测HTML到 {html_output_dir}")
    result = evaluator.evaluate_directories(gt_dir, pred_dir, verbose=True,
                                          optimize_low_scores=True, score_threshold=0.9)
    
    # 保存结果到文件（可选）
    # import json
    # with open('teds_results.json', 'w') as f:
    #     json.dump(result, f, indent=2)


if __name__ == "__main__":
    main()
