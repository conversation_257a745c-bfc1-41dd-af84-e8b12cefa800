#!/usr/bin/env python3
"""
物理表格标注转换器测试脚本
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from phy_teds.phy_table_converter import PhysicalTableConverter


def test_interface():
    """测试转换器接口"""
    print("=== 测试物理表格标注转换器接口 ===\n")
    
    # 创建转换器实例
    converter = PhysicalTableConverter()
    print("✓ 转换器实例创建成功")
    
    # 测试文件路径
    json_file = "cs_data/wired_optimizer/533771053059540074_table_annotation.json"
    txt_file = "cs_data/demo_wired_teds_test/center/533771053059540074.jpg.txt"
    
    # 检查文件是否存在
    if not os.path.exists(json_file):
        print(f"✗ JSON文件不存在: {json_file}")
        return False
    if not os.path.exists(txt_file):
        print(f"✗ TXT文件不存在: {txt_file}")
        return False
    
    print(f"✓ 测试文件存在")
    print(f"  - JSON文件: {json_file}")
    print(f"  - TXT文件: {txt_file}")
    
    # 测试JSON加载
    try:
        json_data = converter.load_json_annotation(json_file)
        print(f"✓ JSON文件加载成功，包含 {len(json_data.get('cells', []))} 个单元格")
    except Exception as e:
        print(f"✗ JSON文件加载失败: {e}")
        return False
    
    # 测试TXT加载
    try:
        txt_data = converter.load_txt_annotation(txt_file)
        print(f"✓ TXT文件加载成功，包含 {len(txt_data)} 行坐标数据")
    except Exception as e:
        print(f"✗ TXT文件加载失败: {e}")
        return False
    
    # 测试JSON到HTML转换接口
    try:
        html_gt = converter.json_to_html(json_data)
        print(f"✓ JSON到HTML转换接口调用成功")
        print(f"  输出长度: {len(html_gt)} 字符")
        print(f"  输出预览: {html_gt[:100]}...")
    except Exception as e:
        print(f"✗ JSON到HTML转换失败: {e}")
        return False
    
    # 测试TXT到HTML转换接口
    try:
        html_pred = converter.txt_to_html(txt_data)
        print(f"✓ TXT到HTML转换接口调用成功")
        print(f"  输出长度: {len(html_pred)} 字符")
        print(f"  输出预览: {html_pred[:100]}...")
    except Exception as e:
        print(f"✗ TXT到HTML转换失败: {e}")
        return False
    
    # 测试文件直接转换接口
    try:
        html_gt_file = converter.json_file_to_html(json_file)
        html_pred_file = converter.txt_file_to_html(txt_file)
        print(f"✓ 文件直接转换接口调用成功")
    except Exception as e:
        print(f"✗ 文件直接转换失败: {e}")
        return False
    
    print("\n=== 接口测试完成 ===")
    print("所有接口都可以正常调用，等待实现具体的转换逻辑")
    return True


def show_data_samples():
    """显示数据样本"""
    print("\n=== 数据样本展示 ===\n")
    
    converter = PhysicalTableConverter()
    
    # JSON数据样本
    json_file = "cs_data/wired_optimizer/533771053059540074_table_annotation.json"
    if os.path.exists(json_file):
        print("JSON数据样本 (前3个单元格):")
        json_data = converter.load_json_annotation(json_file)
        cells = json_data.get('cells', [])
        for i, cell in enumerate(cells[:3]):
            content = cell.get('content', [])
            text = content[0].get('text', '') if content else ''
            lloc = cell.get('lloc', {})
            print(f"  单元格 {i+1}:")
            print(f"    文本: '{text}'")
            print(f"    位置: 行{lloc.get('start_row', 0)}-{lloc.get('end_row', 0)}, "
                  f"列{lloc.get('start_col', 0)}-{lloc.get('end_col', 0)}")
            print(f"    表头: {cell.get('header', False)}")
        print()
    
    # TXT数据样本
    txt_file = "cs_data/demo_wired_teds_test/center/533771053059540074.jpg.txt"
    if os.path.exists(txt_file):
        print("TXT数据样本 (前3行):")
        txt_data = converter.load_txt_annotation(txt_file)
        for i, line in enumerate(txt_data[:3]):
            coords = line.split(';')
            print(f"  第 {i+1} 行: {len(coords)} 个坐标点")
            for j, coord in enumerate(coords):
                x, y = coord.split(',')
                print(f"    点{j+1}: ({float(x):.1f}, {float(y):.1f})")
        print()


def main():
    """主函数"""
    print("物理表格标注转换器测试")
    print("=" * 50)
    
    # 测试接口
    success = test_interface()
    
    if success:
        # 显示数据样本
        show_data_samples()
        
        print("下一步:")
        print("1. 实现 json_to_html() 方法的具体逻辑")
        print("2. 实现 txt_to_html() 方法的具体逻辑")
        print("3. 添加单元测试")
        print("4. 与TEDS评估器集成测试")
    else:
        print("接口测试失败，请检查文件路径和依赖")


if __name__ == "__main__":
    main()
