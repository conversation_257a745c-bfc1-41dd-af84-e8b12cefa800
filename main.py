#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/4/21 21:25
# <AUTHOR> <EMAIL>
# @FileName: main

from modules.pipelines import OCRPipeline
from modules.pipelines.dto import get_available_models

AVAILABLE_MODELS = get_available_models()

DEFAULT_MODEL = AVAILABLE_MODELS[list(AVAILABLE_MODELS.keys())[0]]

# 初始化OCR pipeline
ocr = OCRPipeline(
    det_config=DEFAULT_MODEL["det_config"],
    cls_config=DEFAULT_MODEL["cls_config"],
    rec_config=DEFAULT_MODEL["rec_config"],
)

# OCR 图像目录，内部实现了单图推理
image_dir = "/Users/<USER>/Documents/临时数据/产品测试样本集"
# image_dir = "/aipdf-mlp/xelawk/datasets/private/产品测试样本集"

# output_dir = "/aipdf-mlp/xelawk/debug/onnx_ocr_test/debug"
output_dir = "/Users/<USER>/Documents/临时数据/onnx_ocr_test/debug_v202505081915"

results_dict = ocr.process_batch(
    image_dir=image_dir,
    show_original=True,
    persist_results=True,
    output_dir=output_dir,
)